<ion-header style="background-color:  var(--ion-color-primary-contrast);">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="showWorkboatSettings != true">{{'AA_CONFIGURATION_WINCH_TITLE' | translate}}</ion-title>
    <ion-title *ngIf="showWorkboatSettings == true">{{'AA_CONFIGURATION_WINCH_TITLE_WORKBOAT' | translate}}</ion-title>
    <ion-buttons slot="end">
      <ion-button color="primary" (click)="helpService.switchMode()" class="help-button-style">
        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
    <ion-segment [disabled]='helpService.helpMode || readOnly || isEditLmd || selectedEquipmentList.length > 0 || selectedCertList.length > 0' mode="ios" [(ngModel)]="fieldSegment">
      <ion-segment-button value="equipment" class="ion-segment-button-style ">
        <ion-label>{{'Equipment'| translate}}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="lines" class="ion-segment-button-style ">
        <ion-label>{{'Line' | translate}}</ion-label>
      </ion-segment-button>
    </ion-segment>
    <p *ngIf="!readOnly && !isEditLmd && (selectedEquipmentList.length > 0 || selectedCertList.length > 0)" style="width: 95%; margin: auto; color: var(--ion-color-dark-shade)"> Unselect all items to choose</p>
</ion-header>
<ion-content>

  <div
    style="position: fixed;left: 150px;top: 130px;width: calc(100% - 200px);max-height: calc(100% - 200px);background-color: rgb(248, 242, 221);z-index: 1000; padding-bottom: 10px; border: solid lightgrey 0.5px;box-shadow: 0 0 20px rgb(0 0 0 / 90%);border-radius: 10px;overflow-y: scroll;"
    *ngIf="isUtility == false && ((platformId == 'electron' || device.platform == 'browser' ) && showDiv == true && (selectedEquipmentList.length > 0 || selectedCertList.length > 0))">

    <div style="display: block;">
  
    <p *ngIf="selectedEquipmentList.length > 0 && fieldSegment == 'equipment'" style="display: inline-block; margin: 10px 0px 5px 15px;">
      {{selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_NAME}}</p>
    <p *ngIf="selectedCertList.length > 0 && fieldSegment == 'lines'" style="display: inline-block; margin: 10px 0px 5px 15px;">{{selectedCertList[selectedCertList.length
      - 1].EQUIP_CERT_NAME}}</p>

      <ion-buttons (click)="closeDiv()" tappable style="margin: 10px 0px 5px 15px; float: right;">
        <ion-button style="padding-bottom: 8px;">
          <fa-icon icon="times"></fa-icon>
        </ion-button>
      </ion-buttons>
    </div>
  <!-- && EQIPMENT SECTION START -->
    <div *ngIf="fieldSegment == 'equipment' && isUtility == false" 
      style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829); padding-left: 10px; padding-right: 10px;">
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'Mainline' | translate}}
          <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span>
        </p>
        <div style="width: 100%;">
          <div *ngIf="helpService.helpMode">
            <div>
              <ion-item
                (click)="!(helpService.helpMode || readOnly || !checkIfSelected(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)) ? presentModal('MAINLINE', selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID) : test()"
                no-lines text-wrap tappable style="width: 100% !important;" class="ion-item-generic-style" mode="ios"
                [ngClass]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == undefined ? 'err' : 'valid'">
                <div
                  style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == undefined"
                  class="drop-down-arrow  value-field">{{'Unknown' | translate }}</div>
                <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME != '' && selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME != undefined"
                  class="value-field">
                  {{selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length -
                  1].EQUIP_ID)].MAINLINE_NAME}}</div>
                <ion-note item-right style="display:inline-flex">
                  <p class="drop-down-arrow">
                    <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                  </p>
                </ion-note>
              </ion-item>
              <ion-label *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == undefined" color="danger">{{ 'required' | translate }} </ion-label>
            </div>
          </div>
          <div *ngIf="!helpService.helpMode">
            <div>
              <ion-item
                (click)="!(helpService.helpMode || readOnly || !checkIfSelected(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)) ? presentModal('MAINLINE', selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID) : test()"
                no-lines text-wrap tappable style="width: 100% !important;" class="ion-item-generic-style" mode="ios"
                [ngClass]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == undefined ? 'err' : 'valid'">
                <div
                  style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == undefined"
                  class="drop-down-arrow  value-field">{{'Unknown' | translate }}</div>
                <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME != '' && selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME != undefined"
                  class="value-field">
                  {{selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length -
                  1].EQUIP_ID)].MAINLINE_NAME}}</div>
                <ion-note item-right style="display:inline-flex">
                  <p class="drop-down-arrow">
                    <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                  </p>
                </ion-note>
              </ion-item>
              <ion-label *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].MAINLINE_NAME == undefined" color="danger">{{ 'required' | translate }} </ion-label>
            </div>
          </div>
        </div>
      </div>

      <div style="display:flex">
        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'ESTIMATED_LINE_LENGTH_LABEL' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: flex; width: 100%;">
            <div tooltip="{{'HELP_TEXT_WINCHES_ESTIMATED_LENGTH'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].ESTIMATE_LINE_LENGTH"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px; margin-bottom: 0px;">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode' style="display: flex; width: 100%;">
            <div>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].ESTIMATE_LINE_LENGTH"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
        </div>
  
        <!-- && pre deployment completed checkbox START WEB-->
        <div style="Width: 100%;margin-top:30px;">
          <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: inline-flex; width: 100%;">
            <div tooltip="{{'HELP_TEXT_WINCHES_PRE-DEPLOYMENT_INSPECTION'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-checkbox 
              [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT"
              [checked]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT" color="primary" style="margin-right:10px;" disabled="true"></ion-checkbox>
              <ion-label>{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode' style="display: inline-flex; width: 100%;">
              <ion-checkbox 
              [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT"
              [checked]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT" color="primary" style="margin-right:10px;" (ionChange)="setPreDepldCompleted($event,selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)"
              [disabled]='(helpService.helpMode || readOnly)'></ion-checkbox>
              <ion-label>{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
          </div>
        </div>
        <!-- && pre deployment completed checkbox END WEB-->
      </div>

      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'WINCHES_AVERAGE_LOAD' | translate}}</p>
        <div *ngIf="helpService.helpMode" class="winches-dropdown">
          <div tooltip="{{'HELP_TEXT_WINCHES_AVERAGE_LOAD'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].AVERAGE_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].AVERAGE_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
      </div>
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'WINCHES_PEAK_LOAD' | translate}}</p>
        <div *ngIf="helpService.helpMode" class="winches-dropdown">
          <div tooltip="{{'HELP_TEXT_WINCHES_PEAK_LOAD'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PEAK_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PEAK_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
      </div>
  
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_END_TYPE_LABEL' | translate}}
        </p>
        <div *ngIf="helpService.helpMode" class="winches-dropdown">
          <div tooltip="{{'HELP_TEXT_WINCHES_END_TYPE'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                [(ngModel)]='selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].END_IN_USE'
                labelPlacement="stacked"  interface="popover" placeholder="{{'WINCHES_END_TYPE_PLACEHOLDER' | translate}}"
                [disabled]='helpService.helpMode || readOnly'>
                <ion-select-option value="A">A</ion-select-option>
                <ion-select-option value="B">B</ion-select-option>
              </ion-select>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                [(ngModel)]='selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].END_IN_USE'
                labelPlacement="stacked"  interface="popover" placeholder="{{'WINCHES_END_TYPE_PLACEHOLDER' | translate}}"
                [disabled]='helpService.helpMode || readOnly'>
                <ion-select-option value="A">A</ion-select-option>
                <ion-select-option value="B">B</ion-select-option>
              </ion-select>
            </ion-item>
          </div>
        </div>
      </div>
  
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_TAIL_LENGTH_LABEL' | translate}} <span
          style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span>
        </p>
        <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
          <div tooltip="{{'HELP_TEXT_WINCHES_TAIL_LENGTH'|translate}}" positionV="top" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item [ngClass]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL=='' ?'err':'valid'">
              <ion-select
                style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                [(ngModel)]='selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL'
                labelPlacement="stacked"  interface="popover" placeholder="{{'WINCHES_TAIL_LENGTH_PLACEHOLDER' | translate}}"
                [disabled]='helpService.helpMode || readOnly'
                (ionChange)="setSelectedCert(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)"
                [compareWith]="compareTail">
                <ion-select-option
                  *ngFor="let tail of selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].TAIL_LIST"
                  [value]="tail">{{tail.CURRENT_LENGTH_IN_METER}} <span>{{(tail.NAME!="") ? '- '+tail.NAME : ''}} </span></ion-select-option>
              </ion-select>
            </ion-item>
            <ion-label *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL==''" color="danger">{{ 'required' | translate }} </ion-label>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item [ngClass]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL=='' ?'err':'valid'">
              <ion-select
                style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                [(ngModel)]='selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL'
                labelPlacement="stacked"  interface="popover" placeholder="{{'WINCHES_TAIL_LENGTH_PLACEHOLDER' | translate}}"
                [disabled]='helpService.helpMode || readOnly'
                (ionChange)="setSelectedCert(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)"
                [compareWith]="compareTail">
                <ion-select-option
                  *ngFor="let tail of selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].TAIL_LIST"
                  [value]="tail">{{ (tail.CURRENT_LENGTH_IN_METER!="" && tail.NAME!="") ? tail.CURRENT_LENGTH_IN_METER+" - "+tail.NAME : (tail.CURRENT_LENGTH_IN_METER!="" && tail.NAME=="") ? tail.CURRENT_LENGTH_IN_METER : tail.NAME }} </ion-select-option>
              </ion-select>
            </ion-item>
            <ion-label *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL==''" color="danger">{{ 'required' | translate }} </ion-label>
          </div>
        </div>
      </div>
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'WINCHES_CERTIFICATE_LABEL' | translate}}</p>
        <div style="width: 100%;">
          <div *ngIf="helpService.helpMode" class="winches-dropdown">
            <div tooltip="{{'HELP_TEXT_WINCHES_CERTIFICATE'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item
                (click)="!(helpService.helpMode || readOnly || !checkIfSelected(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)) ? presentModal('CERTIFICATE', selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID) : test()"
                no-lines text-wrap tappable style="width: 100% !important;" *ngIf="rpsList && rpsList.length >= 0"
                class="ion-item-generic-style" mode="ios">
                <div
                  style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME == undefined"
                  class="drop-down-arrow  value-field">{{'WINCHES_CERTIFICATE_PLACEHOLDER' | translate }}</div>
                <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME != '' && selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME != undefined"
                  class="value-field">
                  {{selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length -
                  1].EQUIP_ID)].EQUIP_CERT_NAME}}</div>
                <ion-note item-right style="display:inline-flex">
                  <p class="drop-down-arrow">
                    <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                  </p>
                </ion-note>
              </ion-item>
            </div>
          </div>
          <div *ngIf="!helpService.helpMode">
            <div>
              <ion-item
                (click)="!(helpService.helpMode || readOnly || !checkIfSelected(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)) ? presentModal('CERTIFICATE', selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID) : test()"
                no-lines text-wrap tappable style="width: 100% !important;" *ngIf="rpsList && rpsList.length >= 0"
                class="ion-item-generic-style" mode="ios"
                [disabled]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL.CURRENT_LENGTH_IN_METER!='Other Tail'">
                <div
                  style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME == '' || selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME == undefined"
                  class="drop-down-arrow  value-field">{{'WINCHES_CERTIFICATE_PLACEHOLDER' | translate }}</div>
                <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                  *ngIf="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME != '' && selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].EQUIP_CERT_NAME != undefined"
                  class="value-field">
                  {{selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length -
                  1].EQUIP_ID)].EQUIP_CERT_NAME}}</div>
                <ion-note item-right style="display:inline-flex">
                  <p class="drop-down-arrow">
                    <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                  </p>
                </ion-note>
              </ion-item>
            </div>
          </div>
        </div>
      </div>
  
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'WINCHES_NOTES_LABEL' | translate}}</p>
        <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
          <div tooltip="{{'HELP_TEXT_WINCHES_NOTE'|translate}}" positionV="top" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <!-- <ion-label position="floating">{{'ESTIMATED_LINE_LENGTH_PLACEHOLDER' | translate}}</ion-label> -->
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].NOTES"
                (ionFocus)="onFocusUserInputField($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].NOTES)">
              </ion-textarea>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <!-- <ion-label position="floating">{{'ESTIMATED_LINE_LENGTH_PLACEHOLDER' | translate}}</ion-label> -->
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].NOTES"
                (ionFocus)="onFocusUserInputField($event, selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].NOTES)">
              </ion-textarea>
            </ion-item>
          </div>
        </div>
      </div>
  
  
    </div>
  <!-- && EQIPMENT SECTION END -->

  <!-- && LINE SECTION START -->
    <div *ngIf="fieldSegment == 'lines'"
      style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829); padding-left: 10px; padding-right: 10px;">


      <div style="display:flex">
        <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'ESTIMATED_LINE_LENGTH_LABEL' | translate}}</p>
        <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: inline-flex; width: 100%;">
          <div tooltip="{{'HELP_TEXT_WINCHES_ESTIMATED_LENGTH'|translate}}" positionV="bottom"
            [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
            [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].ESTIMATE_LINE_LENGTH"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
            </ion-item>
          </div>
          <div class="form-group" style="padding:10px 10px 0px 17px">
            <label
              style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
            </label>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode' style="display: inline-flex; width: 100%;">
          <div>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].ESTIMATE_LINE_LENGTH"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
            </ion-item>
          </div>
          <div class="form-group" style="padding:10px 10px 0px 17px">
            <label
              style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
            </label>
          </div>
        </div>
        </div>

        <!-- && Pre-deployment-completed checkbox for LINE web -->
        <div style="Width: 100%;margin-top:30px;">
          <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: inline-flex; width: 100%;">
            <div tooltip="{{'HELP_TEXT_WINCHES_PRE-DEPLOYMENT_INSPECTION'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-checkbox [checked]="PreDeplCmltdCert.includes(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)" color="primary" disabled="true" style="margin-right:10px;" ></ion-checkbox>
              <ion-label>{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode' style="display: inline-flex; width: 100%;">
              <ion-checkbox [checked]="PreDeplCmltdCert.includes(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)" color="primary" style="margin-right:10px;" (ionChange)="setPreDepldCompletedCert($event,selectedCertList[selectedCertList.length - 1].EQUIP_CERT)" ></ion-checkbox>
              <ion-label>{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
          </div>
        </div>
        <!-- && Pre-deployment-completed checkbox for LINE web-->
      </div>

      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'WINCHES_AVERAGE_LOAD' | translate}}</p>
        <div *ngIf="helpService.helpMode" class="winches-dropdown">
          <div tooltip="{{'HELP_TEXT_WINCHES_AVERAGE_LOAD'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].AVERAGE_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].AVERAGE_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
      </div>
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'WINCHES_PEAK_LOAD' | translate}}</p>
        <div *ngIf="helpService.helpMode" class="winches-dropdown">
          <div tooltip="{{'HELP_TEXT_WINCHES_PEAK_LOAD'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].PEAK_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <ion-input ngDefaultControl
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].PEAK_LOAD"
                [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
            </ion-item>
          </div>
        </div>
      </div>
  
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_END_TYPE_LABEL' | translate}}
        </p>
        <div *ngIf="helpService.helpMode" class="winches-dropdown">
          <div tooltip="{{'HELP_TEXT_WINCHES_END_TYPE'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                [(ngModel)]='selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].END_IN_USE' labelPlacement="stacked"  interface="popover"
                placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'>
                <ion-select-option value="A">A</ion-select-option>
                <ion-select-option value="B">B</ion-select-option>
              </ion-select>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                [(ngModel)]='selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].END_IN_USE' labelPlacement="stacked"  interface="popover"
                placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'>
                <ion-select-option value="A">A</ion-select-option>
                <ion-select-option value="B">B</ion-select-option>
              </ion-select>
            </ion-item>
          </div>
        </div>
      </div>
      <!-- <div style="Width: 100%" *ngIf="!showWorkboatSettings">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_CERT_TAIL_LENGTH_LABEL' | translate}} <span
          style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span>
        </p>
        <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
          <div tooltip="{{'HELP_TEXT_WINCHES_TAIL_LENGTH'|translate}}" positionV="top" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item [ngClass]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].SELECTED_TAI=='' ?'err':'valid'">
              <ion-select
                style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                [(ngModel)]='selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].SELECTED_TAIL' interface="popover"
                placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'
                (ionChange)="setSelectedLine(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)" [compareWith]="compareTail">
                <ion-select-option
                  *ngFor="let tail of selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].TAIL_LIST"
                  [value]="tail">{{tail.CURRENT_LENGTH_IN_METER}} - <span>{{tail.NAME}} </span></ion-select-option>
              </ion-select>
            </ion-item>
            <ion-label *ngIf="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].SELECTED_TAIL==''" color="danger">{{ 'required' | translate }} </ion-label>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item [ngClass]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].SELECTED_TAIL=='' ?'err':'valid'">
              <ion-select
                style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                [(ngModel)]='selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].SELECTED_TAIL' interface="popover"
                placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'
                (ionChange)="setSelectedLine(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)" [compareWith]="compareTail">
                <ion-select-option
                  *ngFor="let tail of selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].TAIL_LIST"
                  [value]="tail">{{tail.CURRENT_LENGTH_IN_METER}} - <span>{{tail.NAME}} </span></ion-select-option>
              </ion-select>
            </ion-item>
            <ion-label *ngIf="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].SELECTED_TAIL==''" color="danger">{{ 'required' | translate }} </ion-label>
          </div>
        </div>
      </div> -->
  
      <div style="Width: 100%">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
          {{'WINCHES_NOTES_LABEL' | translate}}</p>
        <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
          <div tooltip="{{'HELP_TEXT_WINCHES_NOTE'|translate}}" positionV="top" [arrow]="helpService.showArrow"
            [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
            <ion-item>
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].NOTES"
                (ionFocus)="onFocusUserInputField($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].NOTES)">
              </ion-textarea>
            </ion-item>
          </div>
        </div>
        <div *ngIf='!helpService.helpMode'>
          <div>
            <ion-item>
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].NOTES"
                (ionFocus)="onFocusUserInputField($event, selectedCertList[getLIDIndexCert(selectedCertList[selectedCertList.length - 1].EQUIP_CERT)].NOTES)">
              </ion-textarea>
            </ion-item>
          </div>
        </div>
      </div>
    </div>
    <!-- && LINE SECTION END -->
  </div>

  <!-- && EQIPMENT SECTION START  -->
  <ion-list *ngIf="fieldSegment == 'equipment'" style="padding-bottom: 50px;">
    <ion-item-sliding *ngFor="let options of equipmentList;let i = index">
      <!-- <ion-item *ngFor="let options of equipmentList;let i = index" tappable> -->
      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);" [ngClass]="{'my-css-class': checkDivIsOpen(options) === true}">
        <ion-item *ngIf='!readOnly' lines="none">
          <ion-checkbox color="primary" (ionChange)="onChecked($event,options.EQUIP_ID, i)" [checked]="checkIfEquipmentSelected(options.EQUIP_ID)" [value]="options.EQUIP_ID"
            [disabled]='helpService.helpMode || readOnly || isEditLmd'>
          </ion-checkbox>
        </ion-item>
        <ion-label (click)="helightItem(options)">
          {{options.EQUIP_NAME}}
        </ion-label>
        <!-- <ion-label>
          {{options.EQUIP_TYPE}}
        </ion-label> -->
        <ion-buttons (click)="icon(i)" tappable *ngIf="isUtility == false">
          <ion-button style="padding-bottom: 8px;" *ngIf="checkIfSelected(options.EQUIP_ID)" class="winch-info-button">
            <span *ngIf="platformId == 'electron' || device.platform == 'browser'" class="winch-info-message">Click to fill line information</span>
            <span *ngIf="platformId !== 'electron' && device.platform != 'browser' && !checkSectionIsOpen(i)" class="winch-info-message">Click to fill line information</span>
            <ion-icon slot="icon-only" slot="end" [name]="checkSectionIsOpen(i) ? 'chevron-up-outline' :'chevron-down-outline'"  class="winch-info-icon"
              style="color:rgb(41, 40, 40)"></ion-icon>
          </ion-button>
        </ion-buttons>

      </ion-item>
      <div *ngIf="checkSectionIsOpen(i)" lines="none"
        style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829); padding-left: 40px;">
        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'Mainline' | translate}}
            <span style="color:rgb(221, 82, 82);font-size:15px;"> *
            </span>
          </p>
          <div style="width: 100%;">
            <div *ngIf="helpService.helpMode">
              <div>
                <ion-item
                  (click)="!(helpService.helpMode || readOnly || !checkIfSelected(options.EQUIP_ID)) ? presentModal('MAINLINE', options.EQUIP_ID) : test()"
                  no-lines text-wrap tappable style="width: 100% !important;" class="ion-item-generic-style" mode="ios"
                  [ngClass]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == undefined ?'err':'valid'">
                  <div
                    style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == undefined"
                    class="drop-down-arrow  value-field">{{'Unknown' | translate }}</div>
                  <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME != '' && selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME != undefined"
                    class="value-field">{{selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME}}</div>
                  <ion-note item-right style="display:inline-flex">
                    <p class="drop-down-arrow">
                      <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                    </p>
                  </ion-note>
                </ion-item>
                <ion-label *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == undefined" class="mainlineNmStyle" color="danger">{{ 'required' | translate }} </ion-label>
              </div>
            </div>
            <div *ngIf="!helpService.helpMode">
              <div>
                <ion-item
                  (click)="!(helpService.helpMode || readOnly || !checkIfSelected(options.EQUIP_ID)) ? presentModal('MAINLINE', options.EQUIP_ID) : test()"
                  no-lines text-wrap tappable style="width: 100% !important;" class="ion-item-generic-style" mode="ios"
                  [ngClass]=" selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == undefined ?'err':'valid'">
                  <div
                    style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == undefined"
                    class="drop-down-arrow  value-field">{{'Unknown' | translate }}</div>
                  <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME != '' && selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME != undefined"
                    class="value-field">{{selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME}}</div>
                  <ion-note item-right style="display:inline-flex">
                    <p class="drop-down-arrow">
                      <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                    </p>
                  </ion-note>
                </ion-item>
                <ion-label *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].MAINLINE_NAME == undefined" class="mainlineNmStyle" color="danger">{{ 'required' | translate }} </ion-label>
              </div>
            </div>
          </div>
        </div>

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'ESTIMATED_LINE_LENGTH_LABEL' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: inline-flex; width: 100%;">
            <div tooltip="{{'HELP_TEXT_WINCHES_ESTIMATED_LENGTH'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <!-- <ion-label position="floating">{{'ESTIMATED_LINE_LENGTH_PLACEHOLDER' | translate}}</ion-label> -->
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].ESTIMATE_LINE_LENGTH"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode' style="display: inline-flex; width: 100%;">
            <div>
              <ion-item>
                <!-- <ion-label position="floating">{{'ESTIMATED_LINE_LENGTH_PLACEHOLDER' | translate}}</ion-label> -->
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].ESTIMATE_LINE_LENGTH"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
        </div>

        <!-- && pre deployment completed checkbox MOBILE-->
        <div style="Width: 100%">
          <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: inline-flex; width: 100%;">
            <div tooltip="{{'HELP_TEXT_WINCHES_PRE-DEPLOYMENT_INSPECTION'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-checkbox color="primary" disabled="true" style="margin-right:10px;"
                [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT"
                [checked]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT"
                disabled="true"></ion-checkbox>
                <ion-label style="display: contents;">{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode' style="display: inline-flex; width: 100%;">
            <ion-item class="no-ripple">
              <ion-checkbox color="primary" style="margin-right:10px;"
              [(ngModel)]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT"
              [checked]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].PRE_DEP_INSP_CMPLT"
              (ionChange)="setPreDepldCompleted($event,options.EQUIP_ID)"
              [disabled]='(helpService.helpMode || readOnly)'></ion-checkbox>
              <ion-label style="display: contents;">{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
            </ion-item>
          </div>
        </div>
        <!-- && pre deployment completed checkbox MOBILE-->

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'WINCHES_AVERAGE_LOAD' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown">
            <div tooltip="{{'HELP_TEXT_WINCHES_AVERAGE_LOAD'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].AVERAGE_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].AVERAGE_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
        </div>
        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'WINCHES_PEAK_LOAD' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown">
            <div tooltip="{{'HELP_TEXT_WINCHES_PEAK_LOAD'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-input ngDefaultControl [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].PEAK_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <ion-input ngDefaultControl [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].PEAK_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
        </div>

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_END_TYPE_LABEL' | translate}}
          </p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown">
            <div tooltip="{{'HELP_TEXT_WINCHES_END_TYPE'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
              [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-select
                  style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                  [(ngModel)]='selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].END_IN_USE' labelPlacement="stacked"  interface="popover"
                  placeholder="{{'WINCHES_END_TYPE_PLACEHOLDER' | translate}}"
                  [disabled]='helpService.helpMode || readOnly'>
                  <ion-select-option value="A">A</ion-select-option>
                  <ion-select-option value="B">B</ion-select-option>
                </ion-select>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <ion-select
                  style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                  [(ngModel)]='selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].END_IN_USE' labelPlacement="stacked"  interface="popover"
                  placeholder="{{'WINCHES_END_TYPE_PLACEHOLDER' | translate}}"
                  [disabled]='helpService.helpMode || readOnly'>
                  <ion-select-option value="A">A</ion-select-option>
                  <ion-select-option value="B">B</ion-select-option>
                </ion-select>
              </ion-item>
            </div>
          </div>
        </div>

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_TAIL_LENGTH_LABEL' | translate}} <span
            style="color:rgb(221, 82, 82);font-size:15px;"> *
          </span>
          </p>
          <!-- & HELP MODE ENABLED START-->
          <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
            <div tooltip="{{'HELP_TEXT_WINCHES_TAIL_LENGTH'|translate}}" positionV="top" [arrow]="helpService.showArrow"
              [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
              <ion-item [ngClass]=" selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].SELECTED_TAIL=='' ?'err':'valid'">
                <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                  [(ngModel)]='selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].SELECTED_TAIL' labelPlacement="stacked"  interface="popover"
                  placeholder="{{'WINCHES_TAIL_LENGTH_PLACEHOLDER' | translate}}"
                  [disabled]='helpService.helpMode || readOnly' (ionChange)="setSelectedCert(options.EQUIP_ID)" [compareWith]="compareTail">
                  <ion-select-option *ngFor="let tail of selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].TAIL_LIST"
                    [value]="tail">{{tail.CURRENT_LENGTH_IN_METER}} <span>{{(tail.NAME!="") ? '- '+tail.NAME : ''}} </span></ion-select-option>
                </ion-select>
              </ion-item>
              <ion-label *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].SELECTED_TAIL==''" color="danger">{{ 'required' | translate }} </ion-label>
            </div>
          </div>
          <!-- & HELP MODE ENABLED END -->

          <!-- ~ HELP MODE DISABLED START -->
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item [ngClass]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].SELECTED_TAIL ?'valid' :'err'">
                <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                [(ngModel)]='selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].SELECTED_TAIL' labelPlacement="stacked"  interface="popover"
                  placeholder="{{'WINCHES_TAIL_LENGTH_PLACEHOLDER' | translate}}"
                  [disabled]='helpService.helpMode || readOnly' (ionChange)="setSelectedCert(options.EQUIP_ID)" [compareWith]="compareTail">
                  <ion-select-option
                    *ngFor="let tail of selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].TAIL_LIST"
                    [value]="tail">{{ (tail.CURRENT_LENGTH_IN_METER!="" && tail.NAME!="") ? tail.CURRENT_LENGTH_IN_METER+" - "+tail.NAME : (tail.CURRENT_LENGTH_IN_METER!="" && tail.NAME=="") ? tail.CURRENT_LENGTH_IN_METER : tail.NAME }}</ion-select-option>
                </ion-select>
              </ion-item>
              <ion-label *ngIf="!selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].SELECTED_TAIL" color="danger">{{ 'required' | translate }} </ion-label>
            </div>
          </div>
          <!-- ~ HELP MODE DISABLED END -->
        </div>
        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'WINCHES_CERTIFICATE_LABEL' | translate}}</p>
          <div style="width: 100%;">
            <div *ngIf="helpService.helpMode" class="winches-dropdown">
              <div tooltip="{{'HELP_TEXT_WINCHES_CERTIFICATE'|translate}}" positionV="bottom"
                [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                [hideOthers]=helpService.hideOthers>
                <ion-item
                  (click)="!(helpService.helpMode || readOnly || !checkIfSelected(options.EQUIP_ID)) ? presentModal('CERTIFICATE', options.EQUIP_ID) : test()"
                  no-lines text-wrap tappable style="width: 100% !important;" *ngIf="rpsList && rpsList.length >= 0"
                  class="ion-item-generic-style" mode="ios">
                  <div
                    style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME == undefined"
                    class="drop-down-arrow  value-field">{{'WINCHES_CERTIFICATE_PLACEHOLDER' | translate }}</div>
                  <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME != '' && selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME != undefined"
                    class="value-field">{{selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME}}</div>
                  <ion-note item-right style="display:inline-flex">
                    <p class="drop-down-arrow">
                      <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                    </p>
                  </ion-note>
                </ion-item>
              </div>
            </div>
            <div *ngIf="!helpService.helpMode">
              <div>
                <ion-item
                  (click)="!(helpService.helpMode || readOnly || !checkIfSelected(options.EQUIP_ID)) ? presentModal('CERTIFICATE', options.EQUIP_ID) : test()"
                  no-lines text-wrap tappable style="width: 100% !important;" *ngIf="rpsList && rpsList.length >= 0"
                  [disabled]="selectedEquipmentList[getLIDIndex(selectedEquipmentList[selectedEquipmentList.length - 1].EQUIP_ID)].SELECTED_TAIL.CURRENT_LENGTH_IN_METER!='Other Tail'"
                  class="ion-item-generic-style" mode="ios">
                  <div
                    style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME == '' || selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME == undefined"
                    class="drop-down-arrow  value-field">{{'WINCHES_CERTIFICATE_PLACEHOLDER' | translate }}</div>
                  <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                    *ngIf="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME != '' && selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME != undefined"
                    class="value-field">{{selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].EQUIP_CERT_NAME}}</div>
                  <ion-note item-right style="display:inline-flex">
                    <p class="drop-down-arrow">
                      <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                    </p>
                  </ion-note>
                </ion-item>
              </div>
            </div>
          </div>
        </div>

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'WINCHES_NOTES_LABEL' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
            <div tooltip="{{'HELP_TEXT_WINCHES_NOTE'|translate}}" positionV="top" [arrow]="helpService.showArrow"
              [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
              <ion-item>
                <!-- <ion-label position="floating">{{'ESTIMATED_LINE_LENGTH_PLACEHOLDER' | translate}}</ion-label> -->
                <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                  [disabled]='helpService.helpMode || readOnly'
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].NOTES" (ionFocus)="onFocusUserInputField($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].NOTES)"></ion-textarea>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <!-- <ion-label position="floating">{{'ESTIMATED_LINE_LENGTH_PLACEHOLDER' | translate}}</ion-label> -->
                <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                  [disabled]='helpService.helpMode || readOnly'
                  [(ngModel)]="selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].NOTES" (ionFocus)="onFocusUserInputField($event, selectedEquipmentList[getLIDIndex(options.EQUIP_ID)].NOTES)"></ion-textarea>
              </ion-item>
            </div>
          </div>
        </div>


      </div>
    </ion-item-sliding>
  </ion-list>
  <!-- && EQIPMENT SECTION END -->

  <!-- && LINE SECTION START-->
  <ion-list *ngIf="fieldSegment == 'lines'" style="padding-bottom: 50px;">
    <ion-item-sliding *ngFor="let options of rpsList;let i = index">
      <!-- <ion-item *ngFor="let options of equipmentList;let i = index" tappable> -->
      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);" [ngClass]="{'my-css-class': checkDivIsOpenCert(options) === true}">
        <ion-item *ngIf='!readOnly' lines="none">
          <ion-checkbox color="primary" (ionChange)="onCheckedCert($event,options.CERTIFICATE_NUM, i)"
            [value]="options.CERTIFICATE_NUM" [checked]="checkIfSelectedCert(options.CERTIFICATE_NUM)"
            [disabled]='helpService.helpMode || readOnly || isEditLmd'></ion-checkbox>
        </ion-item>
        <ion-label (click)="helightItemCert(options)">
          <div>
            <div style="display: block; width: 100%">
              <div style="color: black; width: 100%">
                {{options.NAME}}
              </div>
              <div *ngIf="options.PRODUCT != '' || options.PRODUCT != null" style="color: grey; width: 100%">
                {{'Product' | translate}} : {{options.PRODUCT}}
              </div>
              <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                {{'Diameter' | translate}} : {{options.DIAM}}
              </div>
            </div>
          </div>
        </ion-label>
        <ion-buttons (click)="iconCert(i)" tappable *ngIf="isUtility == false"> 
          <ion-button style="padding-bottom: 8px;" *ngIf="checkIfSelectedCert(options.CERTIFICATE_NUM)" class="winch-info-button">
            <span *ngIf="platformId == 'electron' || device.platform == 'browser'" class="winch-info-message">Click to fill line information</span>
            <span *ngIf="platformId !== 'electron' && device.platform != 'browser' && !checkSectionIsOpenCert(i)" class="winch-info-message">Click to fill line information</span>
            <ion-icon slot="icon-only" slot="end" [name]="checkSectionIsOpenCert(i) ? 'chevron-up-outline' :'chevron-down-outline'"
              style="color:rgb(41, 40, 40)" class="winch-info-icon"></ion-icon>
          </ion-button>
        </ion-buttons>

      </ion-item>
      <div *ngIf="checkSectionIsOpenCert(i)" lines="none"
        style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829); padding-left: 40px;">

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'ESTIMATED_LINE_LENGTH_LABEL' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: inline-flex; width: 100%;">
            <div tooltip="{{'HELP_TEXT_WINCHES_ESTIMATED_LENGTH'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].ESTIMATE_LINE_LENGTH"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, sselectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode' style="display: inline-flex; width: 100%;">
            <div>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].ESTIMATE_LINE_LENGTH"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].ESTIMATE_LINE_LENGTH, 'ESTIMATE_LINE_LENGTH')"></ion-input>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
        </div>

        <div style="Width: 100%">
          <div *ngIf="helpService.helpMode" class="winches-dropdown" style="display: inline-flex; width: 100%;">
            <div tooltip="{{'HELP_TEXT_WINCHES_PRE-DEPLOYMENT_INSPECTION'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-checkbox [checked]="PreDeplCmltdCert.includes(options.CERTIFICATE_NUM)" color="primary" style="margin-right:10px;"></ion-checkbox>
                <ion-label style="display: contents;">{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
              </ion-item>
            </div>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedLMDUomString}}
              </label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode' style="display: inline-flex; width: 100%;">
            <ion-item class="no-ripple">
              <ion-checkbox [checked]="PreDeplCmltdCert.includes(options.CERTIFICATE_NUM)" color="primary" style="margin-right:10px;" (ionChange)="setPreDepldCompletedCert($event,options.CERTIFICATE_NUM)"></ion-checkbox>
              <ion-label style="display: contents;">{{ 'PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL' | translate }}</ion-label>
            </ion-item>
          </div>
        </div>

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'WINCHES_AVERAGE_LOAD' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown">
            <div tooltip="{{'HELP_TEXT_WINCHES_AVERAGE_LOAD'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].AVERAGE_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].AVERAGE_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].AVERAGE_LOAD, 'AVERAGE_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
        </div>
        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'WINCHES_PEAK_LOAD' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown">
            <div tooltip="{{'HELP_TEXT_WINCHES_PEAK_LOAD'|translate}}" positionV="bottom"
              [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
              [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].PEAK_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <ion-input ngDefaultControl
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].PEAK_LOAD"
                  [disabled]='(helpService.helpMode || readOnly)' inputmode="decimal" (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].PEAK_LOAD, 'PEAK_LOAD')"></ion-input>
              </ion-item>
            </div>
          </div>
        </div>

        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_END_TYPE_LABEL' | translate}}
          </p>
          <div *ngIf="helpService.helpMode" class="winches-dropdown">
            <div tooltip="{{'HELP_TEXT_WINCHES_END_TYPE'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
              [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-select
                  style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                  [(ngModel)]='selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].END_IN_USE'
                  labelPlacement="stacked"  interface="popover" placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'>
                  <ion-select-option value="A">A</ion-select-option>
                  <ion-select-option value="B">B</ion-select-option>
                </ion-select>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <ion-select
                  style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                  [(ngModel)]='selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].END_IN_USE'
                  labelPlacement="stacked"  interface="popover" placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'>
                  <ion-select-option value="A">A</ion-select-option>
                  <ion-select-option value="B">B</ion-select-option>
                </ion-select>
              </ion-item>
            </div>
          </div>
        </div>
        <!-- <div style="Width: 100%" *ngIf="!showWorkboatSettings">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'WINCHES_CERT_TAIL_LENGTH_LABEL' | translate}} <span
            style="color:rgb(221, 82, 82);font-size:15px;"> *
          </span>
          </p>
          <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
            <div tooltip="{{'HELP_TEXT_WINCHES_TAIL_LENGTH'|translate}}" positionV="top" [arrow]="helpService.showArrow"
              [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
              <ion-item [ngClass]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].SELECTED_TAIL=='' ?'err':'valid'">
                <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                  [(ngModel)]='selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].SELECTED_TAIL'
                  interface="popover" placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'
                  (ionChange)="setSelectedLine(options.CERTIFICATE_NUM)" [compareWith]="compareTail">
                  <ion-select-option
                    *ngFor="let tail of selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_LIST"
                    [value]="tail">{{tail.CURRENT_LENGTH_IN_METER}} - <span>{{tail.NAME}} </span></ion-select-option>
                </ion-select>
              </ion-item>
              <ion-label *ngIf="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].SELECTED_TAIL==''" color="danger">{{ 'required' | translate }} </ion-label>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item [ngClass]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].SELECTED_TAIL=='' ?'err':'valid'">
                <ion-select style="max-width: 100% !important; width: 100% !important; padding: 0px !important; padding-right: 2px !important;"
                  [(ngModel)]='selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].SELECTED_TAIL'
                  interface="popover" placeholder="Select End Type" [disabled]='helpService.helpMode || readOnly'
                  (ionChange)="setSelectedLine(options.CERTIFICATE_NUM)" [compareWith]="compareTail">
                  <ion-select-option
                    *ngFor="let tail of selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_LIST"
                    [value]="tail">{{tail.CURRENT_LENGTH_IN_METER}} - <span>{{tail.NAME}} </span></ion-select-option>
                </ion-select>
              </ion-item>
              <ion-label *ngIf="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].SELECTED_TAIL==''" color="danger">{{ 'required' | translate }} </ion-label>
            </div>
          </div>
        </div> -->
        
        <div style="Width: 100%">
          <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">
            {{'WINCHES_NOTES_LABEL' | translate}}</p>
          <div *ngIf="helpService.helpMode" class="tooltip-selector-winches-tail">
            <div tooltip="{{'HELP_TEXT_WINCHES_NOTE'|translate}}" positionV="top" [arrow]="helpService.showArrow"
              [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
              <ion-item>
                <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                  [disabled]='helpService.helpMode || readOnly'
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].NOTES" (ionFocus)="onFocusUserInputField($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].NOTES)"></ion-textarea>
              </ion-item>
            </div>
          </div>
          <div *ngIf='!helpService.helpMode'>
            <div>
              <ion-item>
                <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                  [disabled]='helpService.helpMode || readOnly'
                  [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].NOTES" (ionFocus)="onFocusUserInputField($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].NOTES)"></ion-textarea>
              </ion-item>
            </div>
          </div>
        </div>
      </div>
    </ion-item-sliding>
  </ion-list>
  <!-- && LINE SECTION END -->

  <!-- && SAVE FAB BUTTON START -->
  <ion-fab *ngIf='!readOnly && (!isDivOpen || !showDiv)' vertical="bottom" horizontal="end" slot="fixed" style="margin-right:10px;" [topOffset]=helpService.topOffset
    tooltip="{{'Tap to save cropping portNameForm'|translate}}" positionV="top" positionH="right"
    [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : displaySummary()" *ngIf='!isDivOpen && !showDiv && (selectedEquipmentList.length > 0 || selectedCertList.length>0)'>
      <fa-icon class="icon-style-other" icon="save" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : closeDiv()" *ngIf='isDivOpen || showDiv && (selectedEquipmentList.length > 0 || selectedCertList.length>0)'>
      <fa-icon class="icon-style-other" icon="arrow-right" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
  <!-- && SAVE FAB BUTTON END -->

</ion-content>
<!-- Footer -->
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
    (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>