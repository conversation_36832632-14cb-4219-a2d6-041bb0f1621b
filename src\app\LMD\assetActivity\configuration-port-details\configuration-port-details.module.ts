import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ConfigurationPortDetailsPageRoutingModule } from './configuration-port-details-routing.module';

import { ConfigurationPortDetailsPage } from './configuration-port-details.page';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from 'src/app/components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    TooltipsModule,
    FontAwesomeModule,
    ConfigurationPortDetailsPageRoutingModule,
    FooterComponent
  ],
  declarations: [ConfigurationPortDetailsPage]
})
export class ConfigurationPortDetailsPageModule {}
