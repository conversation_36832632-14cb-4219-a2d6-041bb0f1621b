<ion-header>
  <ion-toolbar>
    <ion-title>{{'Routine Inspections' | translate}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
      <span *ngIf='helpService.helpMode' style="padding-right: 5px !important; font-size: medium !important">{{'Exit Help' | translate}}</span>
      <fa-icon class="icon-style-help"  *ngIf='helpService.helpMode' icon="times"></fa-icon>
      <fa-icon class="icon-style-help"  *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
    </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- <div class="inspectionHome"> -->
  <!-- Class change for iPad-->
  <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" style="height:104%">
    <ion-card
      (click)="helpService.helpMode ? '' : newRoutineInspection()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.imageUrlInspectionNew" style="height: 100%; width: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'New' |translate}} </ion-label>
    </ion-card>

    <ion-card
      (click)="helpService.helpMode ? '' : openInprogressIlst()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.imageUrlInspInProgress" style="height: 100%; width: 100%;"/>
      <ion-badge class="card-badge" color="primary" style="font-size:17px; top: 5px;padding-top:7px; position: absolute !important; right: 3px !important;">
        {{inspectionCount}}</ion-badge>
      <ion-label class="card-title" style="font-size: 17px;"> {{'In Progress' | translate}} </ion-label>
  </ion-card>

    <ion-card
      (click)="helpService.helpMode ? '' : openCompletedList()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/Inspections_Completed.jpg" style="height: 100%; width: 100%;"/>
      <!-- <ion-badge class="card-badge" slot="end" color="darkGreen" style="font-size:17px;  top: 5px;padding-top:7px">10</ion-badge> -->
      <ion-label class="card-title" style="font-size: 17px;"> {{'Completed' | translate}} </ion-label>
    </ion-card>
  </div>
  
  <div *ngIf="platformId == 'electron' || device.platform == 'browser'">
    <div class="gridCon"> 
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : newRoutineInspection()">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.imageUrlInspectionNewWin" class="imageTag">
          <p class="wrapperLabel">{{'New' |translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : openCompletedList()">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/op-imgs/Inspections_Completed_2024x2024.png" class="imageTag">
          <p class="wrapperLabel">{{'Completed' |translate}}</p>
        </div>
      </div>
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : openInprogressIlst()">
          <ion-ripple-effect></ion-ripple-effect>
          <p class="inProgressCount"><span>{{inspectionCount}}</span></p>
          <img [src]="dataService.imageUrlInspInProgressWin" class="imageTag">
          <p class="wrapperLabel">{{'In Progress' |translate}}</p>
        </div>
      </div>
    </div>
  </div>

</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
    (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>