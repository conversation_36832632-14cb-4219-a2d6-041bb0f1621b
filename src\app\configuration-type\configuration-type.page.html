<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
    <ion-title>{{'CONFIGURATION_TYPE_TITLE' | translate}}</ion-title>
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
      <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
      <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
    </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-slides #slides [options]="slideOpts" scrollbar='false' style="height: 100%;">


    <ion-slide>
      <div style="width:100% !important;height: 100%;
      position: relative;
      width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
      justify-self: center;
      align-content: center;
      align-items: center;
      flex-direction: column;">
        <p style="text-align: center !important;">{{'CONFIGURATION_TYPE_QUESTION' | translate}}</p>
        <div style="width: 100% !important;">
          <div class="responsive-card-wrapper">
            <ion-card class="card-style" (click)="helpService.helpMode ? '' : navigateToNewConfigurationsPage()">
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/Constructions/other.png" />
            </ion-card>
            <p>{{'CONFIGURATION_TYPE_NEW_CONFIGURATION' | translate}}</p>
          </div>
          <div class="responsive-card-wrapper">
            <ion-card class="card-style" (click)="helpService.helpMode ? '' : navigateToPreviousConfigurationsPage()"
              [disabled]='disablePreviousConfig'>
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/Constructions/lookup.png" style="padding: 15px !important;" />
              <!-- <fa-icon class="icon-style-other"  icon="search" style="font-size: 7rem;color: #003874;"></fa-icon> -->
            </ion-card>
            <p>{{'CONFIGURATION_TYPE_PREVIOUS_CONFIGURATION' | translate}}</p>
          </div>
        </div>
      </div>
    </ion-slide>
  </ion-slides>
</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>

<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>