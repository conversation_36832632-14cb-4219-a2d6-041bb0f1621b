import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faArrowRight, faSave, faSortDown } from '@fortawesome/free-solid-svg-icons';
import { MenuController, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { GenericListPage } from 'src/app/generic-list/generic-list.page';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { LmdService } from 'src/app/services/lmd.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';

@Component({
  selector: 'app-work-order-details',
  templateUrl: './work-order-details.page.html',
  styleUrls: ['./work-order-details.page.scss'],
})
export class WorkOrderDetailsPage implements OnInit {

  public workOderDetailForm: FormGroup;
  public selectedPage: any;
  public workOrders: any;
  public workOrderNo: any = '';
  public showWorkorderResultCard: any;
  public searchedWorkOrder: any;
  samsonWorkOrder: any;
  shouldAssociateWorkOrder: any;
  internalWorkorder: any;
  workOrderAssociated: any;
  workOrderAssociatedForm: FormGroup;
  workOrderAssociatedErrorMessage: string;
  footerClose: boolean = true;
  internalWorkorderForm: FormGroup;
  internalWorkorderErrorMessage: string;
  selectedAsset: any;
  selectedAssetName: string = ''
  isAssetSelected: boolean = false;
  selectedAssetID: any;



  constructor(
    public router: Router,
    public alertService: AlertService,
    public dataService: DataService,
    public unviredCordovaSDK: UnviredCordovaSDK,
    private userPreferenceService: UserPreferenceService,
    public menu: MenuController,
    public modalController: ModalController,
    public translate: TranslateService,
    public formBuilder: FormBuilder,
    public helpService: HelpService,
    public lmdService: LmdService,
    public utilityService: UtilserviceService , public library : FaIconLibrary ) {
      this.library.addIcons(faArrowRight , faSortDown , faSave);
    this.showWorkorderResultCard = false;
  }

  ngOnInit() {
    window.addEventListener('keyboardDidHide', () => {
      this.footerClose = true;
    });
    window.addEventListener('keyboardWillShow', (event) => {
      this.footerClose = false;
    });

    this.selectedPage = this.utilityService.getSelectedLMDPage()
    this.searchedWorkOrder = [];
    this.internalWorkorder = '';
    this.workOrderAssociated = '';
    this.initializePreferenceAsset();
    this.workOrderAssociatedForm = this.formBuilder.group({
      workOrderAssociatedCtrl: [''],
    });
    this.internalWorkorderForm = this.formBuilder.group({
      internalWorkorderCtrl: [''],
    });
  }

  saveAndContinue() {
    if (this.samsonWorkOrder == 'yes' && (this.searchedWorkOrder.length == 0 || this.searchedWorkOrder == undefined)) {
      this.alertService.showAlert("", "Select a work order number to continue");
      return;
    } else if((this.samsonWorkOrder == 'no' && this.shouldAssociateWorkOrder == "yes") && (this.workOrderAssociated == '')) {
      this.alertService.showAlert("", "Please add work order number to continue");
      return;
    } else if ((this.samsonWorkOrder == '' || this.samsonWorkOrder == undefined) && (this.shouldAssociateWorkOrder == '' || this.shouldAssociateWorkOrder == undefined)) {
      this.alertService.showAlert("", "Please fill all fields to continue");
      return;
    } else if (this.samsonWorkOrder == 'no' && this.shouldAssociateWorkOrder == 'no') {
      this.lmdService.setSelectedLMDWorkorder('')
      this.lmdService.setReadOnlyMode(false)
      this.lmdService.setIsFromCompleted(false);
      switch (this.selectedPage) {
        case 'Cropping':
          this.router.navigate(['cropping-lmd'])
          break;
        case 'Repair':
          this.router.navigate(['repair-lmd'])
          break;
        case 'EndForEnd':
          this.router.navigate(['end-for-end-lmd'])
          break;
        case 'EquipmentInsp':
          this.router.navigate(['equipment-insp-lmd'])
          break;
        case 'Rotation':
          this.router.navigate(['rotation-lmd'])
          break;
      }
    } else {
      if(this.searchedWorkOrder.length != 0 || (this.workOrderAssociated != '')) {
        var workorder;
        if(this.searchedWorkOrder.length > 0) {
          workorder = this.searchedWorkOrder[0];
        } else {
          workorder =  {
            "WO_NUMBER" : this.workOrderAssociated,
            "WO_INTERNAL" : this.internalWorkorder,
            "DI": this.workOrderAssociated
          }
        }
        this.lmdService.setSelectedLMDWorkorder(workorder)
        this.lmdService.setReadOnlyMode(false)
        this.lmdService.setIsFromCompleted(false);
        switch (this.selectedPage) {
          case 'Cropping':
            this.router.navigate(['cropping-lmd'])
            break;
          case 'Repair':
            this.router.navigate(['repair-lmd'])
            break;
          case 'EndForEnd':
            this.router.navigate(['end-for-end-lmd'])
            break;
          case 'EquipmentInsp':
            this.router.navigate(['equipment-insp-lmd'])
            break;
          case 'Rotation':
            this.router.navigate(['rotation-lmd'])
            break;
        }
      }
    }
  }

  async presentModal(title: string) {
    var tempList, pageTitle;
    switch (title) {
      case 'WORK_ORDER':
        tempList = this.workOrders
        pageTitle = this.translate.instant("Work Orders")
        break;
    }

    this.alertService.present().then(async () => {
      const modal = await this.modalController.create({
        component: GenericListPage,
        componentProps: { value: tempList, title: pageTitle, page: title }
      });
      await modal.present();

      modal.onDidDismiss().then(async (data) => {
        switch (title) {
          case 'WORK_ORDER':
            this.workOrderNo = data.data.data.WO_NUMBER
            this.showWorkorderResultCard = true;
            this.searchedWorkOrder = [data.data.data]
            break;
        }

      });
    })
  }

  async filterWorkOrder() {
    var whereClause = ""
    if(this.selectedAssetID != '' && this.selectedAssetID != undefined) {
      whereClause = whereClause + ' WHERE ASSET_ID = "' + this.selectedAssetID + '" '
    }
    whereClause = whereClause + 'ORDER BY WO_NUMBER COLLATE NOCASE ASC'
    let workorderRes = await this.unviredCordovaSDK.dbExecuteStatement('SELECT * FROM WORK_ORDER_HEADER  ' + whereClause)
    // let workorderRes = await this.unviredCordovaSDK.dbExecuteStatement(`SELECT * FROM WORK_ORDER_HEADER ORDER BY WO_NUMBER COLLATE NOCASE ASC`)
    if (workorderRes.type == ResultType.success) {
      this.workOrders = workorderRes.data;
    } else {
      this.unviredCordovaSDK.logError("create-inspection", "filterWorkOrder", "Error while getting error from db" + JSON.stringify(workorderRes))
    }
  }

  async resetWorkOrder(ev: any) {
    this.samsonWorkOrder = ev.detail.value
    if (ev.detail.value == 'no') {
      this.showWorkorderResultCard = false;
    } else if (ev.detail.value == 'yes') {
      if (this.searchedWorkOrder.length > 0) {
        this.showWorkorderResultCard = true;
      }
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }
  test() {

  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  hasErrorStart(selectedField) {
    switch (selectedField) {
      case 'workOrderAssociated':
        if (this.workOrderAssociated == '' || this.workOrderAssociated == null || this.workOrderAssociated == undefined) {
          this.workOrderAssociatedForm.controls['workOrderAssociatedCtrl'].setErrors({ 'incorrect': true })
          this.workOrderAssociatedErrorMessage = "Working hour value is mandatory"
          return true;
        } else return false;
        break;
      case 'internalWorkorder':
        if (this.internalWorkorder == '' || this.internalWorkorder == null || this.internalWorkorder == undefined) {
          this.internalWorkorderForm.controls['internalWorkorderCtrl'].setErrors({ 'incorrect': true })
          this.internalWorkorderErrorMessage = "Working hour value is mandatory"
          return true;
        } else return false;
        break;
    }
  }

  async initializePreferenceAsset() {
    var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    
    if (selectedAsset != undefined && selectedAsset != null && selectedAsset != '' && selectedAsset != '""') {
      selectedAsset = JSON.parse(selectedAsset)
      this.selectedAsset = selectedAsset;
      this.selectedAssetID = selectedAsset.ID
      this.selectedAssetName  = selectedAsset.NAME
    }
    this.filterWorkOrder();
  }
}
