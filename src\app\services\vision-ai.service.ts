import { Injectable } from '@angular/core';
import { AlertService } from './alert.service';
import * as tf from '@tensorflow/tfjs';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { DomSanitizer } from '@angular/platform-browser';
import { RequestType, ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstant } from 'src/constants/appConstants';
import { INSPECTION_REPORT_ATTACHMENT } from 'src/models/INSPECTION_REPORT_ATTACHMENT';
import { UtilserviceService } from './utilservice.service';
import { environment } from '../../environments/environment';
import { PlatformService } from './platform.service';
import { AnimationController, Platform } from '@ionic/angular';

declare var cropro: any; // * to work with cropro
declare var cv: any;
declare var ump: any;
@Injectable({
  providedIn: 'root'
})
export class VisionAIService {
  public platformId: string = this.platformService.getPlatformId().toLowerCase();
  private win: any = window;
  model: any = null;
  inputShape: any;
  modelImgWidth: number;
  modelImgHeight: number;
  score: any;
  imageData: any;
  predictions: any;
  // cropArea:any; // * use this if you're using cropro from cropro.js embedded in index.html
  backend: string;
  time: any;
  loadURL: string = '';
  public isOpenCVInitialized = false;
  // screenMode:string;
  // imageSource: string = '';
  scores: number[] = [];
  requiredModelWidth: number = 256;
  requiredModelHeight: number = 256;
  tiledImages: any[] = [];
  showTilesAndSaveTilesEnabled: boolean = true;
  quickInspectHeader: any;
  selectedCertificate: any; //! to save the selected certificate for insight AI feature
  constructor(private alertService: AlertService,
    private platformService: PlatformService,
    private device: Device,
    private domSanitizer: DomSanitizer,
    private file: File,
    private unviredCordovaSdk: UnviredCordovaSDK,
    private animationCtrl: AnimationController,
    private platform: Platform) { }

  enterAnimation = (baseEl: HTMLElement) => {
    const root = baseEl.shadowRoot;

    const backdropAnimation = this.animationCtrl
      .create()
      .addElement(root!.querySelector('ion-backdrop')!)
      .fromTo('opacity', '0.01', 'var(--backdrop-opacity)');

    const wrapperAnimation = this.animationCtrl
      .create()
      .addElement(root!.querySelector('.modal-wrapper')!)
      .keyframes([
        { offset: 0, opacity: '0', transform: 'scale(0)' },
        { offset: 1, opacity: '0.99', transform: 'scale(1)' },
      ]);

    return this.animationCtrl
      .create()
      .addElement(baseEl)
      .easing('ease-out')
      .duration(500)
      .addAnimation([backdropAnimation, wrapperAnimation]);
  };

  leaveAnimation = (baseEl: HTMLElement) => {
    return this.enterAnimation(baseEl).direction('reverse');
  };

  setQuickInspectionHeader(header: any) {
    this.quickInspectHeader = header;
  }

  getQuickInspectionHeader() {
    return this.quickInspectHeader;
  }

  async loadModel(modelToLoad?: any): Promise<any> {
    // let model = '';
    // if((cert.PRODUCT_TYPE == 'HMSF (Class II)' || cert.PRODUCT_TYPE=='HMPE (Class II)') && cert.CONSTRUCTION == "12-Strand") {
    //   model = 'amsteel'
    // } else if(cert.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && cert.CONSTRUCTION == "12-Strand") {
    //   model = 'tenex'
    // } else if ((cert.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
    //   (cert.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
    //   (cert.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
    //   (cert.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
    //   (cert.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
    //   (cert.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
    //     model = 'amsteel'
    // }
    // if(product.includes('AMSTEEL')) {
    //   model = 'amsteel'
    // } else if(product.includes('TENEX')) {
    //   model = 'tenex'
    // }
    // await this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE","loadModel",'loading model from indexDB'+ modelToLoad)
    await tf.loadLayersModel(`indexeddb://${modelToLoad}`).then(async (data) => {
      console.log('%c model loaded: ' + modelToLoad, 'background: hsl(127, 83%, 47%); color: #fbf705');
      this.model = data;
    }).catch(err => {
      return err;
    });
  }

  async checkModel(modelName?: string) {
    // console.log("going to check the model existence now")
    try {
      // console.log("started checking model")
      // Try loading the model directly from IndexedDB
      await tf.loadLayersModel(`indexeddb://${modelName}`);
      // console.log(`Model ${modelName} exists in IndexedDB.`);
      this.unviredCordovaSdk.logInfo("HOME", "checkAndDownloadLatestMlModel", "checking if the model is present in indexDB or not, event though there's no change in the model version");
      return true;
    } catch (err: any) {
      if (err.message.includes('There is no model with')) {
        console.warn(`Model ${modelName} not found in IndexedDB.`);
        this.unviredCordovaSdk.logInfo("HOME", "checkAndDownloadLatestMlModel", "checking if the model is present in indexDB or not, event though there's no change in the model version");
      } else {
        console.warn("Error checking model existence:", err);
        this.unviredCordovaSdk.logInfo("HOME", "checkAndDownloadLatestMlModel", "checking if the model is present in indexDB or not, event though there's no change in the model version");

      }
      return false;
    }
  }

  async downloadModelFromServer(modelName?: string) {
    this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "downloading model from server method START")
    let query = '';
    if (modelName) {
      query = `MODEL_TYPE='${modelName}'`
    }
    let modelRes = await this.unviredCordovaSdk.dbSelect("AI_MODEL_VERSION_HEADER", query)
    if (modelRes.type == ResultType.success) {
      let AIModelData = modelRes.data;
      let modelPath = ''
      let modelServerURL = ''
      let modelHeader;
      for (let i = 0; i < AIModelData.length; i++) {
        modelPath = '/auth/getmodel/' + AIModelData[i].MODEL_TYPE + '/' + AIModelData[i].MODEL_FILE_NAME;
        const getUMPRequestConfig = (modelPath) => {
          return new Promise((resolve, reject) => {
              ump.getUMPRequestConfig(
                  modelPath, {},
                  (result) => {
                      resolve(result);
                  },
                  (error) => {
                      reject(error);
                  }
              );
          });
        };

        try {
          const result: any = await getUMPRequestConfig(modelPath);
          modelHeader = result.headers;
          modelServerURL = result.url;
        } catch (error) {
          console.log(error);
        }


        await tf.ready();
        await tf.loadLayersModel(`indexeddb://${AIModelData[i].MODEL_TYPE}`).then(async (modelData) => {
          this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "model exists")
          if (navigator.onLine) {
            let modelAlert;
            let modelInfo;
            if (!environment.production) { //! if dev
              if (this.alertService.isLoading) {
                try {
                  await this.alertService.dismiss()
                } catch (err) {
                  console.log(err)
                }
              }

              modelAlert = await this.alertService.present('custom-loader', `Downloading model ${AIModelData[i].MODEL_TYPE}...0%`)
              modelInfo = {
                requestInit: { method: "GET", headers: modelHeader },
                onProgress: async (fractionCompleted: number) => {

                  const percentage = Math.round(fractionCompleted * 100);
                  console.log(`Download progress: ${percentage}%`);

                  modelAlert.message = `Downloading model ${AIModelData[i].MODEL_TYPE}...${percentage}%`
                  if (percentage == 100) {
                    await modelAlert.dismiss();
                  }
                }
              }
            } else {//! if production
              modelInfo = { requestInit: { method: "GET", headers: modelHeader } }
            }
            this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "Device online")
            await tf.loadLayersModel(tf.io.http(modelServerURL, modelInfo)).then(async (data) => {
              this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "successfully downloaded the model")
              let modelRes = data;
              tf.io.removeModel(`indexeddb://${AIModelData[i].MODEL_TYPE}`).then(async res => {
                this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "removed exsisting model succesfully")
                await modelRes.save(`indexeddb://${AIModelData[i].MODEL_TYPE}`).then(async _ => {
                  this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "saved model to indexdb successfully")
                  tf.loadLayersModel(`indexeddb://${AIModelData[i].MODEL_TYPE}`).then(async (data) => {
                    let model = data;
                    // let modelImgWidth = this.model.inputs[0].shape[1];
                    // let modelImgHeight =  this.model.inputs[0].shape[2];
                    // console.log()
                    // await this.warmUpModel(model);
                  }).catch(async err => {
                    if (this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                    this.alertService.showAlert('Error!', err);
                  });
                }).catch(err => {
                  this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "error in saving model to indexdb")
                });
                this.resetAll();
              }).catch(async err => {
                // !override and save the model
                await modelRes.save(`indexeddb://${AIModelData[i].MODEL_TYPE}`).then(async _ => {
                  this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "saved model to indexdb successfully")
                  tf.loadLayersModel(`indexeddb://${AIModelData[i].MODEL_TYPE}`).then(async (data) => {
                    let model = data;
                    // let modelImgWidth = this.model.inputs[0].shape[1];
                    // let modelImgHeight =  this.model.inputs[0].shape[2];
                    // console.log()
                    // await this.warmUpModel(model);
                  }).catch(async err => {
                    if (this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                    this.alertService.showAlert('Error!', err);
                  });
                }).catch(err => {
                  this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "error in saving model to indexdb")
                });
              });
            }).catch(async err => {
              // console.log(err);
              // this.model = modelData;
              // this.modelImgWidth = this.model.inputs[0].shape[1];
              // this.modelImgHeight =  this.model.inputs[0].shape[2];
              // await this.warmUpModel();
              if (this.alertService.isLoading) {
                await this.alertService.dismiss();
              }
              await this.alertService.showAlert("Error downloading insightAI data, Please contact Samson for Furthur Assistance.", err);
              this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "Error downloading the model from server")
            });
          } else {
            // this.model = modelData;
            // this.modelImgWidth = this.model.inputs[0].shape[1];
            // this.modelImgHeight =  this.model.inputs[0].shape[2];
            this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "Device is offline, constinue to use the existing model")
          }
        }).catch(async err => {
          this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "model not exists, continue to download the model")
          if (navigator.onLine) {
            this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "device is online, downloading the model")
            // await this.downloadModel(modelServerURL,modelHeader,AIModelData[i].MODEL_TYPE)
            let modelAlert;
            let modelInfo;
            if (!environment.production) { //! if dev
              if (this.alertService.isLoading) {
                try {
                  await this.alertService.dismiss()
                } catch (err) {
                  console.log(err)
                }
              }

              modelAlert = await this.alertService.present('custom-loader', `Downloading model ${AIModelData[i].MODEL_TYPE}...0%`)
              modelInfo = {
                requestInit: { method: "GET", headers: modelHeader },
                onProgress: async (fractionCompleted: number) => {

                  const percentage = Math.round(fractionCompleted * 100);
                  console.log(`Download progress: ${percentage}%`);

                  modelAlert.message = `Downloading model ${AIModelData[i].MODEL_TYPE}...${percentage}%`
                  if (percentage == 100) {
                    await modelAlert.dismiss();
                  }
                }
              }
            } else {//! if production
              modelInfo = { requestInit: { method: "GET", headers: modelHeader } }
            }

            let model;

            try {
              model = await tf.loadLayersModel(tf.io.http(modelServerURL, modelInfo))
              this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "model downloaded succesfully")
              let modelRes = model;
              await modelRes.save(`indexeddb://${AIModelData[i].MODEL_TYPE}`);
              this.model = await tf.loadLayersModel(`indexeddb://${AIModelData[i].MODEL_TYPE}`);
              this.modelImgWidth = this.model.inputs[0].shape[1];
              this.modelImgHeight = this.model.inputs[0].shape[2];
              await this.warmUpModel();
              this.resetAll();
            } catch (err: any) {
              if (this.alertService.isLoading) {
                await this.alertService.dismiss();
              }
              console.log(err);
              await this.alertService.showAlert("Error", err);
              this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "error downloading the model")
            }
          } else {
            this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "Device is offline, not able to download the model")
            await this.alertService.showAlert("Error!", "Device not connected to internet, please connect and try again..")
          }
        })
      }
    } else if (modelRes.type == ResultType.error) {
      this.unviredCordovaSdk.logInfo("VISIONAISERVICE", "createQuickInspectAttachments", " READ AI_MODEL_VERSION_HEADER " + JSON.stringify(modelRes.error))
    }
  }

  async downloadModel(modelServerURL: string, modelHeader: any, MODEL_TYPE: any): Promise<any> {
    // await this.downloadModel(modelServerURL,modelHeader,AIModelData[i].MODEL_TYPE)
    let modelAlert;
    let modelInfo;
    // if(!environment.production) { //! if dev
    if (this.alertService.isLoading) {
      try {
        await this.alertService.dismiss()
      } catch (err) {
        console.log(err)
      }
    }

    modelAlert = await this.alertService.present('custom-loader', `Downloading model ${MODEL_TYPE}...0%`)
    modelInfo = {
      requestInit: { method: "GET", headers: modelHeader },
      onProgress: async (fractionCompleted: number) => {

        const percentage = Math.round(fractionCompleted * 100);
        console.log(`Download progress: ${percentage}%`);

        modelAlert.message = `Downloading model ${MODEL_TYPE}...${percentage}%`
        if (percentage == 100) {
          await modelAlert.dismiss();
        }
      }
    }
    // } else {//! if production
    //   modelInfo =  {requestInit: { method: "GET",headers:modelHeader }}
    // }

    let model;
    try {
      model = await tf.loadLayersModel(tf.io.http(modelServerURL, modelInfo))
      this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "model downloaded succesfully")
      let modelRes = model;
      await modelRes.save(`indexeddb://${MODEL_TYPE}`);
      this.model = await tf.loadLayersModel(`indexeddb://${MODEL_TYPE}`);
      this.modelImgWidth = this.model.inputs[0].shape[1];
      this.modelImgHeight = this.model.inputs[0].shape[2];
      await this.warmUpModel();
      this.resetAll();
    } catch (err: any) {
      if (this.alertService.isLoading) {
        await this.alertService.dismiss();
      }
      console.log(err);
      await this.alertService.showAlert("Error", err);
      this.unviredCordovaSdk.logInfo("VISION_AI_SERVICE", "downloadModelFromServer", "error downloading the model")
    }
  }


  async loadModelVisionPage() {
    await this.alertService.presentLoaderWithMsg('loading model,please wait...');
    await this.alertService.dismiss();
  }

  async predictVisionImage() {

  }

  convertToGreyScale(img: any) {
    // ?convert and return histogram converted image 
    let imgElement = document.getElementById("ropeImage");
    let mat = cv.imread(imgElement);
    let canvasInput = document.createElement("canvas");
    cv.imshow(canvasInput, mat);
    mat.delete();
    let src = cv.imread(canvasInput);
    let equalDst = new cv.Mat();
    let claheDst = new cv.Mat();
    cv.cvtColor(src, src, cv.COLOR_RGBA2GRAY, 0);
    cv.equalizeHist(src, equalDst);
    let tileGridSize = new cv.Size(8, 8);
    // You can try more different parameters
    let clahe = new cv.CLAHE(40, tileGridSize);
    clahe.apply(src, claheDst);
    cv.imshow("ropeHistogramImg", equalDst);
    cv.imshow("ropeHistogramImg", claheDst);
    src.delete();
    equalDst.delete();
    claheDst.delete();
    clahe.delete();
  }

  // & converting tiled images to histogram quantization 
  // convertTiledImgToHistogramImg(imageID:string,canvasID:string) {
  //   let imgElement = document.getElementById(imageID);
  //   let mat = cv.imread(imgElement);
  //   let canvasInput = document.createElement("canvas");
  //   cv.imshow(canvasInput, mat);
  //   mat.delete();
  //   let src = cv.imread(canvasInput);
  //   let equalDst = new cv.Mat();
  //   let claheDst = new cv.Mat();
  //   cv.cvtColor(src, src, cv.COLOR_RGBA2GRAY, 0);
  //   cv.equalizeHist(src, equalDst);
  //   let tileGridSize = new cv.Size(8, 8);
  //   // You can try more different parameters
  //   let clahe = new cv.CLAHE(40, tileGridSize);
  //   clahe.apply(src, claheDst);
  //   cv.imshow(canvasID, equalDst);
  //   cv.imshow(canvasID, claheDst);
  //   src.delete();
  //   equalDst.delete();
  //   claheDst.delete();
  //   clahe.delete();
  // }

  async warmUpModel(model?: any) {
    // Warmup the model before using real data to make the prediction faster from first time onwards
    // You can adjust the batch size
    if (model) {
      const batchSize = 1;
      const inputShape = model.inputs[0].shape;
      inputShape[0] = batchSize;
      const warmUpInputData = tf.randomNormal(inputShape);
      const warmupResult = await model.predict(warmUpInputData)
      warmupResult.dataSync();
      warmupResult.dispose();
    }
  }

  reset(event: any) {
    this.score = null;
    this.predictions = ''
    event.target.value = null;
    this.backend = null;
    this.time = null;
  }

  resetAll() {
    this.score = null;
    this.predictions = null;
    this.backend = null;
    this.time = null;
  }

loadImageElementFromUrl(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous'; // just in case (no harm)
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = url;
    });
  }


  async predictImagesStack(imgArr: any, createAttachments?: boolean, cert?: any): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const startTime = Date.now();
      console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Starting AI processing workflow`);

      this.tiledImages = [];
      console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Loading model`);
      await this.loadModel(cert);
      try {
        let Images = [];
        this.scores = [];
        let folderName;

        if (this.showTilesAndSaveTilesEnabled) {
          console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Saving histogram image`);
          folderName = 'image-' + (new Date().getTime()).toString(16);
          // let base64 = imgArr[0].getAttribute('src').split(',')[1];
          // let blob = this.b64toBlob(base64, 'image/jpeg');
          let fileName = folderName + '.jpg';
          // await this.writeTailedImageToStorage(folderName,blob,fileName);
        }

        console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Starting image prediction loop for ${imgArr.length - 1} images`);

        for (let i = 1; i < imgArr.length; i++) {
          const imageStartTime = Date.now();
          console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Processing image ${i}/${imgArr.length - 1}`);

          let img = imgArr[i];
          img = await this.loadImageElementFromUrl(img);

          console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Converting image ${i} to tensor`);
          let tfImage = tf.browser.fromPixels(img, 1);
          let image = tfImage.reshape([1, 256, 256, 1]);
          let normalizedImage = image.div(255);

          console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Running model prediction for image ${i}`);
          let predictions: tf.Tensor;
          try {
            predictions = await this.model.predict(normalizedImage);
          } catch (err: any) {
            console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Prediction error:`, err);
            await this.unviredCordovaSdk.logError("VISION_AI_SERVICE", "predictImagesStack", "Error in model prediction: " + err?.message);
          }
          let score = predictions.argMax(1).dataSync()[0];
          score += 1;
          this.scores.push(score);
          console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Prediction completed for image ${i}, score: ${score}`);

          if (this.showTilesAndSaveTilesEnabled || createAttachments) {
            console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Saving tile ${i} to storage`);
            if (createAttachments) {
              folderName = 'image-' + (new Date().getTime()).toString(16);
            }
          }

          const imageEndTime = Date.now();
          console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Image ${i} processing completed in ${((imageEndTime - imageStartTime) / 1000).toFixed(2)}s`);
        }

        this.score = Math.max(...this.scores);
        console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] AI processing workflow completed. Total time: ${((Date.now() - startTime) / 1000).toFixed(2)}s`);
        resolve({ score: this.score, scores: this.scores });
      } catch (error: any) {
        console.log(`[${((Date.now() - startTime) / 1000).toFixed(2)}s] Error in prediction workflow:`, error);
        if (this.alertService.isLoading) {
          await this.alertService.dismiss();
        }
        await this.alertService.showAlert('Warning!, "Failed to process the image, Contact Samson for further assistance"');
        console.error("Error processing the image:", error?.message);
        await this.unviredCordovaSdk.logError("VISION_AI_SERVICE", "predictImagesStack", "Error processing the image: " + error?.message);
        reject(error);
      }
    })
  }

  normalizeURL(url: string) {
    if (url == null || url.length == 0) {
      return ""
    }
    if (url.startsWith("http://")) return url;
    var updatedUrl = url
    if (this.device.platform == 'iOS') {
      let stringParts = updatedUrl.split('/Documents/')
      if (stringParts.length > 1) {
        let suffix = stringParts[1]
        updatedUrl = this.file.documentsDirectory + suffix
      }
    }
    var fixedURL = this.win.Ionic.WebView.convertFileSrc(updatedUrl)
    fixedURL = this.domSanitizer.bypassSecurityTrustUrl(fixedURL)
    return fixedURL
  }

  b64toBlob(b64Data, contentType) {
    contentType = contentType || '';
    var sliceSize = 512;
    var byteCharacters = atob(b64Data);
    var byteArrays = [];

    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);

      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      var byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    var blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

  async localSaveDB() {
    let response = await tf.loadLayersModel('./assets/modelnew/model.json').then(async data => {
      let modelRes = data;
      await modelRes.save("indexeddb://rope-model");
      this.model = await tf.loadLayersModel("indexeddb://rope-model");
      this.modelImgWidth = this.model.inputs[0].shape[1];
      this.modelImgHeight = this.model.inputs[0].shape[2];
      await this.warmUpModel();
    }).catch(async err => {
      console.log("error loading model from local path", err);
    });
  }

  // async writeTailedImageToStorage(folderToCreate: string, blob: any, fileName: string, createAttachments?: boolean) {
  //   console.log("Writing file to storage...");
  //   let filePath;

  //   // Helper to create directories recursively and write files
  //   const createAndWriteFile = async (basePath: string, folder: string, file: string, data: any) => {
  //     const folderPath = `${basePath}${folder}/`;
  //     const fullPath = `${folderPath}${file}`;

  //     // Helper to ensure a directory exists, creating it if necessary
  //     const ensureDirExists = async (path: string) => {
  //       const segments = path.split("/");
  //       let currentPath = basePath;

  //       for (const segment of segments) {
  //         if (!segment) continue; // Skip empty segments
  //         try {
  //           console.log(`Checking directory: ${currentPath}${segment}/`);
  //           await this.file.checkDir(currentPath, segment);
  //           console.log(`Directory exists: ${currentPath}${segment}/`);
  //         } catch {
  //           try {
  //             console.log(`Creating directory: ${currentPath}${segment}/`);
  //             await this.file.createDir(currentPath, segment, false);
  //             console.log(`Directory created: ${currentPath}${segment}/`);
  //           } catch (err) {
  //             console.error(`Error creating directory ${currentPath}${segment}/:`, err);
  //             throw err;
  //           }
  //         }
  //         currentPath += `${segment}/`; // Move to the next level
  //       }
  //     };

  //     try {
  //       console.log(`Ensuring directories for path: ${folderPath}`);
  //       await ensureDirExists(folder);

  //       // Write the file
  //       try {
  //         console.log(`Attempting to write file at: ${fullPath}`);
  //         await this.file.writeFile(folderPath, file, data, { replace: true });
  //         console.log(`File written successfully at: ${fullPath}`);
  //       } catch (writeFileErr) {
  //         console.error(`Error writing file at ${fullPath}:`, writeFileErr);
  //         throw writeFileErr;
  //       }

  //       return fullPath;
  //     } catch (err) {
  //       console.error("Unhandled error during create/write file operation:", err);
  //       throw err;
  //     }
  //   };

  //   const platform = this.device.platform;
  //   let basePath: string;
  //   switch (platform) {
  //     case 'Android':
  //     case 'windows':
  //       basePath = this.file.dataDirectory;
  //       break;
  //     case 'iOS':
  //       basePath = this.file.documentsDirectory + "/NoCloud/";
  //       break;
  //     default:
  //       console.log("Unsupported platform:", platform);
  //       return;
  //   }

  //   try {
  //     const filePath = await createAndWriteFile(basePath, "InsightAI/" + folderToCreate, fileName, blob);
  //     console.log("File written to storage successfully:", filePath);
  //   } catch (error) {
  //     console.error("Error writing file to storage:", error);
  //   }
  // }

  async submitPendingQuickInspections() {
    let res = await this.unviredCordovaSdk.dbSelect("QUICK_INSPECTION_HEADER", `SYNC_STATUS='3'`);
    if (res.type == ResultType.success) {
      if (res.data.length > 0) {
        for (let i = 0; i < res.data.length; i++) {
          let qckInspObj = res.data[i];
          let inputObject = {
            "QUICK_INSPECTION_HEDER": qckInspObj
          }
          if (this.device.platform == "browser") {
            this.unviredCordovaSdk.dbSaveWebData();
            await this.unviredCordovaSdk.syncForeground(RequestType.RQST, inputObject, '', AppConstant.ROPE_INSPECTIONS_PA_CREATE_QUICK_INSPECTION, true)
          } else {
            await this.unviredCordovaSdk.syncBackground(RequestType.RQST, inputObject, "", AppConstant.ROPE_INSPECTIONS_PA_CREATE_QUICK_INSPECTION, "QUICK_INSPECTION", qckInspObj.LID, false)
          }
        }
      }
    }
  }

  async createQuickInspectAttachments(imageObj: any, lid: string, modelType?: string) {
    let imageData = imageObj.image;
    let filePath = '';
    let fileName = imageObj.fileName;
    let mimeType = ''
    console.log("Creating attachment for image:", imageObj);
    let platform = await this.unviredCordovaSdk.platform();
    switch (platform) {
      case 'browser':
        filePath = imageData;
        mimeType = imageData.substring(imageData.indexOf("image/") + 6, imageData.indexOf(';'))
        break;
      case 'electron':
        filePath = imageData;
        break;
      default:
        imageData = imageData.substr(imageData.indexOf('_app_file_') + 10, imageData.length - 1);
        imageData = this.device.platform === 'Android' ? `file://${imageData}` : `file:///${imageData}`;
        break;
    }

    let modelVersion;
    modelType = modelType.toLocaleLowerCase();
    let response = await this.unviredCordovaSdk.dbSelect("AI_MODEL_VERSION_HEADER", `MODEL_TYPE='${modelType}'`)
    if (response.type == ResultType.success) {
      if (response.data && response.data.length > 0) {
        modelVersion = response.data[0].MODEL_NAME;
      }
    }
    if (response.type == ResultType.error) {
      modelVersion = '0.0.0'
      this.unviredCordovaSdk.logInfo("VISIONAISERVICE", "createQuickInspectAttachments", " READ AI_MODEL_VERSION_HEADER " + JSON.stringify(response.error))
    }

    const { image, ...tileImageData } = imageObj;
    tileImageData['modelVersion'] = modelVersion;
    console.log('File name: ', fileName);
    let quickInspAttachmentObject = new INSPECTION_REPORT_ATTACHMENT();
    quickInspAttachmentObject.FID = lid;
    quickInspAttachmentObject.MIME_TYPE = mimeType;
    quickInspAttachmentObject.UID = UtilserviceService.guid();
    quickInspAttachmentObject.EXTERNAL_URL = "";
    quickInspAttachmentObject.FILE_NAME = fileName;
    quickInspAttachmentObject.LOCAL_PATH = (this.platformId == 'electron' || this.device.platform == 'browser') ? filePath : imageData.substring(7, imageData.length);
    //TODO: guid already setting in UID, need in TAG1 also?
    quickInspAttachmentObject.TAG1 = UtilserviceService.guid();
    quickInspAttachmentObject.TAG3 = JSON.stringify(tileImageData);

    console.log("Attachment object to be saved:", quickInspAttachmentObject);
    if (this.device.platform == 'browser') {
      quickInspAttachmentObject.EXTERNAL_URL = filePath;
      quickInspAttachmentObject.FILE_NAME = fileName;
      quickInspAttachmentObject.LOCAL_PATH = '';
      this.unviredCordovaSdk.dbInsertOrUpdate("QUICK_INSPECTION_ATTACHMENT", quickInspAttachmentObject, false)
      return {
        "data": [{
          "fields": quickInspAttachmentObject
        }]
      }
    } else {
      return await this.unviredCordovaSdk.createAttachmentItem("QUICK_INSPECTION_ATTACHMENT", quickInspAttachmentObject)
    }
  }

  async deleteInsightAIImages() {
    let basepath = ''
    if (this.platform.is('android')) {
      basepath = this.file.dataDirectory;
    } else if (this.platform.is('ios')) {
      basepath = this.file.documentsDirectory;
    }

    this.file.removeRecursively(basepath, 'Inspections').then(async _ => {
      console.log("Successfully deleted all InsightAI images from storage");
    });
  }
}