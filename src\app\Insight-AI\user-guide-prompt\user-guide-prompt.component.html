<ion-header>
  <ion-toolbar>
    <ion-title>Insight AI Guide</ion-title>
    <ion-buttons slot="end">
      <ion-button size="large" (click)="dismissModal({ role: 'cancelled' })">
        <ion-icon name="close" color="dark" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content scroll-y="false" fullscreen="true">
  <div class="guide-content">
    <div class="image-container">
      @if(abrasionType=='External') {
        <img src="assets/guideImgs/External_Guide.png" class="guide-img">
      } @else if(abrasionType=='Internal') {
        <img src="assets/guideImgs/Internal_Guide.png" class="guide-img">
      }
    </div>
    <div class="button-container">
      <ion-button size="default" class="camButton" (click)="dismissModal({ role: role })">
        Open {{ role }}
      </ion-button>
    </div>
  </div>
</ion-content>
