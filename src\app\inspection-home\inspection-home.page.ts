import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UtilserviceService } from '../services/utilservice.service';
import { MenuController, ModalController } from '@ionic/angular';
import { HelpService } from '../services/help.service';
import { DataService } from '../services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { TranslateService } from '@ngx-translate/core';
import { UserPreferenceService } from '../services/user-preference.service';
import { AlertService } from '../services/alert.service';
import { GenericListPage } from '../generic-list/generic-list.page';
import { INSPECTION_HEADER } from 'src/models/INSPECTION_HEADER';
import { AppConstant } from 'src/constants/appConstants';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faListCheck, faGrip, faEnvelope, faCircleInfo, faCircle } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from '../services/platform.service';
@Component({
  selector: 'app-inspection-home',
  templateUrl: './inspection-home.page.html',
  styleUrls: ['./inspection-home.page.scss'],
})
export class InspectionHomePage implements OnInit {

  mode = true;
  inspectionCount: any = 0;
  isLoggedInUserEmployee : boolean
  platformId: string = this.platformService.getPlatformId();
  accountsList: any = [];
  selectedAccount: any;
  selectedAsset: any;
  RFTWorkOrders: any;
  createdInspectionHeader : any;
  todaysDate = new Date();

  constructor(private route: Router,
    public platformService: PlatformService,
    public device: Device,
    private service: UtilserviceService,
    public menu: MenuController,
    public helpService: HelpService,
    public dataService: DataService,
    private translate: TranslateService,
    private userPreferenceService: UserPreferenceService,
    private modalController: ModalController,
    private alertService: AlertService,
    private unviredCordovaSDK: UnviredCordovaSDK,
    private utilService: UtilserviceService,
    private dataServcie: DataService,
    private faIconLibrary: FaIconLibrary
  ) {
    this.faIconLibrary.addIcons(faBars, faListCheck, faGrip, faEnvelope , faCircleInfo)
    // this.getCountInspections()
    this.dataService.getApplicationSetting()
  }

  async getCountInspections(account?:any,asset?:any) {
    var res = await this.dataService.getCountInspections(account,asset);
    if (res.type == ResultType.success) {
      if (res.data.length > 0) {
        this.inspectionCount = res.data[0].COUNT;
      }
    }
  }


  async ngOnInit() {
    
  }

  async newInspection() {
    if(this.isLoggedInUserEmployee) {
      await this.presentModal('WORK_ORDER')
    } else {
      this.route.navigate(['newinspection']);
    }
    //  this.service.setInspState('new');
    //  this.route.navigate(['inspection']);
  }

  openInsp() {
    this.service.setInspState('open');
    this.route.navigate(['inspection']);
  }

  historyInsp() {
    this.service.setInspState('history');
    this.route.navigate(['inspection']);
  }
  screenMode() {
    this.mode = !this.mode;
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  //Exit help mode
  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  async ionViewWillEnter() {
    this.isLoggedInUserEmployee =  this.dataService.selectedRole=='Employee' ? true : false;
    if(this.isLoggedInUserEmployee) {
      let account = await this.userPreferenceService.getUserPreference('account');
      this.selectedAccount = JSON.parse(account);
      let asset = await this.userPreferenceService.getUserPreference('asset');
      this.selectedAsset = JSON.parse(asset);
      await this.getCountInspections(this.selectedAccount,this.selectedAsset)
      this.filterRFTWorkOrder()
    } else {
      await this.getCountInspections()
    }
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataServcie.navigateToInspection()
  }

  gotoResources() {
    this.dataServcie.navigateToResources()
  }

  gotoContact() {
    this.dataServcie.navigateToContact()
  }

  async presentModal(title: string) {
    var tempList, pageTitle;
    switch (title) {
      case 'WORK_ORDER':
        tempList = this.RFTWorkOrders
        pageTitle = this.translate.instant("Work Orders")
        break;
    }
    // this.alertService.present().then(async () => {
      const modal = await this.modalController.create({
        component: GenericListPage,
        componentProps: { value: tempList, title: pageTitle, page: title, isRFT: true }
      });
      modal.present().then(async ()=>{
        if(this.alertService.isLoading) {
          await this.alertService.dismiss()
        }
      });

      modal.onDidDismiss().then(async (data) => {
        if(data?.data?.data) {
          if(data?.data?.woType=="RFT") {
            // console.log("rft work order object:",data.data.data);
            let RFTWorkOrder = data.data.data;
            this.createdInspectionHeader = new INSPECTION_HEADER();
            this.createdInspectionHeader.INSPECTION_ID = UtilserviceService.guid();
            this.createdInspectionHeader.EXTERNAL_ID = JSON.parse(JSON.stringify(this.createdInspectionHeader.INSPECTION_ID));
            this.createdInspectionHeader.ACCOUNT_ID = RFTWorkOrder.ACCOUNT;
            this.createdInspectionHeader.ACCOUNT_NAME = RFTWorkOrder.ACCOUNT_NAME;
            this.createdInspectionHeader.ASSET_ID = RFTWorkOrder.ASSET_ID;
            let assetRes = await this.unviredCordovaSDK.dbSelect("ASSET_HEADER",`ID = '${RFTWorkOrder.ASSET_ID}' `)
            if(assetRes.type == ResultType.success) {
              this.createdInspectionHeader.ASSET_NAME = assetRes.data[0].NAME;
            } else {
              this.unviredCordovaSDK.logError("presentModal", "ASSET_HEADER", "Error while getting data from db" + JSON.stringify(assetRes))
            }
            // this.createdInspectionHeader.ASSET_NAME = RFTWorkOrder;
            this.createdInspectionHeader.WORK_ORDER = RFTWorkOrder.ID;
            // this.createdInspectionHeader.CONSTRUCTION = RFTWorkOrder.CONSTRUCTION; //* set it from RPS/Cert header table
            this.createdInspectionHeader.CERTIFICATE_NUM = RFTWorkOrder.CERT_ID;
            this.createdInspectionHeader.APPLICATION = RFTWorkOrder.APPLICATION;
            this.createdInspectionHeader.DIAM = RFTWorkOrder.DIAMETER;
            this.createdInspectionHeader.INDUSTRY = RFTWorkOrder.INDUSTRY;
            this.createdInspectionHeader.WORK_ORDER_NUM = RFTWorkOrder.WO_NUMBER;
            this.createdInspectionHeader.LENGTH_UOM = RFTWorkOrder.LENGTH_OF_SAMPLE_UOM;
            this.createdInspectionHeader.EXT_FLD3 = 'RFT'; //! set this to identify that this inspection is being created by using RFT work order
            let totalLength;
            if(RFTWorkOrder.LENGTH_OF_SAMPLE_UOM=='Meter') {
              this.createdInspectionHeader.PRODUCT_SIZE_MM = RFTWorkOrder.DIAMETER;
              this.createdInspectionHeader.DIAM_UOM = 'Millimeter';
              totalLength = RFTWorkOrder.LEN_OF_SAMPLE_MT;
              this.createdInspectionHeader.PRODUCT_SIZE_INCH = '';
            } else if(RFTWorkOrder.LENGTH_OF_SAMPLE_UOM=='Feet') {
              this.createdInspectionHeader.PRODUCT_SIZE_INCH = RFTWorkOrder.DIAMETER;
              this.createdInspectionHeader.DIAM_UOM = 'Inch';
              totalLength = RFTWorkOrder.LEN_OF_SAMPLE_FT;
              this.createdInspectionHeader.PRODUCT_SIZE_MM = '';
            } else {
              this.createdInspectionHeader.PRODUCT_SIZE_INCH = '';
              this.createdInspectionHeader.PRODUCT_SIZE_MM = '';
              this.createdInspectionHeader.DIAM_UOM = '';
            }
            this.createdInspectionHeader.ORIGINAL_LENGTH = totalLength;
            this.createdInspectionHeader.CURRENT_LENGTH = totalLength
            this.createdInspectionHeader.INSPECTED_LENGTH = totalLength;
            this.createdInspectionHeader.PRODUCT_LEN_FEET = RFTWorkOrder.LEN_OF_SAMPLE_FT ;
            this.createdInspectionHeader.PRODUCT_LEN_METER = RFTWorkOrder.LEN_OF_SAMPLE_MT;
            this.createdInspectionHeader.RFT_NUM = null;
            this.createdInspectionHeader.INSP_TYPE = 'planned';
            this.createdInspectionHeader.CONFIG_STATUS = AppConstant.SELECT_CONFIGURATION
            this.createdInspectionHeader.CONFIG_REFERENCE = '';
            let measurementType = await this.userPreferenceService.getUserPreference('measurementType');
            if (measurementType != undefined && measurementType != "undefined" && measurementType != null && measurementType != '') {
              this.createdInspectionHeader.START_POINT = measurementType;
            }
            this.dataServcie.setSelectedInspectionType("planned")
            if(RFTWorkOrder.CERT_ID!=null && RFTWorkOrder.CERT_ID!='') {
              await this.getCertDeytails(RFTWorkOrder.CERT_ID);
              this.route.navigate(['create-inspection']);
            } else {
              this.createdInspectionHeader.CERT_NAME = '';
              let tempInspHeader = JSON.parse(JSON.stringify(this.createdInspectionHeader))
              this.utilService.setSelectedInspectionHeader(tempInspHeader);
              this.route.navigate(['create-inspection']);
              // this.alertService.showAlert("Error!",this.translate.instant("Certificate ID is not available for this RFT work order, please contact the administrator"));
            }
            if(this.alertService.isLoading) {
              await this.alertService.dismiss()
            }
          } else if(data?.data?.woType=="Inspections") {
            console.log("woType:",data.data.woType);
            let inspectionWorkOrder = data.data.data;
            this.createdInspectionHeader = new INSPECTION_HEADER();
            this.createdInspectionHeader.INSPECTION_ID = UtilserviceService.guid();
            this.createdInspectionHeader.EXTERNAL_ID = JSON.parse(JSON.stringify(this.createdInspectionHeader.INSPECTION_ID));
            this.createdInspectionHeader.ACCOUNT_ID = inspectionWorkOrder.ACCOUNT;
            this.createdInspectionHeader.ACCOUNT_NAME = inspectionWorkOrder.ACCOUNT_NAME;
            this.createdInspectionHeader.ASSET_ID = inspectionWorkOrder.ASSET_ID;
            let assetRes = await this.unviredCordovaSDK.dbSelect("ASSET_HEADER",`ID = '${inspectionWorkOrder.ASSET_ID}' `)
            if(assetRes.type == ResultType.success) {
              this.createdInspectionHeader.ASSET_NAME = assetRes.data[0].NAME;
            } else {
              this.unviredCordovaSDK.logError("presentModal", "ASSET_HEADER", "Error while getting data from db" + JSON.stringify(assetRes))
            }
            // this.createdInspectionHeader.ASSET_NAME = RFTWorkOrder;
            this.createdInspectionHeader.WORK_ORDER = inspectionWorkOrder.ID;
            this.createdInspectionHeader.WORK_ORDER_NUM = inspectionWorkOrder.WO_NUMBER;
            this.createdInspectionHeader.EXT_FLD3 = "Inspections";
            this.createdInspectionHeader.CREATED_DATE = new Date(this.todaysDate.getTime() - (this.todaysDate.getTimezoneOffset() * 60000)).toJSON().slice(0, 20) + '000+0000'
            this.createdInspectionHeader.EXT_FLD2 = this.todaysDate.getTime() + '';
            this.dataServcie.setSelectedInspectionType("planned")
            if(this.alertService.isLoading) {
              await this.alertService.dismiss()
            }
            let tempInspHeader = JSON.parse(JSON.stringify(this.createdInspectionHeader))
            this.utilService.setSelectedInspectionHeader(tempInspHeader);
            this.route.navigate(['create-inspection']);
          }
          
        }
      })
    // });
  }

    // ! ----------------------------SAMSON EMPLOYEE RFT WORK ORDER SELECTION START-----------------------------------
    async filterRFTWorkOrder() {
      var whereClause = "ACCOUNT = '" + this.selectedAccount.ID + "'"
      if(this.selectedAsset != '' && this.selectedAsset != undefined) {
        whereClause = whereClause + ' AND ASSET_ID = "' + this.selectedAsset.ID + '" '
      }
      // whereClause = whereClause + `AND RECORD_TYPE = 'RFT'`
      let workorderRes = await this.unviredCordovaSDK.dbExecuteStatement('SELECT * FROM WORK_ORDER_HEADER WHERE ' + whereClause)
      this.unviredCordovaSDK.logInfo("CREATEINSPECTION", "filterWorkOrder", "Filtering WORK_ORDER_HEADER " + 'SELECT * FROM WORK_ORDER_HEADER WHERE ' + whereClause)
      if (workorderRes.type == ResultType.success) {
        if (workorderRes.data.length > 0) {
          this.RFTWorkOrders = workorderRes.data;
          // this.workOrders = this.workOrders.filter(t => t.ACCOUNT == this.selectedAccount.ID);
          // if(this.selectedAsset != '' && this.selectedAsset != undefined) {
          //   this.workOrders = this.workOrders.filter(t => t.ASSET_ID == this.selectedAsset.ID);
          // }
          // if (this.workOrders.length == 0) {
          //   this.workOrders = [];
          // } 
        } else {
          this.RFTWorkOrders = [];
        }
      } else {
        this.unviredCordovaSDK.logError("create-inspection", "filterWorkOrder", "Error while getting error from db" + JSON.stringify(workorderRes))
        this.alertService.showAlert("Error!",this.translate.instant("Error while getting data from db"));
      }
    }
  
  
  
    async getCertDeytails(cert:any) {
      let query = `SELECT * FROM CERTIFICATE_HEADER WHERE ID = '${cert}' `
      let certRes = await this.unviredCordovaSDK.dbExecuteStatement(query);
      if(certRes.type == ResultType.success) {
        if(certRes.data.length>0) {
          let certObj = certRes.data[0];
          this.createdInspectionHeader.CERT_NAME = certObj.NAME;
          this.createdInspectionHeader.EXT_FLD1 = JSON.stringify({ "CertExistsInSamsonSystem" : certObj.NAME, "IsSamsonProduct": ""});
          this.createdInspectionHeader.CONSTRUCTION = certObj.CONSTRUCTION;
          this.createdInspectionHeader.PRODUCT_TYPE = certObj.PRODUCT_TYPE;
          this.createdInspectionHeader.RPS = certObj.RPS;
          this.createdInspectionHeader.MANUFACTURER = certObj.MANUFACTURER;
          this.createdInspectionHeader.PRODUCT = certObj.PRODUCT;
          this.createdInspectionHeader.PRODUCT_CODE = certObj.PRODUCT_CODE;
          this.createdInspectionHeader.PRODUCT_DESC = certObj.PRODUCT_DESC;
          this.createdInspectionHeader.COLOR = certObj.COLOR;
          this.createdInspectionHeader.COLOR_OTHER = certObj.COLOR_OTHER;
          this.createdInspectionHeader.ORIGINAL_CONFIG = certObj.ORIGINAL_CONFIG;
          this.createdInspectionHeader.PRODUCT_CONFIG = certObj.PRODUCT_CONFIG;
          this.createdInspectionHeader.INSPECTION_NAME = certObj.NAME;
          // this.createdInspectionHeader.ORIGINAL_LENGTH = certObj.ORIGINAL_LENGTH;
          // this.createdInspectionHeader.CURRENT_LENGTH = certObj.CURRENT_LENGTH;
          // this.createdInspectionHeader.INSPECTED_LENGTH =certObj.INSPECTED_LENGTH;
          this.createdInspectionHeader.INSPECTION_NOTES = certObj.INSPECTION_NOTES;
          this.createdInspectionHeader.HAS_CHAFE = certObj.HAS_CHAFE;
          this.createdInspectionHeader.CHAFE_TYPE = certObj.CHAFE_TYPE;
          this.createdInspectionHeader.OTHER_CHAFE = certObj.OTHER_CHAFE;
          this.createdInspectionHeader.IS_JACKETED = certObj.IS_JACKETED;
          this.createdInspectionHeader.INSTALLED_DATE = certObj.INSTALLED_DATE ;
          this.createdInspectionHeader.INSTALLED_STATUS = certObj.INSTALLED_STATUS ;
          this.createdInspectionHeader.LOCATION_INSTALLED = certObj.LOCATION_INSTALLED ;
          this.createdInspectionHeader.INSPECTION_STATUS = AppConstant.IN_PROGRESS ;
          this.createdInspectionHeader.USER_ID = certObj.USER_ID ;
          this.createdInspectionHeader.CREATED_BY = certObj.CREATED_BY ;
          this.createdInspectionHeader.CREATED_DATE = certObj.CREATED_DATE ;
          this.createdInspectionHeader.EXT_FLD2 = new Date(certObj.CREATED_DATE).getTime() + ''
          this.createdInspectionHeader.LAST_MODIFIED_BY = certObj.LAST_MODIFIED_BY ;
          this.createdInspectionHeader.LAST_MODIFIED_DATE = certObj.LAST_MODIFIED_DATE ;
          this.createdInspectionHeader.LINE_POSITION = certObj.LINE_POSITION ;
          this.createdInspectionHeader.PRODUCT_LOCATION = certObj.PRODUCT_LOCATION ;
          this.createdInspectionHeader.IS_DELETED = certObj.IS_DELETED ;
          this.createdInspectionHeader.RPS_NAME = certObj.RPS_NAME ;
          // this.createdInspectionHeader.PRODUCT_SIZE_INCH = certObj.PRODUCT_SIZE_INCH ;
          // this.createdInspectionHeader.PRODUCT_SIZE_MM = certObj.PRODUCT_SIZE_MM ;
          // this.createdInspectionHeader.PRODUCT_LEN_FEET = certObj.PRODUCT_LEN_FEET ;
          // this.createdInspectionHeader.PRODUCT_LEN_METER = certObj.PRODUCT_LEN_METER ;
          // this.createdInspectionHeader.PRODUCT_SIZE_MM = certObj.PRODUCT_SIZE_MM ;
          let tempInspHeader = JSON.parse(JSON.stringify(this.createdInspectionHeader))
          this.utilService.setSelectedInspectionHeader(tempInspHeader);
        } else {
          this.createdInspectionHeader.CERT_NAME = '';
          let tempInspHeader = JSON.parse(JSON.stringify(this.createdInspectionHeader))
          this.utilService.setSelectedInspectionHeader(tempInspHeader);
          this.route.navigate(['create-inspection']);
        }
      } else {
        this.unviredCordovaSDK.logError("getCertDeytails", "CERTIFICATE_HEADER", "Error while getting error from db" + JSON.stringify(certRes))
        this.alertService.showAlert("Error!",this.translate.instant("Error while getting data from CERTIFICATE_HEADER"));
      }
    }
  }