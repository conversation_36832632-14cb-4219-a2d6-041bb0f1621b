import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { NewinspectionPageRoutingModule } from './newinspection-routing.module';

import { NewinspectionPage } from './newinspection.page';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    NewinspectionPageRoutingModule,
    TranslateModule,
    FooterComponent
    
  ],
  declarations: [NewinspectionPage],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
})
export class NewinspectionPageModule {}
