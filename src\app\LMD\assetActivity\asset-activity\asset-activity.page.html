<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'ASSET_ACTIVITY_TYPE_TITLE' | translate}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
      <div style="width:100% !important;height: 100%;
      position: relative;
      width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
      justify-self: center;
      align-content: center;
      align-items: center;
      flex-direction: column;">
        <p style="text-align: center !important;">{{'ACTIVITY_TYPE_TITLE' | translate}}</p>
        <div style="width: 100% !important;">
          <div class="responsive-card-wrapper">
            <ion-card class="card-style" (click)="navigateToAssetDetailsActivity()" style="padding: 8px !important;cursor: pointer;">
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/manualEntry.png" />
            </ion-card>
            <p>{{'MANUAL_ENTRY' | translate}}</p>
          </div>
          <div class="responsive-card-wrapper"
            (click)="disableAutomaticEntry == true ? showErrorMessage() : test()">
            <ion-card class="card-style" (click)="navigateToAsiEntryPage()" [disabled]='disableAutomaticEntry'>
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/Constructions/lookup.png" style="padding: 15px !important;cursor: pointer;" />
            </ion-card>
            <p>{{'AIS_ENTRY' | translate}}</p>
          </div>
        </div>
      </div>
</ion-content>
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()"
    (Inspections)="gotoInspections()" (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>