import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { ResourcesSpliceInstructionComponent } from './resources-splice-instruction/resources-splice-instruction.component';

const routes: Routes = [
  { path: '', redirectTo: '', pathMatch: 'full' },
  {
    path: 'home',
    loadChildren: () => import('./home/<USER>').then( m => m.HomePageModule)
  },
  {
    path: 'home-popup',
    loadChildren: () => import('./home-popup/home-popup.module').then( m => m.HomePopupPageModule)
  },
  {
    path: 'contact',
    loadChildren: () => import('./contact/contact.module').then( m => m.ContactPageModule)
  },
  {
    path: 'settings',
    loadChildren: () => import('./settings/settings.module').then( m => m.SettingsPageModule)
  },
  {
    path: 'detailed-routine-inspection',
    loadChildren: () => import('./detailed-routine-inspection/detailed-routine-inspection.module').then( m => m.DetailedRoutineInspectionPageModule)
  },
  {
    path: 'agreement',
    loadChildren: () => import('./agreement/agreement.module').then( m => m.AgreementPageModule)
  },
  {
    path: 'resource',
    loadChildren: () => import('./resource/resource.module').then( m => m.ResourcePageModule)
  },
  {
    path: 'browser-home',
    loadChildren: () => import('./browser-home/browser-home.module').then( m => m.BrowserHomePageModule)
  },
  {
    path: 'user-preferences',
    loadChildren: () => import('./user-preferences/user-preferences.module').then( m => m.UserPreferencesPageModule)
  },
  {
    path: 'generic-list',
    loadChildren: () => import('./generic-list/generic-list.module').then( m => m.GenericListPageModule)
  },
  {
    path: 'filter-popup',
    loadChildren: () => import('./filter-popup/filter-popup.module').then( m => m.FilterPopupPageModule)
  },
  {
    path: 'login',
    loadChildren: () => import('./login/login.module').then( m => m.LoginPageModule)
  },
  {
    path: 'inspection-home',
    loadChildren: () => import('./inspection-home/inspection-home.module').then( m => m.InspectionHomePageModule)
  },
  {
    path: 'routine-inspection-home',
    loadChildren: () => import('./routine-inspection-home/routine-inspection-home.module').then( m => m.RoutineInspectionHomePageModule)
  },
  {
    path: 'insight-AI-home',
    loadChildren: () => import('./Insight-AI/insight-ai-home/insight-ai-home.module').then( m => m.InsightAIHomePageModule)
  },
  {
    path: 'newinspection',
    loadChildren: () => import('./newinspection/newinspection.module').then( m => m.NewinspectionPageModule)
  },
  {
    path: 'inspection',
    loadChildren: () => import('./inspection/inspection.module').then( m => m.InspectionPageModule)
  },
  {
    path: 'insppopover',
    loadChildren: () => import('./insppopover/insppopover.module').then( m => m.InsppopoverPageModule)
  },
  {
    path: 'routine-inspection-certificates',
    loadChildren: () => import('./routine-inspection-certificates/routine-inspection-certificates.module').then( m => m.RoutineInspectionCertificatesPageModule)
  },
  {
    path: 'routine-inspection-in-progress',
    loadChildren: () => import('./routine-inspection-in-progress/routine-inspection-in-progress.module').then( m => m.RoutineInspectionInProgressPageModule)
  },
  {
    path: 'routine-inspection-completed',
    loadChildren: () => import('./routine-inspection-completed/routine-inspection-completed.module').then( m => m.RoutineInspectionCompletedPageModule)
  },
  {
    path: 'insight-AI-rope-detection',
    loadChildren: () => import('./Insight-AI/insight-ai-rope-detection/insight-ai-rope-detection.module').then( m => m.InsightAIRopeDetectionPageModule)
  },
  {
    path: 'planned-inspection',
    loadChildren: () => import('./planned-inspection/planned-inspection.module').then( m => m.PlannedInspectionPageModule)
  },
  {
    path: 'observations',
    loadChildren: () => import('./observations/observations.module').then( m => m.ObservationsPageModule)
  },
  {
    path: 'advanced-assistence',
    loadChildren: () => import('./advanced-assistence/advanced-assistence.module').then( m => m.AdvancedAssistencePageModule)
  },
  {
    path: 'configuration-type',
    loadChildren: () => import('./configuration-type/configuration-type.module').then( m => m.ConfigurationTypePageModule)
  },
  {
    path: 'new-configuration-list',
    loadChildren: () => import('./new-configuration-list/new-configuration-list.module').then( m => m.NewConfigurationListPageModule)
  },
  {
    path: 'inspection-configuration',
    loadChildren: () => import('./inspection-configuration/inspection-configuration.module').then( m => m.InspectionConfigurationPageModule)
  },
  {
    path: 'setup',
    loadChildren: () => import('./setup/setup.module').then( m => m.SetupPageModule)
  },
  {
    path: 'account-setup',
    loadChildren: () => import('./account-setup/account-setup.module').then( m => m.AccountSetupPageModule)
  },
  { 
    path: 'guest-home', 
    loadChildren: () => import ('./guest/guest-home/guest-home.module').then( m => m.GuestHomePageModule) 
  },
  { 
    path: 'guest-contact',
    loadChildren: () => import ('./guest/guest-contact/guest-contact.module').then( m => m.GuestContactPageModule) 
  },
  { 
    path: 'guest-resource',
    loadChildren: () => import ('./guest/guest-resource/guest-resource.module').then( m => m.GuestResourcePageModule) 
  },
  { 
    path: 'guest-resource-abrasion', 
    loadChildren: () => import ('./guest/guest-resource-abrasion/guest-resource-abrasion.module').then( m => m.GuestResourceAbrasionPageModule) 
  },
  { 
    path: 'guest-resource-inspections', 
    loadChildren: () => import ('./guest/guest-resource-inspections/guest-resource-inspections.module').then( m => m.GuestResourceInspectionsPageModule) 
  },
  {
    path: 'guest-abrasion-comparator',
    loadChildren: () => import('./guest/guest-abrasion-comparator/guest-abrasion-comparator.module').then( m => m.GuestAbrasionComparatorPageModule)
  },
  {
    path: 'guest-abrasion-comparator-internal',
    loadChildren: () => import('./guest/guest-abrasion-comparator-internal/guest-abrasion-comparator-internal.module').then( m => m.GuestAbrasionComparatorInternalPageModule)
  },
  {
    path: 'guest-abrasion-comparator-external',
    loadChildren: () => import('./guest/guest-abrasion-comparator-external/guest-abrasion-comparator-external.module').then( m => m.GuestAbrasionComparatorExternalPageModule)
  },
  { 
    path: 'guest-settings', 
    loadChildren: () => import ('./guest/guest-settings/guest-settings.module').then( m => m.GuestSettingsPageModule) 
  },
  {
    path: 'guest-abrasion-comparator-tenex',
    loadChildren: () => import('./guest/guest-abrasion-comparator-tenex/guest-abrasion-comparator-tenex.module').then( m => m.GuestAbrasionComparatorTenexPageModule)
  },
  {
    path: 'line-tracker-home',
    loadChildren: () => import('./line-tracker/line-tracker-home/line-tracker-home.module').then( m => m.LineTrackerHomePageModule)
  },
  {
    path: 'data-entry',
    loadChildren: () => import('./line-tracker/data-entry/data-entry.module').then( m => m.DataEntryPageModule)
  },
  {
    path: 'reporting',
    loadChildren: () => import('./line-tracker/reporting/reporting.module').then( m => m.ReportingPageModule)
  },
  {
    path: 'certificate-list',
    loadChildren: () => import('./line-tracker/certificate-list/certificate-list.module').then( m => m.CertificateListPageModule)
  },
  {
    path: 'new-lmd',
    loadChildren: () => import('./line-tracker/new-lmd/new-lmd.module').then( m => m.NewLmdPageModule)
  },
  {
    path: 'in-progress-lmd',
    loadChildren: () => import('./line-tracker/in-progress-lmd/in-progress-lmd.module').then( m => m.InProgressLmdPageModule)
  },
  {
    path: 'completed-lmd',
    loadChildren: () => import('./line-tracker/completed-lmd/completed-lmd.module').then( m => m.CompletedLmdPageModule)
  },
  {
    path: 'work-order-details',
    loadChildren: () => import('./line-tracker/work-order-details/work-order-details.module').then( m => m.WorkOrderDetailsPageModule)
  },
  {
    path: 'repair-lmd',
    loadChildren: () => import('./LMD/repair-lmd/repair-lmd.module').then( m => m.RepairLmdPageModule)
  },
  {
    path: 'equipment-insp-lmd',
    loadChildren: () => import('./LMD/equipment-insp-lmd/equipment-insp-lmd.module').then( m => m.EquipmentInspLmdPageModule)
  },
  {
    path: 'cropping-lmd',
    loadChildren: () => import('./LMD/cropping-lmd/cropping-lmd.module').then( m => m.CroppingLmdPageModule)
  },
  {
    path: 'end-for-end-lmd',
    loadChildren: () => import('./LMD/end-for-end-lmd/end-for-end-lmd.module').then( m => m.EndForEndLmdPageModule)
  },
  {
    path: 'rotation-lmd',
    loadChildren: () => import('./LMD/rotation-lmd/rotation-lmd.module').then( m => m.RotationLmdPageModule)
  },
  {
    path: 'new-configuration-list',
    loadChildren: () => import('./new-configuration-list/new-configuration-list.module').then( m => m.NewConfigurationListPageModule)
  },
  {
    path: 'inspection-configuration',
    loadChildren: () => import('./inspection-configuration/inspection-configuration.module').then( m => m.InspectionConfigurationPageModule)
  },
  {
    path: 'setup',
    loadChildren: () => import('./setup/setup.module').then( m => m.SetupPageModule)
  },
  {
    path: 'account-setup',
    loadChildren: () => import('./account-setup/account-setup.module').then( m => m.AccountSetupPageModule)
  },
  { 
    path: 'guest-home', 
    loadChildren: () => import ('./guest/guest-home/guest-home.module').then( m => m.GuestHomePageModule) 
  },
  { 
    path: 'guest-contact',
    loadChildren: () => import ('./guest/guest-contact/guest-contact.module').then( m => m.GuestContactPageModule) 
  },
  { 
    path: 'guest-resource',
    loadChildren: () => import ('./guest/guest-resource/guest-resource.module').then( m => m.GuestResourcePageModule) 
  },
  { 
    path: 'guest-resource-abrasion', 
    loadChildren: () => import ('./guest/guest-resource-abrasion/guest-resource-abrasion.module').then( m => m.GuestResourceAbrasionPageModule) 
  },
  { 
    path: 'guest-resource-inspections', 
    loadChildren: () => import ('./guest/guest-resource-inspections/guest-resource-inspections.module').then( m => m.GuestResourceInspectionsPageModule) 
  },
  {
    path: 'guest-abrasion-comparator',
    loadChildren: () => import('./guest/guest-abrasion-comparator/guest-abrasion-comparator.module').then( m => m.GuestAbrasionComparatorPageModule)
  },
  {
    path: 'guest-abrasion-comparator-internal',
    loadChildren: () => import('./guest/guest-abrasion-comparator-internal/guest-abrasion-comparator-internal.module').then( m => m.GuestAbrasionComparatorInternalPageModule)
  },
  {
    path: 'guest-abrasion-comparator-external',
    loadChildren: () => import('./guest/guest-abrasion-comparator-external/guest-abrasion-comparator-external.module').then( m => m.GuestAbrasionComparatorExternalPageModule)
  },
  { 
    path: 'guest-settings', 
    loadChildren: () => import ('./guest/guest-settings/guest-settings.module').then( m => m.GuestSettingsPageModule) 
  },
  {
    path: 'guest-abrasion-comparator-tenex',
    loadChildren: () => import('./guest/guest-abrasion-comparator-tenex/guest-abrasion-comparator-tenex.module').then( m => m.GuestAbrasionComparatorTenexPageModule)
  },
  {
    path: 'create-inspection',
    loadChildren: () => import('./create-inspection/create-inspection.module').then( m => m.CreateInspectionPageModule)
  },
  {
    path: 'previous-configuration',
    loadChildren: () => import('./previous-configuration/previous-configuration.module').then( m => m.PreviousConfigurationPageModule)
  },
  {
    path: 'end-image',
    loadChildren: () => import('./end-image/end-image.module').then( m => m.EndImagePageModule)
  },
  {
    path: 'splice-image',
    loadChildren: () => import('./splice-image/splice-image.module').then( m => m.SpliceImagePageModule)
  },
  {
    path: 'chafe-image',
    loadChildren: () => import('./chafe-image/chafe-image.module').then( m => m.ChafeImagePageModule)
  },
  {
    path: 'configuration-list',
    loadChildren: () => import('./configuration-list/configuration-list.module').then( m => m.ConfigurationListPageModule)
  },
  {
    path: 'install-line-lmd',
    loadChildren: () => import('./LMD/install-line-lmd/install-line-lmd.module').then( m => m.InstallLineLmdPageModule)
  },
  {
    path: 'request-new-line-lmd',
    loadChildren: () => import('./LMD/request-new-line-lmd/request-new-line-lmd.module').then( m => m.RequestNewLineLmdPageModule)
  },
  {
    path: 'asset-activity',
    loadChildren: () => import('./LMD/assetActivity/asset-activity/asset-activity.module').then( m => m.AssetActivityPageModule)
  },
  {
    path: 'line-tracker-popover',
    loadChildren: () => import('./line-tracker/line-tracker-popover/line-tracker-popover.module').then( m => m.LineTrackerPopoverPageModule)
  },
  {
    path: 'inventory',
    loadChildren: () => import('./line-tracker/inventory/inventory.module').then( m => m.InventoryPageModule)
  },
  {
    path: 'rps-details',
    loadChildren: () => import('./line-tracker/rps-details/rps-details.module').then( m => m.RpsDetailsPageModule)
  },
  {
    path: 'maintenance',
    loadChildren: () => import('./line-tracker/maintenance/maintenance.module').then( m => m.MaintenancePageModule)
  },
  {
    path: 'mooring-history',
    loadChildren: () => import('./line-tracker/mooring-history/mooring-history.module').then( m => m.MooringHistoryPageModule)
  },
  {
    path: 'maintenance-details',
    loadChildren: () => import('./line-tracker/maintenance-details/maintenance-details.module').then( m => m.MaintenanceDetailsPageModule)
  },
  {
    path: 'new-configuration-list',
    loadChildren: () => import('./new-configuration-list/new-configuration-list.module').then( m => m.NewConfigurationListPageModule)
  },
  {
    path: 'inspection-configuration',
    loadChildren: () => import('./inspection-configuration/inspection-configuration.module').then( m => m.InspectionConfigurationPageModule)
  },
  {
    path: 'setup',
    loadChildren: () => import('./setup/setup.module').then( m => m.SetupPageModule)
  },
  {
    path: 'account-setup',
    loadChildren: () => import('./account-setup/account-setup.module').then( m => m.AccountSetupPageModule)
  },
  { 
    path: 'guest-home', 
    loadChildren: () => import ('./guest/guest-home/guest-home.module').then( m => m.GuestHomePageModule) 
  },
  { 
    path: 'guest-contact',
    loadChildren: () => import ('./guest/guest-contact/guest-contact.module').then( m => m.GuestContactPageModule) 
  },
  { 
    path: 'guest-resource',
    loadChildren: () => import ('./guest/guest-resource/guest-resource.module').then( m => m.GuestResourcePageModule) 
  },
  { 
    path: 'guest-resource-abrasion', 
    loadChildren: () => import ('./guest/guest-resource-abrasion/guest-resource-abrasion.module').then( m => m.GuestResourceAbrasionPageModule) 
  },
  { 
    path: 'guest-resource-inspections', 
    loadChildren: () => import ('./guest/guest-resource-inspections/guest-resource-inspections.module').then( m => m.GuestResourceInspectionsPageModule) 
  },
  {
    path: 'guest-abrasion-comparator',
    loadChildren: () => import('./guest/guest-abrasion-comparator/guest-abrasion-comparator.module').then( m => m.GuestAbrasionComparatorPageModule)
  },
  {
    path: 'guest-abrasion-comparator-internal',
    loadChildren: () => import('./guest/guest-abrasion-comparator-internal/guest-abrasion-comparator-internal.module').then( m => m.GuestAbrasionComparatorInternalPageModule)
  },
  {
    path: 'guest-abrasion-comparator-external',
    loadChildren: () => import('./guest/guest-abrasion-comparator-external/guest-abrasion-comparator-external.module').then( m => m.GuestAbrasionComparatorExternalPageModule)
  },
  { 
    path: 'guest-settings', 
    loadChildren: () => import ('./guest/guest-settings/guest-settings.module').then( m => m.GuestSettingsPageModule) 
  },
  {
    path: 'guest-abrasion-comparator-tenex',
    loadChildren: () => import('./guest/guest-abrasion-comparator-tenex/guest-abrasion-comparator-tenex.module').then( m => m.GuestAbrasionComparatorTenexPageModule)
  },
  {
    path: 'create-inspection',
    loadChildren: () => import('./create-inspection/create-inspection.module').then( m => m.CreateInspectionPageModule)
  },
  {
    path: 'previous-configuration',
    loadChildren: () => import('./previous-configuration/previous-configuration.module').then( m => m.PreviousConfigurationPageModule)
  },
  {
    path: 'end-image',
    loadChildren: () => import('./end-image/end-image.module').then( m => m.EndImagePageModule)
  },
  {
    path: 'splice-image',
    loadChildren: () => import('./splice-image/splice-image.module').then( m => m.SpliceImagePageModule)
  },
  {
    path: 'chafe-image',
    loadChildren: () => import('./chafe-image/chafe-image.module').then( m => m.ChafeImagePageModule)
  },
  {
    path: 'configuration-list',
    loadChildren: () => import('./configuration-list/configuration-list.module').then( m => m.ConfigurationListPageModule)
  },
  {
    path: 'abrasion-comparator',
    loadChildren: () => import('./abrasion-comparator/abrasion-comparator.module').then( m => m.AbrasionComparatorPageModule)
  },
  {
    path: 'abrasion-comparator-external',
    loadChildren: () => import('./abrasion-comparator-external/abrasion-comparator-external.module').then( m => m.AbrasionComparatorExternalPageModule)
  },
  {
    path: 'abrasion-comparator-internal',
    loadChildren: () => import('./abrasion-comparator-internal/abrasion-comparator-internal.module').then( m => m.AbrasionComparatorInternalPageModule)
  },
  {
    path: 'abrasion-comparator-tenex-external',
    loadChildren: () => import('./abrasion-comparator-tenex-external/abrasion-comparator-tenex-external.module').then( m => m.AbrasionComparatorTenexExternalPageModule)
  },
  {
    path: 'asset-activity-details',
    loadChildren: () => import('./LMD/assetActivity/asset-activity-details/asset-activity-details.module').then( m => m.AssetActivityDetailsPageModule)
  },
  {
    path: 'lmd-configuration-type',
    loadChildren: () => import('./LMD/assetActivity/lmd-configuration-type/lmd-configuration-type.module').then( m => m.LmdConfigurationTypePageModule)
  },
  {
    path: 'configuration-winches',
    loadChildren: () => import('./LMD/assetActivity/configuration-winches/configuration-winches.module').then( m => m.ConfigurationWinchesPageModule)
  },
  {
    path: 'lmd-summary',
    loadChildren: () => import('./LMD/assetActivity/lmd-summary/lmd-summary.module').then( m => m.LmdSummaryPageModule)
  },
  {
    path: 'asi-entry',
    loadChildren: () => import('./LMD/assetActivity/asi-entry/asi-entry.module').then( m => m.AsiEntryPageModule)
  },
  {
    path: 'configuration-port-details',
    loadChildren: () => import('./LMD/assetActivity/configuration-port-details/configuration-port-details.module').then( m => m.ConfigurationPortDetailsPageModule)
  },
  {
    path: 'new-observation',
    loadChildren: () => import('./new-observation/new-observation.module').then( m => m.NewObservationPageModule)
  },
  {
    path: 'external-abrasion',
    loadChildren: () => import('./external-abrasion/external-abrasion.module').then( m => m.ExternalAbrasionPageModule)
  },
  {
    path: 'internal-abrasion',
    loadChildren: () => import('./internal-abrasion/internal-abrasion.module').then( m => m.InternalAbrasionPageModule)
  },
  {
    path: 'cuts',
    loadChildren: () => import('./cuts/cuts.module').then( m => m.CutsPageModule)
  },
  {
    path: 'discoloration',
    loadChildren: () => import('./discoloration/discoloration.module').then( m => m.DiscolorationPageModule)
  },
  {
    path: 'melting',
    loadChildren: () => import('./melting/melting.module').then( m => m.MeltingPageModule)
  },
  {
    path: 'caging',
    loadChildren: () => import('./caging/caging.module').then( m => m.CagingPageModule)
  },
  {
    path: 'contamination',
    loadChildren: () => import('./contamination/contamination.module').then( m => m.ContaminationPageModule)
  },
  {
    path: 'parting',
    loadChildren: () => import('./parting/parting.module').then( m => m.PartingPageModule)
  },
  {
    path: 'twist',
    loadChildren: () => import('./twist/twist.module').then( m => m.TwistPageModule)
  },
  {
    path: 'inconsistent-diameter',
    loadChildren: () => import('./inconsistent-diameter/inconsistent-diameter.module').then( m => m.InconsistentDiameterPageModule)
  },
  {
    path: 'compression',
    loadChildren: () => import('./compression/compression.module').then( m => m.CompressionPageModule)
  },
  {
    path: 'wire-breaks',
    loadChildren: () => import('./wire-breaks/wire-breaks.module').then( m => m.WireBreaksPageModule)
  },
  {
    path: 'corrosion',
    loadChildren: () => import('./corrosion/corrosion.module').then( m => m.CorrosionPageModule)
  },
  {
    path: 'kinking',
    loadChildren: () => import('./kinking/kinking.module').then( m => m.KinkingPageModule)
  },
  {
    path: 'basket-deformation',
    loadChildren: () => import('./basket-deformation/basket-deformation.module').then( m => m.BasketDeformationPageModule)
  },
  {
    path: 'protrusion',
    loadChildren: () => import('./protrusion/protrusion.module').then( m => m.ProtrusionPageModule)
  },
  {
    path: 'heat-damage',
    loadChildren: () => import('./heat-damage/heat-damage.module').then( m => m.HeatDamagePageModule)
  },
  {
    path: 'waviness',
    loadChildren: () => import('./waviness/waviness.module').then( m => m.WavinessPageModule)
  },
  {
    path: 'flattening',
    loadChildren: () => import('./flattening/flattening.module').then( m => m.FlatteningPageModule)
  },
  {
    path: 'pulled',
    loadChildren: () => import('./pulled/pulled.module').then( m => m.PulledPageModule)
  },
  {
    path: 'linear-damage',
    loadChildren: () => import('./linear-damage/linear-damage.module').then( m => m.LinearDamagePageModule)
  },
  // {
  //   path: 'camera',
  //   loadChildren: () => import('./camera/camera.module').then( m => m.CameraPageModule)
  // },
  {
    path: 'baseline',
    loadChildren: () => import('./baseline/baseline.module').then( m => m.BaselinePageModule)
  },
  {
    path: 'guest-external',
    loadChildren: () => import('./guest/guest-external/guest-external.module').then( m => m.GuestExternalPageModule)
  },
  {
    path: 'guest-general',
    loadChildren: () => import('./guest/guest-general/guest-general.module').then( m => m.GuestGeneralPageModule)
  },
  {
    path: 'guest-inspection',
    loadChildren: () => import('./guest/guest-inspection/guest-inspection.module').then( m => m.GuestInspectionPageModule)
  },
  {
    path: 'guest-inspection-home',
    loadChildren: () => import('./guest/guest-inspection-home/guest-inspection-home.module').then( m => m.GuestInspectionHomePageModule)
  },
  {
    path: 'guest-internal',
    loadChildren: () => import('./guest/guest-internal/guest-internal.module').then( m => m.GuestInternalPageModule)
  },
  {
    path: 'guest-new-observation',
    loadChildren: () => import('./guest/guest-new-observation/guest-new-observation.module').then( m => m.GuestNewObservationPageModule)
  },
  {
    path: 'guest-resource-double-braid',
    loadChildren: () => import('./guest/guest-resource-double-braid/guest-resource-double-braid.module').then( m => m.GuestResourceDoubleBraidPageModule)
  },
  {
    path: 'guest-single-braid',
    loadChildren: () => import('./guest/guest-single-braid/guest-single-braid.module').then( m => m.GuestSingleBraidPageModule)
  },
  {
    path: 'guest-resource-external',
    loadChildren: () => import('./guest/guest-resource-external/guest-resource-external.module').then( m => m.GuestResourceExternalPageModule)
  },
  {
    path: 'guest-resource-internal',
    loadChildren: () => import('./guest/guest-resource-internal/guest-resource-internal.module').then( m => m.GuestResourceInternalPageModule)
  },
  { path: 'guest-resources-splice-instruction', 
    loadChildren: () => import ('./guest/guest-resources-splice-instruction/guest-resources-splice-instruction.module').then( m => m.GuestResourcesSpliceInstructionPageModule)
  },
  // {
  //   path: 'camera-comparator',
  //   loadChildren: () => import('./camera-comparator/camera-comparator.module').then( m => m.CameraComparatorPageModule)
  // },
  {

    path: 'add-configuration',
    loadChildren: () => import('./add-configuration/add-configuration.module').then( m => m.AddConfigurationPageModule)
  },
  {
    path: 'other-image',
    loadChildren: () => import('./other-image/other-image.module').then( m => m.OtherImagePageModule)
  },
  {
    path: 'history-component',
    loadChildren: () => import('./history-component/history-component.module').then( m => m.HistoryComponentPageModule)
  },
  {
    path: 'general-line-usage-details',
    loadChildren: () => import('./LMD/assetActivity/general-line-usage-details/general-line-usage-details.module').then( m => m.GeneralLineUsageDetailsPageModule)
  },
  {
    path: 'abrasion-list',
    loadChildren: () => import('./abrasion-list/abrasion-list.module').then( m => m.AbrasionListPageModule)
  },
  {
    path: 'resources-splice-instruction',component: ResourcesSpliceInstructionComponent
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})

export class AppRoutingModule {}