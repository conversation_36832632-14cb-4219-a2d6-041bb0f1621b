<ion-header>
  <ion-toolbar>
    <ion-title>Add Configuration</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="end">

      <!-- <ion-button color="primary" (click)="helpService.switchMode()" class="help-button-style">

        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
      </ion-button> -->
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- <div class="inspectionHome">
  <ion-card style="height:22%;border-radius: 10px;" (click)="helpService.helpMode ? '' : navigateToObservation('End')"
      class="ion-activatable">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/end-image.png" />
      <ion-label class="card-title" style="font-size: 17px;"> {{'End' |translate}} </ion-label>
    </ion-card>

    <ion-card style="height:22%;border-radius: 10px;" (click)="helpService.helpMode ? '' : navigateToObservation('Splice')"
      class="ion-activatable">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/splice-image.png" />
      <ion-label class="card-title" style="font-size: 17px;"> {{'Splice' | translate}} </ion-label>
    </ion-card>

    <ion-card style="height:22%;border-radius: 10px;" (click)="helpService.helpMode ? '' : navigateToObservation('Chafe')"
      class="ion-activatable">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/chafe-image.png" />
      <ion-label class="card-title" style="font-size: 17px;"> {{'Chafe' | translate}} </ion-label>
    </ion-card>

    <ion-card style="height:22%;border-radius: 10px;" (click)="helpService.helpMode ? '' : navigateToObservation('Other')"
      class="ion-activatable">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/other-image.png" />
      <ion-label class="card-title" style="font-size: 17px;"> {{'Other' | translate}} </ion-label>
    </ion-card>
  </div> -->


  <ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'Configuration' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div>
        <ion-row>
          <div class="col6 border-style" (click)="helpService.helpMode ? '' : navigateToObservation('End')">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'End' | translate}}
              </div>
            </div>
          </div>
          <div class="col6 border-style" (click)="helpService.helpMode ? '' : navigateToObservation('Splice')">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Splice' | translate}}
              </div>
            </div>
          </div>
        </ion-row>
        <ion-row>
          <div class="col6 border-style" (click)="helpService.helpMode ? '' : navigateToObservation('Chafe')">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Chafe' | translate}}
              </div>
            </div>
          </div>
          <div class="col6 border-style" (click)="helpService.helpMode ? '' : navigateToObservation('Other')">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Other' | translate}}
              </div>
            </div>
          </div>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card>

</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
