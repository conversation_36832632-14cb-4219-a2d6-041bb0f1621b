import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FileOpenerService } from '../services/file-opener.service';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Menu<PERSON>ontroller, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { LmdService } from '../services/lmd.service';
import { UserPreferenceService } from '../services/user-preference.service';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { EmailComposer } from '@awesome-cordova-plugins/email-composer/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faList<PERSON>heck, faGrip, faMagnifyingGlass, faTrash, faCircleChevronLeft } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-routine-inspection-completed',
  templateUrl: './routine-inspection-completed.page.html',
  styleUrls: ['./routine-inspection-completed.page.scss'],
})
export class RoutineInspectionCompletedPage implements OnInit {
  searchbar = false;
  searchIcon = true;
  inProgressData = [];
  initialData = [];
  title = true;
  tempArray = [];
  currentFilePath: any;
  constructor(private router: Router,
    public helpService: HelpService,
    public menu: MenuController,
    public dataService: DataService,
    public lmdService: LmdService,
    public translate: TranslateService,
    public alertController: AlertController,
    public utilityService: UtilserviceService,
    private unviredCordovaSDK: UnviredCordovaSDK,
    public device: Device,
    private fileOpenerService: FileOpenerService,
    private file: File,
    private emailComposer: EmailComposer,
    private userPreferenceService: UserPreferenceService,
    public faIconLibrary : FaIconLibrary) {

    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip, faMagnifyingGlass, faTrash, faCircleChevronLeft)
  }

  ngOnInit() {
    this.getInitialData();
  }

  toggleSearch() {
    this.searchbar = !this.searchbar;
    this.searchIcon = !this.searchIcon;
    this.title = !this.title;
  }
  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }
  showDetails() {
    this.router.navigate(['/lmd-details']);
  }
  icon(index) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }

  cancelHistoricalItem(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.initializeItems();
    }
  }
  searchHistoricalItem(ev) {
    // this.getInitialData();
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.inProgressData = this.initialData.filter((item) => {
        return (
          (item.LID && item.LID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LMD_DATA.cert && item.LMD_DATA.cert.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LMD_DATA.totalWorkingHour && item.LMD_DATA.totalWorkingHour.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    }
    else {
      this.getInitialData();
    }
  }

  initializeItems() {
    this.inProgressData = this.initialData;
  }
  async getInitialData() {
    const result = await this.unviredCordovaSDK.dbSelect("LMD_HEADER", "LMD_STATUS = 'Completed' AND LMD_TYPE == 'RoutineInspection'");
    if (result.type === ResultType.success) {
      this.initialData = result.data;
      
      // console.log('db data' + JSON.stringify(this.initialData));
      this.inProgressData = [];
      const resultData = result.data;

      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < resultData.length; i++) {
        const value = result.data[i];
        const dataToFormat = value.LMD_DATA;
        const formatedLmdData = dataToFormat.replace(/\"/g, '"');
        const newFormatedData = JSON.parse(formatedLmdData);
        value.LMD_DATA = newFormatedData;
        this.inProgressData.unshift(value);
      }
    } else {
      alert('No data found');
    }
  }

  // navigateToDetailsPage(item) {
  //   this.lmdService.setSelectedAsset(item)
  //   this.lmdService.setSelectedAssetActivity(item)
  //   this.lmdService.setSelectedActivityFromList(false)
  //   this.utilityService.setSelectedLMD(item);
  //   this.lmdService.setIsFromInProgress(false)
  //   this.lmdService.setIsFromCompleted(true);
  //   this.utilityService.setLMDEditMode(true);
  //   this.lmdService.setReadOnlyMode(true)
  //   this.utilityService.setSelectedLMD(item);
  //   switch (item.LMD_TYPE) {
  //     case 'Cropping':
  //       this.router.navigate(['cropping-lmd']);
  //       break;
  //     case 'Repair':
  //       this.router.navigate(['repair-lmd']);
  //       break;
  //     case 'EndForEnd':
  //       this.router.navigate(['end-for-end-lmd']);
  //       break;
  //     case 'EquipmentInsp':
  //       this.router.navigate(['equipment-insp-lmd']);
  //       break;
  //     case 'Rotation':
  //       this.router.navigate(['rotation-lmd']);
  //       break;
  //     case 'InstallLine':
  //       this.router.navigate(['install-line-lmd']);
  //       break;
  //     case 'RequestNewLine':
  //       this.router.navigate(['request-new-line-lmd']);
  //       break;
  //     case 'AssetActivity':
  //       if (item.LMD_DATA.isGeneral == true) {
  //         this.router.navigate(['general-line-usage-details']);
  //       } else {
  //         this.router.navigate(['asset-activity-details']);
  //       }
  //       break;
  //   }

  // }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async inProgressItemTrash(index, item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Lmd'),
      message: '<strong>' + this.translate.instant('You want to delete this entry') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: async () => {
            this.icon(index);
            var result = await this.markAsDeleted(item);
            this.inProgressData.splice(index, 1);
            this.getInitialData();
          }
        }
      ]
    });
    await alert.present();
  }

  async markAsDeleted(item) {
    var whereClause = "LMD_ID like '" + item.LMD_ID + "'"
    return await this.unviredCordovaSDK.dbDelete("LMD_HEADER", whereClause)
  }

  composeEmail() {
    this.generateCSV('email');
  }

  async generateCSV(type: string) {
    const value = await this.convertToCSV();
    // console.log('formatted data' + value);
    const folderPath = this.file.dataDirectory;

    //#region "Get todays date and time"
    const currentdate = new Date();
    const datetime = currentdate.getDate() + '' + (currentdate.getMonth() + 1) + '' +
      currentdate.getFullYear() + '' + currentdate.getHours() +
      '' + currentdate.getMinutes() + '' + currentdate.getSeconds();
    //#endregion

    const fileName = `RIN${datetime}.csv`;
    const blob = new Blob(['\ufeff' + value], { type: 'text/csv;charset=utf-8;' });
    if(this.device.platform == "browser") {
      const a = document.createElement('a');
      const url = window.URL.createObjectURL(blob);

      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();

    } else {
      this.file.writeFile(`${folderPath}`, fileName, blob, { replace: true, append: false }).then(async (res) => {
        // console.log('successfully written', res);
        if (type === 'export') {
          this.fileOpenerService.openFile(fileName, 'text/csv');
          // .then(_ => console.log('Opened successfully')).
          // catch(err => console.log('error while opening file', err));
        } else {
          const mailId = await this.userPreferenceService.getUserPreference('email');
          const email = {
            to: `${mailId ? mailId : ''}`,
            cc: '',
            attachments: [
              this.currentFilePath
            ],
            subject: 'Inventory certificates report',
            body: 'Please find the attachment',
            isHtml: true
          };
          this.emailComposer.open(email);
        }
      }).catch(err => console.log('error while writing to file' + err));
    }

    const filePath = folderPath + fileName;
    this.currentFilePath = `${filePath}`;
  }

  async convertToCSV() {
    let tempArr:any[]=[];
    let tempHeader=[];
    let excelHeader=[];
    this.initialData.forEach((ele,index)=>{
      let ar=[];
      if(index==0) {
        excelHeader.push({title:'LMD_ID',value:'LMD ID',position:0},{title:'LMD_TYPE',value:'LMD Type',position:1})
        Object.keys(ele.LMD_DATA).forEach((e,j)=>{
          if(e=='ANOMOLIES_SELECTED') {
            this.initialData.forEach(ele=>{
              if(ele.LMD_DATA.ANOMOLIES_SELECTED.length > 0) {
                ele.LMD_DATA.ANOMOLIES_SELECTED.forEach(an=>{
                  if(!tempHeader.includes(an.ANOMOLY_NAME)) {
                    excelHeader.push({property:an.ANOMOLY_NAME,value:an.ANOMOLY_NAME,position:excelHeader.length+1})
                    tempHeader.push(an.ANOMOLY_NAME);
                  }
                })
              }
            })
          } else {
            if(this.initialData.some(ele=>(ele.LMD_DATA[e]!="" && ele.LMD_DATA[e]!=null && ele.LMD_DATA[e]!=undefined)) && !['CERTIFICATE_NUM','WINCH_ID','showExternalErrorMessage','showExternalErrorMessageType','ASSET','RPS','showJacketedError','showTailError','showWireError','showHmpeError','ZONE_ONE_INSIGHT_AI','ZONE_TWO_INSIGHT_AI'].includes(e) ) {
              excelHeader.push({property:e,value:this.translate.instant(e),position:excelHeader.length+1})
            }
          }
        });
        tempArr.push(excelHeader.map(ele=>ele.value));
        }
      ar.push(ele.LMD_NAME,ele.LMD_TYPE);
      
      Object.keys(ele.LMD_DATA).forEach(e=>{
          if(e=='ANOMOLIES_SELECTED') {
            if(ele.LMD_DATA.ANOMOLIES_SELECTED.length>0) {
              let temp =Array.from(Array(tempHeader.length), () => '');
              ele.LMD_DATA.ANOMOLIES_SELECTED.forEach((anomoly:any)=>{
                  temp[tempHeader.indexOf(anomoly.ANOMOLY_NAME)]=(anomoly.ANOMOLY_TYPE==="C")?'true':anomoly.ANOMOLY_VALUE
              })
              ar.push(...temp);
            } else {
              ar.push(...Array.from(Array(tempHeader.length), () => ''));
            }
          } else {
            if(!['CERTIFICATE_NUM','WINCH_ID','showExternalErrorMessage','showExternalErrorMessageType','ASSET','RPS','showJacketedError','showTailError','showWireError','showHmpeError','ZONE_ONE_INSIGHT_AI','ZONE_TWO_INSIGHT_AI'].includes(e)) {
                if(excelHeader.map(ele=>ele.property).includes(e)) {
                  if(e == 'CERT_NAME') {
                    ar[excelHeader.map(ele=>ele.property).indexOf(e)]= " " + ele.LMD_DATA[e] + " ";
                  } else if((e=='ZONE_ONE_EXTERNAL_ABRASION' && ele.LMD_DATA['ZONE_ONE_INSIGHT_AI']) || (e=='ZONE_TWO_EXTERNAL_ABRASION' && ele.LMD_DATA['ZONE_TWO_INSIGHT_AI'])) {
                    ar[excelHeader.map(ele=>ele.property).indexOf(e)]="Insight AI   "+ele.LMD_DATA[e]
                  } else {
                    ar[excelHeader.map(ele=>ele.property).indexOf(e)]=ele.LMD_DATA[e]
                  }
              }
            }
          }
      });
      tempArr.push(ar);
    })
    let csvString='';
    tempArr.forEach((dt,index)=>{
      csvString+=dt.toString()+'\n'
    })
    return csvString;
  }

}
