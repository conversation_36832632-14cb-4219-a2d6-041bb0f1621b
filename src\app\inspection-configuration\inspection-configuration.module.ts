import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { InspectionConfigurationPageRoutingModule } from './inspection-configuration-routing.module';

import { InspectionConfigurationPage } from './inspection-configuration.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    InspectionConfigurationPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule,
    FooterComponent
  ],
  declarations: [InspectionConfigurationPage]
})
export class InspectionConfigurationPageModule {}
