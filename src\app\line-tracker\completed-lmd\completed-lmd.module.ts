import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CompletedLmdPageRoutingModule } from './completed-lmd-routing.module';

import { CompletedLmdPage } from './completed-lmd.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from 'src/app/components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    CompletedLmdPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    FooterComponent
  ],
  declarations: [CompletedLmdPage]
})
export class CompletedLmdPageModule {}
