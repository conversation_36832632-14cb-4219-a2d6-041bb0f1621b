<ion-header>
  <ion-toolbar>
    <ion-title>{{'INSPECTION_CONFIGURATION_TITLE' | translate}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="editInspection()" color="light" fill="clear" *ngIf="selectedInspectionHeader.INSPECTION_STATUS==statusReopened && !readOnly">
        <ion-icon name="create" color="primary"></ion-icon>
      </ion-button>
      <ion-button color="primary" style="padding-right: 0px !important;" *ngIf="!readOnly"
        (click)="helpService.helpMode ? test() : presentAlertConfirm()" class="help-button-style" mode="md">
        <span
          style="padding-right: 5px !important; font-size: 14px !important; text-transform: none;">{{'Inspect' | translate}}</span>
      </ion-button>
      <!-- <ion-button color="primary" slot="end" (click)="helpService.helpMode ? test() : presentResetConfirm()" class="help-button-style" style="padding-right: 0px !important;padding-left: 0px !important; margin: 0px">
        <fa-icon class="icon-style-help" icon="trash"></fa-icon>
      </ion-button> -->
      <!-- <ion-button color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
      </ion-button> -->
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card style="border-radius: 10px; padding: 10px 10px 0px 10px;" class="ion-activatable">
    <p>Configuration Type : {{productConfig}}</p>
    <p>Product Type : {{product}}</p>
    <p>{{'INSPECTION_CONFIGURATION_OVERALL_LENGTH_LABEL' | translate}} : {{overallLength}}</p>
  </ion-card>
  <p style="margin-top:1rem; margin-left:1rem">Configuration Summary</p>
  <div style="padding:10px 15px 0px 15px" *ngIf="!isLoggedInUserEmployee">
    <div class="alert alert-info" role="alert" style="text-align:center" *ngIf='(configurationMeasurementList == 0)'>
      {{'INSPECTION_CONFIGURATION_ZERO_DATA_TEXT' | translate}}
    </div>
  </div>

  <!--& show list for customer  -->
  <ion-list lines="none" *ngIf="!isLoggedInUserEmployee">
    <div *ngFor="let item of configurationMeasurementList ; let i = index">
      <ion-item-sliding>
        <ion-item>
          <ion-label style="font-size:16px;" *ngIf="(item.DATA.type == 'Other')">
            <span>{{item.DATA.type}}: {{item.DATA.otherData.otherDescription}}</span><br />
            <span>{{item.DATA.start}} - {{item.ending}} <span>{{selectedUnit}}</span> </span>
          </ion-label>
          <ion-label style="font-size:16px;" *ngIf="(item.DATA.type == 'End')">
            <span>{{item.DATA.type}}: {{item.DATA.otherData.endType}}</span><br />
            <span>{{item.DATA.otherData.ropeEnd}}: {{item.DATA.otherData.measurementLength}}
              <span>{{selectedUnit}}</span> </span>
          </ion-label>
          <ion-label style="font-size:16px;"
            *ngIf="(item.DATA.type == 'Splice' && (item.ending != undefined && item.ending != ''))">
            <span>{{item.DATA.type}}: {{item.DATA.otherData.spliceType}}</span><br />
            <span>{{item.DATA.start}} - {{item.ending}} <span>{{selectedUnit}}</span></span>
          </ion-label>
          <ion-label style="font-size:16px;"
            *ngIf="(item.DATA.type == 'Splice' && (item.ending == undefined || item.ending == ''))">
            <span>{{item.DATA.type}}: {{item.DATA.otherData.spliceType}}</span><br />
            <span>{{item.DATA.start}} - {{item.ending}} <span>{{selectedUnit}}</span> </span>
          </ion-label>
          <ion-label style="font-size:16px;"
            *ngIf="(item.DATA.type == 'Chafe' && (item.ending != undefined && item.ending != ''))">
            <span>{{item.DATA.type}}: {{item.DATA.otherData.chafeType}}</span><br />
            <span>{{item.DATA.start}} - {{item.ending}} <span>{{selectedUnit}}</span> </span>
          </ion-label>
          <ion-label style="font-size:16px;"
            *ngIf="(item.DATA.type == 'Chafe' && (item.ending == undefined || item.ending == ''))">
            <span>{{item.DATA.type}}: {{item.DATA.otherData.chafeType}} </span><br />
            <span>{{item.DATA.otherData.measurementLength}} <span>{{selectedUnit}}</span> </span>
          </ion-label>
          <ion-button
            *ngIf="((item.DATA.type == 'External' || item.DATA.type == 'Internal') && item.DATA.start !== null)  || (item.DATA.type !== 'External' && item.DATA.type !== 'Internal')"
            (click)="review(item.DATA)" fill="outline" style="text-transform: none;" color="darkMedium" slot="end"
            mode="md" [disabled]='helpService.helpMode'>
            &nbsp; {{'Review' | translate}} &nbsp;
          </ion-button>
        </ion-item>
        <ion-item-options side="end" *ngIf='!readOnly'>
          <ion-item-option color="light" (click)="resetConfirm(item)">
            <ion-icon slot="icon-only" mode="md" color="danger" name="trash" style="font-size:22px;"></ion-icon>
          </ion-item-option>
        </ion-item-options>
      </ion-item-sliding>
    </div>
  </ion-list>

  <!-- * ------------------------------------------------------------------------------------------------------------------------------------------------------>
  <!-- & show grid for samson employee -->
  <!-- ~ showing  table format for desktop and tablet screens -->
  <div class="desktopConfig" *ngIf="isLoggedInUserEmployee">
    <h4 class="configTabTitle">Configurations Recieved</h4>
    <ion-card>
      <div class="table_component" role="region" tabindex="0">
        <table class="configTable">
          <thead>
            <tr>
              <th>Sample</th>
              <th>Type</th>
              <th>Start</th>
              <th>End</th>
              <th>Notes</th>
              <th>Photo</th>
              <th>Options</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let config of labEmployeeConfigList;let i=index">
              <td>{{ i + 1 }}</td>
              <td>{{ config.type }}</td>
              <td>
                <div *ngIf="i>0;else OALStart">
                  {{ config.DATA!=undefined && config.DATA!='' ? config.DATA.start : '' }}
                </div>
                <ng-template #OALStart>
                  <ion-item lines="none" class="OALinput">
                    <ion-input style="width:55px;font-size: 14px;font-weight: 600;" clearInput="true" placeholder="Enter Start" type="number" [(ngModel)]="config.DATA.start" [disabled]="readOnly"></ion-input>
                    <!-- <ion-input [readonly]="OALStartDisabled" style="width:55px;" clearInput="true" placeholder="Start" (ionBlur)="OALStartDisabled = true" #start [(ngModel)]="config.DATA.start" (ionInput)="onOALEdit($event)"></ion-input>
                    <ion-button fill="solid" color="primary" slot="end" (click)="editOAL(start,'start')" class="configBtn" *ngIf="!readOnly">
                      <ion-icon color="light" slot="icon-only" name="pencil"></ion-icon>
                    </ion-button> -->
                  </ion-item>
                </ng-template>
              </td> <!-- * show start value if config type contains DATA -->
              <td>
                <div *ngIf="i>0;else OALEnd">
                  {{ config.DATA!=undefined && config.DATA!='' ? config.DATA.otherData.ending : '' }}
                </div>
                <ng-template #OALEnd>
                  <ion-item lines="none" class="OALinput">
                    <ion-input clearInput="true" style="width:55px;font-size: 14px;font-weight: 600;" placeholder="Enter End" type="number" #end [(ngModel)]="config.DATA.end" [disabled]="readOnly"></ion-input>
                    <!-- <ion-input clearInput="true" [readonly]="OALEndDIsabled" style="width:55px;" placeholder="End" (ionBlur)="OALEndDIsabled = true" #end [(ngModel)]="config.DATA.end" (ionInput)="onOALEdit($event)"></ion-input> -->
                    <!-- <ion-button fill="solid" color="primary" slot="end" class="configBtn" (click)="editOAL(end,'end')" *ngIf="!readOnly">
                      <ion-icon color="light" slot="icon-only" name="pencil"></ion-icon>
                    </ion-button> -->
                  </ion-item>
                </ng-template>
              </td>
              <td>{{ (config.DATA!=undefined && config.DATA!='' && i>0) ? config.DATA.otherData.observationNotes: '' }}</td>
              <td *ngIf="config.DATA!=undefined && config.DATA!='' && i>0;else noImages">
                <div style="display: flex;flex-direction: row;justify-content: flex-start;flex-wrap: wrap;border:none;">
                  <ion-thumbnail *ngFor="let photo of config.photos" class="configPhoto" style="margin: 0px 3px !important;">
                    <img [src]="photo" />
                  </ion-thumbnail>
                </div>
              </td>
              <ng-template #noImages>
                  <td>
                  </td>
              </ng-template>
              <td>
                <ion-button color="primary" *ngIf="i>0 && !readOnly" size="small" fill="solid" class="configBtn optionsBtn" (click)="Edit(config.typeValue,i, 'edit')" title="Edit {{config.type}}">
                  <fa-icon icon="pencil" style="color:#ffffff;" ></fa-icon>
                </ion-button>
                <ion-button color="primary" *ngIf="i>0 && readOnly" size="small" fill="solid" class="configBtn optionsBtn" (click)="Edit(config.typeValue,i, 'edit')" title="Preview {{config.type}}">
                  <fa-icon icon="eye" style="color:#ffffff;"></fa-icon>
                </ion-button>
                <ion-button color="primary" *ngIf="i>0 && !readOnly" size="small" fill="solid" class="configBtn optionsBtn" (click)="presentPopover(config.typeValue,i,'add')" title="Add Configuration">
                  <fa-icon icon="plus" style="color:#ffffff;"></fa-icon>
                </ion-button>
                <ion-button *ngIf="i>0 && !readOnly" size="small" fill="clear" color="danger" class="configBtn optionsBtn" title="Delete {{config.type}}" (click)="resetConfirm(config)">
                  <fa-icon icon="trash"></fa-icon>
                </ion-button>
              </td>
            </tr>
          </tbody>
        </table>
        <p *ngIf="labEmployeeConfigList.length==1" style="color:red;text-align:center; margin-top: 50px;">No Configurations available</p>
            <ion-button *ngIf="labEmployeeConfigList.length==1" color="primary" fill="solid" style="position:absolute;bottom: 0;right:0;" (click)="presentPopover('',0,'add')" title="Add Configuration">
              <fa-icon icon="plus"></fa-icon>
            </ion-button>
      </div>
    </ion-card>
  </div>
  <!-- ~ showing cards format for mobile screens -->
  <div class="modileConfig" *ngIf="isLoggedInUserEmployee">
    <p *ngIf="labEmployeeConfigList.length==0" style="color:red;text-align:center; margin-top: 50px;">No Observations available</p>
    <ion-button *ngIf="labEmployeeConfigList.length==0" color="primary" fill="solid" style="position:absolute;bottom: 0;right:0;" (click)="presentPopover('',0,'add')">
      <fa-icon icon="plus"></fa-icon>
    </ion-button>
    <ion-card *ngFor="let config of labEmployeeConfigList;let i=index">
      <ion-card-header style="padding:5px 0 0 10px">
        <ion-row>
          <ion-col size="7">
            <ion-card-title>{{ config.type }}</ion-card-title>
          </ion-col>
          <ion-col size="5" style="text-align:end">
            <ion-button *ngIf="i>0 && (!readOnly || config.DATA!='')" size="small" color="primary" fill="solid" class="configBtn optionsBtn" (click)="Edit(config.typeValue,i,'edit')">
              <fa-icon icon="pencil" style="color:#ffffff;" *ngIf="!readOnly"></fa-icon>
              <fa-icon icon="eye" *ngIf="readOnly" style="color:#ffffff;"></fa-icon>
            </ion-button>
            <ion-button *ngIf="i>0 && (!readOnly)" color="primary" size="small" fill="solid" class="configBtn optionsBtn" (click)="presentPopover(config.typeValue,i,'add')" title="Add Configuration">
              <fa-icon icon="plus" style="color:#ffffff;" *ngIf="!readOnly" ></fa-icon>
            </ion-button>
            <ion-button *ngIf="i>0 && !readOnly" size="small"  fill="clear" title="Delete {{ config.type }}" (click)="resetConfirm(config)" [disabled]="config.DATA==undefined || config.DATA==''" style="width:40px">
              <fa-icon icon="trash" style="color:#ff0000"></fa-icon>
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-card-header>
    
      <ion-card-content>
        <!-- * using div start-->
        <div *ngIf="i>0"><strong>Specimen :</strong> {{ i  }} </div>
        <!-- <div><strong>Type :</strong> {{ config.type }} </div> -->
        <div>
          <div *ngIf="i>0;else OALStart">
            <strong>Start : </strong>{{ config.DATA!=undefined && config.DATA!='' ? config.DATA.start : '' }}
          </div>
          <ng-template #OALStart>
            <ion-item lines="none" class="OALinput">
              <!-- <ion-label style="font-weight:bold;">Start :</ion-label> -->
              <mat-form-field style="width:100%">
                <input matInput type="number"  maxlength="8"
                  placeholder="Enter start" step="any" inputmode="decimal" [readonly]="readOnly"
                  [(ngModel)]="config.DATA.start">
              </mat-form-field>
              <!-- <ion-input label="Start :" placeholder="Enter Start" type="text" inputmode="numeric" pattern="[0-9]" [readonly]="OALStartDisabled" clearInput="true" (ionBlur)="OALStartDisabled = true" #start [(ngModel)]="config.DATA.start"></ion-input> -->
              <!-- <ion-button fill="clear" slot="end" aria-label="Show/hide" style="--ripple-color:transparent" (click)="editOAL(start,'start')" style="margin:0px;" *ngIf="!readOnly">
                <ion-icon slot="icon-only" name="pencil"></ion-icon>
              </ion-button> -->
            </ion-item>
          </ng-template>
        </div>
        <div>
          <div *ngIf="i>0;else OALEnd">
            <strong>End : </strong>{{ config.DATA!=undefined && config.DATA!='' ? config.DATA.otherData.ending : '' }}
          </div>
          <ng-template #OALEnd>
            <ion-item lines="none" class="OALinput">
              <!-- <ion-label style="font-weight:bold;">End :</ion-label> -->
              <mat-form-field style="width:100%">
                <!-- <mat-label>Start</mat-label> -->
                <input matInput type="number" [(ngModel)]="config.DATA.end" maxlength="8" [readonly]="readOnly"
                  placeholder="Enter End" step="any" inputmode="decimal">
              </mat-form-field>
              <!-- <ion-input label="Start :" placeholder="Enter End" type="text" inputmode="numeric" pattern="[0-9]" [readonly]="OALEndDIsabled" clearInput="true" (ionBlur)="OALEndDIsabled = true" #end [(ngModel)]="config.DATA.end"></ion-input> -->
              <!-- <ion-button fill="clear" slot="end" aria-label="Show/hide" style="--ripple-color:transparent" (click)="editOAL(end,'end')" style="margin:0px;" *ngIf="!readOnly">
                <ion-icon slot="icon-only" name="pencil"></ion-icon>
              </ion-button> -->
            </ion-item>
          </ng-template>
        </div>
        <div *ngIf="i!=0"><strong>Notes :</strong> {{ (config.DATA!=undefined && config.DATA!='' && i>0) ? config.DATA.otherData.observationNotes: '' }} </div>
        <div *ngIf="i!=0"><strong>Photo :</strong>
          <div *ngIf="(config.DATA!=undefined && config.DATA!='' && i>0 );else noImages" style="display: flex;flex-direction: row;flex-wrap: wrap;">
            <ion-thumbnail *ngFor="let photo of config.photos" class="configPhoto" style="margin: 0px 3px !important;">
              <img [src]="photo" />
            </ion-thumbnail>
          </div>
          <ng-template #noImages>
              <ion-label>
              </ion-label>
          </ng-template>
        </div>
        <!-- <p *ngIf="labEmployeeConfigList.length==1" style="color:red;text-align:center; margin-top: 50px;">No Configurations available</p> -->
        
        <!-- * using div end -->
      </ion-card-content>
    </ion-card>
    <div style="display: flex;justify-content: flex-end;" *ngIf="!readOnly">
      <ion-button *ngIf="labEmployeeConfigList.length==1" color="primary" fill="solid" style="margin-right:10px" (click)="presentPopover('',0,'add')">
        <fa-icon icon="plus"></fa-icon>
      </ion-button>
    </div>
  </div>

</ion-content>
<div style="position: absolute; bottom: 56px; width: 100%" *ngIf="!isLoggedInUserEmployee">
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : addMeasurement()">
      <!-- <ion-icon name="add" style="font-size:30px;"></ion-icon> -->
      <fa-icon class="icon-style-other" icon="plus" style="font-size:26px!important"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</div>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>

<app-footer *ngIf="!footerClose" (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>