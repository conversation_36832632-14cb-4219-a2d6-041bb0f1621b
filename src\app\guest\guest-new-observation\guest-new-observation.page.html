<ion-header>
  <ion-toolbar>
    <ion-title>{{'Select Observation' | translate}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="card-style" *ngIf="standard.length > 0">
    <ion-card-header class="card-header-style">
      {{'Abrasion Rating' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div *ngIf="noOptions == false">
        <ion-row *ngFor="let row of standard">
          <div *ngFor="let item of row" class="{{item.class}} border-style" (click)='navigateToObservation(item.label)'>
            <div class="tile" style="line-height:unset !important;">
              <div
                style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
                <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                  <div *ngIf="item.icon != ''">
                    <img [src]="item.icon" style="height: 100% !important; max-width: 40px !important" />
                  </div>
                </div>
                <div style="width:62%; padding-left: 15px; margin: auto !important;">
                  {{item.label}}</div>
                <div style="width: 18% !important; float: right !important">
                  <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">
                    {{item.count}}</ion-badge>
                </div>
              </div>

            </div>
          </div>
          <br>
        </ion-row>
      </div>
      <div *ngIf="noOptions == true">{{'No Options found' | translate}}</div>
    </ion-card-content>
  </ion-card>

  <ion-card ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'Anomaly' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div *ngIf="noOptions == false">
        <ion-row *ngFor="let row of others">
          <div *ngFor="let item of row" class="{{item.class}} border-style" (click)='standardobservation(item.label)'>
            <div class="tile" style="line-height:unset !important;">
              <div
                style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
                <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                  <img [src]="item.icon" style="height: 100% !important; max-width: 40px !important" />
                </div>
                <div style="width:80%; padding-left: 15px; margin: auto !important;">
                  {{item.label}}</div>
              </div>
            </div>

          </div>
          <br>
        </ion-row>
      </div>
      <div *ngIf="noOptions == true">{{'No Options found' | translate}}</div>
    </ion-card-content>
  </ion-card>
</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>