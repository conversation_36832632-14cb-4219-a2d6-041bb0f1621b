
<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
    <ion-title>{{'Inspection Setup' | translate}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content #mySelect>
  <div class='paddingg-create-top' *ngIf="showFinishCard == false">
    <!-----------------------------------------------------------------------------Account Div START------------------------------------------------------------------------------------------>
    <div style="width:100% !important">
      <ion-card class="card-style-multiple">
        <ion-card-content class="card-content-style">
          <p>{{'Please select account' | translate}}</p>
         <ion-select labelPlacement="stacked"  placeholder="{{'Select Account' | translate}}" interface="popover" [(ngModel)]="selectedAccount"
            *ngIf="accountsList && accountsList.length <=0" (ionChange)="resetData('Account')">
            <ion-select-option *ngFor="let option of accountsList" [value]="option"><span
                *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
            </ion-select-option>
          </ion-select>

          <ion-item (click)="checkAndPresentModal('ACCOUNT')" no-lines text-wrap tappable
            *ngIf="accountsList && accountsList.length > 0" class="ion-item-generic-style" mode="ios">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="!selectedAccount || selectedAccount.NAME == ''" class="drop-down-arrow  value-field">{{ 'Select
            Account' | translate
            }}</div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="selectedAccount && selectedAccount.NAME != ''" class="value-field">{{selectedAccount.NAME}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
        </ion-card-content>
      </ion-card>
    </div>
    <!-----------------------------------------------------------------------------Account Div END------------------------------------------------------------------------------------------>


    <!-----------------------------------------------------------------------------Asset Div START------------------------------------------------------------------------------------------>
    <div style="width:100% !important">
      <ion-card class="card-style-multiple" *ngIf='selectedAccount && selectedAccount.NAME != ""'>
        <ion-card-content class="card-content-style">
          <p>{{'Please select asset' | translate}}</p>
         <ion-select labelPlacement="stacked"  placeholder="{{'Select Asset' || translate}}" interface="popover" [(ngModel)]="selectedAsset"
            *ngIf="assetList && assetList.length <=0" (ionChange)="resetData('Asset')">
            <ion-select-option *ngFor="let option of assetList" [value]="option"><span
                *ngIf="option.NAME ==''">{{option.NAME}}</span>
            </ion-select-option>
          </ion-select>

          <ion-item (click)="checkAndPresentModal('ASSET')" no-lines text-wrap tappable
            *ngIf="assetList && assetList.length > 0" class="ion-item-generic-style" mode="ios">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="!selectedAsset || selectedAsset.NAME == ''" class="drop-down-arrow  value-field">{{ 'Select
            Asset' | translate
            }}</div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="selectedAsset && selectedAsset.NAME != ''" class="value-field">{{selectedAsset.NAME}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
        </ion-card-content>
      </ion-card>
    </div>
    <!-----------------------------------------------------------------------------Asset Div END------------------------------------------------------------------------------------------>

    <!-----------------------------------------------------------------------------Work Order Div Start------------------------------------------------------------------------------------------>
    <div style="width:100% !important" *ngIf="inspectionType == 'planned'">
        <!-- <ion-card class="card-style-multiple" *ngIf='selectedAsset && selectedAsset.NAME != ""'>
        <ion-card-content>
          <p>{{'INSPECTION_SETUP_WORK_ORDER_QUESTION' | translate}}</p>

          <p style="font-style: italic; padding-top: 15px;">{{'INSPECTION_SETUP_WORKORDER_INSTRUCTION' | translate}}
          </p>
          <ion-radio-group [(ngModel)]="searchWorkOrder" (ionChange)="resetWorkOrder($event)">
            <ion-row class="ion-radio-row">
              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                  <ion-label>{{'Yes' | translate}}</ion-label>
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                  <ion-label>{{'No' | translate}}</ion-label>
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-radio-group>
        </ion-card-content>
      </ion-card> -->


      <ion-card class="card-style-multiple" *ngIf='selectedAsset && selectedAsset.NAME != ""'>
        <ion-card-content>
          <p>{{'INSPECTION_SETUP_WORK_ORDER_QUESTION' | translate}}</p>

          <p style="font-style: italic; padding-top: 15px;">{{'INSPECTION_SETUP_WORKORDER_INSTRUCTION' | translate}}
          </p>
          <!-- <ion-radio-group [(ngModel)]="searchWorkOrder" (ionChange)="resetWorkOrder($event)">
            <ion-row class="ion-radio-row">
              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                  <ion-label>{{'Yes' | translate}}</ion-label>
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                  <ion-label>{{'No' | translate}}</ion-label>
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-radio-group> -->
          <ion-row class="ion-radio-row">
            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <div class="radio">
                  <input id="yes1" type="radio" value="yes" name="workOrder" [(ngModel)]="searchWorkOrder" (click)="resetWorkOrder($event)">
                  <label  for="yes1" class="radio-label">Yes</label>
                </div>
              </ion-item>
            </ion-col>
        
            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <div class="radio">
                  <input id="no1" type="radio" value="no" name="workOrder" [(ngModel)]="searchWorkOrder" (click)="resetWorkOrder($event)">
                  <label for="no1" class="radio-label">No</label>
                </div>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-card-content>
      </ion-card>

      <ion-card class="card-style-multiple" *ngIf="searchWorkOrder == 'yes'">
        <ion-card-content>
          <p>{{'Workorder Number' | translate}}</p>
          <ion-item (click)="presentModal('WORK_ORDER')" no-lines text-wrap tappable *ngIf="workOrders"
            class="ion-item-generic-style" mode="ios">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="workOrderNo == ''" class="drop-down-arrow  value-field">{{ 'Select Workorder Number' | translate
            }}</div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="workOrderNo != ''" class="value-field">{{workOrderNo}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
        </ion-card-content>
      </ion-card>

      <ion-card class="card-style-multiple" *ngIf="showWorkorderResultCard == true && searchedWorkOrder.length > 0">
        <ion-card-content>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedWorkOrder.length > 0 && searchedWorkOrder[0].WO_NUMBER && searchedWorkOrder[0].WO_NUMBER != "")'>
            <div style="width: 45%">{{'Workorder#' | translate }} : </div>
            <div style="width: 45%">{{searchedWorkOrder[0].WO_NUMBER}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedWorkOrder.length > 0 && searchedWorkOrder[0].WO_SUBJECT && searchedWorkOrder[0].WO_SUBJECT != "")'>
            <div style="width: 45%">{{'Subject' | translate }} : </div>
            <div style="width: 45%">{{searchedWorkOrder[0].WO_SUBJECT}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedWorkOrder.length > 0 && searchedWorkOrder[0].WO_DESC && searchedWorkOrder[0].WO_DESC != "")'>
            <div style="width: 45%">{{'Description' | translate }} : </div>
            <div style="width: 45%">{{searchedWorkOrder[0].WO_DESC}}</div>
          </div>
        </ion-card-content>
      </ion-card>

      <ion-card class="card-style-multiple" *ngIf="showWorkorderResultCard == true && searchedWorkOrder.length == 0">
        <ion-card-content>
          <p>{{'No work order found.' | translate}}</p>
        </ion-card-content>
      </ion-card>
    </div>
    <!-----------------------------------------------------------------------------Work Order Div End------------------------------------------------------------------------------------------>

    <!-----------------------------------------------------------------------------Certificate Div Start------------------------------------------------------------------------------------------>
    
    <div style="width:100% !important" *ngIf="inspectionType != ''">

      <!-- && CUSTOMER WORK ORDER NUMBER START -->
      <ion-card class="card-style-multiple" *ngIf="searchWorkOrder== 'no' || (inspectionType == 'adhoc' && (selectedAsset && selectedAsset.NAME != ''))">
        <ion-card-content>
          <p>{{'Customer Work Order Number' | translate}}</p>
          <div style="width: 100% !important;" [formGroup]="custWorkOrderForm">
            <ion-input maxlength="8" 
            [ngClass]="!custWorkOrderForm.get('workOrderNoCtrl').errors?.required && (custWorkOrderForm.get('workOrderNoCtrl')).invalid?'inputInValid':'inputValid'"  
            (keydown)="customWorkOrderChanged($event);" placeholder="{{'Customer Work Order Number' | translate}}"
              type="text" formControlName="workOrderNoCtrl" (focusout)="reset('customOwrkOrder')"></ion-input>
              <mat-error *ngIf="custWorkOrderForm.get('workOrderNoCtrl').errors?.maxlength">{{ "Character Limit Exceeded" | translate: { limit: '8'} }}</mat-error>
              <!-- <mat-error *ngIf="custWorkOrderForm.get('workOrderNoCtrl').errors?.pattern" > {{ "Only letters and numbers are allowed" | translate }}</mat-error> -->
          </div>
        </ion-card-content>
      </ion-card>
      <!-- && CUSTOMER WORK ORDER NUMBER END -->
      
      <ion-card class="card-style-multiple" *ngIf="(searchWorkOrder == 'no' && (custWorkOrderForm.get('workOrderNoCtrl')).value!='' && (custWorkOrderForm.get('workOrderNoCtrl')).value!=null) || showWorkorderResultCard == true || (inspectionType == 'adhoc' && (selectedAccount && selectedAccount.NAME != ''))">
        <ion-card-content class="card-content-style">
          <p>{{'Event Date' | translate}}</p>
          <div [formGroup]="eventDateForm">
            <mat-form-field style="width:100%">
              <input matInput [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="eventDate"
                placeholder="{{'Please select event date' | translate}}" step="any"
                (keydown)="keyPressedDate($event, 'eventDate', false)" (dateInput)="closeCalender('input', $event)"
                (dateChange)="closeCalender('change', $event)"
                formControlName="eventDateCtrl" (focus)="openCalendar();" (click)="openCalendar();" readonly>
              <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
        
        </ion-card-content>
      </ion-card>
      <ion-card class="card-style-multiple"
        *ngIf="(searchWorkOrder == 'no' && (custWorkOrderForm.get('workOrderNoCtrl')).value!='' && (custWorkOrderForm.get('workOrderNoCtrl')).value!=null) || showWorkorderResultCard == true || (inspectionType == 'adhoc' && (selectedAccount && selectedAccount.NAME != ''))">
        <ion-card-content>
          <p>{{'INSPECTION_SETUP_CERT_NO_QUESTION' | translate}}</p>
          <!-- <ion-radio-group [(ngModel)]="searchCertNo">
            <ion-row class="ion-radio-row">
              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes"></ion-radio>
                  <ion-label>{{'Yes' | translate}}</ion-label>
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no"></ion-radio>
                  <ion-label>{{'No' | translate}}</ion-label>
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-radio-group> -->
          <ion-row class="ion-radio-row">
            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <div class="radio">
                  <input id="yes2" type="radio" value="yes" name="certNo" [(ngModel)]="searchCertNo" (click)="resetCertNo($event)">
                  <label  for="yes2" class="radio-label">Yes</label>
                </div>
              </ion-item>
            </ion-col>
        
            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <div class="radio">
                  <input id="no2" type="radio" value="no" name="certNo" [(ngModel)]="searchCertNo" (click)="resetCertNo($event)">
                  <label for="no2" class="radio-label">No</label>
                </div>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-card-content>
      </ion-card>

      <!-- & UNIQUE ROPE IDENTIFIER START-->
      <ion-card class="card-style-multiple" *ngIf="searchCertNo == 'no'">
        <ion-card-content>
          <p>{{'INSPECTION_SETUP_UNIQUE_IDENTIFIER_LABEL' | translate}}</p>
          <div style="display: inline-flex; width: 100%;" class="customHelpBtn" [formGroup]="certForm">
            <ion-input maxlength='25' type="text" (keyup)="certNumChanged($event)" placeholder="{{'INSPECTION_SETUP_UNIQUE_IDENTIFIER_PLACEHOLDER' | translate}}"
            formControlName="certNumCtrl" (focusout)="reset('uniqueRopeId')"
            ></ion-input>
            <fa-icon style='padding-top: 5px;' class="icon-style" icon="tower-broadcast" (click)="scanRfid()" tooltip="Scan Line RFID Tag" positionV="bottom" desktopEvent="hover" mobileEvent="none" arrow="true"></fa-icon>
            <ion-icon class="icon" *ngIf="device.platform!='iOS'" name="information-circle" color="primary" tooltip="Scan Line RFID Tag" positionV="bottom" event="click" arrow="true" duration="1000"></ion-icon>
            <ion-icon class="icon" *ngIf="device.platform=='iOS'" (click)="showToast()" name="information-circle" color="primary" tooltip="Scan Line RFID Tag" positionV="bottom" event="click" arrow="true" duration="1000"></ion-icon>
          </div>
          <mat-error *ngIf="certForm.get('certNumCtrl').errors?.maxlength">{{ "Character Limit Exceeded" | translate: { limit: '25'} }}</mat-error>
          <mat-error *ngIf="certForm.get('certNumCtrl').errors?.pattern">{{ "letters numbers and symbols allowed" | translate }}</mat-error>
        </ion-card-content>
      </ion-card>
      <!-- & UNIQUE ROPE IDENTIFIER END-->

      <ion-card class="card-style-multiple"
        *ngIf="(searchWorkOrder == 'no' && searchCertNo == 'yes') || (searchCertNo == 'yes' && showWorkorderResultCard == true)  || (inspectionType == 'adhoc' && searchCertNo == 'yes')">
        <ion-card-content>
          <p>{{'Certificate No' | translate}}</p>    
          <div style="display: inline-flex; width: 100%;" class="customHelpBtn">
            <ion-item (click)="presentModal('CERTIFICATE')" no-lines text-wrap tappable style="width: 100% !important;"
              *ngIf="certificateList && certificateList.length >= 0" class="ion-item-generic-style" mode="ios">
              <div
                style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                *ngIf="certNo == ''" class="drop-down-arrow  value-field">{{ 'Select Certificate No' | translate
            }}</div>
              <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                *ngIf="certNo != ''" class="value-field">{{certNo}}</div>
              <ion-note item-right style="display:inline-flex">
                <p class="drop-down-arrow">
                  <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                </p>
              </ion-note>
            </ion-item>
            <fa-icon style='padding-top: 5px;' class="icon-style" icon="tower-broadcast" (click)="scanRfid()" tooltip="Scan Line RFID Tag" positionV="bottom" desktopEvent="hover" mobileEvent="none" arrow="true"></fa-icon>
            <ion-icon class="icon" *ngIf="device.platform!='iOS'" name="information-circle" color="primary" tooltip="Scan Line RFID Tag" positionV="bottom" event="click" arrow="true" duration="1000"></ion-icon>
            <ion-icon class="icon" *ngIf="device.platform=='iOS'" (click)="showToast()" name="information-circle" color="primary" tooltip="Scan Line RFID Tag" positionV="bottom" event="click" arrow="true" duration="1000"></ion-icon>
          </div>
        </ion-card-content>
      </ion-card>

      <ion-card class="card-style-multiple" *ngIf="showCertificateResultCard == true">
        <ion-card-content *ngIf="searchedInspection.length > 0">


          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].ACCOUNT_NAME && searchedInspection[0].ACCOUNT_NAME != "")'>
            <div style="width: 45%">{{'Account Id' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].ACCOUNT_NAME}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].ASSET_NAME && searchedInspection[0].ASSET_NAME != "")'>
            <div style="width: 45%">{{'Asset Id' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].ASSET_NAME}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].INDUSTRY)'>
            <div style="width: 45%">{{'Industry' | translate }} : </div>
            <div *ngIf="searchedInspection[0].INDUSTRY != ''" style="width: 45%">{{searchedInspection[0].INDUSTRY}}
            </div>
            <div *ngIf="searchedInspection[0].INDUSTRY == ''" style="width: 45%">Unknown</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].APPLICATION )'>
            <div style="width: 45%">{{'Application' | translate }} : </div>
            <div *ngIf="searchedInspection[0].APPLICATION != ''" style="width: 45%">
              {{searchedInspection[0].APPLICATION}}</div>
            <div *ngIf="searchedInspection[0].APPLICATION == ''" style="width: 45%">Unknown</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].PRODUCT_TYPE && searchedInspection[0].PRODUCT_TYPE != "")'>
            <div style="width: 45%">{{'Product Type' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].PRODUCT_TYPE}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].PRODUCT && searchedInspection[0].PRODUCT != "")'>
            <div style="width: 45%">{{"Product Name" | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].PRODUCT}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].PRODUCT_CODE && searchedInspection[0].PRODUCT_CODE != "")'>
            <div style="width: 45%">{{'Product Code' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].PRODUCT_CODE}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].PRODUCT_DESC && searchedInspection[0].PRODUCT_DESC != "")'>
            <div style="width: 45%">{{'Product Desc' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].PRODUCT_DESC}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].COLOR && searchedInspection[0].COLOR != "")'>
            <div style="width: 45%">{{'Color' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].COLOR}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].CONSTRUCTION && searchedInspection[0].CONSTRUCTION != "")'>
            <div style="width: 45%">{{'Construction' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].CONSTRUCTION}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].IS_JACKETED && searchedInspection[0].IS_JACKETED != "")'>
            <div style="width: 45%">{{'Is Jacketed' | translate }} : </div>
            <div style="width: 45%">
              <span *ngIf="searchedInspection[0].IS_JACKETED == 1">{{'Yes' | translate}}</span>
              <span *ngIf="searchedInspection[0].IS_JACKETED == 0">{{'No' | translate}}</span>
            </div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].INSTALLED_DATE && searchedInspection[0].INSTALLED_DATE != "")'>
            <div style="width: 45%">{{'Installed Date' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].INSTALLED_DATE}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].INSTALLED_STATUS && searchedInspection[0].INSTALLED_STATUS != "")'>
            <div style="width: 45%">{{'Installed Status' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].INSTALLED_STATUS}}</div>
          </div>
          <div style="display:inline-flex !important; width: 100% !important"
            *ngIf='(searchedInspection.length > 0 && searchedInspection[0].LOCATION_INSTALLED && searchedInspection[0].LOCATION_INSTALLED != "")'>
            <div style="width: 45%">{{'Location Installed' | translate }} : </div>
            <div style="width: 45%">{{searchedInspection[0].LOCATION_INSTALLED}}</div>
          </div>
        </ion-card-content>
        <ion-card-content *ngIf="searchedInspection.length == 0">
          <div>{{'No inspections found.' | translate}}</div>
        </ion-card-content>
      </ion-card>
    </div>
    <!-----------------------------------------------------------------------------Certificate Div End------------------------------------------------------------------------------------------>


    <!-- &----------------------------------------------------------------------------Certificate yes section ------------------------------------------------------------------------------------>
    <div *ngIf="showCertificateResultCard == true && searchedInspection.length != 0">
      <!-----------------------------------------------------------------------------Industry Div Start------------------------------------------------------------------------------------------>
      <!-- <div style="width:100% !important"
        *ngIf="showCertificateResultCard == true && searchedInspection.length != 0 && showIndustry == true"> -->
      <div style="width:100% !important"
        *ngIf="showCertificateResultCard == true && searchedInspection.length > 0">
        <ion-card class="card-style-multiple">
          <ion-card-content class="card-content-style">
            <p>{{'INSPECTION_SETUP_INDUSTRY_LABEL' | translate}}</p>
           <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_INDUSTRY_PLACEHOLDER' | translate}}" interface="popover"
              [(ngModel)]="selectedIndustry" (ngModelChange)="getApplicationType()"
              >
              <ion-select-option *ngFor="let option of industryList, let i = index " [value]="option"><span
                  *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select>
            <div *ngIf="showindistryDesc == true">
              <p> {{'Industry Description' | translate}} </p>
              <ion-input maxlength="225" placeholder="{{'Industry Description' | translate}}"
                [(ngModel)]="indistryDesc">
              </ion-input>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
      <!-----------------------------------------------------------------------------Industry Div End------------------------------------------------------------------------------------------>

      <!-----------------------------------------------------------------------------Application Div Start------------------------------------------------------------------------------------------>
      <div style="width:100% !important"
        *ngIf="showCertificateResultCard == true && searchedInspection.length != 0 && showApplication == true">
        <ion-card class="card-style-multiple">
          <ion-card-content class="card-content-style">
            <p>{{'INSPECTION_SETUP_APPLICATION_LABEL' | translate}}</p>
           <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_APPLICATION_PLACEHOLDER' | translate}}" interface="popover"
              [(ngModel)]="selectedCertApplicationType"
              (ionChange)="checkApplicationType(selectedCertApplicationType, true)">
              <ion-select-option *ngFor="let option of applicationTypeList, let i = index " [value]="option"><span
                  *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select>
            <div *ngIf="showApplicationDescription == true">
              <p> {{'Application Description' | translate}} </p>
              <ion-input maxlength="225" placeholder="{{'Application Description' | translate}}"
                [(ngModel)]="applicationDescription">
              </ion-input>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
      <!-- <div style="width:100% !important"
      *ngIf="showCertificateResultCard == true && searchedInspection.length != 0">
        <ion-card class="card-style-multiple">
          <ion-card-content class="card-content-style">
            <p>{{'INSPECTION_SETUP_APPLICATION_LABEL' | translate}}</p>
           <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_APPLICATION_PLACEHOLDER' | translate}}" interface="popover"
              [(ngModel)]="selectedIndustry"
              (ionChange)="resetData('ApplicationType');checkApplicationType(selectedCertApplicationType)">
              <ion-select-option *ngFor="let option of applicationTypeList" [value]="option"><span *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select>
            <div *ngIf="showApplicationDescription == true">
              <p> {{'Application Description' | translate}} </p>
              <ion-input maxlength="225" placeholder="{{'Application Description' | translate}}"
                [(ngModel)]="applicationDescription"></ion-input>
            </div>
          </ion-card-content>
        </ion-card>
      </div> -->
      <!-------------------------------------------------------------------------------Application Div End ------------------------------------------------------------------------------------------>

      <!------------------------------------------------------------------------------ Diameter section ------------------------------------------------------------------------------------>

      <ion-card class="card-style-multiple" *ngIf="showCertificateResultCard == true && searchedInspection.length != 0 && selectedCertApplicationType != '' && selectedCertApplicationType != undefined">
        <ion-card-content>
          <div style="display:inline-flex; width: 100%;">
            <div style="width: 40%">
              <p> {{'INSPECTION_SETUP_DIAM_UNIT_LABEL' | translate}} </p>
             <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_DIAM_UNITS_PLACEHOLDER' | translate}}" interface="popover"
                [(ngModel)]="inspectionObject.diaUom" (ionChange)=getDiameterFromCertificate() class="unitMeasure">
                <ion-select-option *ngFor="let option of diaUomList" value="{{option.UOM}}"><span
                    *ngIf="option.UOM ==''">{{option.VALUE}}</span><span *ngIf="option.UOM !=''">{{option.UOM}}</span>
                </ion-select-option>
              </ion-select>
            </div>
            <div style="width: 60%">
              <p> {{'INSPECTION_SETUP_DIAM_LABEL' | translate}} </p>
              <ion-input maxlength="80" placeholder="{{'INSPECTION_SETUP_DIAM_PLACEHOLDER' | translate}}"
                [(ngModel)]="inspectionObject.productDiam" readonly></ion-input>
            </div>
          </div>
        </ion-card-content>
      </ion-card>

      <!------------------------------------------------------------------------------Diameter section end ------------------------------------------------------------------------------------>

      <!------------------------------------------------------------------------------ Inspected Length section ------------------------------------------------------------------------------------>

      <ion-card class="card-style-multiple"
        *ngIf="(showCertificateResultCard == true && searchedInspection.length != 0) && (inspectionObject.diaUom != undefined && inspectionObject.diaUom != '') && (selectedCertApplicationType != '' && selectedCertApplicationType != undefined)">
        <ion-card-content>
          <div style="display:inline-flex; width: 100%;">
            <div style="width: 40%">
              <p> {{'INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_LABEL' | translate}} </p>
             <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_PLACEHOLDER' | translate}}"
                interface="popover" [(ngModel)]="inspectionObject.totalLengthUom"
                (ionChange)='getLengthFromCertificate($event)' class="unitMeasure">
                <ion-select-option *ngFor="let option of lengthUomList" value="{{option.UOM}}"><span
                    *ngIf="option.UOM ==''">{{option.VALUE}}</span><span *ngIf="option.UOM !=''">{{option.UOM}}</span>
                </ion-select-option>
              </ion-select>
            </div>
            <div style="width: 60%">
              <p> {{'INSPECTION_SETUP_TOTAL_LENGTH_LABEL' | translate}} </p>
              <ion-input placeholder="{{'INSPECTION_SETUP_TOTAL_LENGTH_PLACEHOLDER' | translate}}" type="number"
                [(ngModel)]="inspectionObject.totalLength" readonly *ngIf="showLengthReadOnly == true"></ion-input>
              <ion-input placeholder="{{'Please Enter' | translate}}" type="number" style="color:slategray ;"
                [(ngModel)]="inspectionObject.totalLength" *ngIf="showLengthReadOnly == false"></ion-input>
            </div>
          </div>
        </ion-card-content>
      </ion-card>


      <ion-card class="card-style-multiple"
        *ngIf="(showCertificateResultCard == true && searchedInspection.length != 0) && (inspectionObject.totalLengthUom != undefined && inspectionObject.totalLengthUom !='') && (inspectionObject.totalLengthUom != undefined && inspectionObject.totalLengthUom != '') && selectedCertApplicationType != '' && selectedCertApplicationType != undefined">
        <ion-card-content>
          <p>{{'Will the entire product be inspected now?'| translate}}</p>
          <ion-radio-group [(ngModel)]="inspectionObject.entireInspection">
            <ion-row class="ion-radio-row">
              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                  <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                  <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-radio-group>
        </ion-card-content>
      </ion-card>

      <ion-card class="card-style-multiple"
        *ngIf="(inspectionObject.entireInspection == 'no' && showCertificateResultCard == true)">
        <ion-card-content>
          <p>{{'INSPECTION_SETUP_INSPECTED_LENGTH_LABEL' | translate}}</p>
          <div style="display: inline-flex !important; width: 100% !important;">
            <ion-input maxlength="18" placeholder="{{'INSPECTION_SETUP_INSPECTED_LENGTH_PLACEHOLDER' | translate}}"
              type="text" [(ngModel)]="inspectionObject.inspectionLength" (ionInput)="getItems($event)"
              [disabled]="inspectionObject.totalLength == ''" step="any" inputmode="decimal"
              (keydown)="keyPressed($event, inspectionObject.inspectionLength)"></ion-input>
            <div class="form-group" style="padding:10px 10px 0px 17px">
              <label
                style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{inspectionObject.totalLengthUom}}
              </label>
            </div>
          </div>
          <div style="color: red; padding-top: 0.2rem" *ngIf="showValidationError == true">
            {{'Inspected length cannot be more than total length which is' | translate }}
            {{getFormattedLengthWithUOM(inspectionObject.totalLength, inspectionObject.totalLengthUom)}}
          </div>
          <div style="color: red; padding-top: 0.2rem" *ngIf="showNegetiveError == true">
            {{'Inspected length cannot less than 1' | translate }}
          </div>
        </ion-card-content>
      </ion-card>

      <!------------------------------------------------------------------------------Inspected Length section end ------------------------------------------------------------------------------------>

      <!------------------------------------------------------------------------------ Installation section ------------------------------------------------------------------------------------>

      <ion-card class="card-style-multiple"
        *ngIf="(showCertificateResultCard == true && searchedInspection.length != 0) && (inspectionObject.entireInspection != undefined && inspectionObject.entireInspection != '')">
        <ion-card-content>
          <p>{{'Is this product installed?' | translate}}</p>
          <ion-radio-group [(ngModel)]="inspectionObject.isInstalled">
            <ion-row class="ion-radio-row">
              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                  <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                  <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-radio-group>
        </ion-card-content>
      </ion-card>

      <!-- & ROPE INSTALLED LOCATION START -->
      <ion-card class="card-style-multiple" *ngIf="inspectionObject.isInstalled == 'yes'">
        <ion-card-content>
          <div [formGroup]="installedLoctnForm">
            <p>{{'INSPECTION_SETUP_INSTALLED_LOCATION_LABEL' | translate}}</p>
          <ion-input maxlength="25" placeholder="{{'INSPECTION_SETUP_INSTALLED_LOCATION_PLACEHOLDER' | translate}}" (keyup)="locationChanged($event)"
          formControlName="ropeLoctnCtrl" (focusout)="reset('ropeInstalledLocn')"></ion-input>
            <mat-error *ngIf="installedLoctnForm.get('ropeLoctnCtrl').hasError('maxlength')">{{ "Character Limit Exceeded" | translate: { limit: '25' } }}</mat-error>
          </div>
        </ion-card-content>
      </ion-card>
      <!-- & ROPE INSTALLED LOCATION END -->
      <!------------------------------------------------------------------------------ Installation section end ------------------------------------------------------------------------------------>

      <!------------------------------------------------------------------------------ Starting point section --------------------------------------------------------------------------------------->
      <ion-card class="card-style-multiple"
        *ngIf="(showCertificateResultCard == true && searchedInspection.length != 0) && (inspectionObject.isInstalled != undefined && inspectionObject.isInstalled !='')">
        <ion-card-content>
          <p>{{'Which end will you be starting from?'| translate}}</p>
          <p style="font-style: italic; padding-top: 15px;">{{'INSPECTION_SETUP_START_END_DESCRIPTION'| translate}}</p>
          <ion-radio-group [(ngModel)]="inspectionObject.startPoint">
            <ion-row class="ion-radio-row">
              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="A">{{'A' | translate}}</ion-radio>
                  <!-- <ion-label>{{'A' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="B">{{'B' | translate}}</ion-radio>
                  <!-- <ion-label>{{'B' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="other">{{'Other' | translate}}</ion-radio>
                  <!-- <ion-label>{{'Other' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-radio-group>
        </ion-card-content>
      </ion-card>

      <ion-card class="card-style-multiple" *ngIf="inspectionObject.startPoint == 'other'">
        <ion-card-content>
          <p>{{'INSPECTION_SETUP_START_DESCRIPTION_LABEL' | translate}}</p>
          <ion-input maxlength="225" placeholder="{{'INSPECTION_SETUP_START_DESCRIPTION_PLACEHOLDER' | translate}}"
            [(ngModel)]="inspectionObject.startPointDesc"></ion-input>
        </ion-card-content>
      </ion-card>
      <!-------------------------------------------------------------------------------- Starting point section end ------------------------------------------------------------------------------------->
    </div>
    <!-------------------------------------------------------------------------------- End Certificate yes section ------------------------------------------------------------------------------------>

    <!-------------------------------------------------------------------------------- Certificate No section ------------------------------------------------------------------------------------>
    <div *ngIf=" searchCertNo == 'no' && (inspectionObject.idNo != undefined && inspectionObject.idNo !='')">

      <!-----------------------------------------------------------------------------Industry Div Start------------------------------------------------------------------------------------------>
      <div style="width:100% !important"
        *ngIf=" searchCertNo == 'no' && (inspectionObject.idNo != undefined && inspectionObject.idNo !='')">
        <ion-card class="card-style-multiple">
          <ion-card-content class="card-content-style">
            <p>{{'INSPECTION_SETUP_INDUSTRY_LABEL' | translate}}</p>
           <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_INDUSTRY_PLACEHOLDER' | translate}}" interface="popover"
              [(ngModel)]="selectedIndustry" (ngModelChange)="getApplicationType()"
              >
              <ion-select-option *ngFor="let option of industryList, let i = index " [value]="option"><span
                  *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select>
            <div *ngIf="showindistryDesc == true">
              <p> {{'Industry Description' | translate}} </p>
              <ion-input maxlength="225" placeholder="{{'Industry Description' | translate}}"
                [(ngModel)]="indistryDesc">
              </ion-input>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
      <!-----------------------------------------------------------------------------Industry Div End------------------------------------------------------------------------------------------>

      <!-----------------------------------------------------------------------------Application Div Start------------------------------------------------------------------------------------------>
      <div style="width:100% !important"
        *ngIf="selectedIndustry != undefined && selectedIndustry != '' &&  selectedIndustry != null">
        <ion-card class="card-style-multiple">
          <ion-card-content class="card-content-style">
            <p>{{'INSPECTION_SETUP_APPLICATION_LABEL' | translate}}</p>
           <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_APPLICATION_PLACEHOLDER' | translate}}" interface="popover"
              [(ngModel)]="selectedApplicationType"
              (ionChange)="checkApplicationType(selectedApplicationType, false)">
              <ion-select-option *ngFor="let option of applicationTypeList" [value]="option"><span
                  *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select>
            <div *ngIf="showApplicationDescription == true">
              <p> {{'Application Description' | translate}} </p>
              <ion-input maxlength="225" placeholder="{{'Application Description' | translate}}"
                [(ngModel)]="applicationDescription"></ion-input>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
      <!-------------------------------------------------------------------------------Application Div End ------------------------------------------------------------------------------------------>
      <!----------------------------------------------------------------------------- Product Color Section ---------------------------------------------------------------------------------------->
      <ion-card class="card-style-multiple"
        *ngIf="selectedApplicationType != undefined && selectedApplicationType != '' &&  selectedApplicationType != null && selectedApplicationType.NAME != '' && selectedApplicationType.NAME != undefined">
        <ion-card-content>
          <p>{{'Product Color' | translate}}</p>
         <ion-select labelPlacement="stacked"  placeholder="{{'Select Product Color' | translate}}" interface="popover"
            [(ngModel)]="inspectionObject.productColor">
            <ion-select-option *ngFor="let option of productColorList" value="{{option.NAME}}"><span
                *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
            </ion-select-option>
          </ion-select>
        </ion-card-content>
      </ion-card>
      <!-------------------------------------------------------------------------------- Product Color Section end ----------------------------------------------------------------------------------->

      <!-------------------------------------------------------------------------------- Product Details section ------------------------------------------------------------------------------------->
      <!-------------------------------------------------------------------------------- Samson product section ------------------------------------------------------------------------------------->
      <ion-card class="card-style-multiple"
        *ngIf="(inspectionObject.productColor != undefined && inspectionObject.productColor != '')">
        <ion-card-content>
          <p>{{'Is this a Samson Product?' | translate}}</p>
          <ion-radio-group [(ngModel)]="inspectionObject.product" (ionChange)="setNextSlide($event)">
            <ion-row class="ion-radio-row">
              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                  <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>

              <ion-col>
                <ion-item class='ion-radio-item-style'>
                  <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                  <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-radio-group>
        </ion-card-content>
      </ion-card>
      <!-------------------------------------------------------------------------------- Samson product section  end ------------------------------------------------------------------------------------->

      <!-------------------------------------------------------------------------------- Samson product yes section ------------------------------------------------------------------------------------->
      <div *ngIf="inspectionObject.product == 'yes'">
        <ion-card class="card-style-multiple"
          *ngIf="inspectionObject.product == 'yes' && (inspectionObject.productColor != undefined && inspectionObject.productColor != '')">
          <ion-card-content>
            <p>{{'Do you know the product name?' | translate}}</p>
            <ion-radio-group [(ngModel)]="inspectionObject.knowProductName" (ionChange)="resetProduct($event)">
              <ion-row class="ion-radio-row">
                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                    <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>

                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                    <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>
              </ion-row>
            </ion-radio-group>
          </ion-card-content>
        </ion-card>

        <!-------------------------------------------------------------------------------- Samson product name section ------------------------------------------------------------------------------------->

        <ion-card class="card-style-multiple" *ngIf="inspectionObject.knowProductName=='yes'">
          <ion-card-content>
            <p>{{'INSPECTION_SETUP_PRODUCT_NAME_LABEL' | translate}}</p>
           <ion-select labelPlacement="stacked"  placeholder="{{'Select Product Name' | translate}}" interface="popover"
              [(ngModel)]="selectedProduct" *ngIf="productNames && productNames.length <=20"
              >
              <ion-select-option *ngFor="let option of productNames" [value]="option"><span
                  *ngIf="option.PRODUCT_FAMILY ==''">{{option.VALUE}}</span><span
                  *ngIf="option.PRODUCT_FAMILY !=''">{{option.PRODUCT_FAMILY}}</span>
              </ion-select-option>
            </ion-select>
            <ion-item (click)="presentModal('PRODUCT_NAME')" no-lines text-wrap tappable *ngIf="!productNames"
              class="ion-item-generic-style" mode="ios">
              <div
                style="width:100%; text-align: left; background-color: white !important;font-size: 14px;color: darkgray;"
                *ngIf="(!selectedProduct.PRODUCT_FAMILY )|| selectedProduct.PRODUCT_FAMILY == ''"
                class="drop-down-arrow  value-field">{{
          'INSPECTION_SETUP_PRODUCT_NAME_PLACEHOLDER' | translate
          }}</div>
              <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                *ngIf="selectedProduct.PRODUCT_FAMILY != ''" class="value-field">{{selectedProduct.PRODUCT_FAMILY}}
              </div>
              <ion-note item-right style="display:inline-flex">
                <p class="drop-down-arrow">
                  <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                </p>
              </ion-note>
            </ion-item>

            <ion-item (click)="presentModal('PRODUCT_NAME')" no-lines text-wrap tappable
              *ngIf="productNames && productNames.length > 20" class="ion-item-generic-style" mode="ios">
              <div
                style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                *ngIf="selectedProduct.PRODUCT_FAMILY == ''" class="drop-down-arrow  value-field">{{ 'INSPECTION_SETUP_PRODUCT_NAME_PLACEHOLDER' | translate
          }}</div>
              <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                *ngIf="selectedProduct.PRODUCT_FAMILY != ''" class="value-field">{{selectedProduct.PRODUCT_FAMILY}}
              </div>
              <ion-note item-right style="display:inline-flex">
                <p class="drop-down-arrow">
                  <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                </p>
              </ion-note>
            </ion-item>
          </ion-card-content>
        </ion-card>
        <!-------------------------------------------------------------------------------- Samson product name section end ------------------------------------------------------------------------------------->
        <!----------------------------------------------------------------------------- Product Color Section ---------------------------------------------------------------------------------------->
        <ion-card class="card-style-multiple"
        *ngIf="inspectionObject.knowProductName=='no' || (inspectionObject.knowProductName=='yes' && selectedProduct != undefined && selectedProduct.PRODUCT_FAMILY != '')">
          <ion-card-content>
            <p>{{'Product Type' | translate}}</p>
           <ion-select labelPlacement="stacked"  placeholder="{{'Select Product Type' | translate}}" interface="popover"
              [(ngModel)]="inspectionObject.productType">
              <ion-select-option *ngFor="let option of productTypes" value="{{option.ID}}"><span
                  *ngIf="option.ID ==''">{{option.ID}}</span><span *ngIf="option.PRODUCT_TYPE !=''">{{option.PRODUCT_TYPE}}</span>
              </ion-select-option>
            </ion-select>
          </ion-card-content>
        </ion-card>
      <!-------------------------------------------------------------------------------- Product Color Section end ----------------------------------------------------------------------------------->

        <!-------------------------------------------------------------------------------- Construction class section ------------------------------------------------------------------------------------------>
        <ion-card class="card-style-multiple" *ngIf="inspectionObject.knowProductName=='no' && inspectionObject.productType != undefined && inspectionObject.productType != ''">
          <ion-card-content>
            <p>{{'Select the Construction Class' | translate}}</p>
            <mat-form-field style="width: 100% !important">
              <mat-select disableOptionCentering (selectionChange)="filterConstructionList($event)"
                placeholder="{{'INSPECTION_SETUP_CONSTRUCTION_CLASS_PLACEHOLDER' | translate}}" interface="popover"
                [(value)]="inspectionObject.constructionClass">
                <mat-select-trigger
                  *ngIf="inspectionObject.constructionClass != undefined && inspectionObject.constructionClass != ''">
                  <div style="display: inline-flex;"><img style="width: 70px; height: 70px"
                      [src]='inspectionObject.constructionClass.img'><span style="height: 100%; margin: auto;">
                      {{inspectionObject.constructionClass.item}}</span></div>
                </mat-select-trigger>
                <mat-option *ngFor="let option of constructionClassList let i = index" [value]="option"
                  style="padding: 10px 5px; height: 100px">
                  <img width="70" height="70" [src]='option.img'>
                  {{option.item}}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <!--<ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_CONSTRUCTION_CLASS_PLACEHOLDER' | translate}}" interface="popover"
              [(ngModel)]="inspectionObject.constructionClass" (ionChange)="resetData('ConstructionClass')">
              <ion-select-option *ngFor="let option of constructions" value="{{option.NAME}}"><span *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select> -->
          </ion-card-content>
        </ion-card>
        <!-- && ------------------------------------------------------------------------------- Construction Class Section End ------------------------------------------------------------------------------>
        <!-- && ------------------------------------------------------------------------------- Construction section ------------------------------------------------------------------------------------>
        <ion-card class="card-style-multiple"
          *ngIf="inspectionObject.knowProductName=='no' && (inspectionObject.constructionClass != undefined && inspectionObject.constructionClass != '')">
          <ion-card-content>
            <p>{{'Select the Construction Type' | translate}}</p>
            <!--<ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_CONSTRUCTION_TYPE_PLACEHOLDER' | translate}}" interface="popover"
              [(ngModel)]="inspectionObject.construction" *ngIf="constructionsList && constructionsList.length <=10" (ionChange)="resetData('ConstructionType'); setJacketStatus(inspectionObject.construction)">
              <ion-select-option *ngFor="let option of constructionsList, let i = index" value="{{option.NAME}}" (ionSelect)="constructionIndes = i"><span *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select> -->

            <mat-form-field style="width: 100% !important" *ngIf="constructionsList && constructionsList.length <=10">
              <mat-select disableOptionCentering
                (selectionChange)="setJacketStatus(inspectionObject.construction)"
                placeholder="{{'INSPECTION_SETUP_CONSTRUCTION_TYPE_PLACEHOLDER' | translate}}" interface="popover"
                [(value)]="inspectionObject.construction">
                <mat-select-trigger
                  *ngIf="inspectionObject.construction != undefined && inspectionObject.construction != ''">
                  <div style="display: inline-flex;"><img style="width: 70px; height: 70px"
                      [src]='inspectionObject.construction.IMAGE'><span style="height: 100%; margin: auto;">
                      {{inspectionObject.construction.NAME}}</span></div>
                </mat-select-trigger>
                <mat-option *ngFor="let option of constructionsList, let i = index" [value]="option"
                  style="padding: 10px 5px; height: 100px">
                  <img width="70" height="70" [src]='option.IMAGE'>
                  {{option.NAME}}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <ion-item (click)="presentModal('CONSTRUCTION')" no-lines text-wrap tappable
              *ngIf="constructionsList && constructionsList.length > 10" class="ion-item-generic-style" mode="ios">
              <div
                style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                *ngIf="!inspectionObject.construction || inspectionObject.construction == ''"
                class="drop-down-arrow  value-field">{{ 'INSPECTION_SETUP_CONSTRUCTION_TYPE_PLACEHOLDER' | translate
              }}</div>
              <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                *ngIf="inspectionObject.construction && inspectionObject.construction != ''" class="value-field">
                <div style="display: inline-flex;"><img *ngIf="inspectionObject.construction.IMAGE != ''"
                    style="width: 70px; height: 70px" [src]='inspectionObject.construction.IMAGE'><span
                    style="height: 100%; margin: auto;"> {{inspectionObject.construction.NAME}}</span></div>
              </div>
              <ion-note item-right style="display:inline-flex">
                <p class="drop-down-arrow">
                  <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                </p>
              </ion-note>
            </ion-item>
          </ion-card-content>
        </ion-card>
        <!--------------------------------------------------------------------------------- Construction Section end ------------------------------------------------------------------------------------------->
        <!-- Diameter Section -->
        <ion-card class="card-style-multiple"
          *ngIf="(inspectionObject.knowProductName == 'yes' ||  inspectionObject.knowProductName == 'no') && inspectionObject.productType != undefined && inspectionObject.productType != ''">
          <ion-card-content>
            <div style="display:inline-flex; width: 100%;">
              <div style="width: 40%">
                <p> {{'INSPECTION_SETUP_DIAM_UNIT_LABEL' | translate}} </p>
               <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_DIAM_UNITS_PLACEHOLDER' | translate}}" interface="popover"
                  [(ngModel)]="inspectionObject.diaUom" (ionChange)=filterDiameterList(inspectionObject.diaUom) class="unitMeasure">
                  <ion-select-option *ngFor="let option of diaUomList" value="{{option.UOM}}"><span
                      *ngIf="option.UOM ==''">{{option.VALUE}}</span><span *ngIf="option.UOM !=''">{{option.UOM}}</span>
                  </ion-select-option>
                </ion-select>
              </div>
              <div style="width: 60%">
                <p> {{'INSPECTION_SETUP_DIAM_LABEL' | translate}} </p>
               <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_DIAM_PLACEHOLDER' | translate}}" interface="popover"
                  [(ngModel)]="inspectionObject.productDiam" [disabled]="inspectionObject.diaUom == ''"
                  *ngIf="diamList && diamList.length <=20" class="unitMeasure">
                  <ion-select-option *ngFor="let option of diamList" value="{{option.UOM_VALUE}}">{{option.UOM_VALUE}}
                  </ion-select-option>
                </ion-select>

                <ion-item (click)="presentModal('DIAMETER')" no-lines text-wrap tappable
                  *ngIf="diamList && diamList.length > 20" class="ion-item-generic-style"
                  [disabled]="inspectionObject.diaUom == ''" mode="ios">
                  <div
                    style="width:100%; text-align: left; background-color: white !important; font-size: 16px;color: gray;"
                    *ngIf="!inspectionObject.productDiam || inspectionObject.productDiam == ''"
                    class="drop-down-arrow  value-field">{{ 'INSPECTION_SETUP_DIAM_PLACEHOLDER' | translate }}</div>
                  <div
                    style="width:100%; text-align: left; background-color: white !important; font-size: 16px;color: gray;"
                    *ngIf="inspectionObject.productDiam && inspectionObject.productDiam != ''" class="value-field">
                    {{inspectionObject.productDiam}}</div>
                  <ion-note item-right style="display:inline-flex">
                    <p class="drop-down-arrow">
                      <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                    </p>
                  </ion-note>
                </ion-item>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
        <!-- -->

        <!-- Length -->
        <ion-card class="card-style-multiple"
          *ngIf="(inspectionObject.knowProductName == 'yes' ||  inspectionObject.knowProductName == 'no') && inspectionObject.diaUom != undefined && inspectionObject.diaUom != '' && inspectionObject.productDiam != undefined && inspectionObject.productDiam != ''">
          <!-- <ion-card-header>
      Total Length and UOM
    </ion-card-header> -->
          <ion-card-content>
            <div style="display:inline-flex; width: 100%;">
              <div style="width: 40%">
                <p> {{'INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_LABEL' | translate}} </p>
               <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_PLACEHOLDER' | translate}}"
                  interface="popover" [(ngModel)]="inspectionObject.totalLengthUom" class="unitMeasure"
                  >
                  <ion-select-option *ngFor="let option of lengthUomList" value="{{option.UOM}}"><span
                      *ngIf="option.UOM ==''">{{option.VALUE}}</span><span *ngIf="option.UOM !=''">{{option.UOM}}</span>
                  </ion-select-option>
                </ion-select>
              </div>
              <div style="width: 60%">
                <p> {{'INSPECTION_SETUP_TOTAL_LENGTH_LABEL' | translate}} </p>
                <ion-input maxlength="18" placeholder="{{'INSPECTION_SETUP_TOTAL_LENGTH_PLACEHOLDER' | translate}}"
                  type="number" [(ngModel)]="inspectionObject.totalLength"
                  (keydown)="keyPressed($event, inspectionObject.totalLength)">
                </ion-input>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
        <ion-card class="card-style-multiple"
          *ngIf="(inspectionObject.knowProductName == 'yes' ||  inspectionObject.knowProductName == 'no') && inspectionObject.totalLength != undefined && inspectionObject.totalLength != '' && inspectionObject.totalLengthUom != undefined && inspectionObject.totalLengthUom != ''">
          <!-- <ion-card-header>
      Inspection Information
    </ion-card-header> -->
          <ion-card-content>
            <p>{{'Will the entire product be inspected now?'| translate}}</p>
            <ion-radio-group [(ngModel)]="inspectionObject.entireInspection" (ionChange)="resetLength($event)">
              <ion-row class="ion-radio-row">
                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                    <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>

                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                    <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>
              </ion-row>
            </ion-radio-group>
          </ion-card-content>
        </ion-card>

        <ion-card class="card-style-multiple"
          *ngIf="(inspectionObject.knowProductName == 'yes' ||  inspectionObject.knowProductName == 'no') && inspectionObject.entireInspection == 'no'">
          <ion-card-content>
            <p>{{'INSPECTION_SETUP_INSPECTED_LENGTH_LABEL' | translate}}</p>
            <div style="display: inline-flex !important; width: 100% !important;">
              <ion-input maxlength="18" placeholder="{{'INSPECTION_SETUP_INSPECTED_LENGTH_PLACEHOLDER' | translate}}"
                type="text" [(ngModel)]="inspectionObject.inspectionLength" (ionInput)="getItems($event)"
                [disabled]="inspectionObject.totalLength == ''" step="any" inputmode="decimal"
                (keydown)="keyPressed($event, inspectionObject.inspectionLength)"></ion-input>
              <div class="form-group" style="padding:10px 10px 0px 17px">
                <label
                  style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{inspectionObject.totalLengthUom}}
                </label>
              </div>
            </div>
            <div style="color: red; padding-top: 0.2rem" *ngIf="showValidationError == true">
              {{'Inspected length cannot be more than total length which is' | translate }}
              {{getFormattedLengthWithUOM(inspectionObject.totalLength, inspectionObject.totalLengthUom)}}
            </div>
            <div style="color: red; padding-top: 0.2rem" *ngIf="showNegetiveError == true">
              {{'Inspected length cannot less than 1' | translate }}
            </div>

            <div style="color: red; padding-top: 0.2rem" *ngIf="showlengthError == true">
              {{'Inspected should be greater than 0' | translate }}
            </div>
          </ion-card-content>
        </ion-card>
        <!-- -->

        <!-- inststallation section -->
        <ion-card class="card-style-multiple"
          *ngIf="(inspectionObject.knowProductName == 'yes' ||  inspectionObject.knowProductName == 'no') && (inspectionObject.entireInspection == 'yes' ||  inspectionObject.entireInspection == 'no')">

          <ion-card-content>
            <p>{{'Is this product installed?' | translate}}</p>
            <ion-radio-group [(ngModel)]="inspectionObject.isInstalled" (ionChange)="resetInstalled($event)">
              <ion-row class="ion-radio-row">
                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                    <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>

                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                    <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>
              </ion-row>
            </ion-radio-group>
          </ion-card-content>
        </ion-card>

        <ion-card class="card-style-multiple"
          *ngIf="(inspectionObject.knowProductName == 'yes' ||  inspectionObject.knowProductName == 'no') && inspectionObject.isInstalled == 'yes'">

          <ion-card-content>
              <div [formGroup]="installedLoctnForm">
                <p>{{'INSPECTION_SETUP_INSTALLED_LOCATION_LABEL' | translate}}</p>
              <ion-input maxlength="25" placeholder="{{'INSPECTION_SETUP_INSTALLED_LOCATION_PLACEHOLDER' | translate}}" (keyup)="locationChanged($event)"
              formControlName="ropeLoctnCtrl" (focusout)="reset('ropeInstalledLocn')"></ion-input>
                <mat-error *ngIf="installedLoctnForm.get('ropeLoctnCtrl').hasError('maxlength')">{{ "Character Limit Exceeded" | translate: { limit: '25' } }}</mat-error>
                <!-- <mat-error *ngIf="installedLoctnForm.get('ropeLoctnCtrl').errors?.pattern">{{ "letters numbers and symbols allowed" | translate }}</mat-error> -->
              </div>
          </ion-card-content>
        </ion-card>
        <!-- -->
      </div>


      <!-------------------------------------------------------------------------------- Samson product yes section end ------------------------------------------------------------------------------------->

      <div *ngIf="inspectionObject.product == 'no'">
        <!-------------------------------------------------------------------------------- Samson product no section ------------------------------------------------------------------------------------>

        <ion-card class="card-style-multiple" mode="ios" expand="block" *ngIf="inspectionObject.product == 'no'">
          <ion-card-content>
            <p> {{'Manufacturer' | translate}} </p>
           <ion-select labelPlacement="stacked"  placeholder="{{'Select Manufacturer' | translate}}" interface="popover"
              [(ngModel)]="selectedManufacturer" *ngIf="manufacturers && manufacturers.length <=10"
              >
              <ion-select-option *ngFor="let option of manufacturers" [value]="option"><span
                  *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
              </ion-select-option>
            </ion-select>

            <ion-item (click)="presentModal('MANUFACTURER')" no-lines text-wrap tappable
              *ngIf="manufacturers && manufacturers.length > 10" class="ion-item-generic-style" mode="ios">
              <div
                style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                *ngIf="!selectedManufacturer || selectedManufacturer.NAME == ''" class="drop-down-arrow  value-field">{{ 'Select
                Manufacturer' | translate
              }}</div>
              <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                *ngIf="selectedManufacturer && selectedManufacturer.NAME != ''" class="value-field">
                {{selectedManufacturer.NAME}}</div>
              <ion-note item-right style="display:inline-flex">
                <p class="drop-down-arrow">
                  <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                </p>
              </ion-note>
            </ion-item>
          </ion-card-content>
        </ion-card>

        <div style="width:100% !important"
          *ngIf="inspectionObject.product == 'no' && (selectedManufacturer != undefined && selectedManufacturer != '')">
          <ion-list>

            <ion-card class="card-style-multiple"
              *ngIf="inspectionObject.product == 'no' && (selectedManufacturer != undefined && selectedManufacturer != '')">
              <ion-card-content>
                <p> {{'INSPECTION_SETUP_NONSAMSON_PRODUCT_NAME_LABEL' | translate}} </p>
                <ion-input maxlength="225"
                  placeholder="{{'INSPECTION_SETUP_NONSAMSON_PRODUCT_NAME_PLACEHOLDER' | translate}}"
                  [(ngModel)]="selectedProduct.PRODUCT_FAMILY"></ion-input>
                <p> {{'INSPECTION_SETUP_NONSAMSON_PRODUCT_CODE_LABEL' | translate}} </p>
                <ion-input maxlength="225"
                  placeholder="{{'INSPECTION_SETUP_NONSAMSON_PRODUCT_CODE_PLACEHOLDER' | translate}}"
                  [(ngModel)]="selectedProduct.PRODUCT_FAMILY_CODE"></ion-input>
                <p> {{'INSPECTION_SETUP_NONSAMSON_PRODUCT_DESCRIPTION_LABEL' | translate}} </p>
                <ion-input maxlength="225"
                  placeholder="{{'INSPECTION_SETUP_NONSAMSON_PRODUCT_DESCRIPTION_PLACEHOLDER' | translate}}"
                  [(ngModel)]="selectedProduct.PRODUCT_FAMILY_DESC"></ion-input>
                <!-- <p> {{'Product Color' | translate}} </p>
               <ion-select labelPlacement="stacked"  placeholder="{{'Select Product Color' | translate}}"
                  [(ngModel)]="inspectionObject.productColor" interface="popover"
                  (ionChange)="resetData('ProductColor')">
                  <ion-select-option *ngFor="let option of productColorList" value="{{option.NAME}}"><span *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
                  </ion-select-option>
                </ion-select> -->
                <div style="display:inline-flex; width: 100%;">
                  <div style="width: 40%">
                    <p> {{'INSPECTION_SETUP_DIAM_UNIT_LABEL' | translate}} </p>
                   <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_DIAM_UNITS_PLACEHOLDER' | translate}}"
                      interface="popover" [(ngModel)]="inspectionObject.diaUom"
                      (ionChange)='filterDiameterList(inspectionObject.diaUom)' class="unitMeasure">
                      <ion-select-option *ngFor="let option of diaUomList" value="{{option.UOM}}"><span
                          *ngIf="option.UOM ==''">{{option.VALUE}}</span><span
                          *ngIf="option.UOM !=''">{{option.UOM}}</span>
                      </ion-select-option>
                    </ion-select>
                  </div>
                  <div style="width: 60%">
                    <p> {{'INSPECTION_SETUP_DIAM_LABEL' | translate}} </p>
                   <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_DIAM_PLACEHOLDER' | translate}}" interface="popover"
                      [(ngModel)]="inspectionObject.productDiam" [disabled]="inspectionObject.diaUom == ''"
                      *ngIf="diamList && diamList.length <=20" class="unitMeasure">
                      <ion-select-option *ngFor="let option of diamList" value="{{option.UOM_VALUE}}">
                        {{option.UOM_VALUE}}
                      </ion-select-option>
                    </ion-select>

                    <ion-item (click)="presentModal('DIAMETER')" no-lines text-wrap tappable
                      *ngIf="diamList && diamList.length > 20" class="ion-item-generic-style"
                      [disabled]="inspectionObject.diaUom == ''" mode="ios">
                      <div
                        style="width:100%; text-align: left; background-color: white !important; font-size: 16px;color: gray;"
                        *ngIf="!inspectionObject.productDiam || inspectionObject.productDiam == ''"
                        class="drop-down-arrow  value-field">
                        {{ 'INSPECTION_SETUP_DIAM_PLACEHOLDER' | translate }}</div>
                      <div
                        style="width:100%; text-align: left; background-color: white !important; font-size: 16px; color: gray;"
                        *ngIf="inspectionObject.productDiam && inspectionObject.productDiam != ''" class="value-field">
                        {{inspectionObject.productDiam}}</div>
                      <ion-note item-right style="display:inline-flex">
                        <p class="drop-down-arrow">
                          <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                        </p>
                      </ion-note>
                    </ion-item>
                  </div>
                </div>
                <div style="display:inline-flex; width: 100%;">

                  <div style="width: 40%">
                    <p> {{'INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_LABEL' | translate}} </p>
                   <ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_PLACEHOLDER' | translate}}"
                      interface="popover" [(ngModel)]="inspectionObject.totalLengthUom" class="unitMeasure"
                      >
                      <ion-select-option *ngFor="let option of lengthUomList" value="{{option.UOM}}"><span
                          *ngIf="option.UOM ==''">{{option.VALUE}}</span><span
                          *ngIf="option.UOM !=''">{{option.UOM}}</span>
                      </ion-select-option>
                    </ion-select>
                  </div>
                  <div style="width: 60%">
                    <p> {{'INSPECTION_SETUP_TOTAL_LENGTH_LABEL' | translate}} </p>
                    <ion-input maxlength="18" placeholder="{{'INSPECTION_SETUP_TOTAL_LENGTH_PLACEHOLDER' | translate}}"
                      type="number" [(ngModel)]="inspectionObject.totalLength"
                      (keydown)="keyPressed($event, inspectionObject.totalLength)">
                    </ion-input>
                  </div>
                </div>
                <!-- <p>{{'Is Jacketed' | translate}}</p> -->
                <!-- <ion-radio-group [(ngModel)]="inspectionObject.isJacketed"
                  (ionSelect)="getConstructionsBasedOnJAcketStatus($event)">
                  <ion-row class="ion-radio-row">
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                        <ion-label>{{'Yes' | translate}}</ion-label>
                      </ion-item>
                    </ion-col>

                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                        <ion-label>{{'No' | translate}}</ion-label>
                      </ion-item>
                    </ion-col>
                  </ion-row>
                </ion-radio-group> -->
                <p>{{'Select the Construction Class' | translate}}</p>
                <mat-form-field style="width: 100% !important">
                  <mat-select disableOptionCentering (selectionChange)="filterConstructionList($event)"
                    placeholder="{{'INSPECTION_SETUP_CONSTRUCTION_CLASS_PLACEHOLDER' | translate}}" interface="popover"
                    [(value)]="inspectionObject.constructionClass">
                    <mat-select-trigger
                      *ngIf="inspectionObject.constructionClass != undefined && inspectionObject.constructionClass != ''">
                      <div style="display: inline-flex;"><img style="width: 70px; height: 70px"
                          [src]='inspectionObject.constructionClass.img'><span style="height: 100%; margin: auto;">
                          {{inspectionObject.constructionClass.item}}</span></div>
                    </mat-select-trigger>
                    <mat-option *ngFor="let option of constructionClassList let i = index" [value]="option"
                      style="padding: 10px 5px; height: 100px">
                      <img width="70" height="70" [src]='option.img'>
                      {{option.item}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <p>{{'Select the Construction Type' | translate}}</p>
                <!--<ion-select labelPlacement="stacked"  placeholder="{{'INSPECTION_SETUP_CONSTRUCTION_TYPE_PLACEHOLDER' | translate}}" interface="popover"
                  [(ngModel)]="inspectionObject.construction" *ngIf="constructionsList && constructionsList.length <=10" [disabled]="inspectionObject.constructionClass == ''">
                  <ion-select-option *ngFor="let option of constructionsList" value="{{option.NAME}}"><span *ngIf="option.NAME ==''">{{option.VALUE}}</span><span *ngIf="option.NAME !=''">{{option.NAME}}</span>
                  </ion-select-option>
                </ion-select> -->

                <mat-form-field style="width: 100% !important"
                  *ngIf="constructionsList && constructionsList.length <=10">
                  <mat-select disableOptionCentering
                    (selectionChange)="setJacketStatus(inspectionObject.construction)"
                    placeholder="{{'INSPECTION_SETUP_CONSTRUCTION_TYPE_PLACEHOLDER' | translate}}" interface="popover"
                    [disabled]="inspectionObject.constructionClass == ''" [(value)]="inspectionObject.construction">
                    <mat-select-trigger
                      *ngIf="inspectionObject.construction != undefined && inspectionObject.construction != ''">
                      <div style="display: inline-flex;"><img style="width: 70px; height: 70px"
                          [src]='inspectionObject.construction.IMAGE'><span style="height: 100%; margin: auto;">
                          {{inspectionObject.construction.NAME}}</span></div>
                    </mat-select-trigger>
                    <mat-option *ngFor="let option of constructionsList, let i = index" [value]="option"
                      style="padding: 10px 5px; height: 100px">
                      <img width="70" height="70" [src]='option.IMAGE'>
                      {{option.NAME}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <ion-item (click)="presentModal('CONSTRUCTION')" no-lines text-wrap tappable
                  *ngIf="constructionsList && constructionsList.length > 10" class="ion-item-generic-style" mode="ios">
                  <div
                    style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
                    *ngIf="!inspectionObject.construction || inspectionObject.construction == ''"
                    class="drop-down-arrow  value-field">{{ 'INSPECTION_SETUP_CONSTRUCTION_TYPE_PLACEHOLDER' | translate
                  }}</div>
                  <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
                    *ngIf="inspectionObject.construction && inspectionObject.construction != ''" class="value-field">
                    <div style="display: inline-flex;"><img *ngIf="inspectionObject.construction.IMAGE != ''"
                        style="width: 70px; height: 70px" [src]='inspectionObject.construction.IMAGE'><span
                        style="height: 100%; margin: auto;"> {{inspectionObject.construction.NAME}}</span></div>
                  </div>
                  <ion-note item-right style="display:inline-flex">
                    <p class="drop-down-arrow">
                      <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                    </p>
                  </ion-note>
                </ion-item>
              </ion-card-content>
            </ion-card>

          </ion-list>

           <!----------------------------------------------------------------------------- Product Color Section ---------------------------------------------------------------------------------------->
           <ion-card class="card-style-multiple"
           *ngIf="inspectionObject.totalLength != undefined && inspectionObject.totalLength != '' ">
             <ion-card-content>
               <p>{{'Product Type' | translate}}</p>
              <ion-select labelPlacement="stacked"  placeholder="{{'Select Product Type' | translate}}" interface="popover"
                 [(ngModel)]="inspectionObject.productType" style="padding: 0px 10px;">
                 <ion-select-option *ngFor="let option of productTypes" value="{{option.ID}}"><span
                     *ngIf="option.ID ==''">{{option.ID}}</span><span *ngIf="option.PRODUCT_TYPE !=''">{{option.PRODUCT_TYPE}}</span>
                 </ion-select-option>
               </ion-select>
             </ion-card-content>
           </ion-card>
         <!-------------------------------------------------------------------------------- Product Color Section end ----------------------------------------------------------------------------------->

          <ion-card class="card-style-multiple"
            *ngIf="inspectionObject.productType != undefined && inspectionObject.productType != '' ">
            <ion-card-content>
              <p>{{'Will the entire product be inspected now?'| translate}}</p>
              <ion-radio-group [(ngModel)]="inspectionObject.entireInspection">
                <ion-row class="ion-radio-row">
                  <ion-col>
                    <ion-item class='ion-radio-item-style'>
                      <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                      <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                    </ion-item>
                  </ion-col>

                  <ion-col>
                    <ion-item class='ion-radio-item-style'>
                      <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                      <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                    </ion-item>
                  </ion-col>
                </ion-row>
              </ion-radio-group>
            </ion-card-content>
          </ion-card>

          <ion-card class="card-style-multiple" *ngIf="inspectionObject.entireInspection == 'no'">
            <ion-card-content>
              <p>{{'INSPECTION_SETUP_INSPECTED_LENGTH_LABEL' | translate}}</p>
              <div style="display: inline-flex !important; width: 100% !important;">
                <ion-input maxlength="18" placeholder="{{'INSPECTION_SETUP_INSPECTED_LENGTH_PLACEHOLDER' | translate}}"
                  type="text" [(ngModel)]="inspectionObject.inspectionLength" (ionInput)="getItems($event)"
                  [disabled]="inspectionObject.totalLength == ''" step="any" inputmode="decimal"
                  (keydown)="keyPressed($event, inspectionObject.inspectionLength)"></ion-input>
                <div class="form-group" style="padding:10px 10px 0px 17px">
                  <label
                    style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{inspectionObject.totalLengthUom}}
                  </label>
                </div>
              </div>
              <div style="color: red; padding-top: 0.2rem" *ngIf="showValidationError == true">
                {{'Inspected length cannot be more than total length which is' | translate }}
                {{getFormattedLengthWithUOM(inspectionObject.totalLength, inspectionObject.totalLengthUom)}}
              </div>
              <div style="color: red; padding-top: 0.2rem" *ngIf="showNegetiveError == true">
                {{'Inspected length cannot less than 1' | translate }}
              </div>

              <div style="color: red; padding-top: 0.2rem" *ngIf="showlengthError == true">
                {{'Inspected should be greater than 0' | translate }}
              </div>
            </ion-card-content>
          </ion-card>
        </div>

        <ion-card class="card-style-multiple"
          *ngIf="(inspectionObject.selectedManufacturer == undefined &&  inspectionObject.selectedManufacturer != '') && inspectionObject.entireInspection != undefined && inspectionObject.entireInspection != ''">
          <!-- <ion-card-header>
          Inspection Information
        </ion-card-header> -->
          <ion-card-content>
            <p>{{'Is this product installed?' | translate}}</p>
            <ion-radio-group [(ngModel)]="inspectionObject.isInstalled">
              <ion-row class="ion-radio-row">
                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes">{{'Yes' | translate}}</ion-radio>
                    <!-- <ion-label>{{'Yes' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>

                <ion-col>
                  <ion-item class='ion-radio-item-style'>
                    <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="no">{{'No' | translate}}</ion-radio>
                    <!-- <ion-label>{{'No' | translate}}</ion-label> -->
                  </ion-item>
                </ion-col>
              </ion-row>
            </ion-radio-group>
          </ion-card-content>
        </ion-card>

        <ion-card class="card-style-multiple" *ngIf="inspectionObject.isInstalled == 'yes'">
          <ion-card-content>
              <div [formGroup]="installedLoctnForm">
                <p>{{'INSPECTION_SETUP_INSTALLED_LOCATION_LABEL' | translate}}</p>
              <ion-input maxlength="25" placeholder="{{'INSPECTION_SETUP_INSTALLED_LOCATION_PLACEHOLDER' | translate}}" (keyup)="locationChanged($event)"
              formControlName="ropeLoctnCtrl" (focusout)="reset('ropeInstalledLocn')"></ion-input>
                <mat-error *ngIf="installedLoctnForm.get('ropeLoctnCtrl').hasError('maxlength')">{{ "Character Limit Exceeded" | translate: { limit: '25' } }}</mat-error>
              </div>
          </ion-card-content>
        </ion-card>
        <!-------------------------------------------------------------------------------- Samson product no section end ------------------------------------------------------------------------------------->
      </div>

      <!-------------------------------------------------------------------------------- Product Details Section End ----------------------------------------------------------------------------------------->
    </div>
    <ion-card class="card-style-multiple"
      *ngIf="searchCertNo == 'no' && this.inspectionObject.isInstalled != undefined && inspectionObject.isInstalled != ''">
      <ion-card-content>
        <p>{{'Which end will you be starting from?'| translate}}</p>
        <p style="font-style: italic; padding-top: 15px;">{{'INSPECTION_SETUP_START_END_DESCRIPTION'| translate}}</p>
        <ion-radio-group [(ngModel)]="inspectionObject.startPoint">
          <ion-row class="ion-radio-row">
            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="A">{{'A' | translate}}</ion-radio>
                <!-- <ion-label>{{'A' | translate}}</ion-label> -->
              </ion-item>
            </ion-col>

            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="B">{{'B' | translate}}</ion-radio>
                <!-- <ion-label>{{'B' | translate}}</ion-label> -->
              </ion-item>
            </ion-col>

            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="other">{{'Other' | translate}}</ion-radio>
                <!-- <ion-label>{{'Other' | translate}}</ion-label> -->
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-radio-group>
      </ion-card-content>
    </ion-card>

    <ion-card class="card-style-multiple" *ngIf="searchCertNo == 'no' && inspectionObject.startPoint == 'other'">
      <ion-card-content>
        <p>{{'INSPECTION_SETUP_START_DESCRIPTION_LABEL' | translate}}</p>
        <ion-input maxlength="225" placeholder="{{'INSPECTION_SETUP_START_DESCRIPTION_PLACEHOLDER' | translate}}"
          [(ngModel)]="inspectionObject.startPointDesc"></ion-input>
      </ion-card-content>
    </ion-card>
    <ion-grid
      *ngIf="showFinishCard == false && (inspectionObject.startPoint != undefined && inspectionObject.startPoint != '')">
      <ion-row>
        <ion-col>
          <ion-button mode="ios" size="small" fill="outline" style="float:right" (click)="slideNext()">
            {{'Next'| translate}}
            <ion-icon mode="ios" slot="end" name="arrow-forward"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>


  <!-- ! INSPECTION SUMMARY  - START -->
  <div style="width:100% !important" *ngIf="showFinishCard == true">
    <ion-card class="card-style-multiple">
      <ion-card-content>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.ACCOUNT_NAME && createdInspectionHeader.ACCOUNT_NAME != "")'>
          <div style="width: 45%">{{'Account Id' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.ACCOUNT_NAME}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.INDUSTRY && createdInspectionHeader.INDUSTRY != "")'>
          <div style="width: 45%">{{'Industry' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.INDUSTRY}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.INDUSTRY && createdInspectionHeader.INDUSTRY != "")'>
          <div style="width: 45%">{{'Event Date' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.INSPECTION_DATE}}</div>
      </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.CERT_NAME && createdInspectionHeader.CERT_NAME != "")'>
          <div style="width: 45%">{{'Cert #' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.CERT_NAME}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.APPLICATION &&createdInspectionHeader.APPLICATION != "")'>
          <div style="width: 45%">{{'Application' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.APPLICATION}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.PRODUCT_TYPE && createdInspectionHeader.PRODUCT_TYPE != "")'>
          <div style="width: 45%">{{'Product Type' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.PRODUCT_TYPE}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.MANUFACTURER && createdInspectionHeader.MANUFACTURER != "")'>
          <div style="width: 45%">{{'Manufacturer' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.MANUFACTURER}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.PRODUCT && createdInspectionHeader.PRODUCT != "")'>
          <div style="width: 45%">{{'Product' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.PRODUCT}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.PRODUCT_CODE && createdInspectionHeader.PRODUCT_CODE != "")'>
          <div style="width: 45%">{{'Product Code' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.PRODUCT_CODE}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.PRODUCT_DESC && createdInspectionHeader.PRODUCT_DESC != "")'>
          <div style="width: 45%">{{'Product Desc' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.PRODUCT_DESC}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.COLOR && createdInspectionHeader.COLOR != "")'>
          <div style="width: 45%">{{'Color' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.COLOR}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.CONSTRUCTION && createdInspectionHeader.CONSTRUCTION != "")'>
          <div style="width: 45%">{{'Construction' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.CONSTRUCTION}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.PRODUCT_CONFIG && createdInspectionHeader.PRODUCT_CONFIG != "")'>
          <div style="width: 45%">{{'Configuration' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.PRODUCT_CONFIG}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.DIAM && createdInspectionHeader.DIAM != "")'>
          <div style="width: 45%">{{'Diameter' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.DIAM}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.DIAM_UOM && createdInspectionHeader.DIAM_UOM != "")'>
          <div style="width: 45%">{{'Diameter UOM' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.DIAM_UOM}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.ORIGINAL_LENGTH &&createdInspectionHeader.ORIGINAL_LENGTH != "")'>
          <div style="width: 45%">{{'Current Length' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.ORIGINAL_LENGTH}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.INSPECTED_LENGTH && createdInspectionHeader.INSPECTED_LENGTH != "")'>
          <div style="width: 45%">{{'Inspected Length' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.INSPECTED_LENGTH}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.LENGTH_UOM && createdInspectionHeader.LENGTH_UOM != "")'>
          <div style="width: 45%">{{'Length UOM' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.LENGTH_UOM}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
        *ngIf='(searchedWorkOrder.length > 0 && searchedWorkOrder[0].WO_NUMBER && searchedWorkOrder[0].WO_NUMBER != "")'>
          <div style="width: 45%">{{'Work Order' | translate }} : </div>
          <div style="width: 45%">{{ searchedWorkOrder[0].WO_NUMBER }}</div>
        </div>
        <!-- <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.HAS_CHAFE && createdInspectionHeader.HAS_CHAFE != "")'>
          <div style="width: 45%">{{'Has Chafe' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.HAS_CHAFE}}</div>
          <div style="width: 45%">
            <span *ngIf="createdInspectionHeader.HAS_CHAFE == 1">{{'Yes' | translate}}</span>
            <span *ngIf="createdInspectionHeader.HAS_CHAFE == 0">{{'No' | translate}}</span>
          </div>
        </div> -->
        <!-- <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.CHAFE_TYPE && createdInspectionHeader.CHAFE_TYPE != "")'>
          <div style="width: 45%">{{'Chafe Type' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.CHAFE_TYPE}}</div>
        </div> -->
        <!-- <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.IS_JACKETED && createdInspectionHeader.IS_JACKETED != "")'>
          <div style="width: 45%">{{'Is Jacketed' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.IS_JACKETED}}</div>
          <div style="width: 45%">
            <span *ngIf="createdInspectionHeader.IS_JACKETED == 1">{{'Yes' | translate}}</span>
            <span *ngIf="createdInspectionHeader.IS_JACKETED == 0">{{'No' | translate}}</span>
          </div>
        </div> -->
        <!-- <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.INSTALLED_DATE && createdInspectionHeader.INSTALLED_DATE != "")'>
          <div style="width: 45%">{{'Installed Date' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.INSTALLED_DATE}}</div>
        </div> -->

        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(createdInspectionHeader.LOCATION_INSTALLED && createdInspectionHeader.LOCATION_INSTALLED != "")'>
          <div style="width: 45%">{{'Location Installed' | translate }} : </div>
          <div style="width: 45%">{{createdInspectionHeader.LOCATION_INSTALLED}}</div>
        </div>
      </ion-card-content>
    </ion-card>

    <ion-grid>
      <ion-row>
        <ion-col style="text-align:start">
          <ion-button mode="ios" size="small" fill="outline" (click)="slideBack()">{{'Previous'
            | translate}}
            <ion-icon mode="ios" slot="start" name="arrow-back"></ion-icon>
          </ion-button>
        </ion-col>
        <ion-col>
          <ion-button mode="ios" size="small" fill="outline" style="float:right"
            (click)="finishInspection(createdInspectionHeader)">{{'Finish'| translate}}
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
    <!-- ! INSPECTION SUMMARY  - END -->
  <div style="padding: 60px;"> </div>

  <ion-grid *ngIf="showFinishCard == true">
    <ion-row>
      <ion-col>
        <ion-button mode="ios" size="small" fill="outline" style="float:right" *ngIf="finishButton" (click)="finish()">{{'Finish'
          |translate}}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>

</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer *ngIf='footerClose' (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>

