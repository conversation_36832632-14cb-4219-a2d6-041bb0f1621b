import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, NavigationExtras } from '@angular/router';
import { MenuController, ToastController, Platform, ActionSheetController, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { CameraService } from 'src/app/services/camera.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { Camera , CameraOptions } from '@awesome-cordova-plugins/camera/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx'
import { PlatformService } from 'src/app/services/platform.service';

@Component({
  selector: 'app-guest-abrasion-comparator-tenex',
  templateUrl: './guest-abrasion-comparator-tenex.page.html',
  styleUrls: ['./guest-abrasion-comparator-tenex.page.scss'],
})
export class GuestAbrasionComparatorTenexPage implements OnInit {
  platformId: string = this.platformService.getPlatformId();
  imageData: any;
  component: any;
  constructor(private router:Router,
    public platformService: PlatformService,
    private menu: MenuController,
    public helpService: HelpService,
    public dataService: DataService,
    private camera: Camera,
    public device: Device,
    public domSanitizer: DomSanitizer,
    public toastController: ToastController,
    public cameraService: CameraService,
    public platform: Platform,
    public actionSheetController: ActionSheetController,
    public screenOrientation: ScreenOrientation,
    public translate: TranslateService,
    public alertController: AlertController) { }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  gotoHome() {
    this.router.navigate(['home'])
  }

  gotoInspections() {
    this.router.navigate(['inspection-home']);
  }

  gotoResources() {
    this.router.navigate(['resource'])
  }

  gotoContact() {
    this.router.navigate(['contact'])
  }

  back() {
    this.router.navigate(['guest-abrasion-comparator']);
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  async takePicture() {
    this.imageData = await this.cameraService.takePictureComparator();
    console.log("Image data:", this.imageData);

    if (this.imageData) {
      const blob = this.cameraService.b64toBlob(this.imageData.split(',')[1], 'image/jpeg');
      const imgURL = URL.createObjectURL(blob);
      this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
    }
   }

  // async presentActionSheet() {
  //   const actionSheet = await this.actionSheetController.create({
  //     cssClass: 'my-custom-class',
  //     buttons: [{
  //       text: 'Camera',
  //       icon: 'camera',
  //       handler: () => {
  //         actionSheet.dismiss(1, 'camera');
  //       }
  //     }, {
  //       text: 'Gallery',
  //       icon: 'image',
  //       handler: () => {
  //         actionSheet.dismiss(2, 'gallery');
  //       }
  //     }]
  //   });
  //   await actionSheet.present();

  //   let dataOnDismissed = await actionSheet.onDidDismiss();
  //   return dataOnDismissed;
  // }

  onFileSelected(event) {
    console.log(event);
    var filepath = URL.createObjectURL(event.target.files[0])
    var mimeType = event.target.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      var message = "Only images are supported.";
      return;
    }
    var reader = new FileReader();
    var imagePath = event.target.files;
    reader.readAsDataURL(event.target.files[0]);
    reader.onload = (_event) => {
      let imgURL = reader.result;
      console.log(imgURL)
      this.imageData = imgURL
    }
  }

  setImageData() {
    let realData = this.cameraService.getComparator();
    let blob = this.cameraService.b64toBlob(realData, 'image/jpeg');
    var imgURL = URL.createObjectURL(blob);
    this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
  }

  unlockScreen() {
    this.screenOrientation.unlock();
  }
}
