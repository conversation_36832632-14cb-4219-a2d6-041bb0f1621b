<ion-header>
  <ion-toolbar>
    <ion-title>{{'Inspections' | translate}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" style="height:104%">
    <ion-card
      (click)="helpService.helpMode ? '' : detailedInspection()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.imageUrlDetailedInspection" style="height: 100%; width: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'DETAILED INSPECTION' |translate}} </ion-label>
    </ion-card>

    <ion-card *ngIf="isVesselMoornigOrUtility && isRoutineInspectionEnabled"
      (click)="helpService.helpMode ? '' : routineInspection()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.imageUrlRoutineInspection" style="height: 100%; width: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'ROUTINE INSPECTION' | translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : goTOVisionAI()" class="ion-activatable responsive-card-style" *ngIf="dataService.isUserEnabledForInsightAI">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/VisionInspection.png" style="height: 100%; width: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{ 'INSIGHT QUICK INSPECT' | translate}} </ion-label>
    </ion-card>
  </div>

  <div *ngIf="platformId == 'electron' || device.platform == 'browser'">
    <div class="gridCon"> 
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : detailedInspection()">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.imageUrlDetailedInspectionWin" class="imageTag">
          <p class="wrapperLabel">{{'DETAILED INSPECTION' |translate}}</p>
        </div>
      </div>
      <div class="gridCol" *ngIf="isVesselMoornigOrUtility && isRoutineInspectionEnabled">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : routineInspection()">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.imageUrlRoutineInspectionWin" class="imageTag">
          <p class="wrapperLabel">{{'ROUTINE INSPECTION' |translate}}</p>
        </div>
      </div>
      <div class="gridCol" *ngIf="dataService.isUserEnabledForInsightAI">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : goTOVisionAI()">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/op-imgs/VisionInspection.png" class="imageTag">
          <p class="wrapperLabel">{{ 'INSIGHT QUICK INSPECT' |translate }}</p>
        </div>
      </div>
    </div>
  </div>
</ion-content>

<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
    (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
