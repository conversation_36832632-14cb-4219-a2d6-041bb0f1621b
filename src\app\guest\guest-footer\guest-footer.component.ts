import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-guest-footer',
  templateUrl: './guest-footer.component.html',
  styleUrls: ['./guest-footer.component.scss'],
  standalone: true,
  imports: [IonicModule, FontAwesomeModule]
})
export class GuestFooterComponent  implements OnInit {

  constructor(
    private faIconLibrary: FaIconLibrary,
  ) { 
    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip)
  }

  ngOnInit() { }

  @Output() Menu = new EventEmitter<void>();
  @Output() Home = new EventEmitter<void>();
  @Output() Inspections = new EventEmitter<void>();
  @Output() Resources = new EventEmitter<void>();
  @Output() Contact = new EventEmitter<void>();
}
