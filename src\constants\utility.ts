// import { Injectable } from '@angular/core';
// import * as moment from 'moment';

// @Injectable()
// export class Utility {

//     formatDate(date): string {
//         let dateString = moment.utc(date).local().format("DD-MM-YYYY");
//         return dateString
//     }

//     getCurrentDateTime(): string {
//         let dateTimeString = moment.utc().local().format("DD MMM YYYY h:mm A");
//         return dateTimeString
//     }

//     static isEmpty(input: any): boolean {
//         return (!input || (input === ""));
//     }

//     static guid(): string {
//         function s4() {
//             return Math.floor((1 + Math.random()) * 0x10000)
//                 .toString(16)
//                 .substring(1).toUpperCase();
//         }
//         return s4() + s4() + '-' + s4() + '-' + s4() + '-' +
//             s4() + '-' + s4() + s4() + s4();
//     }


// }
