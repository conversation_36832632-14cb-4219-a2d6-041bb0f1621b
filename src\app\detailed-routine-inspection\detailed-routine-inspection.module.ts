import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { DetailedRoutineInspectionPageRoutingModule } from './detailed-routine-inspection-routing.module';

import { DetailedRoutineInspectionPage } from './detailed-routine-inspection.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    DetailedRoutineInspectionPageRoutingModule,
    TranslateModule,
    FontAwesomeModule,
    FooterComponent
  ],
  declarations: [DetailedRoutineInspectionPage]
})
export class DetailedRoutineInspectionPageModule {}
