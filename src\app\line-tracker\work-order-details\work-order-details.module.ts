import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule,ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { WorkOrderDetailsPageRoutingModule } from './work-order-details-routing.module';

import { WorkOrderDetailsPage } from './work-order-details.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { FooterComponent } from 'src/app/components/footer/footer.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    WorkOrderDetailsPageRoutingModule,
    FontAwesomeModule,
    TooltipsModule,
    TranslateModule,
    FooterComponent,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule
  ],
  declarations: [WorkOrderDetailsPage]
})
export class WorkOrderDetailsPageModule {}
