import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AbrasionComparatorTenexExternalPageRoutingModule } from './abrasion-comparator-tenex-external-routing.module';

import { AbrasionComparatorTenexExternalPage } from './abrasion-comparator-tenex-external.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FontAwesomeModule,
    TranslateModule,
    TooltipsModule,
    AbrasionComparatorTenexExternalPageRoutingModule,
    FooterComponent
  ],
  declarations: [AbrasionComparatorTenexExternalPage]
})
export class AbrasionComparatorTenexExternalPageModule {}
