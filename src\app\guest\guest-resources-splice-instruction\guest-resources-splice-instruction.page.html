<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Splice Instructions</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="height: 98% !important">
    <div style="width:100% !important">
      <div class="responsive-div-wrapper">
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('3Strand')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/3-Strand.png" />
          </ion-card>
          <p>3-strand</p>
        </div>
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('8Strand')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/8-Strand.png" style="padding: 15px !important;" />
            <!-- <fa-icon class="icon-style-other"  icon="search" style="font-size: 7rem;color: #003874;"></fa-icon> -->
          </ion-card>
          <p>8 strand</p>
        </div>
      </div>
      <div class="responsive-div-wrapper">
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('8x3Strand')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/8x3-Strand.png" />
          </ion-card>
          <p>8x3-strand</p>
        </div>
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('12Strand')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/12-Strand.png" style="padding: 15px !important;" />
            <!-- <fa-icon class="icon-style-other"  icon="search" style="font-size: 7rem;color: #003874;"></fa-icon> -->
          </ion-card>
          <p>12 strand</p>
        </div>
      </div>
      <div class="responsive-div-wrapper">
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('16Strand')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/16-Strand.png" />
          </ion-card>
          <p>16-strand</p>
        </div>
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('DoubleBraid')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/Double_Braid.png"style="padding: 15px !important;" />
            <!-- <fa-icon class="icon-style-other"  icon="search" style="font-size: 7rem;color: #003874;"></fa-icon> -->
          </ion-card>
          <p>Double Braid</p>
        </div>
      </div>
      <div class="responsive-div-wrapper">
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('RoundPlait')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/12-S_Round_Plait.png" />
          </ion-card>
          <p>Round Plait</p>
        </div>
        <div style="width: 50%; text-align: center; margin: auto; display:inline-block">
          <ion-card class="card-style" (click)="presentPopover('Other')">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/img/Constructions/other.png"style="padding: 15px !important;" />
            <!-- <fa-icon class="icon-style-other"  icon="search" style="font-size: 7rem;color: #003874;"></fa-icon> -->
          </ion-card>
          <p>Misc</p>
        </div>
      </div>
    </div>
  </div>
</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>

<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>