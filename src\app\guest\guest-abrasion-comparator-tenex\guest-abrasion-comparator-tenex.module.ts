import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestAbrasionComparatorTenexPageRoutingModule } from './guest-abrasion-comparator-tenex-routing.module';

import { GuestAbrasionComparatorTenexPage } from './guest-abrasion-comparator-tenex.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestAbrasionComparatorTenexPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    TooltipsModule,
    GuestFooterComponent
  ],
  declarations: [GuestAbrasionComparatorTenexPage]
})
export class GuestAbrasionComparatorTenexPageModule {}
