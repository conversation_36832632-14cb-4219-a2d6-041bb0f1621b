import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { EmailComposer } from '@awesome-cordova-plugins/email-composer/ngx';
import { FileOpenerService } from 'src/app/services/file-opener.service';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, NavController, LoadingController } from '@ionic/angular';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { LmdService } from 'src/app/services/lmd.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faChevronCircleLeft, faDownload, faPlus } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-certificate-list',
  templateUrl: './certificate-list.page.html',
  styleUrls: ['./certificate-list.page.scss'],
})
export class CertificateListPage implements OnInit {

  certificateData: any[] = [];
  certExportData: any[] = [];
  assetId: any;
  showLoading: any;
  currentFilePath: any;
  dataArray: any[] = [];
  objArray = {};
  constructor(private router: Router,
    private emailComposer: EmailComposer,
    private file: File,
    public lmdService: LmdService,
    public utilityService: UtilserviceService,
    public menu: MenuController,
    public alertService: AlertService,
    public dataService: DataService,
    private userPreferenceService: UserPreferenceService,
    private fileOpenerService: FileOpenerService,
    public device: Device,
    public navCtrl: NavController,
    private unviredCordovaSDK: UnviredCordovaSDK, 
    private loadingController: LoadingController,
    public faIconLibrary: FaIconLibrary) { 
      this.faIconLibrary.addIcons(faPlus, faDownload , faChevronCircleLeft)
    }

  ngOnInit() {
    this.assetId = this.router.getCurrentNavigation().extras.state['AssetId'];
    this.loadData(this.assetId);
  }

  async loadData(assetId) {
    this.alertService.present().then(async () => {
      this.getCertificates(assetId);
    });
  }

  async presentLoading(msg) {
    this.showLoading = await this.loadingController.create({
      message: msg,
      spinner: 'crescent',
      animated: true,
      showBackdrop: true,
      translucent: true
    });
    await this.showLoading.present();
  }

  showRpdDetails(certNum, certName) {
    this.router.navigate(['rps-details'], { state: { CertificateNum: certNum, CertificateName: certName } });
  }

  composeEmail() {
    this.generateCSV('email');
    console.log('current file path' + this.currentFilePath);
  }

  async generateCSV(type: string) {
    const value = await this.convertToCSV();
    // console.log('formatted data' + value);
    // await this.unviredCordovaSDK.dbExportWebData();
    const folderPath = this.file.dataDirectory;

    //#region "Get todays date and time"
    const currentdate = new Date();
    const datetime = currentdate.getDate() + '' + (currentdate.getMonth() + 1) + '' +
      currentdate.getFullYear() + '' + currentdate.getHours() +
      '' + currentdate.getMinutes() + '' + currentdate.getSeconds();
    //#endregion

    const fileName = `IN${datetime}.csv`;
    const blob = new Blob(['\ufeff' + value], { type: 'text/csv;charset=utf-8;' });
    if (this.device.platform == "browser") {
      const a = document.createElement('a');
      const url = window.URL.createObjectURL(blob);

      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();

    } else {
      if (this.device.platform == 'Android') {
        if (type === 'export') {
          this.file.checkDir(this.file.externalRootDirectory + "Download/", 'Inspections')
            .then(_ => {
              this.file.writeFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, blob).then(response => {
                let filePath = this.file.applicationDirectory + 'www/assets';
                this.file.copyFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, this.file.dataDirectory, fileName).then(result => {
                  this.fileOpenerService.openFile(fileName, 'text/csv');
                }).catch(err => {
                })
              })
            })
            .catch(err => {
              this.file.createDir(this.file.externalRootDirectory + "Download/", 'Inspections/', false).then(result => {
                this.file.writeFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, blob).then(response => {
                  this.file.copyFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, this.file.dataDirectory, fileName).then(result => {
                    this.fileOpenerService.openFile(fileName, 'text/csv');
                  }).catch(err => {
                  })
                }).catch(err => {
                  // ACTION
                })
              })
            });
        } else {
          const mailId = await this.userPreferenceService.getUserPreference('email');
          const email = {
            to: `${mailId ? mailId : ''}`,
            cc: '',
            attachments: [
              this.currentFilePath
            ],
            subject: 'Inventory certificates report',
            body: 'Please find the attachment',
            isHtml: true
          };
          this.emailComposer.open(email);
        }
      } else {
        this.file.writeFile(`${folderPath}`, fileName, blob, { replace: true, append: false }).then(async (res) => {
          console.log('successfully written', res);
          if (type === 'export') {
            this.fileOpenerService.openFile(fileName, 'text/csv');
            // .then(_ => console.log('Opened successfully')).
            // catch(err => console.log('error while opening file', err));
          } else {
            const mailId = await this.userPreferenceService.getUserPreference('email');
            const email = {
              to: `${mailId ? mailId : ''}`,
              cc: '',
              attachments: [
                this.currentFilePath
              ],
              subject: 'Inventory certificates report',
              body: 'Please find the attachment',
              isHtml: true
            };
            this.emailComposer.open(email);
          }
        }).catch(err => console.log('error while writing to file' + err));
      }
    }

    const filePath = folderPath + fileName;
    this.currentFilePath = `${filePath}`;
  }

  async convertToCSV() {
    console.log('certexpo in csv' + JSON.stringify(this.dataArray));
    // tslint:disable-next-line: prefer-for-of
    const obj = { data: this.dataArray };
    console.log('obj' + JSON.stringify(obj.data));
    const array = typeof obj.data !== 'object' ? JSON.parse(obj.data) : obj.data;
    let str = 'Certificate,Product,Application,Construction,Material,MFG,Diameter(mm),Original Length(in m),Current Length(in m),' +
      'Install Date,Total End A Hours,Total End B hours,Working Hours,Rope Status,Current End In Use,Equipment Details,Last Maintenance \r\n';

    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < array.length; i++) {
      let line = '';
      // tslint:disable-next-line: forin
      for (const index in array[i]) {
        if (line !== '') {
          line += ',';
        }
        if (line === '') {
          line += ' ';
        }
        line += array[i][index];
      }
      str += line + '\r\n';
    }
    console.log(str);
    return str;
  }

  async getCertificates(assetId) {
    // const result = await this.unviredCordovaSDK.dbExecuteStatement(`Select * from CERTIFICATE_HEADER as C, ROPE_PRODUCT_SPEC_HEADER as R where C.ASSET_ID = '${assetId}' AND C.RPS = R.ID  order by cast (C.ORIGINAL_LENGTH as unsigned), R.WORKING_HRS`)
    // const result = await this.unviredCordovaSDK.dbSelect('CERTIFICATE_HEADER', 'ASSET_ID = \'' + assetId + '\' order by cast (ORIGINAL_LENGTH as unsigned)');
    const result = await this.unviredCordovaSDK.dbExecuteStatement(`SELECT x.* FROM CERTIFICATE_HEADER x LEFT OUTER JOIN ROPE_PRODUCT_SPEC_HEADER rpsh ON x.RPS  = rpsh.ID WHERE x.ASSET_ID  = '${assetId}' AND rpsh.ROPE_STATUS  != 'Retired' AND rpsh.ID IS NOT NULL`);
    if (result.type === ResultType.success) {
      console.log('certificates data ' + JSON.stringify(result.data));
      this.certificateData = result.data;
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < this.certificateData.length; i++) {
        const certNum = this.certificateData[i].CERTIFICATE_NUM;
        const val = await this.unviredCordovaSDK.dbSelect('ROPE_PRODUCT_SPEC_HEADER', 'CERT =  \'' + certNum + '\' order by WORKING_HRS');
        if (val.type === ResultType.success) {
          const resultData = val.data;
          for (let j = 0; j < resultData.length; j++) {
            this.objArray = {};
            this.objArray['certificate'] = this.certificateData[i].NAME ? " " + this.certificateData[i].NAME + " " : '';
            this.objArray['product'] = this.certificateData[i].PRODUCT ? this.certificateData[i].PRODUCT : '';
            this.objArray['APPLICATION'] = resultData[j].APPLICATION ? resultData[j].APPLICATION : '';
            this.objArray['CONSTRUCTION'] = resultData[j].CONSTRUCTION ? resultData[j].CONSTRUCTION : '';
            this.objArray['MATERIAL'] = resultData[j].MATERIAL ? resultData[j].MATERIAL : '';
            this.objArray['MFG'] = resultData[j].MFG ? resultData[j].MFG : '';
            this.objArray['Diameter'] = resultData[j].DIAM_MM ? resultData[j].DIAM_MM : '';
            this.objArray['ORIGINAL_LENGTH_IN_METER'] = resultData[j].ORIGINAL_LENGTH_IN_METER ? resultData[j].ORIGINAL_LENGTH_IN_METER : '';
            this.objArray['CURRENT_LENGTH_IN_METER'] = resultData[j].CURRENT_LENGTH_IN_METER ? resultData[j].CURRENT_LENGTH_IN_METER : '';
            this.objArray['INSTALL_DATE'] = resultData[j].INSTALL_DATE ? resultData[j].INSTALL_DATE : '';
            this.objArray['TOTAL_END_A_HOURS'] = resultData[j].END_A_HOURS ? resultData[j].END_A_HOURS : '';
            this.objArray['TOTAL_END_B_HOURS'] = resultData[j].END_B_HOURS ? resultData[j].END_B_HOURS : '';
            this.objArray['WORKING_HRS'] = resultData[j].WORKING_HRS ? resultData[j].WORKING_HRS : '';
            this.objArray['ROPE_STATUS'] = resultData[j].ROPE_STATUS ? resultData[j].ROPE_STATUS : '';
            this.objArray['END_IN_USE'] = resultData[j].END_IN_USE ? resultData[j].END_IN_USE : ''; 
            const equipId = resultData[j].EQUIP_DETAILS;
            if (equipId !== undefined && equipId) {
              const equpData = await this.unviredCordovaSDK.dbSelect('EQUIPMENT_HEADER', `EQUIP_ID = '${equipId}'`);
              if (equpData.type === ResultType.success) {
                const resData = equpData.data;
                console.log('equipment details' + JSON.stringify(resData));
                // tslint:disable-next-line:max-line-length
                if (resData.length > 0) {
                  let equipType = '';
                  if (resData[0].EQUIP_TYPE !== undefined) {
                    equipType = resData[0].EQUIP_TYPE;
                  }
                  const formatedData = resData[0].EQUIP_NAME + '(' + equipType + ')';
                  this.objArray['EQUIP_DETAILS'] = formatedData ? formatedData : '';
                } else {
                  console.log('no data found');
                }
              } else {
                console.log('no data found');
              }
            } else {
              this.objArray['EQUIP_DETAILS'] = '';
            }
            this.objArray['LAST_MAINTENANCE'] = resultData[j].LAST_MAINTENANCE ? resultData[j].LAST_MAINTENANCE : '';
            this.dataArray.push(this.objArray);
            console.log('data array' + JSON.stringify(this.dataArray));
          }
        } else {
          alert('no data found');
          this.alertService.dismiss()
          // await this.showLoading.dismiss();
        }
      }
      this.alertService.dismiss()
      // await this.showLoading.dismiss();
    } else {
      // alert('no certificates found for certificate ID' + assetId);
      // await this.showLoading.dismiss();
      this.alertService.dismiss()
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  naviateToRequestNewLine() {
    this.lmdService.setReadOnlyMode(false);
    this.utilityService.setLMDEditMode(false)
    this.router.navigate(['request-new-line-lmd']);
  }

  backButtonClick() {
    this.navCtrl.pop();
  }
}
