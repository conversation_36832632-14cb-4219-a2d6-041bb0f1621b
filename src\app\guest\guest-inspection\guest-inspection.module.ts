import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestInspectionPageRoutingModule } from './guest-inspection-routing.module';

import { GuestInspectionPage } from './guest-inspection.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestInspectionPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    GuestFooterComponent
  ],
  declarations: [GuestInspectionPage]
})
export class GuestInspectionPageModule {}
