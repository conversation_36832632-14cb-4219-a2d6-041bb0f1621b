<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Work Order Details' | translate}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="width:100% !important">
    <ion-card class="card-style-multiple">
      <ion-card-content>
        <p>{{'Is this a planned maintenance event?' | translate}}</p>

        <ion-radio-group [(ngModel)]="samsonWorkOrder">
          <ion-row class="ion-radio-row" >
            <ion-col>
              <ion-item class='ion-radio-item-style' lines="none">
                <ion-radio mode="md" item-left value="yes" labelPlacement="end" slot="start">{{'Yes' | translate}}</ion-radio>
              </ion-item>
            </ion-col>
 
            <ion-col>
              <ion-item class='ion-radio-item-style' lines="none">
                <ion-radio mode="md" item-left value="no" labelPlacement="end" slot="start">{{'No' | translate}}</ion-radio>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-radio-group>

      </ion-card-content>
    </ion-card>

    <ion-card class="card-style-multiple" *ngIf="samsonWorkOrder == 'yes'">
      <ion-card-content>
        <p>{{'Workorder Number' | translate}}</p>
        <ion-item (click)="presentModal('WORK_ORDER')" no-lines text-wrap tappable *ngIf="workOrders"
          class="ion-item-generic-style" mode="ios">
          <div
            style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
            *ngIf="workOrderNo == ''" class="drop-down-arrow  value-field">{{ 'Select Workorder Number' | translate
          }}</div>
          <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
            *ngIf="workOrderNo != ''" class="value-field">{{workOrderNo}}</div>
          <ion-note item-right style="display:inline-flex">
            <p class="drop-down-arrow">
              <fa-icon class="icon-style" icon="sort-down"></fa-icon>
            </p>
          </ion-note>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <ion-card class="card-style-multiple" *ngIf="searchedWorkOrder.length > 0 && samsonWorkOrder == 'yes'">
      <ion-card-content>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(searchedWorkOrder.length > 0 && searchedWorkOrder[0].WO_NUMBER && searchedWorkOrder[0].WO_NUMBER != "")'>
          <div style="width: 45%">{{'Workorder#' | translate }}:</div>
          <div style="width: 45%">{{searchedWorkOrder[0].WO_NUMBER}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(searchedWorkOrder.length > 0 && searchedWorkOrder[0].WO_TYPE && searchedWorkOrder[0].WO_TYPE != "")'>
          <div style="width: 45%">{{'Type' | translate }} : </div>
          <div style="width: 45%">{{searchedWorkOrder[0].WO_TYPE}}</div>
        </div>
        <div style="display:inline-flex !important; width: 100% !important"
          *ngIf='(searchedWorkOrder.length > 0 && searchedWorkOrder[0].EVENT_DATE && searchedWorkOrder[0].EVENT_DATE != "")'>
          <div style="width: 45%">{{'Event Date' | translate }} : </div>
          <div style="width: 45%">{{searchedWorkOrder[0].EVENT_DATE}}</div>
        </div>
      </ion-card-content>
    </ion-card>

    <ion-card class="card-style-multiple" *ngIf="samsonWorkOrder == 'no'">
      <ion-card-content>
        <p>{{'Do you want to associate work order?' | translate}}</p>

        

        <ion-radio-group [(ngModel)]="shouldAssociateWorkOrder">

          <ion-row class="ion-radio-row">
            <ion-col>
              <ion-item class='ion-radio-item-style' lines="none"> 
                <ion-radio mode="md" slot="start" labelPlacement="end" value="yes">{{'Yes' | translate}}</ion-radio>
              </ion-item>
            </ion-col>

            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio mode="md" slot="start" labelPlacement="end" value="no">{{'No' | translate}}</ion-radio>
              </ion-item>
            </ion-col>
          </ion-row>

        </ion-radio-group>


  
      </ion-card-content>
    </ion-card>



    <ion-card class="card-style-multiple" *ngIf="samsonWorkOrder == 'no' && shouldAssociateWorkOrder == 'yes'">
      <ion-card-content>
        <!-- <p>{{'Work Order Number' | translate}}</p>
        <input maxlength="225" placeholder="{{'Enter Work Order Number' | translate}}"
          [(ngModel)]="workOrderAssociated"></ion-input>
        <p>{{'Internal Work Order' | translate}}</p>
        <ion-input maxlength="225" placeholder="{{'Enter Internal WorkOrder' | translate}}"
          [(ngModel)]="internalWorkorder"></ion-input> -->
          
         
            <label>{{'Customer Work Order Number' | translate}}</label>
            <ion-row *ngIf='helpService.helpMode'>
              <ion-col tooltip="{{'Enter Customer Work Order Number'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
                [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
                <mat-form-field style="width:100%">
                  <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="workOrderAssociated" maxlength="18"
                    placeholder="{{'Enter Work Order Number' | translate}}"
                    step="any">
                </mat-form-field>
              </ion-col>
            </ion-row>
            <ion-row *ngIf='!helpService.helpMode'>
              <ion-col>
                <div [formGroup]="workOrderAssociatedForm">
                  <mat-form-field style="width:100%">
                    <input matInput type="text" [(ngModel)]="workOrderAssociated" maxlength="18"
                      placeholder="{{'Enter Customer Work Order Number' | translate}}" step="any"
                      (change)="onChangeDisable('workOrderAssociated')"
                      formControlName="workOrderAssociatedCtrl">
                    <!-- <mat-error *ngIf="hasErrorStart('workOrderAssociated')">{{workOrderAssociatedErrorMessage}}</mat-error> -->
                  </mat-form-field>
                </div>
              </ion-col>&nbsp;
            </ion-row>

    
      </ion-card-content>      
    </ion-card>

    <div style="height: 200px;">

    </div>

  </div>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveAndContinue()">
      <fa-icon class="icon-style-other" icon="arrow-right" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); enableAllFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>