import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController } from '@ionic/angular';
import { DataService } from 'src/app/services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { PlatformService } from 'src/app/services/platform.service';
@Component({
  selector: 'app-reporting',
  templateUrl: './reporting.page.html',
  styleUrls: ['./reporting.page.scss'],
})
export class ReportingPage implements OnInit {
  public platformId: string = this.platformService.getPlatformId();

  constructor(
    public platformService: PlatformService,
    public menu: MenuController,
    public dataService: DataService,
    public router: Router,
    public device: Device) { }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  navigateToLinehistory() {
    this.router.navigate(['maintenance']);
  }

  navigateToUsagehistory() {
    this.router.navigate(['mooring-history']);
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

}
