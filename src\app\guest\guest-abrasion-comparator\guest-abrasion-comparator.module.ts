import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestAbrasionComparatorPageRoutingModule } from './guest-abrasion-comparator-routing.module';

import { GuestAbrasionComparatorPage } from './guest-abrasion-comparator.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestAbrasionComparatorPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    GuestFooterComponent
  ],
  declarations: [GuestAbrasionComparatorPage]
})
export class GuestAbrasionComparatorPageModule {}
