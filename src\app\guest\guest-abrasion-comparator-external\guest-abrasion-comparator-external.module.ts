import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestAbrasionComparatorExternalPageRoutingModule } from './guest-abrasion-comparator-external-routing.module';

import { GuestAbrasionComparatorExternalPage } from './guest-abrasion-comparator-external.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestAbrasionComparatorExternalPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    TooltipsModule,
    GuestFooterComponent
  ],
  declarations: [GuestAbrasionComparatorExternalPage]
})
export class GuestAbrasionComparatorExternalPageModule {}
