import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AbrasionComparatorInternalPageRoutingModule } from './abrasion-comparator-internal-routing.module';

import { AbrasionComparatorInternalPage } from './abrasion-comparator-internal.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TooltipsModule } from 'ionic4-tooltips';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    FontAwesomeModule,
    AbrasionComparatorInternalPageRoutingModule,
    TooltipsModule,
    FooterComponent
  ],
  declarations: [AbrasionComparatorInternalPage]
})
export class AbrasionComparatorInternalPageModule {}
