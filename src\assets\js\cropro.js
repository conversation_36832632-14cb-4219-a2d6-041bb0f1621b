/* **********************************
cropro version 1.5.0
@license SEE LICENSE IN LICENSE

copyright <PERSON>
see README and LICENSE for details
********************************** */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).cropro={})}(this,(function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,i)};function i(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}function o(t,e,i,o){return new(i||(i=Promise))((function(s,n){function r(t){try{h(o.next(t))}catch(t){n(t)}}function a(t){try{h(o.throw(t))}catch(t){n(t)}}function h(t){var e;t.done?s(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(r,a)}h((o=o.apply(t,e||[])).next())}))}function s(t,e){var i,o,s,n,r={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return n={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(n[Symbol.iterator]=function(){return this}),n;function a(n){return function(a){return function(n){if(i)throw new TypeError("Generator is already executing.");for(;r;)try{if(i=1,o&&(s=2&n[0]?o.return:n[0]?o.throw||((s=o.return)&&s.call(o),0):o.next)&&!(s=s.call(o,n[1])).done)return s;switch(o=0,s&&(n=[2&n[0],s.value]),n[0]){case 0:case 1:s=n;break;case 4:return r.label++,{value:n[1],done:!1};case 5:r.label++,o=n[1],n=[0];continue;case 7:n=r.ops.pop(),r.trys.pop();continue;default:if(!(s=r.trys,(s=s.length>0&&s[s.length-1])||6!==n[0]&&2!==n[0])){r=0;continue}if(3===n[0]&&(!s||n[1]>s[0]&&n[1]<s[3])){r.label=n[1];break}if(6===n[0]&&r.label<s[1]){r.label=s[1],s=n;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(n);break}s[2]&&r.ops.pop(),r.trys.pop();continue}n=e.call(t,r)}catch(t){n=[6,t],o=0}finally{i=s=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,a])}}}var n=function(){function t(){}return t.addKey=function(e){t.key=e},Object.defineProperty(t,"isLicensed",{get:function(){return!!t.key&&new RegExp(/^CRPR-[A-Z][0-9]{3}-[A-Z][0-9]{3}-[0-9]{4}$/,"i").test(t.key)},enumerable:!1,configurable:!0}),t}(),r=function(){function t(){}return t.createDefs=function(){return document.createElementNS("http://www.w3.org/2000/svg","defs")},t.setAttributes=function(t,e){for(var i=0,o=e;i<o.length;i++){var s=o[i],n=s[0],r=s[1];t.setAttribute(n,r)}},t.createRect=function(e,i,o){var s=document.createElementNS("http://www.w3.org/2000/svg","rect");return s.setAttribute("width",e.toString()),s.setAttribute("height",i.toString()),o&&t.setAttributes(s,o),s},t.createLine=function(e,i,o,s,n){var r=document.createElementNS("http://www.w3.org/2000/svg","line");return r.setAttribute("x1",e.toString()),r.setAttribute("y1",i.toString()),r.setAttribute("x2",o.toString()),r.setAttribute("y2",s.toString()),n&&t.setAttributes(r,n),r},t.createPolygon=function(e,i){var o=document.createElementNS("http://www.w3.org/2000/svg","polygon");return o.setAttribute("points",e),i&&t.setAttributes(o,i),o},t.createCircle=function(e,i){var o=document.createElementNS("http://www.w3.org/2000/svg","circle");return o.setAttribute("cx",(e/2).toString()),o.setAttribute("cy",(e/2).toString()),o.setAttribute("r",e.toString()),i&&t.setAttributes(o,i),o},t.createEllipse=function(e,i,o){var s=document.createElementNS("http://www.w3.org/2000/svg","ellipse");return s.setAttribute("cx",(e/2).toString()),s.setAttribute("cy",(i/2).toString()),s.setAttribute("rx",(e/2).toString()),s.setAttribute("ry",(i/2).toString()),o&&t.setAttributes(s,o),s},t.createGroup=function(e){var i=document.createElementNS("http://www.w3.org/2000/svg","g");return e&&t.setAttributes(i,e),i},t.createTransform=function(){return document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGTransform()},t.createMarker=function(e,i,o,s,n,r,a){var h=document.createElementNS("http://www.w3.org/2000/svg","marker");return t.setAttributes(h,[["id",e],["orient",i],["markerWidth",o.toString()],["markerHeight",s.toString()],["refX",n.toString()],["refY",r.toString()]]),h.appendChild(a),h},t.createText=function(e){var i=document.createElementNS("http://www.w3.org/2000/svg","text");return i.setAttribute("x","0"),i.setAttribute("y","0"),e&&t.setAttributes(i,e),i},t.createTSpan=function(e,i){var o=document.createElementNS("http://www.w3.org/2000/svg","tspan");return o.textContent=e,i&&t.setAttributes(o,i),o},t.createImage=function(e){var i=document.createElementNS("http://www.w3.org/2000/svg","image");return e&&t.setAttributes(i,e),i},t.createPoint=function(t,e){var i=document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGPoint();return i.x=t,i.y=e,i},t.createPath=function(e,i){var o=document.createElementNS("http://www.w3.org/2000/svg","path");return o.setAttribute("d",e),i&&t.setAttributes(o,i),o},t.createElement=function(e,i){var o=document.createElementNS("http://www.w3.org/2000/svg",e);return i&&t.setAttributes(o,i),o},t.getHollowRectanglePath=function(t,e,i,o,s,n,r,a){return"M"+t+","+e+"V"+(e+o)+"H"+(t+i)+"V"+e+"H"+s+"V"+n+"H"+(s+r)+"V"+(n+a)+"H"+s+"V"+e+"Z"},t}(),a=function(){function t(t){this._classNamePrefixBase="__cropro_",this.classes=[],this.rules=[],this.settings=this.defaultSettings,this._classNamePrefix=this._classNamePrefixBase+"_"+t+"_"}return Object.defineProperty(t.prototype,"classNamePrefixBase",{get:function(){return this._classNamePrefixBase},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"classNamePrefix",{get:function(){return this._classNamePrefix},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"defaultSettings",{get:function(){return{canvasBackgroundColor:"#333333",toolbarBackgroundColor:"#111111",toolbarBackgroundHoverColor:"#333333",toolbarBackgroundActiveColor:"#282828",toolbarColor:"#eeeeee",cropShadeColor:"#333333",cropFrameColor:"#D2DF06",gripColor:"#D2DF06",gripFillColor:"#D2DF06",toolbarHeight:40}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fadeInAnimationClassName",{get:function(){return this.classNamePrefix+"_fade_in"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fadeOutAnimationClassName",{get:function(){return this.classNamePrefix+"_fade_out"},enumerable:!1,configurable:!0}),t.prototype.addClass=function(t){return void 0===this.styleSheet&&this.addStyleSheet(),t.name=""+this.classNamePrefix+t.localName,this.classes.push(t),this.styleSheet.sheet.insertRule("."+t.name+" {"+t.style+"}",this.styleSheet.sheet.cssRules.length),t},t.prototype.addRule=function(t){void 0===this.styleSheet&&this.addStyleSheet(),this.rules.push(t),this.styleSheet.sheet.insertRule(t.selector+" {"+t.style+"}",this.styleSheet.sheet.cssRules.length)},t.prototype.addStyleSheet=function(){var t;this.styleSheet=document.createElement("style"),(null!==(t=this.styleSheetRoot)&&void 0!==t?t:document.head).appendChild(this.styleSheet),this.addRule(new h("."+this.classNamePrefix+" h3","font-family: sans-serif")),this.addRule(new h("@keyframes "+this.classNamePrefix+"_fade_in_animation_frames","\n        from {\n          opacity: 0;\n        }\n        to {\n          opacity: 1;\n        }\n    ")),this.addRule(new h("@keyframes "+this.classNamePrefix+"_fade_out_animation_frames","\n        from {\n          opacity: 1;\n        }\n        to {\n          opacity: 0;\n        }\n    ")),this.addClass(new l("_fade_in","\n      animation-duration: 0.3s;\n      animation-name: "+this.classNamePrefix+"_fade_in_animation_frames;\n    ")),this.addClass(new l("_fade_out","\n      animation-duration: 0.3s;\n      animation-name: "+this.classNamePrefix+"_fade_out_animation_frames;\n    "))},t.prototype.removeStyleSheet=function(){var t;this.styleSheet&&((null!==(t=this.styleSheetRoot)&&void 0!==t?t:document.head).removeChild(this.styleSheet),this.styleSheet=void 0)},t}(),h=function(t,e){this.selector=t,this.style=e},l=function(t,e){this.localName=t,this.style=e},c=function(){function t(){this.blocks=[],this.display=""}return t.prototype.addButtonBlock=function(t){t.className=this.blockClassName,t.buttonClassName=this.buttonClassName,t.buttonColorsClassName=this.buttonColorsClassName,t.buttonActiveColorsClassName=this.buttonActiveColorsClassName,this.blocks.push(t)},t.prototype.addElementBlock=function(t){void 0===t.className&&(t.className=this.blockClassName),this.blocks.push(t)},t.prototype.getUI=function(){var t=document.createElement("div");return t.className=this.className+" "+this.colorsClassName+" "+this.fadeInClassName,t.style.display=this.display,this.blocks.forEach((function(e){return t.appendChild(e.getUI())})),t},t}(),p=function(){function t(){}return t.prototype.getUI=function(){var t=document.createElement("div");if(t.className=this.className,void 0!==this.minWidth&&(t.style.minWidth=this.minWidth),void 0!==this.contentAlign)switch(this.contentAlign){case"start":t.style.justifyContent="flex-start";break;case"center":t.style.justifyContent="center";break;case"end":t.style.justifyContent="flex-end"}return t.style.whiteSpace="nowrap",t},t}(),d=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.buttons=[],e}return i(e,t),e.prototype.addButton=function(t){t.className=this.buttonClassName,t.colorsClassName=this.buttonColorsClassName,t.activeColorsClassName=this.buttonActiveColorsClassName,this.buttons.push(t)},e.prototype.getUI=function(){var e=t.prototype.getUI.call(this);return this.buttons.forEach((function(t){return e.appendChild(t.getUI())})),e},e}(p),g=function(){function t(t,e){this._isActive=!1,this._isHidden=!1,this._icon=t,this.title=e,this.uiContainer=document.createElement("div"),this.adjustClassName=this.adjustClassName.bind(this)}return Object.defineProperty(t.prototype,"icon",{get:function(){return this._icon},set:function(t){this._icon=t,this.buttonContainer.innerHTML=this._icon},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isActive",{get:function(){return this._isActive},set:function(t){this._isActive=t,this.adjustClassName()},enumerable:!1,configurable:!0}),t.prototype.getUI=function(){var t=this;return this.buttonContainer=document.createElement("div"),this.buttonContainer.title=this.title,this.buttonContainer.className=this.className+" "+this.colorsClassName,this.adjustClassName(),this.buttonContainer.innerHTML=this._icon,this.onClick&&this.buttonContainer.addEventListener("click",(function(){return t.onClick()})),this.uiContainer.appendChild(this.buttonContainer),this.uiContainer.style.display=this._isHidden?"none":"inline-block",this.uiContainer},t.prototype.adjustClassName=function(){this.activeColorsClassName&&(this._isActive&&this.buttonContainer.className.indexOf(this.activeColorsClassName)<0?this.buttonContainer.className+=" "+this.activeColorsClassName:this._isActive||(this.buttonContainer.className=this.buttonContainer.className.replace(this.activeColorsClassName,"")))},t.prototype.hide=function(){this._isHidden=!0},t}(),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.elements=[],e}return i(e,t),e.prototype.addElement=function(t){this.elements.push(t)},e.prototype.getUI=function(){var e=t.prototype.getUI.call(this);return this.elements.forEach((function(t){return e.appendChild(t)})),e},e}(p),m=function(){function t(t,e){this.horizontal=t,this.vertical=e}return Object.defineProperty(t.prototype,"ratio",{get:function(){return 1*this.horizontal/this.vertical},enumerable:!1,configurable:!0}),t.prototype.getVerticalLength=function(t){return t/this.ratio},t.prototype.getHorizontalLength=function(t){return t*this.ratio},t}(),y=function(){function t(){}return t.getIcon=function(t,e){if(t>0&&e>0){var i=new m(t,e),o=i.ratio>=1?20:i.getHorizontalLength(20),s=i.ratio<1?20:i.getVerticalLength(20),n=document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("xmlns","http://www.w3.org/2000/svg"),n.setAttribute("viewBox","0 0 24 24"),document.body.appendChild(n);var a=r.createDefs();n.appendChild(a);var h=r.createElement("clipPath",[["id","text-bg-clip"]]);a.appendChild(h);var l=r.createPath(r.getHollowRectanglePath((24-o)/2,(24-s)/2,o,s,(24-o)/2+2,(24-s)/2+2,o-4,s-4));n.appendChild(l);var c=r.createText([["x","0"],["y","0"],["font-size","7px"],["font-family","monospace"]]);c.appendChild(r.createTSpan(t+":"+e)),n.appendChild(c);var p=c.getBBox();r.setAttributes(c,[["x",((24-p.width)/2).toString()],["y",((24-p.height)/2-p.y).toString()]]);var d=r.createPath(r.getHollowRectanglePath(0,0,24,24,(24-Math.ceil(p.width))/2,(24-Math.ceil(p.height))/2,Math.ceil(p.width),Math.ceil(p.height)));return h.appendChild(d),r.setAttributes(l,[["clip-path","url(#text-bg-clip)"]]),document.body.removeChild(n),n.outerHTML}return'<svg viewBox="0 0 24 24"><path d="M19 12h-2v3h-3v2h5v-5M7 9h3V7H5v5h2V9m14-6H3a2 2 0 00-2 2v14a2 2 0 002 2h18a2 2 0 002-2V5a2 2 0 00-2-2m0 16H3V5h18v14z"/></svg>'},t}(),v=function(t){function e(e,i,o){var s=t.call(this,e,i)||this;return s.isDropdownOpen=!1,s.onClick=s.dropdownButtonClicked,s.dropdownButtons=o,s}return i(e,t),e.prototype.setupDropdown=function(){var t=this;this.dropdownBlock=document.createElement("div"),this.dropdownBlock.className=this.dropdownClassName+" "+this.dropdownColorsClassName,this.dropdownBlock.style.display="none",this.dropdownButtons.forEach((function(e){e.className=t.className,e.colorsClassName=t.colorsClassName,t.dropdownBlock.appendChild(e.getUI())})),this.uiContainer.appendChild(this.dropdownBlock)},e.prototype.positionDropdown=function(){this.dropdownBlock.style.left=this.uiContainer.clientLeft+"px",this.dropdownBlock.style.top=this.uiContainer.clientTop+this.uiContainer.clientHeight+"px"},e.prototype.dropdownButtonClicked=function(){void 0===this.dropdownBlock&&this.setupDropdown(),this.isDropdownOpen=!this.isDropdownOpen,this.toggleDropdown()},e.prototype.toggleDropdown=function(){this.positionDropdown(),this.dropdownBlock.style.display=this.isDropdownOpen?"inline-block":"none"},e.prototype.showDropdown=function(){this.isDropdownOpen=!0,this.toggleDropdown()},e.prototype.hideDropdown=function(){this.isDropdownOpen=!1,this.toggleDropdown()},e}(g),f=function(){function t(t,e){this.GRIP_SIZE=10,this.color=t,this.fillColor=e,this.visual=r.createGroup(),this.visual.appendChild(r.createCircle(1.5*this.GRIP_SIZE,[["fill","transparent"]])),this.visual.appendChild(r.createCircle(this.GRIP_SIZE,[["fill",this.fillColor],["fill-opacity","0.9"],["stroke",this.color],["stroke-width","2"],["stroke-opacity","0.7"]]))}return t.prototype.ownsTarget=function(t){return t===this.visual||t===this.visual.childNodes[0]||t===this.visual.childNodes[1]},t.prototype.setCenter=function(t,e){this.visual.childNodes.forEach((function(i){return r.setAttributes(i,[["cx",t.toString()],["cy",e.toString()]])}))},t}(),C=function(){function t(t,e,i,o,s,n,r,a){this.isMoving=!1,this._isGridVisible=!0,this.numberOfGridLines=2,this.horizontalGridLines=[],this.verticalGridLines=[],this._zoomFactor=1,this._zoomToCropEnabled=!1,this.cropRectChanged=!1,this.canvasWidth=t,this.canvasHeight=e,this.margin=i,this.container=o,this.requiredMinWidth=r,this.requiredMinHeight=a,this.originalWidth=s,this.originalHeight=n,this.attachEvents=this.attachEvents.bind(this),this.onPointerDown=this.onPointerDown.bind(this),this.onPointerMove=this.onPointerMove.bind(this),this.onPointerUp=this.onPointerUp.bind(this),this.resize=this.resize.bind(this),this.adjustCropRect=this.adjustCropRect.bind(this),this.scaleCanvas=this.scaleCanvas.bind(this),this.getRescaledRect=this.getRescaledRect.bind(this)}return Object.defineProperty(t.prototype,"aspectRatio",{get:function(){return this._aspectRatio},set:function(t){this._aspectRatio=t,this.adjustCropRect(),this.setCropRectangle(this.cropRect)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isGridVisible",{get:function(){return this._isGridVisible},set:function(t){this._isGridVisible=t,this.gridContainer&&r.setAttributes(this.gridContainer,[["display",this._isGridVisible?"":"none"]])},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"zoomFactor",{get:function(){return this._zoomFactor},set:function(t){this._zoomFactor=t,this.setCropRectangle(this.cropRect)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"zoomToCropEnabled",{set:function(t){this._zoomToCropEnabled=t},enumerable:!1,configurable:!0}),t.prototype.open=function(){var t=this;this.cropShadeElement=r.createPath("M0,0Z",[["fill",this.cropShadeColor],["fill-opacity","0.8"]]),this.container.appendChild(this.cropShadeElement),this.gridContainer=r.createGroup([["display",this.isGridVisible?"":"none"]]),this.container.appendChild(this.gridContainer);for(var e=0;e<this.numberOfGridLines;e++)this.horizontalGridLines.push(r.createLine(0,0,0,0,[["stroke",this.cropFrameColor],["stroke-width","1"],["stroke-dasharray","3 1"],["opacity","0.7"]])),this.verticalGridLines.push(r.createLine(0,0,0,0,[["stroke",this.cropFrameColor],["stroke-width","1"],["stroke-dasharray","3 1"],["opacity","0.7"]]));this.horizontalGridLines.forEach((function(e){return t.gridContainer.appendChild(e)})),this.verticalGridLines.forEach((function(e){return t.gridContainer.appendChild(e)})),this.cropRectElement=r.createRect(0,0,[["stroke",this.cropFrameColor],["stroke-width","3"],["fill","transparent"]]),this.container.appendChild(this.cropRectElement),this.topLeftGrip=new f(this.gripColor,this.gripFillColor),this.container.appendChild(this.topLeftGrip.visual),this.topRightGrip=new f(this.gripColor,this.gripFillColor),this.container.appendChild(this.topRightGrip.visual),this.bottomLeftGrip=new f(this.gripColor,this.gripFillColor),this.container.appendChild(this.bottomLeftGrip.visual),this.bottomRightGrip=new f(this.gripColor,this.gripFillColor),this.container.appendChild(this.bottomRightGrip.visual),this.attachEvents()},t.prototype.setCropRectangle=function(t){this.cropRect=t;var e=Object.assign({},this.cropRect);this._zoomToCropEnabled&&(e.width=this.cropRect.width*this.zoomFactor,e.height=this.cropRect.height*this.zoomFactor,e.x=this.cropRect.height/this.cropRect.width<this.canvasHeight/this.canvasWidth?this.margin:this.margin+this.canvasWidth/2-e.width/2,e.y=this.cropRect.height/this.cropRect.width>this.canvasHeight/this.canvasWidth?this.margin:this.margin+this.canvasHeight/2-e.height/2),r.setAttributes(this.cropRectElement,[["x",e.x.toString()],["y",e.y.toString()],["width",e.width.toString()],["height",e.height.toString()]]);var i=e.height/(this.numberOfGridLines+1);this.horizontalGridLines.forEach((function(t,o){var s=e.y+i*(o+1);r.setAttributes(t,[["x1",""+e.x],["y1",""+s],["x2",""+(e.x+e.width)],["y2",""+s]])}));var o=e.width/(this.numberOfGridLines+1);this.verticalGridLines.forEach((function(t,i){var s=e.x+o*(i+1);r.setAttributes(t,[["x1",""+s],["y1",""+e.y],["x2",""+s],["y2",""+(e.y+e.height)]])})),r.setAttributes(this.cropShadeElement,[["d",r.getHollowRectanglePath(0,0,this.canvasWidth+2*this.margin,this.canvasHeight+2*this.margin,e.x,e.y,e.width,e.height)]]),this.topLeftGrip.setCenter(e.x,e.y),this.topRightGrip.setCenter(e.x+e.width,e.y),this.bottomLeftGrip.setCenter(e.x,e.y+e.height),this.bottomRightGrip.setCenter(e.x+e.width,e.y+e.height),this.cropRectChanged&&this.onCropChange&&(this.cropRectChanged=!1,this.onCropChange(this.cropRect)),this.cropRectChanged=!1},t.prototype.getgridContainer=function(){return this.gridContainer},t.prototype.getCropRectElement=function(){return this.cropRectElement},t.prototype.attachEvents=function(){this.container.style.touchAction="none",this.container.addEventListener("pointerdown",this.onPointerDown),this.container.addEventListener("pointermove",this.onPointerMove),this.container.addEventListener("pointerup",this.onPointerUp)},t.prototype.clientToLocalCoordinates=function(t,e){var i=this.container.getBoundingClientRect();return{x:t-i.left+this.margin,y:e-i.top+this.margin}},t.prototype.onPointerDown=function(t){this.container.setPointerCapture(t.pointerId),this.previousPoint=this.clientToLocalCoordinates(t.clientX,t.clientY),this.cropRectElement===t.target?this.isMoving=!0:this.topLeftGrip.ownsTarget(t.target)?this.activeGrip=this.topLeftGrip:this.bottomLeftGrip.ownsTarget(t.target)?this.activeGrip=this.bottomLeftGrip:this.topRightGrip.ownsTarget(t.target)?this.activeGrip=this.topRightGrip:this.bottomRightGrip.ownsTarget(t.target)&&(this.activeGrip=this.bottomRightGrip)},t.prototype.onPointerMove=function(t){if(this.isMoving)this.move(this.clientToLocalCoordinates(t.clientX,t.clientY));else if(this.activeGrip){var e=this.clientToLocalCoordinates(t.clientX,t.clientY);this.resize(e)}t.preventDefault()},t.prototype.onPointerUp=function(t){this.activeGrip=void 0,this.isMoving=!1,this.container.releasePointerCapture(t.pointerId)},t.prototype.move=function(t){var e=t.x-this.previousPoint.x,i=t.y-this.previousPoint.y;1!==this.zoomFactor&&(e=-e/this.zoomFactor,i=-i/this.zoomFactor),this.cropRect.x=Math.min(Math.max(this.margin,this.cropRect.x+e),this.canvasWidth-this.cropRect.width+this.margin),this.cropRect.y=Math.min(Math.max(this.margin,this.cropRect.y+i),this.canvasHeight-this.cropRect.height+this.margin),this.onCropChange?(this.cropRectChanged=!0,this.onCropChange(this.cropRect)):this.setCropRectangle(this.cropRect),this.previousPoint=t},t.prototype.resize=function(t){var e=Object.assign({},this.cropRect),i=t.x-this.previousPoint.x,o=t.y-this.previousPoint.y;switch(this.activeGrip){case this.bottomLeftGrip:case this.topLeftGrip:e.x+i<this.margin&&(i=this.margin-e.x),e.x+=i,e.width=this.cropRect.x+this.cropRect.width-e.x;break;case this.bottomRightGrip:case this.topRightGrip:e.width+i>this.canvasWidth*this.zoomFactor&&(i=this.canvasWidth*this.zoomFactor-e.width),e.width+=i}switch(this.activeGrip){case this.topLeftGrip:case this.topRightGrip:this.aspectRatio?(e.y=this.cropRect.y-(this.activeGrip===this.topLeftGrip?-1:1)*this.aspectRatio.getVerticalLength(i),e.height=this.aspectRatio.getVerticalLength(e.width)):(e.y+=o,e.height=this.cropRect.y+this.cropRect.height-e.y);break;case this.bottomLeftGrip:case this.bottomRightGrip:this.aspectRatio?e.height=this.aspectRatio.getVerticalLength(e.width):e.height+=o}var s=this.canvasWidth/(this.originalWidth/this.requiredMinWidth);(!(this.originalWidth<this.requiredMinWidth)&&e.width<s||this.originalWidth<this.requiredMinWidth)&&(e.x=this.cropRect.x,e.width=this.cropRect.width);var n=this.canvasHeight/(this.originalHeight/this.requiredMinHeight);(!(this.originalHeight<this.requiredMinHeight)&&e.height<n||this.originalHeight<this.requiredMinHeight)&&(e.y=this.cropRect.y,e.height=this.cropRect.height),this.previousPoint=t,e.x>=this.margin&&e.y>=this.margin&&Math.floor(e.x-this.margin+e.width)<=Math.ceil(this.canvasWidth)&&Math.floor(e.y-this.margin+e.height)<=Math.ceil(this.canvasHeight)&&(this.cropRect=e,this.onCropChange?(this.cropRectChanged=!0,this.onCropChange(this.cropRect)):this.setCropRectangle(this.cropRect))},t.prototype.adjustCropRect=function(){if(this.aspectRatio&&Math.round(this.cropRect.height)!==Math.round(this.aspectRatio.getVerticalLength(this.cropRect.width))){var t=this.cropRect.x+this.cropRect.width/2,e=this.cropRect.y+this.cropRect.height/2,i=this.aspectRatio.getHorizontalLength(this.cropRect.height),o=this.aspectRatio.getVerticalLength(this.cropRect.width);i/this.canvasWidth<o/this.canvasHeight?this.cropRect.width=i:this.cropRect.height=o,this.cropRect.width>this.canvasWidth&&(this.cropRect.height/=this.cropRect.width/this.canvasWidth,this.cropRect.width=this.canvasWidth),this.cropRect.height>this.canvasHeight&&(this.cropRect.width/=this.cropRect.height/this.canvasHeight,this.cropRect.height=this.canvasHeight),this.cropRect.x=t-this.cropRect.width/2,this.cropRect.y=e-this.cropRect.height/2,this.cropRect.x+this.cropRect.width>this.margin+this.canvasWidth&&(this.cropRect.x=this.margin+this.canvasWidth-this.cropRect.width),this.cropRect.y+this.cropRect.height>this.margin+this.canvasHeight&&(this.cropRect.y=this.margin+this.canvasHeight-this.cropRect.height),this.cropRect.x=Math.max(this.cropRect.x,this.margin),this.cropRect.y=Math.max(this.cropRect.y,this.margin)}},t.prototype.getRescaledRect=function(t,e,i,o,s,n){var r=i/t,a=o/e;return{x:(s.x-n)*r+n,y:(s.y-n)*a+n,width:s.width*r,height:s.height*a}},t.prototype.scaleCanvas=function(t,e){var i=this.getRescaledRect(this.canvasWidth,this.canvasHeight,t,e,this.cropRect,this.margin);this.canvasWidth=t,this.canvasHeight=e,this.onCropChange&&this.onCropChange(i),this.setCropRectangle(i)},t}(),b=function(){function t(t){this._angle=0,this.isDragging=!1,this.width=401,this.height=24,this.title=t,this.uiContainer=document.createElement("div"),this.getUI=this.getUI.bind(this),this.onPointerDown=this.onPointerDown.bind(this),this.onPointerMove=this.onPointerMove.bind(this),this.onPointerUp=this.onPointerUp.bind(this),this.setAngleLabel=this.setAngleLabel.bind(this),this.positionScaleShape=this.positionScaleShape.bind(this)}return Object.defineProperty(t.prototype,"angle",{get:function(){return this._angle},set:function(t){this._angle=t,this.setAngleLabel(),this.positionScaleShape()},enumerable:!1,configurable:!0}),t.prototype.getUI=function(){return this.controlContainer=document.createElement("div"),this.controlContainer.title=this.title,this.controlContainer.className=this.className+" "+this.colorsClassName,this.controlContainer.appendChild(this.getVisual()),this.controlContainer.addEventListener("pointerdown",this.onPointerDown),this.controlContainer.addEventListener("pointermove",this.onPointerMove),this.controlContainer.addEventListener("pointerup",this.onPointerUp),this.uiContainer.appendChild(this.controlContainer),this.uiContainer.style.display="inline-block",this.uiContainer.style.touchAction="none",this.setAngleLabel(),this.positionScaleShape(),this.uiContainer},t.prototype.onPointerDown=function(t){this.controlContainer.setPointerCapture(t.pointerId),this.isDragging=!0,this.previousPoint={x:t.clientX,y:t.clientY}},t.prototype.onPointerMove=function(t){this.isDragging&&(this.onAngleChange&&this.onAngleChange((t.clientX-this.previousPoint.x)/5),this.previousPoint.x=t.clientX)},t.prototype.onPointerUp=function(t){this.isDragging&&this.onAngleChange&&this.onAngleChange((t.clientX-this.previousPoint.x)/5),this.isDragging=!1,this.controlContainer.releasePointerCapture(t.pointerId)},t.prototype.setAngleLabel=function(){if(this.angleLabelText){this.angleLabelText.innerHTML=""+Math.round(this._angle);var t=this.angleLabelText.getBBox();r.setAttributes(this.angleLabelElement,[["x",((this.width-t.width)/2).toString()],["y",(this.height/2).toString()]])}},t.prototype.positionScaleShape=function(){if(this.scaleShape){var t=this.scaleShape.transform.baseVal.getItem(0);t.setTranslate(this._angle%5*5-25,0),this.scaleShape.transform.baseVal.replaceItem(t,0)}},t.prototype.getVisual=function(){var t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("width",this.width+"px"),t.setAttribute("height",this.height+"px"),t.setAttribute("viewBox","0 0 "+this.width+" "+this.height),t.style.flexShrink="0",document.body.appendChild(t);for(var e="M0,"+(this.height-1)+" v"+-this.height/3+"h1v"+this.height/3,i=1;i<=this.width/5+10;i++){var o=i%5==0?this.height/3:this.height/6;e+="h4v"+-o+"h1v"+o}e+="v1H0Z",this.scaleShape=r.createPath(e);var s=r.createTransform();this.scaleShape.transform.baseVal.appendItem(s),t.appendChild(this.scaleShape),this.angleLabelElement=r.createText([["x","0"],["y","0"],["font-size","10px"],["font-family","monospace"]]),this.angleLabelText=r.createTSpan(""),this.angleLabelElement.appendChild(this.angleLabelText);var n=r.createTSpan("");return n.innerHTML="&deg;",this.angleLabelElement.appendChild(n),t.appendChild(this.angleLabelElement),document.body.removeChild(t),t},t}(),w=function(){function t(){this.naturalSize=!1,this.imageType="image/png",this.rasterize=this.rasterize.bind(this),this.isImageDownScaled=!1}return t.prototype.rasterize=function(t,e,i,o,s,n,r){var a=this;return new Promise((function(h){var l=document.createElement("canvas");l.width=t.width.baseVal.value-2*o,l.height=t.height.baseVal.value-2*o;var c=1,p=1;if(!0===a.naturalSize?(c=Math.abs(e.naturalWidth/(t.width.baseVal.value-2*o)/n),p=Math.abs(e.naturalHeight/(t.height.baseVal.value-2*o)/r),l.width=Math.abs(e.naturalWidth/n),l.height=Math.abs(e.naturalHeight/r)):void 0!==a.width&&void 0!==a.height&&(c=a.width/i.width,p=a.height/i.height,l.width*=c,l.height*=p),void 0!==a.maxSize&&l.width*l.height>=a.maxSize){var d=l.width*l.height,g=Math.sqrt(a.maxSize/d);c*=g,p*=g,l.width*=g,l.height*=g,a.isImageDownScaled=!0}var u=l.getContext("2d");u.translate(l.width/2,l.height/2),u.rotate(s*Math.PI/180),u.scale(n,r),u.translate(-l.width/2,-l.height/2),u.drawImage(e,0,0,l.width,l.height);var m=document.createElement("canvas");m.width=i.width*c,m.height=i.height*p,m.getContext("2d").putImageData(u.getImageData((i.x-o)*c,(i.y-o)*p,i.width*c,i.height*p),0,0),h({result:m.toDataURL(a.imageType,a.imageQuality),isImageDownScaled:a.isImageDownScaled})}))},t}(),R=function(){function t(e,i,o,s,n){this._zoomToCropEnabled=!0,this.zoomFactor=1,this.flippedHorizontally=!1,this.flippedVertically=!1,this._isGridVisible=!1,this._gridLines=2,this._rotationAngle=0,this.scaleFactor=1,this.renderEventListeners=[],this.closeEventListeners=[],this._isOpen=!1,this.CANVAS_MARGIN=20,this.renderAtNaturalSize=!1,this.renderImageType="image/png",this.displayMode="inline",this.popupMargin=30,this.toolbarHeight=40,this.aspectRatios=[{horizontal:0,vertical:0},{horizontal:4,vertical:3},{horizontal:3,vertical:2},{horizontal:16,vertical:9},{horizontal:1,vertical:1},{horizontal:3,vertical:4},{horizontal:2,vertical:3},{horizontal:9,vertical:16}],this._instanceNo=t.instanceCounter++,this.styles=new a(this.instanceNo),this.target=e,this.requiredMinWidth=s,this.requiredMinHeight=n,this.originalWidth=i,this.originalHeight=o,this.targetRoot=document.body,this.open=this.open.bind(this),this.setTopLeft=this.setTopLeft.bind(this),this.overrideOverflow=this.overrideOverflow.bind(this),this.restoreOverflow=this.restoreOverflow.bind(this),this.close=this.close.bind(this),this.closeUI=this.closeUI.bind(this),this.addCloseEventListener=this.addCloseEventListener.bind(this),this.removeCloseEventListener=this.removeCloseEventListener.bind(this),this.addRenderEventListener=this.addRenderEventListener.bind(this),this.removeRenderEventListener=this.removeRenderEventListener.bind(this),this.clientToLocalCoordinates=this.clientToLocalCoordinates.bind(this),this.onWindowResize=this.onWindowResize.bind(this),this.setWindowHeight=this.setWindowHeight.bind(this),this.rotateBy=this.rotateBy.bind(this),this.applyRotation=this.applyRotation.bind(this),this.cropRectChanged=this.cropRectChanged.bind(this),this.zoomToCrop=this.zoomToCrop.bind(this),this.unzoomFromCrop=this.unzoomFromCrop.bind(this),this.rotateLeftButtonClicked=this.rotateLeftButtonClicked.bind(this),this.rotateRightButtonClicked=this.rotateRightButtonClicked.bind(this),this.flipHorizontallyButtonClicked=this.flipHorizontallyButtonClicked.bind(this),this.flipVerticallyButtonClicked=this.flipVerticallyButtonClicked.bind(this),this.applyFlip=this.applyFlip.bind(this),this.startRenderAndClose=this.startRenderAndClose.bind(this),this.render=this.render.bind(this),this.onPopupResize=this.onPopupResize.bind(this),this.applyAspectRatio=this.applyAspectRatio.bind(this),this.renderState=this.renderState.bind(this)}return Object.defineProperty(t.prototype,"instanceNo",{get:function(){return this._instanceNo},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"zoomToCropEnabled",{get:function(){return this._zoomToCropEnabled},set:function(t){this._zoomToCropEnabled=t,void 0!==this.cropLayer&&(this.cropLayer.zoomToCropEnabled=this._zoomToCropEnabled),t?this.zoomToCrop():this.unzoomFromCrop()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isGridVisible",{get:function(){return this._isGridVisible},set:function(t){this._isGridVisible=t,this.cropLayer&&(this.cropLayer.isGridVisible=this._isGridVisible)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"gridLines",{get:function(){return this._gridLines},set:function(t){this._gridLines=t,this.cropLayer&&(this.cropLayer.numberOfGridLines=this._gridLines)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"rotationAngle",{get:function(){return this._rotationAngle},set:function(t){this._rotationAngle=t,this.straightener&&(this.straightener.angle=this._rotationAngle)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddedImageWidth",{get:function(){return this.imageWidth+2*this.CANVAS_MARGIN},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddedImageHeight",{get:function(){return this.imageHeight+2*this.CANVAS_MARGIN},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isOpen",{get:function(){return this._isOpen},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"aspectRatio",{get:function(){var t;return null!==(t=this._aspectRatio)&&void 0!==t?t:this.aspectRatios[0]},set:function(t){this._aspectRatio=t},enumerable:!1,configurable:!0}),t.prototype.open=function(){this.imageWidth=Math.round(this.target.clientWidth),this.imageHeight=Math.round(this.target.clientHeight),this.setupResizeObserver(),this.initCropCanvas(),this.setEditingTarget(),this.setTopLeft(),this.initCropLayer(),this.attachEvents(),this.applyAspectRatio(),"popup"===this.displayMode&&this.onPopupResize(),this._isOpen=!0},t.prototype.show=function(){this.showUI(),this.open()},t.prototype.close=function(t){void 0===t&&(t=!1),this.isOpen&&(this.coverDiv&&this.closeUI(),this.targetObserver&&this.targetObserver.unobserve(this.target),"popup"===this.displayMode&&window.removeEventListener("resize",this.setWindowHeight),t||this.closeEventListeners.forEach((function(t){return t()})),this._isOpen=!1)},t.prototype.addRenderEventListener=function(t){this.renderEventListeners.push(t)},t.prototype.removeRenderEventListener=function(t){this.renderEventListeners.indexOf(t)>-1&&this.renderEventListeners.splice(this.renderEventListeners.indexOf(t),1)},t.prototype.addCloseEventListener=function(t){this.closeEventListeners.push(t)},t.prototype.removeCloseEventListener=function(t){this.closeEventListeners.indexOf(t)>-1&&this.closeEventListeners.splice(this.closeEventListeners.indexOf(t),1)},t.prototype.setupResizeObserver=function(){var t=this;"inline"===this.displayMode?window.ResizeObserver&&(this.targetObserver=new ResizeObserver((function(){t.resize(t.target.clientWidth,t.target.clientHeight)})),this.targetObserver.observe(this.target)):"popup"===this.displayMode&&(window.ResizeObserver&&(this.targetObserver=new ResizeObserver(this.onPopupResize),this.targetObserver.observe(this.contentDiv)),window.addEventListener("resize",this.setWindowHeight))},t.prototype.onPopupResize=function(){if(this.contentDiv.clientWidth>0&&this.contentDiv.clientHeight>0){var t=1*this.target.clientWidth/this.target.clientHeight,e=this.contentDiv.clientWidth/t>this.contentDiv.clientHeight?(this.contentDiv.clientHeight-2*this.CANVAS_MARGIN)*t:this.contentDiv.clientWidth-2*this.CANVAS_MARGIN,i=e+2*this.CANVAS_MARGIN<this.contentDiv.clientWidth?this.contentDiv.clientHeight-2*this.CANVAS_MARGIN:(this.contentDiv.clientWidth-2*this.CANVAS_MARGIN)/t;this.resize(e,i)}},t.prototype.setWindowHeight=function(){this.windowHeight=window.innerHeight},t.prototype.setEditingTargetSize=function(){this.editorCanvas.style.width=this.imageWidth+2*this.CANVAS_MARGIN+"px",this.editorCanvas.style.height=this.imageHeight+2*this.CANVAS_MARGIN+"px",r.setAttributes(this.editingTarget,[["width",""+this.imageWidth],["height",""+this.imageHeight]]),this.editingTarget.style.transformOrigin=this.imageWidth/2+"px "+this.imageHeight/2+"px"},t.prototype.resize=function(t,e){this.imageWidth=Math.round(t),this.imageHeight=Math.round(e),this.setEditingTargetSize(),this.cropImage.setAttribute("width",this.paddedImageWidth.toString()),this.cropImage.setAttribute("height",this.paddedImageHeight.toString()),this.cropImage.setAttribute("viewBox","0 0 "+this.paddedImageWidth.toString()+" "+this.paddedImageHeight.toString()),this.cropImageHolder.style.width=this.paddedImageWidth+"px",this.cropImageHolder.style.height=this.paddedImageHeight+"px","popup"!==this.displayMode?this.coverDiv.style.width=this.paddedImageWidth+"px":(this.setTopLeft(),this.positionCropImage()),this.cropLayer.scaleCanvas(this.imageWidth,this.imageHeight),this.applyRotation()},t.prototype.setEditingTarget=function(){r.setAttributes(this.editingTarget,[["href",this.target.src]]),this.setEditingTargetSize()},t.prototype.setTopLeft=function(){var t=this.target.getBoundingClientRect(),e=this.editorCanvas.getBoundingClientRect();this.left=t.left-e.left-this.CANVAS_MARGIN,this.top=t.top-e.top-this.CANVAS_MARGIN},t.prototype.initCropCanvas=function(){this.cropImageHolder=document.createElement("div"),this.cropImageHolder.style.setProperty("touch-action","none"),this.cropImageHolder.style.setProperty("-ms-touch-action","none"),this.cropImage=document.createElementNS("http://www.w3.org/2000/svg","svg"),this.cropImage.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.cropImage.setAttribute("width",this.paddedImageWidth.toString()),this.cropImage.setAttribute("height",this.paddedImageHeight.toString()),this.cropImage.setAttribute("viewBox","0 0 "+this.paddedImageWidth.toString()+" "+this.paddedImageHeight.toString()),this.cropImage.style.pointerEvents="auto",this.cropImageHolder.style.width=this.paddedImageWidth+"px",this.cropImageHolder.style.height=this.paddedImageHeight+"px",this.cropImageHolder.style.transformOrigin="top left",this.positionCropImage(),this.defs=r.createDefs(),this.cropImage.appendChild(this.defs),this.editingTarget=r.createImage([["href",""]]),this.editingTarget.style.transformOrigin=this.imageWidth/2+"px "+this.imageHeight/2+"px",this.editingTargetRotationContainer=r.createGroup(),this.editingTargetRotationScaleContainer=r.createGroup(),this.editingTargetRotationScaleContainer.appendChild(this.editingTarget),this.editingTargetRotationContainer.appendChild(this.editingTargetRotationScaleContainer);var t=r.createTransform();this.editingTargetRotationContainer.transform.baseVal.appendItem(t);var e=r.createTransform();this.editingTargetRotationScaleContainer.transform.baseVal.appendItem(e),this.editingTargetContainer=r.createGroup(),this.editingTargetContainer.style.transform="translate("+this.CANVAS_MARGIN+"px, "+this.CANVAS_MARGIN+"px)",this.editingTargetContainer.appendChild(this.editingTargetRotationContainer),this.cropImage.appendChild(this.editingTargetContainer),this.cropImageHolder.appendChild(this.cropImage),this.editorCanvas.appendChild(this.cropImageHolder)},t.prototype.positionCropImage=function(){this.cropImageHolder.style.top=this.top+"px",this.cropImageHolder.style.left=this.left+"px"},t.prototype.initCropLayer=function(){this.cropRect={x:this.CANVAS_MARGIN,y:this.CANVAS_MARGIN,width:this.imageWidth,height:this.imageHeight},this.cropLayerContainer=r.createGroup(),this.cropImage.appendChild(this.cropLayerContainer),this.cropLayer=new C(this.imageWidth,this.imageHeight,this.CANVAS_MARGIN,this.cropLayerContainer,this.originalWidth,this.originalHeight,this.requiredMinWidth,this.requiredMinHeight),this.cropLayer.onCropChange=this.cropRectChanged,this.cropLayer.numberOfGridLines=this.gridLines,this.cropLayer.isGridVisible=this.isGridVisible,this.cropLayer.cropShadeColor=this.styles.settings.cropShadeColor,this.cropLayer.cropFrameColor=this.styles.settings.cropFrameColor,this.cropLayer.gripColor=this.styles.settings.gripColor,this.cropLayer.gripFillColor=this.styles.settings.gripFillColor,this.cropLayer.zoomToCropEnabled=this.zoomToCropEnabled,this.cropLayer.open(),this.cropLayer.setCropRectangle(this.cropRect),this.zoomToCropEnabled&&this.zoomToCrop()},t.prototype.zoomToCrop=function(){if(this.cropRect){var t=this.cropRect.x-this.CANVAS_MARGIN+this.cropRect.width/2,e=this.cropRect.y-this.CANVAS_MARGIN+this.cropRect.height/2;this.zoomFactor=Math.min(this.imageWidth/this.cropRect.width,this.imageHeight/this.cropRect.height),this.editingTargetContainer&&this.cropLayer&&(this.editingTargetContainer.style.transformOrigin=t+"px "+e+"px",this.editingTargetContainer.style.transform="translate("+(this.imageWidth/2-t+this.CANVAS_MARGIN)+"px,"+(this.imageHeight/2-e+this.CANVAS_MARGIN)+"px) scale("+this.zoomFactor+")",this.cropLayer.zoomFactor=this.zoomFactor)}},t.prototype.unzoomFromCrop=function(){this.zoomFactor=1,this.editingTargetContainer&&this.cropLayer&&(this.editingTargetContainer.style.transformOrigin="center",this.editingTargetContainer.style.transform="translate("+this.CANVAS_MARGIN+"px, "+this.CANVAS_MARGIN+"px) scale(1)",this.cropLayer.zoomFactor=this.zoomFactor)},t.prototype.cropRectChanged=function(t){this.cropRect=t,this.zoomToCropEnabled?this.zoomToCrop():this.cropLayer.zoomFactor=1},t.prototype.attachEvents=function(){window.addEventListener("resize",this.onWindowResize)},t.prototype.overrideOverflow=function(){this.scrollXState=window.scrollX,this.scrollYState=window.scrollY,this.bodyOverflowState=document.body.style.overflow,window.scroll({top:0,left:0}),document.body.style.overflow="hidden"},t.prototype.restoreOverflow=function(){document.body.style.overflow=this.bodyOverflowState,window.scroll({top:this.scrollYState,left:this.scrollXState})},t.prototype.showUI=function(){var t,e;switch(this.addStyles(),"popup"===this.displayMode&&this.overrideOverflow(),this.coverDiv=document.createElement("div"),this.coverDiv.className=this.styles.classNamePrefixBase+" "+this.styles.classNamePrefix,this.coverDiv.style.fontSize="16px",this.displayMode){case"inline":this.coverDiv.style.position="absolute";var i=this.styles.settings.hideTopToolbar?0:this.toolbarHeight,o=this.target.offsetTop>i+this.CANVAS_MARGIN?this.target.offsetTop-(i+this.CANVAS_MARGIN):0;o+=null!==(t=this.uiOffsetTop)&&void 0!==t?t:0;var s=this.target.offsetLeft>this.CANVAS_MARGIN?this.target.offsetLeft-this.CANVAS_MARGIN:0;s+=null!==(e=this.uiOffsetLeft)&&void 0!==e?e:0,this.coverDiv.style.top=o+"px",this.coverDiv.style.left=s+"px",this.coverDiv.style.width=this.target.offsetWidth+this.CANVAS_MARGIN+"px",this.coverDiv.style.zIndex=void 0!==this.styles.settings.zIndex?this.styles.settings.zIndex:"5";break;case"popup":this.coverDiv.style.position="absolute",this.coverDiv.style.top="0px",this.coverDiv.style.left="0px",this.coverDiv.style.width="100vw",this.coverDiv.style.height=window.innerHeight+"px",this.coverDiv.style.backgroundColor="rgba(0, 0, 0, 0.75)",this.coverDiv.style.zIndex=void 0!==this.styles.settings.zIndex?this.styles.settings.zIndex:"1000",this.coverDiv.style.display="flex"}this.targetRoot.appendChild(this.coverDiv),this.uiDiv=document.createElement("div"),this.uiDiv.style.display="flex",this.uiDiv.style.flexDirection="column",this.uiDiv.style.flexGrow="2",this.uiDiv.style.margin="popup"===this.displayMode?this.popupMargin+"px":"0px",this.uiDiv.style.border="0px",this.coverDiv.appendChild(this.uiDiv),this.addToolbars(),this.uiDiv.appendChild(this.topToolbar.getUI()),this.contentDiv=document.createElement("div"),this.contentDiv.style.display="flex",this.contentDiv.style.alignItems="center",this.contentDiv.style.flexDirection="row",this.contentDiv.style.flexGrow="2",this.contentDiv.style.flexShrink="1",this.contentDiv.style.overflow="hidden",this.contentDiv.style.backgroundColor=this.styles.settings.canvasBackgroundColor,"popup"===this.displayMode&&(this.contentDiv.style.maxHeight="calc(100vh - "+(2*this.popupMargin+2*this.toolbarHeight)+"px)",this.contentDiv.style.maxWidth="calc(100vw - "+2*this.popupMargin+"px)"),this.uiDiv.appendChild(this.contentDiv),this.editorCanvas=document.createElement("div"),this.editorCanvas.style.flexGrow="2",this.editorCanvas.style.flexShrink="1",this.editorCanvas.style.position="relative",this.editorCanvas.style.overflow="hidden",this.editorCanvas.style.display="flex","popup"===this.displayMode&&(this.editorCanvas.style.alignItems="center",this.editorCanvas.style.justifyContent="center"),this.editorCanvas.style.pointerEvents="none",this.contentDiv.appendChild(this.editorCanvas),this.processingUi=document.createElement("div"),this.processingUi.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="50px" height="50px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">\n    <rect x="19" y="19" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0s" calcMode="discrete"></animate>\n    </rect><rect x="40" y="19" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0.125s" calcMode="discrete"></animate>\n    </rect><rect x="61" y="19" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0.25s" calcMode="discrete"></animate>\n    </rect><rect x="19" y="40" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0.875s" calcMode="discrete"></animate>\n    </rect><rect x="61" y="40" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0.375s" calcMode="discrete"></animate>\n    </rect><rect x="19" y="61" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0.75s" calcMode="discrete"></animate>\n    </rect><rect x="40" y="61" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0.625s" calcMode="discrete"></animate>\n    </rect><rect x="61" y="61" width="20" height="20" fill="#eeeeee">\n      <animate attributeName="fill" values="#888888;#eeeeee;#eeeeee" keyTimes="0;0.125;1" dur="1s" repeatCount="indefinite" begin="0.5s" calcMode="discrete"></animate>\n    </rect>\n    </svg>',this.processingUi.style.position="absolute",this.processingUi.style.width="100%",this.processingUi.style.height="100%",this.processingUi.style.backgroundColor="rgba(0,0,0,0.3)",this.processingUi.style.alignItems="center",this.processingUi.style.justifyContent="center",this.processingUi.style.display="none",this.editorCanvas.appendChild(this.processingUi),this.uiDiv.appendChild(this.bottomToolbar.getUI()),this.straightener.angle=this.rotationAngle},t.prototype.addToolbars=function(){this.addTopToolbar(),this.addBottomToolbar()},t.prototype.addTopToolbar=function(){var t=this;this.topToolbar=new c,this.topToolbar.display=this.styles.settings.hideTopToolbar?"none":"",this.topToolbar.className=this.toolbarStyleClass.name,this.topToolbar.colorsClassName=this.styles.settings.toolbarStyleColorsClassName?this.styles.settings.toolbarStyleColorsClassName:this.toolbarStyleColorsClass.name,this.topToolbar.fadeInClassName=this.styles.fadeInAnimationClassName,this.topToolbar.blockClassName=this.toolbarBlockStyleClass.name,this.topToolbar.buttonClassName=this.toolbarButtonStyleClass.name,this.topToolbar.buttonColorsClassName=this.styles.settings.toolbarButtonStyleColorsClassName?this.styles.settings.toolbarButtonStyleColorsClassName:this.toolbarButtonStyleColorsClass.name,this.topToolbar.buttonActiveColorsClassName=this.styles.settings.toolbarActiveButtonStyleColorsClassName?this.styles.settings.toolbarActiveButtonStyleColorsClassName:this.toolbarActiveButtonStyleColorsClass.name;var e=new d;e.minWidth=3*this.toolbarHeight+"px",this.topToolbar.addButtonBlock(e);var i=[];this.aspectRatios.forEach((function(e){var o=new g(y.getIcon(e.horizontal,e.vertical),0===e.horizontal&&0===e.vertical?"FREE":e.horizontal+":"+e.vertical);o.onClick=function(){return t.ratioButtonClicked(e)},i.push(o)})),this.aspectRatioButton=new v(y.getIcon(0,0),"Aspect ratio",i),this.aspectRatioButton.dropdownClassName=this.toolbarDropdownStyleClass.name,this.aspectRatioButton.dropdownColorsClassName=this.styles.settings.toolbarDropdownStyleColorsClassName?this.styles.settings.toolbarDropdownStyleColorsClassName:this.toolbarDropdownStyleColorsClass.name,e.addButton(this.aspectRatioButton),this.aspectRatios.length<2&&this.aspectRatioButton.hide();var o=new g('<svg viewBox="0 0 24 24"><path d="M10 4v4h4V4h-4m6 0v4h4V4h-4m0 6v4h4v-4h-4m0 6v4h4v-4h-4m-2 4v-4h-4v4h4m-6 0v-4H4v4h4m0-6v-4H4v4h4m0-6V4H4v4h4m2 6h4v-4h-4v4M4 2h16a2 2 0 012 2v16a2 2 0 01-2 2H4c-1.08 0-2-.9-2-2V4a2 2 0 012-2z"/></svg>',"Toggle grid");o.isActive=this.isGridVisible,o.onClick=function(){t.isGridVisible=!t.isGridVisible,o.isActive=t.isGridVisible},e.addButton(o);var s=new g('<svg viewBox="0 0 24 24"><path d="M15.5 14l5 5-1.5 1.5-5-5v-.79l-.27-.28A6.471 6.471 0 019.5 16 6.5 6.5 0 013 9.5 6.5 6.5 0 019.5 3 6.5 6.5 0 0116 9.5c0 1.61-.59 3.09-1.57 4.23l.28.27h.79m-6 0C12 14 14 12 14 9.5S12 5 9.5 5 5 7 5 9.5 7 14 9.5 14m2.5-4h-2v2H9v-2H7V9h2V7h1v2h2v1z"/></svg>',"Zoom to selection");if(s.isActive=this.zoomToCropEnabled,s.onClick=function(){t.zoomToCropEnabled=!t.zoomToCropEnabled,s.isActive=t.zoomToCropEnabled},e.addButton(s),!n.isLicensed){var r=new u;this.topToolbar.addElementBlock(r);var a=document.createElement("div");a.className=this.topToolbar.buttonClassName+" "+this.topToolbar.buttonColorsClassName;var h=document.createElement("a");h.style.color="currentColor",h.href="https://markerjs.com/products/cropro",h.target="_blank",h.innerHTML='<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2"><path d="M16.326 14.895c0 1.126-.558 1.688-1.673 1.688H9.931c-1.116 0-1.674-.562-1.674-1.688V3.733c0-1.126.558-1.688 1.674-1.688h4.722c1.115 0 1.673.562 1.673 1.688v2.899h-1.957V3.793h-4.124v11.042h4.124v-3.242h1.957v3.302z" fill="currentColor"/><path d="M15.94 7.364a.783.783 0 00-1.065-.304l-11.01 6.126a.783.783 0 00-.303 1.065l4.498 8.085a.783.783 0 001.065.304l11.01-6.126a.783.783 0 00.303-1.065L15.94 7.364zM5.311 14.173l3.737 6.718 9.641-5.364-3.737-6.718-9.641 5.364z" fill="#eef762"/></svg>',a.appendChild(h),r.addElement(a)}var l=new d;l.minWidth=3*this.toolbarHeight+"px",l.contentAlign="end",this.topToolbar.addButtonBlock(l);var p=new g('<svg viewBox="0 0 24 24"><path d="M9 20.42l-6.21-6.21 2.83-2.83L9 14.77l9.88-9.89 2.83 2.83L9 20.42z"/></svg>',"OK");p.onClick=function(){t.processingUi.style.display="flex",setTimeout(t.startRenderAndClose,100)},l.addButton(p),this.styles.settings.toolbarOkButtonStyleColorsClassName&&(p.colorsClassName=this.styles.settings.toolbarOkButtonStyleColorsClassName);var m=new g('<svg viewBox="0 0 24 24"><path d="M20 6.91L17.09 4 12 9.09 6.91 4 4 6.91 9.09 12 4 17.09 6.91 20 12 14.91 17.09 20 20 17.09 14.91 12 20 6.91z"/></svg>',"Close");m.onClick=this.close,l.addButton(m),this.styles.settings.toolbarCloseButtonStyleColorsClassName&&(m.colorsClassName=this.styles.settings.toolbarCloseButtonStyleColorsClassName)},t.prototype.addBottomToolbar=function(){var t=this;this.bottomToolbar=new c,this.bottomToolbar.display=this.styles.settings.hideBottomToolbar?"none":"",this.bottomToolbar.className=this.toolbarStyleClass.name,this.bottomToolbar.colorsClassName=this.styles.settings.toolbarStyleColorsClassName?this.styles.settings.toolbarStyleColorsClassName:this.toolbarStyleColorsClass.name,this.bottomToolbar.fadeInClassName=this.styles.fadeInAnimationClassName,this.bottomToolbar.blockClassName=this.toolbarBlockStyleClass.name,this.bottomToolbar.buttonClassName=this.toolbarButtonStyleClass.name,this.bottomToolbar.buttonColorsClassName=this.styles.settings.toolbarButtonStyleColorsClassName?this.styles.settings.toolbarButtonStyleColorsClassName:this.toolbarButtonStyleColorsClass.name,this.bottomToolbar.buttonActiveColorsClassName=this.styles.settings.toolbarActiveButtonStyleColorsClassName?this.styles.settings.toolbarActiveButtonStyleColorsClassName:this.toolbarActiveButtonStyleColorsClass.name;var e=new d;e.minWidth=2*this.toolbarHeight+"px",this.bottomToolbar.addButtonBlock(e);var i=new g('<svg viewBox="0 0 24 24"><path d="M13 4.07V1L8.45 5.55 13 10V6.09c2.84.48 5 2.94 5 5.91s-2.16 5.43-5 5.91v2.02c3.95-.49 7-3.85 7-7.93s-3.05-7.44-7-7.93M7.1 18.32c1.16.9 2.51 1.44 3.9 1.61V17.9c-.87-.15-1.71-.49-2.46-1.03L7.1 18.32M6.09 13H4.07c.17 1.39.72 2.73 1.62 3.89l1.41-1.42c-.52-.75-.87-1.59-1.01-2.47m1.02-4.47L5.7 7.11C4.8 8.27 4.24 9.61 4.07 11h2.02c.14-.87.49-1.72 1.02-2.47z"/></svg>',"Rotate left");i.onClick=this.rotateLeftButtonClicked,e.addButton(i);var o=new g('<svg viewBox="0 0 24 24"><path d="M16.89 15.5l1.42 1.39c.9-1.16 1.45-2.5 1.62-3.89h-2.02c-.14.87-.48 1.72-1.02 2.5M13 17.9v2.02c1.39-.17 2.74-.71 3.9-1.61l-1.44-1.44c-.75.54-1.59.89-2.46 1.03m6.93-6.9a7.906 7.906 0 00-1.62-3.89l-1.42 1.42c.54.75.88 1.6 1.02 2.47m-2.36-5.45L11 1v3.07C7.06 4.56 4 7.92 4 12s3.05 7.44 7 7.93v-2.02c-2.84-.48-5-2.94-5-5.91s2.16-5.43 5-5.91V10l4.55-4.45z"/></svg>',"Rotate right");o.onClick=this.rotateRightButtonClicked,e.addButton(o);var s=new u;s.className=this.toolbarStraightenerBlockStyleClass.name,this.bottomToolbar.addElementBlock(s),this.straightener=new b("Straighten"),this.straightener.className=this.toolbarStraightenerStyleClass.name,this.straightener.colorsClassName=this.styles.settings.toolbarStraightenerColorsClassName?this.styles.settings.toolbarStraightenerColorsClassName:this.toolbarStraightenerStyleColorsClass.name,this.straightener.onAngleChange=function(e){t.rotateBy(e),t.straightener.angle=t.rotationAngle},s.addElement(this.straightener.getUI());var n=new d;n.minWidth=2*this.toolbarHeight+"px",n.contentAlign="end",this.bottomToolbar.addButtonBlock(n);var r=new g('<svg viewBox="0 0 24 24"><path d="M15 21h2v-2h-2m4-10h2V7h-2M3 5v14c0 1.1.9 2 2 2h4v-2H5V5h4V3H5c-1.1 0-2 .9-2 2m16-2v2h2c0-1.1-.9-2-2-2m-8 20h2V1h-2m8 16h2v-2h-2M15 5h2V3h-2m4 10h2v-2h-2m0 10c1.1 0 2-.9 2-2h-2z"/></svg>',"Flip horizontal");r.onClick=this.flipHorizontallyButtonClicked,n.addButton(r);var a=new g('<svg viewBox="0 0 24 24"><path d="M3 15v2h2v-2m10 4v2h2v-2m2-16H5c-1.1 0-2 .9-2 2v4h2V5h14v4h2V5c0-1.1-.9-2-2-2m2 16h-2v2c1.1 0 2-.9 2-2M1 11v2h22v-2M7 19v2h2v-2m10-4v2h2v-2m-10 4v2h2v-2M3 19c0 1.1.9 2 2 2v-2z"/></svg>',"Flip vertical");a.onClick=this.flipVerticallyButtonClicked,n.addButton(a)},t.prototype.ratioButtonClicked=function(t){this.aspectRatio=t,this.applyAspectRatio(),this.aspectRatioButton.hideDropdown()},t.prototype.applyAspectRatio=function(){this.setCropLayerAspectRatio(),this.aspectRatioButton.icon=y.getIcon(this.aspectRatio.horizontal,this.aspectRatio.vertical)},t.prototype.setCropLayerAspectRatio=function(){this.cropLayer&&(this.aspectRatio&&0!==this.aspectRatio.horizontal&&0!==this.aspectRatio.vertical?this.cropLayer.aspectRatio=new m(this.aspectRatio.horizontal,this.aspectRatio.vertical):this.cropLayer.aspectRatio=void 0)},t.prototype.closeUI=function(){"popup"===this.displayMode&&this.restoreOverflow(),this.targetRoot.removeChild(this.coverDiv)},t.prototype.getState=function(){return{width:this.imageWidth,height:this.imageHeight,rotationAngle:this.rotationAngle,flippedHorizontally:this.flippedHorizontally,flippedVertically:this.flippedVertically,cropRect:Object.assign({},this.cropRect)}},t.prototype.restoreState=function(t){if(t){var e=this.cropLayer.getRescaledRect(t.width,t.height,this.imageWidth,this.imageHeight,t.cropRect,this.CANVAS_MARGIN);this.cropLayer.setCropRectangle(e),this.cropRectChanged(e),this.flippedHorizontally=t.flippedHorizontally,this.flippedVertically=t.flippedVertically,this.applyFlip(),this.rotationAngle=t.rotationAngle,this.applyRotation()}},t.prototype.renderState=function(t){this.displayMode="inline",this.isOpen||this.show(),this.restoreState(t),this.startRenderAndClose()},t.prototype.clientToLocalCoordinates=function(t,e){var i=this.cropImage.getBoundingClientRect();return{x:t-i.left,y:e-i.top}},t.prototype.onWindowResize=function(){this.positionUI()},t.prototype.positionUI=function(){switch(this.setTopLeft(),this.displayMode){case"inline":var t=this.target.offsetTop>this.toolbarHeight?this.target.offsetTop-this.toolbarHeight:0;this.coverDiv.style.top=t+"px",this.coverDiv.style.left=this.target.offsetLeft.toString()+"px";break;case"popup":this.coverDiv.style.top="0px",this.coverDiv.style.left="0px",this.coverDiv.style.width="100vw",this.coverDiv.style.height=this.windowHeight+"px",this.contentDiv.style.maxHeight="calc(100vh - "+(2*this.popupMargin+2*this.toolbarHeight)+"px)"}this.positionCropImage()},t.prototype.rotateLeftButtonClicked=function(){console.log(this.cropImage.getBoundingClientRect());var t=this.rotationAngle-90;this.rotationAngle%90!=0&&(t+=this.rotationAngle>=0?90-this.rotationAngle%90:-this.rotationAngle%90),this.rotateTo(t),this.cropRect.x=(this.cropImage.getBoundingClientRect().width-this.editingTarget.getBoundingClientRect().width)/2,this.cropRect.y=(this.cropImage.getBoundingClientRect().height-this.editingTarget.getBoundingClientRect().height)/2,this.cropRect.width=this.editingTarget.getBoundingClientRect().width,this.cropRect.height=this.editingTarget.getBoundingClientRect().height,this.cropLayer.setCropRectangle(this.cropRect)},t.prototype.rotateRightButtonClicked=function(){console.log(this.cropImage.getBoundingClientRect());var t=this.rotationAngle+90;this.rotationAngle%90!=0&&(t-=this.rotationAngle>=0?this.rotationAngle%90:90+this.rotationAngle%90),this.rotateTo(t),this.cropRect.x=(this.cropImage.getBoundingClientRect().width-this.editingTarget.getBoundingClientRect().width)/2,this.cropRect.y=(this.cropImage.getBoundingClientRect().height-this.editingTarget.getBoundingClientRect().height)/2,this.cropRect.width=this.editingTarget.getBoundingClientRect().width,this.cropRect.height=this.editingTarget.getBoundingClientRect().height,this.cropLayer.setCropRectangle(this.cropRect)},t.prototype.rotateTo=function(t){t=(t=t>180?t-360:t)<=-180?t+360:t,this.rotationAngle=t,this.applyRotation()},t.prototype.rotateBy=function(t){this.rotateTo((this.rotationAngle+t)%360)},t.prototype.applyRotation=function(){var t=this.zoomToCropEnabled;this.zoomToCropEnabled=!1,this.editingTargetRotationScaleContainer.style.transformOrigin=this.imageWidth/2+"px "+this.imageHeight/2+"px",this.editingTargetRotationScaleContainer.style.transform="scale(1)";var e=this.editingTargetRotationContainer.transform.baseVal.getItem(0);e.setRotate(this.rotationAngle,this.imageWidth/2,this.imageHeight/2),this.editingTargetRotationContainer.transform.baseVal.replaceItem(e,0);var i=this.editingTarget.getBoundingClientRect();this.scaleFactor=Math.min(this.imageWidth/i.width,this.imageHeight/i.height),this.editingTargetRotationScaleContainer.style.transform="scale("+this.scaleFactor+")",this.zoomToCropEnabled=t},t.prototype.flipHorizontallyButtonClicked=function(){this.flippedHorizontally=!this.flippedHorizontally,this.applyFlip()},t.prototype.flipVerticallyButtonClicked=function(){this.flippedVertically=!this.flippedVertically,this.applyFlip()},t.prototype.applyFlip=function(){this.editingTarget.style.transform="scale("+(this.flippedHorizontally?-1:1)+","+(this.flippedVertically?-1:1)+")"},t.prototype.startRenderAndClose=function(){return o(this,void 0,void 0,(function(){var t,e;return s(this,(function(i){switch(i.label){case 0:return[4,this.render()];case 1:return t=i.sent(),e=this.getState(),this.renderEventListeners.forEach((function(i){return i(t,e)})),this.close(!0),[2]}}))}))},t.prototype.render=function(){return o(this,void 0,void 0,(function(){var t,e,i;return s(this,(function(o){switch(o.label){case 0:return(t=new w).naturalSize=this.renderAtNaturalSize,t.imageType=this.renderImageType,t.imageQuality=this.renderImageQuality,t.width=this.renderWidth,t.height=this.renderHeight,t.maxSize=this.renderMaxSize,this.unzoomFromCrop(),r.setAttributes(this.cropLayerContainer,[["display","none"]]),[4,t.rasterize(this.cropImage,this.target,{x:this.cropRect.x,y:this.cropRect.y,width:this.cropRect.width,height:this.cropRect.height},this.CANVAS_MARGIN,this.rotationAngle,this.scaleFactor*(this.flippedHorizontally?-1:1),this.scaleFactor*(this.flippedVertically?-1:1))];case 1:return o.sent(),[4,t.rasterize(this.cropImage,this.target,{x:this.cropRect.x,y:this.cropRect.y,width:this.cropRect.width,height:this.cropRect.height},this.CANVAS_MARGIN,this.rotationAngle,this.scaleFactor*(this.flippedHorizontally?-1:1),this.scaleFactor*(this.flippedVertically?-1:1))];case 2:return e=o.sent(),this.processingUi.style.display="none",e.state=this.getState(),(this.rotationAngle<0||this.rotationAngle>0)&&(i=this.cropLayer.getgridContainer(),this.cropLayer.getCropRectElement(),e.state.cropRect.x=i.getBoundingClientRect().x-this.editingTarget.getBoundingClientRect().x<0?0:i.getBoundingClientRect().x-this.editingTarget.getBoundingClientRect().x,e.state.cropRect.y=i.getBoundingClientRect().y-this.editingTarget.getBoundingClientRect().y<0?0:i.getBoundingClientRect().y-this.editingTarget.getBoundingClientRect().y,e.imageHeight=this.editingTarget.getBoundingClientRect().height,e.imageWidth=this.editingTarget.getBoundingClientRect().width),[2,e]}}))}))},t.prototype.addStyles=function(){this.toolbarStyleClass=this.styles.addClass(new l("toolbar","\n      width: "+("inline"===this.displayMode?"100%":"calc(100vw - "+2*this.popupMargin+"px)")+";\n      flex-shrink: 0;\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;      \n      height: "+this.toolbarHeight+"px;\n      box-sizing: content-box;\n      overflow: hidden;\n      line-height: 0px;\n    ")),this.toolbarStyleColorsClass=this.styles.addClass(new l("toolbar_colors","\n      background-color: "+this.styles.settings.toolbarBackgroundColor+";\n    ")),this.toolbarBlockStyleClass=this.styles.addClass(new l("toolbar-block","\n        display: flex;\n        align-items: center;\n        box-sizing: content-box;\n    "));var t=this.toolbarHeight/4;this.toolbarButtonStyleClass=this.styles.addClass(new l("toolbar_button","\n      display: inline-block;\n      width: "+(this.toolbarHeight-2*t)+"px;\n      height: "+(this.toolbarHeight-2*t)+"px;\n      padding: "+t+"px;\n      cursor: default;\n      user-select: none;\n      box-sizing: content-box;\n    ")),this.toolbarButtonStyleColorsClass=this.styles.addClass(new l("toolbar_button_colors","\n      color: "+this.styles.settings.toolbarColor+";\n      fill: currentColor;\n    ")),this.toolbarActiveButtonStyleColorsClass=this.styles.addClass(new l("toolbar_active_button","\n      color: "+this.styles.settings.toolbarColor+";\n      fill: currentColor;\n      background-color: "+this.styles.settings.toolbarBackgroundActiveColor+"\n    ")),this.styles.addRule(new h("."+this.toolbarButtonStyleClass.name+" svg","\n      height: "+this.toolbarHeight/2+"px;\n    ")),this.styles.addRule(new h("."+this.toolbarButtonStyleColorsClass.name+":hover","\n        background-color: "+this.styles.settings.toolbarBackgroundHoverColor+"\n    ")),this.toolbarDropdownStyleClass=this.styles.addClass(new l("toolbar_dropdown","\n      position: absolute;\n      max-width: "+4*this.toolbarHeight+"px;\n      z-index: 20;\n      white-space: normal;\n      box-sizing: content-box;\n      box-shadow: 3px 3px rgba(33, 33, 33, 0.1);\n      margin: "+("inline"===this.displayMode?"0":this.popupMargin)+"px;\n      line-height: 0px;\n    ")),this.toolbarDropdownStyleColorsClass=this.styles.addClass(new l("toolbar_dropdown_colors","\n      background-color: "+this.styles.settings.toolbarBackgroundColor+";\n    ")),this.toolbarStraightenerBlockStyleClass=this.styles.addClass(new l("toolbar_straightener_block","\n      display: flex;\n      overflow: hidden;\n      justify-content: center;\n      -webkit-mask-image: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);\n    ")),this.toolbarStraightenerStyleClass=this.styles.addClass(new l("toolbar_straightener","\n      display: flex;\n      overflow: hidden;\n      justify-content: center;\n      height: "+(this.toolbarHeight-2*t)+"px;\n      padding: "+t+"px;\n      cursor: default;\n      user-select: none;\n      box-sizing: content-box;\n    ")),this.toolbarStraightenerStyleColorsClass=this.styles.addClass(new l("toolbar_straightener_colors","\n      color: "+this.styles.settings.toolbarColor+";\n      fill: currentColor;\n    "))},t.instanceCounter=0,t}();t.Activator=n,t.AspectRatio=m,t.CropArea=R,t.StyleManager=a,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=cropro.js.map
