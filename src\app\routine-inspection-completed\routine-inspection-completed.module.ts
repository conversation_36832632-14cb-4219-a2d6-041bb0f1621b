import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RoutineInspectionCompletedPageRoutingModule } from './routine-inspection-completed-routing.module';

import { RoutineInspectionCompletedPage } from './routine-inspection-completed.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RoutineInspectionCompletedPageRoutingModule,
    FontAwesomeModule,
    FooterComponent
  ],
  declarations: [RoutineInspectionCompletedPage]
})
export class RoutineInspectionCompletedPageModule {}
