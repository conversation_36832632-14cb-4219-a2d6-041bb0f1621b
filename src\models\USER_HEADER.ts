import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class USER_HEADER extends DATA_STRUCTURE { //13
    ID: string | undefined;
    CONATCT_ID: string | undefined;
    USERNAME: string | undefined;
    LAST_NAME: string | undefined;
    FIRST_NAME: string | undefined;
    NAME: string | undefined;
    EMAIL: string | undefined;
    USER_TYPE: string | undefined;
    LINE_TRACKER_TYPE: string | undefined;
    INSIGHT_AI: string | undefined;
    EXTFLD1: string | undefined; //! using this field to decide whether to show option to user to show/hide the Tiled images in insight AI rope Detection page
    EXTFLD2: string | undefined;
    EXTFLD3: string | undefined; //! using this field to set the maximum size limit on uploading images for inspection obseervations
}