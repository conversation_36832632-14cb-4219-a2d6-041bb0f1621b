<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="back()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Resources' | translate}}</ion-title>
    <ion-buttons color="primary" slot="end" (click)="showResourceDownloadAlert(false)" class="help-button-style">
      <fa-icon class="icon-style-help" icon="download"></fa-icon>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="card-style">
    <!-- <ion-card-header class="card-header-style">
      {{'Abrasion Guides' | translate}}
    </ion-card-header> -->
    <div style="padding:15px 10px 0px 12px">
      <ion-card-content style="padding: 0px;border: solid #0057b3 2px; border-radius: 5px;">
        <div>
          <ion-select class="resourceSelect" #countryList style="padding-left: 5px;" placeholder="{{'Abrasion Guides' | translate}}" labelPlacement="stacked" interface="popover"
            (ionChange)="pageSelectedAbrationGuide()" [(ngModel)]="selection">
            <ion-select-option value="External">External Abrasion, 12-Strand Class II - <span style="color: darkgray;">PDF</span></ion-select-option>
            <ion-select-option value="Internal">Internal Abrasion, 12-Strand Class II - <span style="color: darkgray;">PDF</span></ion-select-option>
            <ion-select-option value="Tenex">External Abrasion, 12-Strand Class I -Tenex™ - <span style="color: darkgray;">PDF</span></ion-select-option>
            <ion-select-option value="Abrasion">Understanding Abrasion Document</ion-select-option>
          </ion-select>
        </div>
      </ion-card-content>
    </div>

    <!-- <ion-card-content class="card-content-style">
      <div>
        <ion-row>
          <div class="col6 border-style" (click)="openPdfExternal()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'External' | translate}}
              </div>
            </div>
          </div>
          <div class="col6 border-style" (click)="openPdfInternal()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Internal' | translate}}
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content> -->
  </ion-card>
  <ion-card class="card-style">
    <!-- <ion-card-header class="card-header-style">
      {{'Inspection Guides' | translate}}
    </ion-card-header> -->
    <div style="padding:15px 10px 0px 12px">
      <ion-card-content style="padding: 0px;border: solid #0057b3 2px; border-radius: 5px;">
        <div>
          <ion-select class="resourceSelect" style="padding-left: 5px;" placeholder="{{'Inspection Guides' | translate}}" labelPlacement="stacked" interface="popover"
            (ionChange)="pageSelectedInspectionGuide()" [(ngModel)]="selection">
            <ion-select-option value="SingleBraid">Single braid - <span style="color: darkgray;">PDF</span></ion-select-option>
            <ion-select-option value="DoubleBraid">Double braid - <span style="color: darkgray;">PDF</span></ion-select-option>
            <ion-select-option value="Inspection">Inspection</ion-select-option>
            <ion-select-option value="Retirement">Inspection & Retirement Criteria Development - <span style="color: darkgray;">PDF</span></ion-select-option>
            <ion-select-option value="LocalizedDamage">Localized Damage Assessment 12-Strand Class 2 Ropes - <span style="color: darkgray;">PDF</span>
            </ion-select-option>
            <ion-select-option value="RopeMeasurement">Rope Measurement - <span style="color: darkgray;">PDF</span></ion-select-option>
            <ion-select-option value="EffectOfTwist">The Effect of Twist on Braided Ropes - <span style="color: darkgray;">PDF</span></ion-select-option>
          </ion-select>
        </div>
      </ion-card-content>
    </div>
    <!-- <ion-card-content class="card-content-style">
      <div>
        <ion-row>
          <div class="col6 border-style" (click)="openPdfSinglebraid()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Single braid' | translate}}
              </div>
            </div>
          </div>
          <div class="col6 border-style" (click)="openPdfDoublebraid()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Double braid' | translate}}
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content> -->
  </ion-card>
  <ion-card class="card-style">
    <!-- <ion-card-header class="card-header-style">
      {{'Splice Instructions' | translate}}
    </ion-card-header> -->
    <div style="padding:10px 0px 0px 0px">
    <ion-card-content class="card-content-style">
      <div (click)="openSpliceInstructions()">
        <ion-row style="border: solid #0057b3 2px;border-radius: 5px;">
          <div style="padding-left: 5px;">
            <div style="line-height:unset !important;">
              <div
                style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important; color: #000000;">
                {{'Splice Instructions' | translate}}
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content>
  </div>
  </ion-card>

  <ion-card class="card-style">
    <!-- <ion-card-header class="card-header-style">
      {{'Technical Bulletins' | translate}}
    </ion-card-header> -->
    <ion-card-content class="card-content-style">
      <div>
        <ion-row style="border: solid #0057b3 2px;border-radius: 5px;">
          <div (click)="presentModal()" style="padding-left: 5px; width: 100%;">
            <div style="line-height:unset !important; width: 100%; display:block;">
              <div
                style="text-align: left !important; padding-top: 10px!important; padding-bottom: 10px!important; display: inline-block; width: 96%; color: #000000;">
                {{'Technical Bulletins' | translate}}
              </div>
              <div item-right style="display:inline-block; color: #000000;">
                <p class="drop-down-arrow">
                  <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                </p>
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content>



    <!-- <div style="padding:15px 10px 0px 17px">
      <ion-card-content style="padding: 0px;">
        <div>
        <ion-select style="padding-left: 0px;" placeholder="Abrasion Guide" interface="popover" (ionChange)="paseSelectedTechnicalGuide()" [(ngModel)]="selection">
          <ion-select-option value="FAQ" >AmSteel®-Blue Frequently Asked Questions</ion-select-option>
          <ion-select-option value="COF" >Coefficient of Friction (CoF)</ion-select-option>
          <ion-select-option value="DPX" >DPX™ - Innovative Fiber Technology</ion-select-option>
          <ion-select-option value="ElasticStiffness" >Elastic Stiffness</ion-select-option>
          <ion-select-option value="ClosedRoller" >HMPE Ropes and Chocks - Closed vs. Roller</ion-select-option>
          <ion-select-option value="ProductionProcess" >HMPE Post-Production Process</ion-select-option>
          <ion-select-option value="DesignPerformance" >HMPE Rope: Design vs. Performance</ion-select-option>
          <ion-select-option value="ProperHandlingTechniques" >Proper Handling techniques for Samson 8-Strand High Performance Ropes on   H-Bitts</ion-select-option>
          <ion-select-option value="RopeMeasurementTec" >Rope Measurement</ion-select-option>
          <ion-select-option value="SurfacePreparation" >Surface Preparation for Synthetic Ropes</ion-select-option>
          <ion-select-option value="TypeApprovals" >Type Approvals and Product Certifications</ion-select-option>
          <ion-select-option value="UnderstandingCreep" >Understanding Creep</ion-select-option>
          <ion-select-option value="HowColdCanYouGo" >How Cold Can You Go?</ion-select-option>
          <ion-select-option value="Mooring" >Mooring in High-Temperature Climates</ion-select-option>
          <ion-select-option value="MooringLineRopeEndIdentification">Multi-Color Mooring Line Rope End Identification</ion-select-option>
          <ion-select-option value="TugBoatMessengerLineAttachment" >Tug Boat Messenger Line Attachment</ion-select-option>
          <ion-select-option value="ASyntheticEmergencyTowOffPendant" >VULCAN: A Synthetic Emergency Tow-Off Pendant</ion-select-option>
          <ion-select-option value="RopeCare" >Rope Care</ion-select-option>
          <ion-select-option value="PerformanceResults" >MP-1 Performance Results</ion-select-option>
          <ion-select-option value="RopeUsersManual" >Rope Users Manual</ion-select-option>
        </ion-select>
        </div>
      </ion-card-content>
    </div> -->
  </ion-card>

  <ion-card class="card-style">
    <ion-card-content class="card-content-style">
    <div>
        <ion-row style="border: solid #0057b3 2px;border-radius: 5px;">
          <div style="padding-left: 5px; width: 100%;" *ngIf="isUserEnabledForInsightAI">
              <ion-select class="resourceSelect" style="padding-left: 5px;" placeholder="{{'User Guides' | translate}}" labelPlacement="stacked" interface="popover" (ionChange)="pageSelectedUserGuides()" [(ngModel)]="selection">
                <ion-select-option value="UserGuide">User Guide</ion-select-option>
                <ion-select-option value="InsightAIQuickInspectGuide">Insight AI Quick Inspection Guide</ion-select-option>
                <ion-select-option value="InsightAIDetailedInspectionGuide">Insight AI Detailed Inspection Guide</ion-select-option>
                <ion-select-option value="InsightAIRoutineInspectionGuide">Insight AI Routine Inspection Guide<span style="color: darkgray;">PDF</span></ion-select-option>
              </ion-select>
          </div>
          <div (click)="openUserGuide()" style="padding-left: 5px; width: 100%;" *ngIf="!isUserEnabledForInsightAI || isUserEnabledForInsightAI==null">
            <div style="line-height:unset !important; width: 100%; display:block;">
              <div
                style="text-align: left !important; padding-top: 10px!important; padding-bottom: 10px!important; display: inline-block; width: 96%; color: #000000;">
                {{'User Guide' | translate}}
              </div>
              <div item-right style="display:inline-block; color: #000000;">
                <p class="drop-down-arrow">
                  <fa-icon class="icon-style" icon="sort-down"></fa-icon>
                </p>
              </div>
            </div>
          </div>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card>

</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>