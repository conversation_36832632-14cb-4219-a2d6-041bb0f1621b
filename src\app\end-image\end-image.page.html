<ion-header>
  <ion-toolbar>
    <ion-title> {{ 'Add' | translate: { configType: ( configTitle | translate) } }} </ion-title>
    <ion-buttons slot="start" *ngIf="!isLoggedInUserEmployee">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button color="primary" (click)="helpService.switchMode(); disableFormFields();" class="help-button-style">
        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
      </ion-button>
      <ion-button (click)="closeEndImageModal()" size="large" *ngIf="isLoggedInUserEmployee">close</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:15px 10px 10px 17px">
    <div *ngIf='helpService.helpMode'>
      <p style="margin-bottom: 0px !important;">{{'END_CONFIG_DESCRIPTION' | translate}} <span
          style="color:rgb(221, 82, 82);font-size:15px;"> * </span></p>
      <ion-radio-group [(ngModel)]="ropeEnd" (ionChange)="radioChanged()">
        <ion-row class="ion-radio-row">
          <ion-col>
            <ion-item class='ion-radio-item-style'>
              <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="A" [disabled]='true'>{{'A' | translate}}</ion-radio>
              <!-- <ion-label>{{'A' | translate}}</ion-label> -->
            </ion-item>
          </ion-col>

          <ion-col>
            <ion-item class='ion-radio-item-style'>
              <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="B" [disabled]='true'>{{'B' | translate}}</ion-radio>
              <!-- <ion-label>{{'B' | translate}}</ion-label> -->
            </ion-item>
          </ion-col>

          <ion-col>
            <ion-item class='ion-radio-item-style'>
              <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="other" [disabled]='true'>{{'Other' | translate}}</ion-radio>
              <!-- <ion-label>{{'Other' | translate}}</ion-label> -->
            </ion-item>
          </ion-col>
        </ion-row>
      </ion-radio-group>
      <mat-form-field style="width:100%" *ngIf="ropeEnd=='other'">
        <input matInput type="text" [(ngModel)]="endDescription" [required] [disabled]='true'
          placeholder="{{'Enter end description' | translate}}" step="any">
      </mat-form-field>
    </div>
    <div *ngIf='!helpService.helpMode'>
      <p style="margin-bottom: 0px !important;">{{'END_CONFIG_DESCRIPTION' | translate}} <span
          style="color:rgb(221, 82, 82);font-size:15px;"> * </span></p>
      <ion-radio-group [(ngModel)]="ropeEnd" (ionChange)="resetRopeEnd($event)">
        <ion-row class="ion-radio-row" (ionChange)="radioChanged()">
          <ion-col>
            <ion-item class='ion-radio-item-style'>
              <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="A" [disabled]="(isLoggedInUserEmployee && (ropeEnd=='A' || ropeEnd=='B') && !endPreFilled) || readOnly">{{'A' | translate}}</ion-radio>
              <!-- <ion-label>{{'A' | translate}}</ion-label> -->
            </ion-item>
          </ion-col>

          <ion-col>
            <ion-item class='ion-radio-item-style'>
              <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="B" [disabled]="(isLoggedInUserEmployee && (ropeEnd=='A' || ropeEnd=='B') && !endPreFilled) || readOnly">{{'B' | translate}}</ion-radio>
              <!-- <ion-label>{{'B' | translate}}</ion-label> -->
            </ion-item>
          </ion-col>

          <ion-col>
            <ion-item class='ion-radio-item-style'>
              <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="other" [disabled]="(isLoggedInUserEmployee && (ropeEnd=='A' || ropeEnd=='B') && !endPreFilled) || readOnly">{{'Other' | translate}}</ion-radio>
              <!-- <ion-label>{{'Other' | translate}}</ion-label> -->
            </ion-item>
          </ion-col>
        </ion-row>
      </ion-radio-group>
      <div [formGroup]="endDescriptionForm" *ngIf="ropeEnd=='other'">
        <mat-form-field style="width:100%">
          <input matInput type="text" [(ngModel)]="endDescription" [required] formControlName="endDescriptionCtrl"
            (change)="onChangeDisable()" placeholder="{{'Enter end description' | translate}}" step="any">
          <mat-error *ngIf="hasErrorStart('endDescription')">{{endDescriptionError}}</mat-error>
        </mat-form-field>
      </div>
    </div>

    <div *ngIf='!helpService.helpMode'>
      <p>{{'END_CONGIG_END_TYPE_LABEL' | translate}} <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></p>
      <!--<ion-select labelPlacement="stacked"  style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px" (ionChange)="observationChanged()"
        placeholder="{{'END_CONGIG_END_TYPE_DESCRIPTION' | translate}}" interface="popover" [(ngModel)]="ropeEndType">
        <ion-select-option *ngFor="let endType of endTypeList" value="{{endType.NAME}}"><div><span *ngIf="endType.NAME ==''">{{endType.VALUE}}</span><span *ngIf="endType.NAME !=''">{{endType.NAME}}</span></div></ion-select-option>
      </ion-select> -->
      <mat-form-field style="width: 100% !important">
        <mat-select disableOptionCentering (selectionChange)="observationChanged()"
          placeholder="{{'END_CONGIG_END_TYPE_DESCRIPTION' | translate}}" interface="popover" [(value)]="ropeEndType" [disabled]="(isLoggedInUserEmployee && endPreFilled) || readOnly">
          <mat-select-trigger *ngIf="ropeEndType != undefined && ropeEndType != ''">
            <img width="200" height="70" [src]='ropeEndType.img'>{{ropeEndType.item}}
          </mat-select-trigger>
          <mat-option *ngFor="let endType of ropeEndTypeList" [value]="endType" [disabled]="endTypeDisabled || readOnly"
            style="padding: 10px 5px; height: 100px">
            <img width="200" height="70" [src]='endType.img'>
            {{endType.item}}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field style="width:100%" *ngIf="ropeEndType=='Other'">
        <input matInput type="text" [(ngModel)]="endDescription" [required] [disabled]='true'
          placeholder="{{'Enter end description' | translate}}" step="any">
      </mat-form-field>
    </div>
    <div *ngIf='helpService.helpMode'>
      <p>{{'END_CONGIG_END_TYPE_LABEL' | translate}} <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></p>
      <!--<ion-select labelPlacement="stacked"  style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px" [disabled]='true'
        placeholder="{{'END_CONGIG_END_TYPE_DESCRIPTION' | translate}}" interface="popover" [(ngModel)]="ropeEndType">
        <ion-select-option *ngFor="let endType of endTypeList" value="{{endType.NAME}}"><span *ngIf="endType.NAME ==''">{{endType.VALUE}}</span><span *ngIf="endType.NAME !=''">{{endType.NAME}}</span></ion-select-option>
      </ion-select> -->
      <mat-form-field style="width: 100% !important">
        <mat-select disableOptionCentering (selectionChange)="observationChanged()" [disabled]='true'
          placeholder="{{'END_CONGIG_END_TYPE_DESCRIPTION' | translate}}" interface="popover" [(value)]="ropeEndType">
          <mat-select-trigger *ngIf="ropeEndType != undefined && ropeEndType != ''">
            <img width="200" height="70" [src]='ropeEndType.img'>{{ropeEndType.item}}
          </mat-select-trigger>
          <mat-option *ngFor="let endType of ropeEndTypeList" [value]="endType"
            style="padding: 10px 5px; height: 100px" [disabled]="readOnly">
            <img width="200" height="70" [src]='endType.img'>
            {{endType.item}}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <div [formGroup]="endTypeDescriptionForm" *ngIf="ropeEndType =='Other'">
        <mat-form-field style="width:100%">
          <input matInput type="text" [(ngModel)]="endTypeDescription" [required] formControlName="endTypeDescriptionCtrl"
            (change)="onChangeDisable()" placeholder="{{'Enter end description' | translate}}" step="any">
          <mat-error *ngIf="hasErrorStart('endTypeDescription')">{{endTypeDescriptionError}}</mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- * show only length field if the logged in user is customer - START -->
     <!-- ~ LENGTH FIELD FOR CUSTOMER START -->
    <div *ngIf="!isLoggedInUserEmployee">
      <div style="padding-top: 15px;" *ngIf='helpService.helpMode'>
        <label>{{'OBSERVATION_LENGTH_LABEL' | translate:{ measurementName: lengthType } }}: <span
            style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
        <div style="display: inline-flex; width: 100%;">
          <div style="width:100%">
            <mat-form-field style="width:100%">
              <input matInput id="measLength" type="text" [(ngModel)]="ropeEndLength" [required]
                placeholder="{{'OBSERVATION_LENGTH_PLACEHOLDER' | translate}}" [disabled]='true'
                (keydown)="keyPressed($event, ropeEndLength, measLength)" step="any" inputmode="decimal">
            </mat-form-field>
          </div>
          <div class="form-group" style="padding:10px 10px 0px 17px">
            <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
            </label>
          </div>
          <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
            <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
              (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('length')"></fa-icon>
          </div>
        </div>
      </div>
      <div style="padding-top: 15px;" *ngIf='!helpService.helpMode'>
        <label>{{'OBSERVATION_LENGTH_LABEL' | translate:{ measurementName: lengthType } }}:</label>
        <div style="display: inline-flex; width: 100%;">
          <div style="width:100%">
            <div [formGroup]="lengthForm">
              <mat-form-field style="width:100%">
                <input matInput id="measLength" type="text" [(ngModel)]="ropeEndLength" [required]
                  formControlName="ropeLengthCtrl" placeholder="{{'OBSERVATION_LENGTH_PLACEHOLDER' | translate}}"
                  (change)="onChangeDisable('')" (keydown)="keyPressed($event, start, measLength)" step="any"
                  inputmode="decimal" [disabled]="readOnly">
                <mat-error *ngIf='hasErrorStart("Length")'>{{lengthErrorMessage}}</mat-error>
              </mat-form-field>
            </div>
          </div>
          <div class="form-group" style="padding:10px 10px 0px 17px">
            <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
            </label>
          </div>
          <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
            <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
              (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('ropeEndLength')"></fa-icon>
          </div>
        </div>
      </div>
    </div>
    <!-- ~ LENGTH FIELD FOR CUSTOMER END -->
    <!-- * customer length field END -->

    <!-- * show start, end and length fields if the logged in user is samson employee - START-->
    <!-- ! START FIELD SAMSON EMPLOYEE - START -->
    <div style="padding:15px 0 0 0" *ngIf="isLoggedInUserEmployee && ropeEndType?.item!='Blunt Cut/Bitter End'" >
      <label>{{'OBSERVATION_START_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span></label>
      <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
        <div tooltip="{{'Enter the measurement starting point'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers style="width:100%">
          <div [formGroup]="startForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="ropeEndStart" maxlength="18"
                placeholder="{{'OBSERVATION_START_PLACEHOLDER' | translate}}"
                (keydown)="keyPressed($event, ropeEndStart, 'ropeEndStart')" step="any" inputmode="decimal"
                [required] formControlName="ropeStartCtrl" [disabled]='true'>
            </mat-form-field>
          </div>
        </div>
        <div class="form-group" style="padding:10px 10px 0px 17px">
          <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
          </label>
        </div>
        <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement start'|translate}}" positionV="bottom"
          [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
          <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
            (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('start')"></fa-icon>
        </div>
      </div>
  
      <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
        <div style="width:100%">
          <div [formGroup]="startForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="ropeEndStart" maxlength="18" [disabled]="ropeEndType?.item=='Blunt Cut/Bitter End'"
                placeholder="{{'OBSERVATION_START_PLACEHOLDER' | translate}}"
                (keydown)="keyPressed($event, ropeEndStart, 'ropeEndStart')" step="any" inputmode="decimal" (change)="onChangeDisable('start')"
                [required] formControlName="ropeStartCtrl" [readonly]="readOnly">
              <mat-error *ngIf="hasErrorStart('Start')">{{startErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
        </div>&nbsp;
        <div class="form-group" style="padding:10px 10px 0px 17px">
          <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
          </label>
        </div>
        <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
          <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
            (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('start')"></fa-icon>
        </div>
      </div>
    </div>
    <!-- ! START FIELD SAMSON EMPLOYEE - END -->

    <!-- ! END FIELD SAMSON EMPLOYEE - START -->
    <div style="padding:15px 0 0 0" *ngIf="isLoggedInUserEmployee">
      <label>{{'OBSERVATION_END_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span></label>
      <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
        <div tooltip="{{'Enter the measurement starting point of cuts'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers style="width:100%">
          <div [formGroup]="endForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="ropeEndEnd" maxlength="18"
                placeholder="{{'OBSERVATION_END_PLACEHOLDER' | translate}}" step="any" inputmode="decimal" [disabled]="true" [required]
                formControlName="helpMeasurementEndCtrl">
            </mat-form-field>
          </div>
        </div>
        <div class="form-group" style="padding:10px 10px 0px 17px">
          <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
          </label>
        </div>
        <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement length'|translate}}" positionV="bottom"
          [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
          <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
            (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('end')"></fa-icon>
        </div>
      </div>
  
      <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
        <div style="width:100%">
          <div [formGroup]="endForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="ropeEndEnd" maxlength="18"
                placeholder="{{'OBSERVATION_END_PLACEHOLDER' | translate}}" step="any" (change)="onChangeDisable('end')"
                inputmode="decimal" (keydown)="keyPressed($event, ropeEndEnd, 'end')" [required]
                formControlName="ropeEndCtlr" [readonly]="readOnly">
              <mat-error *ngIf="hasErrorStart('End')">{{endErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="form-group" style="padding:10px 10px 0px 17px">
          <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
          </label>
        </div>
        <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
          <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
            (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('end')"></fa-icon>
        </div>
      </div>
    </div>
    <!-- ! END FIELD SAMSON EMPLOYEE END

    <!-- ! LENGTH FIELD SAMSON EMPLOYEE START -->
    <div style="padding:15px 0 0 0" *ngIf="isLoggedInUserEmployee && ropeEndType?.item!='Blunt Cut/Bitter End'">
      <label>{{'OBSERVATION_LENGTH_LABEL' | translate:{ measurementName: lengthType } }}:</label>
      <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
        <div tooltip="{{'Enter the measurement starting point of cuts'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers style="width:100%">
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='helpService.helpMode || readOnly || isStartValid'
              [(ngModel)]="ropeEndLength" maxlength="18" placeholder="{{'Length Value' | translate}}"
              (keydown)="keyPressed($event, ropeEndLength, 'length')" step="any" inputmode="decimal">
          </mat-form-field>
        </div>
        <div class="form-group" style="padding:10px 10px 0px 17px">
          <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
          </label>
        </div>
      </div>
  
      <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
        <div style="width:100%">
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="ropeEndLength" maxlength="18"
              placeholder="{{'Length Value' | translate}}" (keydown)="keyPressed($event, ropeEndLength, 'length')"
              step="any" inputmode="decimal">
          </mat-form-field>
        </div>
        <div class="form-group" style="padding:10px 10px 0px 17px">
          <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
          </label>
        </div>
      </div>
    </div>
    <!-- ! LENGTH FIELD SAMSON EMPLOYEE END -->
    <!-- * show start,end and length fields for samson employee - END -->

    <div>
      <div *ngIf='!helpService.helpMode'>
        <p>{{'END_CONFIG_CHAFE_TYPE_LABEL' | translate}} </p>
       <ion-select labelPlacement="stacked"  style="height:44px;border: 1px solid rgb(185, 184, 184);padding:0px 10px" [disabled]='readOnly'
          (ionChange)="observationChanged()" placeholder="{{'END_CONFIG_CHAFE_TYPE_PLACEHOLDER' | translate}}"
          interface="popover" [(ngModel)]="ropeEndChafe">
          <ion-select-option *ngFor="let endChafe of chafeTypeList" value="{{endChafe.NAME}}"><span
              *ngIf="endChafe.NAME ==''">{{endChafe.VALUE}}</span><span
              *ngIf="endChafe.NAME !=''">{{endChafe.NAME}}</span></ion-select-option>
        </ion-select>
      </div>
      <div *ngIf='helpService.helpMode'>
        <p>{{'END_CONFIG_CHAFE_TYPE_LABEL' | translate}}</p>
       <ion-select labelPlacement="stacked"  style="height:44px;border: 1px solid rgb(185, 184, 184);padding:0px 10px" [disabled]='true'
          placeholder="{{'END_CONFIG_CHAFE_TYPE_PLACEHOLDER' | translate}}" interface="popover"
          [(ngModel)]="ropeEndChafe">
          <ion-select-option *ngFor="let endChafe of chafeTypeList" value="{{endChafe.NAME}}"><span
              *ngIf="endChafe.NAME ==''">{{endChafe.VALUE}}</span><span
              *ngIf="endChafe.NAME !=''">{{endChafe.NAME}}</span></ion-select-option>
        </ion-select>
      </div>
    </div>
    <div style="padding-top: 15px;" *ngIf='!helpService.helpMode'>
      <p>{{'END_CONFIG_CHAFE_TYPE_HARDWARE_LABEL' | translate}} </p>
     <ion-select labelPlacement="stacked"  style="height:44px;border: 1px solid rgb(185, 184, 184);padding:0px 10px"
        (ionChange)="observationChanged()" placeholder="{{'END_CONFIG_CHAFE_TYPE_HARDWARE_PLACEHOLDER' | translate}}" [disabled]='readOnly'
        interface="popover" [(ngModel)]="ropeEndHardware">
        <ion-select-option *ngFor="let hwType of hardwareTypeList" value="{{hwType.NAME}}"><span
            *ngIf="hwType.NAME ==''">{{hwType.VALUE}}</span><span *ngIf="hwType.NAME !=''">{{hwType.NAME}}</span>
        </ion-select-option>
      </ion-select>
    </div>
    <div style="padding-top: 15px;" *ngIf='helpService.helpMode'>
      <p>{{'END_CONFIG_CHAFE_TYPE_HARDWARE_LABEL' | translate}}</p>
     <ion-select labelPlacement="stacked"  style="height:44px;border: 1px solid rgb(185, 184, 184);padding:0px 10px" [disabled]='true'
        placeholder="{{'END_CONFIG_CHAFE_TYPE_HARDWARE_PLACEHOLDER' | translate}}" interface="popover"
        [(ngModel)]="ropeEndHardware">
        <ion-select-option *ngFor="let hwType of hardwareTypeList" value="{{hwType.NAME}}"><span
            *ngIf="hwType.NAME ==''">{{hwType.VALUE}}</span><span *ngIf="hwType.NAME !=''">{{hwType.NAME}}</span>
        </ion-select-option>
      </ion-select>
    </div>
  </div>

  <!-- ^ Observation Notes Section -->
  <div style="padding:15px 10px 10px 17px">
    <label>{{'OBSERVATION_NOTES' | translate}}:</label>
      <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
      [disabled]='helpService.helpMode || readOnly' [(ngModel)]="observationNote" (ionFocus)="onFocusUserInputField($event)" (ionChange)="setChanged()">
    </ion-textarea>
  </div>

  <ion-label style="padding: 10px 17px 10px 17px;">Photos:</ion-label>
  <ion-row style="padding: 10px 17px 10px 17px;" *ngIf="device.platform != 'browser'">
    <div class="img-wrap" *ngFor="let item of cameraService.editedImg; let i = index"
      style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
      <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;"
        (click)="(helpService.helpMode || readOnly) ? test() : saveAndEdit(item.Image, i, '')" />
      <ion-icon *ngIf="item.Image!='./assets/img/samson2.png' && !readOnly" name="trash" class="close" slot="end" mode="md" color="danger"
        (click)="(helpService.helpMode || readOnly) ? test() : deleteImage(i)"></ion-icon>
    </div>

    <p style="margin:auto;" *ngIf="cameraService.editedImg!=undefined && cameraService.editedImg.length==0 && readOnly">No Photos Attached!!</p>

    <div *ngIf="helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of configuration'|translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers
        (click)="(helpService.helpMode || readOnly) ? test() : '' "
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>

    <div *ngIf="!helpService.helpMode && !readOnly" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png"
        (click)="(helpService.helpMode || readOnly) ? test() : saveAndCapture()"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>
  </ion-row>

  <ion-row style="padding: 10px 17px 10px 17px;" *ngIf="device.platform == 'browser'" >
    <input id="myInput" type="file" style="visibility:hidden; display: none;" accept="image/x-png,image/jpeg" (change)="cameraService.onFileSelected($event)"/>
    <div class="img-wrap" *ngFor="let item of cameraService.editedImg; let i = index"
      style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
      <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;"
        (click)="(helpService.helpMode || readOnly) ? test() :cameraService.editImage(item.Image, i, '')" />
      <ion-icon *ngIf="item.Image!='./assets/img/samson2.png'" name="trash" class="close" slot="end" mode="md" color="danger"
        (click)="(helpService.helpMode || readOnly) ? test() : deleteImage(i)"></ion-icon>
    </div>
    <div *ngIf="helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers
        (click)="(helpService.helpMode || readOnly) ? test() : ''"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>

    <div *ngIf="!helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png"
        (click)="(helpService.helpMode || readOnly) ? test() : saveAndCapture() "
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>
  </ion-row>

  <!-- Save Button -->
  <ion-fab *ngIf='!readOnly' vertical="bottom" horizontal="end" slot="fixed" [topOffset]=helpService.topOffset
    tooltip="{{'Tap to save contamination measurement'|translate}}" positionV="top" positionH="right"
    [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="helpService.helpMode || readOnly ? test() : saveMeasurement()">
      <fa-icon class="icon-style-other" icon="floppy-disk" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>

<!-- Footer -->
<div *ngIf='footerClose && !isLoggedInUserEmployee'>
  <div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>