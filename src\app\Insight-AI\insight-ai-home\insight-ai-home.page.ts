import { Router } from '@angular/router';
import { Component, ElementRef, OnInit, ViewChild, ChangeDetectorRef, Input } from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { IonModal, MenuController, ModalController, Platform } from '@ionic/angular';
import { GenericListPage } from 'src/app/generic-list/generic-list.page';
import { AlertService } from 'src/app/services/alert.service';
import { CameraService } from 'src/app/services/camera.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { VisionAIService } from 'src/app/services/vision-ai.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { Camera, CameraOptions as camOptions } from '@awesome-cordova-plugins/camera/ngx';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { QUICK_INSPECTION_HEADER } from 'src/models/QUICK_INSPECTION_HEADER';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip } from '@fortawesome/free-solid-svg-icons';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { PlatformService } from 'src/app/services/platform.service';
declare var CaptureImageWithinBox : any;
declare var capture: any;
import { SamsonCamera, CameraOptions as Camopts } from "@samson/camcrop";
import { InsightAIRopeDetectionPage } from '../insight-ai-rope-detection/insight-ai-rope-detection.page';
import { WebView } from '@awesome-cordova-plugins/ionic-webview/ngx';
import { UserGuidePromptComponent } from '../user-guide-prompt/user-guide-prompt.component';
import { AppConstant } from 'src/constants/appConstants';
import { ActionSheetController } from '@ionic/angular/standalone';


// declare var CaptureImageWithinBox : any;
// declare var capture: any;
// declare var window: any;
@Component({
  selector: 'app-vision-home',
  templateUrl: './insight-ai-home.page.html',
  styleUrls: ['./insight-ai-home.page.scss'],
})
export class InsightAIHomePage implements OnInit {

  HMPECertList: any[] = [];
  searchText: string = '';
  tempHMPECertlist: any[] = [];
  selectedAccount: any;
  selectedAsset: any;
  imageData:any;
  platformId: string = this.platformService.getPlatformId();
  @ViewChild(CdkVirtualScrollViewport) viewport: CdkVirtualScrollViewport;
  @ViewChild('contentRef', { read: ElementRef }) contentRef: ElementRef;
  quickInspectHeader: QUICK_INSPECTION_HEADER;
  isFirstTimeGuidePromptOpen: boolean = false;
  @ViewChild(IonModal) modal!: IonModal;
  role: string = '';
  safeImagePath: SafeUrl;
  constructor(private router: Router,
    public platformService: PlatformService,
    public device: Device,
    public menu: MenuController,
    public helpService: HelpService,
    public dataService: DataService,
    public modalController: ModalController,
    private unviredCordovaSDK: UnviredCordovaSDK,
    private alertService: AlertService,
    private userPreferenceService: UserPreferenceService,
    private camera: Camera,
    private cameraService: CameraService,
    private visionService: VisionAIService,
    private file: File,
    public faIconLibrary: FaIconLibrary,
    public cdr: ChangeDetectorRef,
    private sanitizer: DomSanitizer,
    private webview: WebView,
    private platform: Platform,
    private actionSheetCtrl: ActionSheetController
  ) {

    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip)
  }

  async ngOnInit() {
    await this.loadData();
    if (this.contentRef?.nativeElement) {
      const modalElement = this.contentRef.nativeElement;
      const modalHeight = modalElement.getBoundingClientRect().height;
      const viewportElement = this.viewport.elementRef.nativeElement;
      viewportElement.style.height = `${modalHeight}px`;
    }
  }

  async ionViewWillLeave() {
    this.searchText = '';
  }

  async loadData() {
    // this.alertService.present();
    var selectedPrevoiusAccpount = await this.userPreferenceService.getUserPreference('account');
    if (selectedPrevoiusAccpount != '' && selectedPrevoiusAccpount != undefined && selectedPrevoiusAccpount != null && selectedPrevoiusAccpount != 'null') {
      this.selectedAccount = JSON.parse(selectedPrevoiusAccpount)
    }
    var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    if (selectedAsset != undefined && selectedAsset != null && selectedAsset != '' && selectedAsset != '""') {
      this.selectedAsset = JSON.parse(selectedAsset)
    }
    if (this.selectedAsset == undefined || this.selectedAccount == undefined || this.selectedAccount == '') {
      // await this.alertService.showAlert("Warning", "Please set asset and account in preference for viewing the data.");
      let Alert;
      await this.alertService.showAndGetAlertModal('Warning!', "Please set asset and account in preference for viewing the data.").then(alert => {
        Alert = alert;
      });
      await Alert.present();
      await Alert.onDidDismiss().then(async data => {
        this.router.navigate(['user-preferences'])
      })
      return
    }
    let query = `ACCOUNT_ID='${this.selectedAccount.ID}' AND ASSET_ID='${this.selectedAsset.ID}' 
            AND CONSTRUCTION LIKE '12-Strand' 
            AND MANUFACTURER = 'Samson' 
            AND (
                  (PRODUCT_TYPE = 'Conventional Fiber (Class I)' AND PRODUCT LIKE '%TENEX%') OR
                  PRODUCT_TYPE IN ('HMSF (Class II)', 'HMPE (Class II)')
                )`
    var rpsRowData = await this.unviredCordovaSDK.dbSelect("CERTIFICATE_HEADER", query)
    // var rpsRowData = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM CERTIFICATE_HEADER;");
    if (rpsRowData.type == ResultType.success) {

      if (rpsRowData.data.length > 0) {
        this.HMPECertList = JSON.parse(JSON.stringify(rpsRowData.data));
        this.tempHMPECertlist = JSON.parse(JSON.stringify(rpsRowData.data));
      } else {
        // await this.alertService.dismiss();
        this.alertService.showAlert('Error', 'No HMPE certificates found in db....');
      }
    }
    // console.log(this.HMPECertList);
    if (this.alertService.isLoading) {
      // await this.alertService.dismiss();
    }
  }

  async clearInsightAIStatus() {
    let result = await this.unviredCordovaSDK.dbDelete("APP_SETTINGS_HEADER", "KEY_FLD='InsightAIExternalFirst' OR KEY_FLD='InsightAIInternalFirst'");
  }

  async presentResultModal(props?: any) {
    return new Promise(async (resolve, reject) => {
      const resModal = await this.modalController.create({
        component: InsightAIRopeDetectionPage,
        backdropDismiss: false,
        componentProps: props,
        cssClass: 'insightAIGuidePrompt',
        enterAnimation: this.visionService.enterAnimation,
        leaveAnimation: this.visionService.leaveAnimation
      });
      await resModal.present();
      resModal.onDidDismiss().then(async (data: any) => {
        resolve(data.data);
      }).catch(err => {
        reject(err);
      });
    })
  }

  async goTOVisionPage(options: any) {
    const actionSheet = await this.actionSheetCtrl.create({
      header: 'Select Abrasion type',
      buttons: [
        {
          text: 'External',
          role: 'External'
        },
        {
          text: 'Internal',
          role: 'Internal'
        }
      ]
    });
    await actionSheet.present();
    // let data = await actionSheet.onWillDismiss();
    actionSheet.onDidDismiss().then(async (data: any) => {
      console.log("Modal dismissed with data:", data);
      if (data && data?.role!="backdrop") {
        let abrasionType = data.role;
        await this.cameraService.presentActionSheet().then(async (dismissed) => {
          if (dismissed.data != undefined) {
            this.quickInspectHeader = new QUICK_INSPECTION_HEADER();
            let inspID = UtilserviceService.guid();
            this.quickInspectHeader.INSP_ID = inspID;
            this.quickInspectHeader.INSP_NAME = inspID;
            this.quickInspectHeader.CERT_ID = options.CERTIFICATE_NUM;
            this.quickInspectHeader.ACCOUNT_ID = options.ACCOUNT_ID;
            this.quickInspectHeader.PRODUCT_NAME = options.PRODUCT;
            this.quickInspectHeader.RPS = options.RPS;
          }

          if (dismissed.data == 1 && dismissed.role == 'camera') {
            let guidePrompt=''
            if (abrasionType == "External") {
              guidePrompt = "InsightAIExternalFirst"
            } else if (abrasionType == "Internal") {
              guidePrompt = "InsightAIInternalFirst"
            }
            let result = await this.unviredCordovaSDK.dbSelect("APP_SETTINGS_HEADER", `KEY_FLD='${guidePrompt}'`);
            if (result.type == ResultType.success) {
              if (result.data.length > 0) {
                this.isFirstTimeGuidePromptOpen = JSON.parse(result.data[0].VALUE);
              } else {
                this.isFirstTimeGuidePromptOpen = true;
              }

              if (this.isFirstTimeGuidePromptOpen) {
                this.role = dismissed.role;
                const guidePrompt = await this.modalController.create({
                  component: UserGuidePromptComponent,
                  cssClass: 'insightAIGuidePrompt',
                  componentProps: {
                    role: dismissed.role,
                    screenMode: 'QUICKINSPECT',
                    abrasionType: abrasionType
                  }
                })
                guidePrompt.present();
                let promptDismissed = await guidePrompt.onDidDismiss()
                if (promptDismissed.data && promptDismissed.data.role == 'cancelled') return;
              }
            } else {
              this.unviredCordovaSDK.logError("INSIGHT AI HOME", "Error fetching APP_SETTINGS_HEADER:", result.message);
            }

            // await this.alertService.presentLoaderWithMsg('Please wait.......');
            const options: Camopts = {
              preview: true,
              location: false,
              croppingEnabled: false,
              flashEnabled: true,
              autoFlash: false,
              mode: "InsightAI",
              cropOnPreview: true,
              inspectionType: abrasionType,
              minCamRectHeight: 256,
              minCamRectWidth: 256,
              cropperOptions: {
                mode: "InsightAI",
                inspectionType: abrasionType,
                minCropBoxHeight: 256,
                minCropBoxWidth: 256,
              }
            };
            const camera = new SamsonCamera();
            camera
              .openCamera(options)
              .then(result => {
                if (result && result.image) {
                  console.log(result)
                  this.visionService.imageData = result.image;
                  // if (this.visionService.screenMode == 'QUICKINSPECT') {
                  // this.visionService.setQuickInspectionHeader(this.quickInspectHeader)
                  // }
                  //* commented this out to make the insight AI page modal
                  //& this.router.navigate(['insight-AI-rope-detection']);
                  this.presentResultModal({ screenMode: "QUICKINSPECT", abrasionType: abrasionType, imageObj: result, certObject: this.quickInspectHeader }).then(async (data: any) => {
                    if(data.action=="RETAKE") {
                      this.goTOVisionPage(options);
                    }
                  })
                }
              })
              .catch(err => {
                // Handle user cancel or error
                console.log(err);
              });

          } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
            await this.alertService.preasentLoading()
            if (this.platform.is('android')) {
              let onSuccess = async (imageURI) => {
                console.log(imageURI);
                let that = this;
                const noQuery = imageURI.split('?')[0];
                const fileName = noQuery.split('/').pop();
                const tname = 'image-' + (new Date().getTime()).toString(16) + ".jpg"; // optional if needed to rename
                const fromPath = this.file.cacheDirectory;
                const toPath = this.file.dataDirectory;

                let orgPath = this.file.dataDirectory + fileName;
                // this.file.copyFile(fromPath, fileName, toPath, tname).then((entry) => {
                const safePath = this.webview.convertFileSrc(imageURI);
                this.safeImagePath = this.sanitizer.bypassSecurityTrustUrl(safePath);
                // this.safeImagePath = safePath;
                console.log('Image moved and ready to use:', this.safeImagePath);
                console.log(this.safeImagePath.toString())
                let path: any = this.safeImagePath;
                path = path.changingThisBreaksApplicationSecurity;
                console.log("Path:", path);
                const options: Camopts = {
                  image: path,
                  preview: true,
                  flashEnabled: true,
                  autoFlash: false,
                  location: false,
                  croppingEnabled: false,
                  mode: "InsightAI",
                  inspectionType: abrasionType,
                  cropOnPreview: true,
                  cropperOptions: {
                    mode: "InsightAI",
                    inspectionType: abrasionType,
                    minCropBoxHeight: 256,
                    minCropBoxWidth: 256
                  }
                };
                const camera = new SamsonCamera();
                camera
                  .openCamera(options)
                  .then(async result => {
                    // result: { image, latitude, longitude }
                    console.log("result", result);
                    // Handle the result here (e.g., show preview, upload, etc.)
                    // console.log("Captured image:", result.image);
                    // console.log("Latitude:", result.latitude, "Longitude:", result.longitude);
                    if (that.alertService.isLoading) {
                      await that.alertService.dismiss();
                    }
                    console.log("Success:", result);
                    // that.visionService.imageData = result.image;
                    // that.visionService.setQuickInspectionHeader(that.quickInspectHeader)
                    // that.router.navigate(['insight-AI-rope-detection']);
                    this.presentResultModal({ screenMode: "QUICKINSPECT", abrasionType: abrasionType, imageObj: result, certObject: this.quickInspectHeader }).then(async (data: any) => {
                      if (data.action == "RETAKE") {
                        this.goTOVisionPage(options);
                      }
                    })

                  })
                  .catch(async err => {
                    // Handle user cancel or error
                    console.error(err);
                    await that.alertService.showAlert("Error", "Failed to capture image: " + err.message);
                  });
              }

              let onFail = async (message) => {
                console.error("Failed to get picture: " + message);
                // await this.zone.run(async () => {
                if (this.alertService.isLoading) {
                  await this.alertService.dismiss();
                }
                await this.alertService.showAlert("Warning!", message);
                // });
              };

              const openGallery = async () => {
                await this.alertService.present();
                (navigator as any).camera.getPicture(onSuccess, onFail, {
                  quality: 100,
                  correctOrientation: true,
                  destinationType: this.camera.DestinationType.FILE_URI,
                  sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
                  mediaType: this.camera.MediaType.PICTURE,
                  encodingType: this.camera.EncodingType.JPEG
                } as camOptions);
              };
              await openGallery();
            } else if (this.platform.is('ios')) {
              let galleryOptions: camOptions = {
                quality: 100,
                correctOrientation: true,
                destinationType: this.camera.DestinationType.FILE_URI,
                sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
                mediaType: this.camera.MediaType.PICTURE,
                encodingType: this.camera.EncodingType.JPEG
              };

              this.camera.getPicture(galleryOptions).then(async (imageData) => {
                console.log("Image URI: " + imageData);
                imageData = this.webview.convertFileSrc(imageData);
                console.log("Converted Image URI: " + imageData);
                const options: Camopts = {
                  image: imageData,
                  preview: true,
                  flashEnabled: true,
                  autoFlash: true,
                  location: false,
                  mode: "InsightAI",
                  inspectionType: abrasionType,
                  croppingEnabled: false,
                  cropOnPreview: true,
                  cropperOptions: {
                    mode: "InsightAI",
                    inspectionType: abrasionType,
                    minCropBoxHeight: 256,
                    minCropBoxWidth: 256,
                  }
                };
                const camera = new SamsonCamera();
                camera
                  .openCamera(options)
                  .then(async result => {
                    // result: { image, latitude, longitude }
                    console.log("result", result);
                    // Handle the result here (e.g., show preview, upload, etc.)
                    console.log("Captured image:", result.image);
                    console.log("Latitude:", result.latitude, "Longitude:", result.longitude);
                    if (this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                    console.log("Success:", result);
                    // this.visionService.imageData = result.image;
                    // this.visionService.setQuickInspectionHeader(this.quickInspectHeader)
                    // that.router.navigate(['insight-AI-rope-detection']);
                    this.presentResultModal({ screenMode: "QUICKINSPECT", imageObj: result, abrasionType: abrasionType, certObject: this.quickInspectHeader }).then(async (data: any) => {
                      if (data.action == "RETAKE") {
                        this.goTOVisionPage(options);
                      }
                    });
                  })
                  .catch(async err => {
                    // Handle user cancel or error
                    console.error(err);
                    await this.alertService.showAlert("Error", "Failed to capture image: " + err.message);
                  });
              })
            } else {
              let myInput = document.createElement('input')
              myInput.type = 'file';
              myInput.accept = 'image/x-png,image/jpeg,image/jpg,image/png';
              myInput.onchange = (event) => this.onImageChange(event,abrasionType);
              myInput.click();
            }
            if (this.alertService.isLoading) {
              await this.alertService.dismiss();
            }
          }
        })
      }
    })
    console.log("options:", options)
  }

  async onImageChange(event: Event, abrasionType: any) {
    const inputElement = event.target as HTMLInputElement;
    const file = inputElement.files[0];
    const reader = new FileReader();
    reader.onload = async () => {
      this.imageData = <string>reader.result;
      // this.visionService.setQuickInspectionHeader(this.quickInspectHeader)
      // this.visionService.imageData = this.imageData;
      const options: Camopts = {
        image: this.imageData,
        preview: true,
        flashEnabled: true,
        autoFlash: true,
        location: false,
        croppingEnabled: false,
        mode: "InsightAI",
        inspectionType: abrasionType,
        cropOnPreview: true,
        cropperOptions: {
          mode: "InsightAI",
          inspectionType: abrasionType,
          minCropBoxHeight: 256,
          minCropBoxWidth: 256
        }
      };
      
      const camera = new SamsonCamera();
      camera
        .openCamera(options)
        .then(async result => {
          // result: { image, latitude, longitude }
          console.log("result", result);
          // Handle the result here (e.g., show preview, upload, etc.)
          console.log("Captured image:", result.image);
          console.log("Latitude:", result.latitude, "Longitude:", result.longitude);
          if (this.alertService.isLoading) {
            await this.alertService.dismiss();
          }
          console.log("Success:", result);
          // this.visionService.imageData = result.image;
          this.presentResultModal({ screenMode: "QUICKINSPECT", abrasionType: abrasionType, imageObj: result, certObject: this.quickInspectHeader }).then(async (data: any) => {
            if (data.action == "RETAKE") {
              this.goTOVisionPage(options);
            }
          })
        })
        .catch(async err => {
          // Handle user cancel or error
          console.error(err);
          await this.alertService.showAlert("Error", "Failed to capture image: " + err.message);
        });
      // this.router.navigate(['insight-AI-rope-detection']);
    };
    reader.readAsDataURL(file);
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this);
  }

  getItems(event) {
    // this.searchText = event.target.value;
    var val = event.target.value;
    this.HMPECertList = this.tempHMPECertlist
    if (val && val.trim() != '') {
      this.HMPECertList = this.HMPECertList.filter((product) => {
        return ((product.NAME && (product.NAME.toLowerCase().indexOf(val.toLowerCase()) > -1)) || (product.CERTIFICATE_NUM && (product.CERTIFICATE_NUM.toLowerCase().indexOf(val.toLowerCase()) > -1)) ||
          (product.PRODUCT && (product.PRODUCT.toLowerCase().indexOf(val.toLowerCase()) > -1)) || (product.DIAM && (product.DIAM.toLowerCase().indexOf(val.toLowerCase()) > -1)) || (product.DIAM_UOM && (product.DIAM_UOM.toLowerCase().indexOf(val.toLowerCase()) > -1)));
      })
    }
    this.cdr.detectChanges();
  }
  gotoInspections() {
    this.dataService.navigateToInspection();
  }

  gotoResources() {
    this.dataService.navigateToContact();
  }

  gotoContact() {
    this.dataService.navigateToContact();
  }
}

