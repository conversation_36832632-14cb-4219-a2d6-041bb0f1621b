import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AbrasionComparatorExternalPageRoutingModule } from './abrasion-comparator-external-routing.module';

import { AbrasionComparatorExternalPage } from './abrasion-comparator-external.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TooltipsModule } from 'ionic4-tooltips';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    FontAwesomeModule,
    AbrasionComparatorExternalPageRoutingModule,
    TooltipsModule,
    FooterComponent
  ],
  declarations: [AbrasionComparatorExternalPage]
})
export class AbrasionComparatorExternalPageModule {}
