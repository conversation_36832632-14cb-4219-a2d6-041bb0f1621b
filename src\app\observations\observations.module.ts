import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ObservationsPageRoutingModule } from './observations-routing.module';
import { ObservationsPage } from './observations.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FontAwesomeModule,
    TranslateModule,
    TooltipsModule,
    MatTooltipModule,
    ObservationsPageRoutingModule,
    FooterComponent
    
  ],
  declarations: [ObservationsPage]
})
export class ObservationsPageModule {}
