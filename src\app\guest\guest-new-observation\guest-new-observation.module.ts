import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestNewObservationPageRoutingModule } from './guest-new-observation-routing.module';

import { GuestNewObservationPage } from './guest-new-observation.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestNewObservationPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    GuestFooterComponent
  ],
  declarations: [GuestNewObservationPage]
})
export class GuestNewObservationPageModule {}
