<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/guest-home"></ion-back-button>
    </ion-buttons>
    <!-- <ion-buttons slot="primary" *ngIf="search">
      <ion-button (click)="toggleSearch()" *ngIf="searchIcon" [disabled]='helpService.helpMode'>
        <ion-icon slot="icon-only" name="search"></ion-icon>
      </ion-button>
      <ion-button (click)="presentPopover()" [disabled]='helpService.helpMode'>
        <ion-icon slot="icon-only" name="eye"></ion-icon>
      </ion-button>
    </ion-buttons> -->
    <ion-buttons slot="primary" *ngIf="search">
      <ion-button color="primary" slot="end" (click)="toggleSearch()" *ngIf="searchIcon" class="icon">
        <fa-icon class="icon-style" icon="search"></fa-icon>
      </ion-button>
      <ion-button color="primary" slot="end" (click)="trashPopover()" class="icon"
        *ngIf="inspectionType == 'open' && (inProgressTrashArray == undefined || inProgressTrashArray.length == 0)">
        <fa-icon class="icon-style" icon="trash-alt"></fa-icon>
      </ion-button>
      <ion-button color="primary" slot="end" (click)="trashPopover()" class="icon"
        *ngIf="inspectionType == 'open' && (inProgressTrashArray.length && inProgressTrashArray.length != 0)">
        <fa-icon class="icon-style" icon="trash"></fa-icon>
      </ion-button>
      <ion-button color="primary" slot="end" (click)="trashPopoverHistory()" class="icon"
        *ngIf="inspectionType == 'history' && (completedTrashArray  == undefined || completedTrashArray.length == 0)">
        <fa-icon class="icon-style" icon="trash-alt"></fa-icon>
      </ion-button>
      <ion-button color="primary" slot="end" (click)="trashPopoverHistory()" class="icon"
        *ngIf="inspectionType == 'history' && (completedTrashArray.length && completedTrashArray.length != 0)">
        <fa-icon class="icon-style" icon="trash"></fa-icon>
      </ion-button>
    </ion-buttons>
    <!-- <ion-buttons slot="primary">
  <ion-button color="primary" slot="end" (click)="helpService.switchMode()" class="icon">
    <span *ngIf='helpService.helpMode' style="padding-right: 5px !important; font-size: medium !important">{{'Exit Help' | translate}}</span>
    <fa-icon class="icon-style"  *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
  </ion-button>
</ion-buttons> -->
    <ion-title>{{inspTitle}}</ion-title>
  </ion-toolbar>
  <ion-toolbar *ngIf="searchbar" style="padding-bottom:5px;">
    <ion-searchbar *ngIf="inspectionType == 'history'" class="searchBar" showCancelButton debounce="500"
      (ionCancel)="cancelHistoricalItem($event)" animated (ionInput)="searchHistoricalItem($event)">
    </ion-searchbar>
    <ion-searchbar *ngIf="inspectionType == 'open'" class="searchBar" showCancelButton debounce="500"
      (ionCancel)="cancelOpenItem($event)" animated (ionInput)="searchinProgressItem($event)">
    </ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content>

  <!-- In Progress Inspection div -->
  <div *ngIf="inspectionType =='open'">
    <div style="padding-top:2px" *ngIf='historyList && historyList.length > 0'>

      <ion-list>
        <ion-item-sliding *ngFor="let item of historyList; let i = index">

          <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);">
            <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start" icon="circle"
              *ngIf="item.INSPECTION_STATUS =='Ready' && item.SYNC_STATUS == '3'"
              class="syncError"
              (click)="getInfoMsg($event, item)" tappable>
            </fa-icon>
            <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start" icon="circle"
              *ngIf="item.INSPECTION_STATUS =='Ready'&& item.SYNC_STATUS == '2'"
              class="syncProgress"
              (click)="getInfoMsg($event, item)" tappable>
            </fa-icon>
            <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start"
              [icon]="['far', 'circle']"
              *ngIf="(item.SYNC_STATUS == '0' || item.SYNC_STATUS == '1') && item.INSPECTION_STATUS =='Ready'"
              class="syncReady"
              (click)="getInfoMsg($event, item)" tappable>
            </fa-icon>
            <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start"
              [icon]="['far', 'circle']"
              *ngIf="(item.SYNC_STATUS == '0' || item.SYNC_STATUS == '1') && item.INSPECTION_STATUS !='Ready'"
              class="inProgress"
              (click)="getInfoMsg($event, item)" tappable>
            </fa-icon>
            <ion-label (click)="viewInProgressInsp(item)">

              <h2 style="color:rgb(37, 91, 145)">{{'Cert' | translate}} # : {{item.INSPECTION_NAME}}</h2>

              <p *ngIf="view == 'INSPECTION_ID'">{{'Inspection Id' | translate}} : {{item.INSPECTION_ID}}</p>
              <p *ngIf="view == 'EXTERNAL_ID'">{{'External Id' | translate}} : {{item.EXTERNAL_ID}} </p>
              <p *ngIf="view == 'RPS'">{{'RPS' | translate}} : {{item.RPS}}</p>
              <p *ngIf="view == 'RFT_NUM'">{{'RFT Number' | translate}} : {{item.RFT_NUM}} </p>
              <p *ngIf="view == 'ACCOUNT_NAME'">{{'Account Id' | translate}} : {{item.ACCOUNT_NAME}}</p>
              <p *ngIf="view == 'ASSET_NAME'">{{'Asset Id' | translate}} : {{item.ASSET_NAME}}</p>
              <p *ngIf="view == 'MANUFACTURER'">{{'Manufacturer' | translate}} : {{item.MANUFACTURER}}</p>
              <p *ngIf="view == 'INDUSTRY'">{{'Industry' | translate}} : {{item.INDUSTRY}}</p>
              <p *ngIf="view == 'APPLICATION'">{{'Application' | translate}} : {{item.APPLICATION}}</p>
              <p *ngIf="view == 'PRODUCT_TYPE'">{{'Product Type' | translate}} : {{item.PRODUCT_TYPE}}</p>
              <p *ngIf="view == 'PRODUCT'">{{'Product' | translate}} : {{item.PRODUCT}}</p>
              <p *ngIf="view == 'PRODUCT_CODE'">{{'Product Code' | translate}} : {{item.PRODUCT_CODE}}</p>
              <p *ngIf="view == 'PRODUCT_DESC'">{{'Product Description' | translate}} : {{item.PRODUCT_DESC}}</p>
              <p *ngIf="view == 'COLOR'">{{'Color' | translate}} : {{item.COLOR}}</p>
              <p *ngIf="view == 'COLOR_OTHER'">{{'Color Other' | translate}} : {{item.COLOR_OTHER}}</p>
              <p *ngIf="view == 'CONSTRUCTION'">{{'Construction' | translate}} : {{item.CONSTRUCTION}}</p>
              <p *ngIf="view == 'ORIGINAL_CONFIG'">{{'Original Configuration' | translate}} : {{item.ORIGINAL_CONFIG}}
              </p>
              <p *ngIf="view == 'PRODUCT_CONFIG'">{{'Product Configuration' | translate}} : {{item.PRODUCT_CONFIG}}</p>
              <p *ngIf="view == 'DIAM'">{{'Diameter' | translate}} : {{item.DIAM}}</p>
              <p *ngIf="view == 'DIAM_UOM'">{{'Diameter UOM' | translate}} : {{item.DIAM_UOM}}</p>
              <p *ngIf="view == 'ORIGINAL_LENGTH'">{{'Original Length' | translate}} : {{item.ORIGINAL_LENGTH}}</p>
              <p *ngIf="view == 'CURRENT_LENGTH'">{{'Current Length' | translate}} : {{item.CURRENT_LENGTH}}</p>
              <p *ngIf="view == 'INSPECTED_LENGTH'">{{'Inspection Length' | translate}} : {{item.INSPECTED_LENGTH}}</p>
              <p *ngIf="view == 'LENGTH_UOM'">{{'Length UOM' | translate}} : {{item.LENGTH_UOM}}</p>
              <p *ngIf="view == 'HAS_CHAFE'">{{'Has Chafe' | translate}} : <span
                  *ngIf="item.HAS_CHAFE == 1">{{'Yes' | translate}}</span><span
                  *ngIf="item.HAS_CHAFE == 0">{{'No' | translate}}</span></p>
              <p *ngIf="view == 'CHAFE_TYPE'">{{'Chafe Type' | translate}} : {{item.CHAFE_TYPE}}</p>
              <p *ngIf="view == 'OTHER_CHAFE'">{{'Other Chafe' | translate}} : {{item.OTHER_CHAFE}}</p>
              <p *ngIf="view == 'IS_JACKETED'">{{'Is Jacketed' | translate}} :<span
                  *ngIf="item.IS_JACKETED == 1">{{'Yes' | translate}}</span><span
                  *ngIf="item.IS_JACKETED == 0">{{'No' | translate}}</span></p>
              <p *ngIf="view == 'INSTALLED_DATE'">{{'Installed Date' | translate}} : {{item.INSTALLED_DATE}}</p>
              <p *ngIf="view == 'INSTALLED_STATUS'">{{'Installed Status' | translate}} : {{item.INSTALLED_STATUS}}</p>
              <p *ngIf="view == 'LOCATION_INSTALLED'">{{'Location Installed' | translate}} : {{item.LOCATION_INSTALLED}}
              </p>
              <p *ngIf="view == 'INSPECTION_STATUS'">{{'Inspection Status' | translate}} : {{item.INSPECTION_STATUS}}
              </p>
              <p *ngIf="view == 'USER_ID'">{{'User Id' | translate}} : {{item.USER_ID}}</p>
              <p *ngIf="view == 'CREATED_BY'">{{'Created By' | translate}} : {{item.CREATED_BY}}</p>
              <p *ngIf="view == 'CREATED_DATE'">{{'Created Date' | translate}} : {{item.CREATED_DATE}}</p>
              <p *ngIf="view == 'LAST_MODIFIED_BY'">{{'Modified By' | translate}} : {{item.LAST_MODIFIED_BY}}</p>
              <p *ngIf="view == 'LAST_MODIFIED_DATE'">{{'Modified Date' | translate}} : {{item.LAST_MODIFIED_DATE}}</p>
              <p *ngIf="view == 'LINE_POSITION'">{{'Line Position' | translate}} : {{item.LINE_POSITION}}</p>
              <p *ngIf="view == 'PRODUCT_LOCATION'">{{'Product Location' | translate}} : {{item.PRODUCT_LOCATION}}</p>

            </ion-label>
            <ion-buttons (click)="icon(i)">
              <ion-button style="padding-bottom: 8px;">
                <ion-icon slot="icon-only" slot="end" [name]="checkSectionIsOpen(i) ? 'ios-arrow-up' :'ios-arrow-down'"
                  style="color:rgb(41, 40, 40)"></ion-icon>
              </ion-button>
            </ion-buttons>
          </ion-item>

          <ion-item *ngIf="checkSectionIsOpen(i)" lines="none"
            style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829);">
            <ion-label style="padding-left:26px;">
              <ion-text color="primary" *ngIf="checkSectionIsOpen(i)">
                <div>
                  <div style="display: inline-flex !important; width: 100% !important">
                    <div style="width:40%  !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">

                      <!-- <p *ngIf="view != 'INSPECTION_ID' && (item.INSPECTION_ID != '' && item.INSPECTION_ID != undefined)">{{'Inspection Id' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'EXTERNAL_ID'  && (item.EXTERNAL_ID != '' && item.EXTERNAL_ID != undefined)">{{'External Id' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'RPS'  && (item.RPS != '' && item.RPS != undefined)">{{'RPS' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'RFT_NUM'  && (item.RFT_NUM != '' && item.RFT_NUM != undefined)">{{'RFT Number' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'ACCOUNT_NAME'  && (item.ACCOUNT_NAME != '' && item.ACCOUNT_NAME != undefined)">{{'Account Id' | translate}}</p>
                      <p *ngIf="view != 'ASSET_NAME'  && (item.ASSET_NAME != '' && item.ASSET_NAME != undefined)">{{'Asset Id' | translate}}</p>
                      <p *ngIf="view != 'MANUFACTURER'  && (item.MANUFACTURER != '' && item.MANUFACTURER != undefined)">{{'Manufacturer' | translate}}</p>
                      <p *ngIf="view != 'INDUSTRY'  && (item.INDUSTRY != '' && item.INDUSTRY != undefined)">{{'Industry' | translate}}</p>
                      <p *ngIf="view != 'APPLICATION'  && (item.APPLICATION != '' && item.APPLICATION != undefined)">{{'Application' | translate}}</p>
                      <p *ngIf="view != 'PRODUCT_TYPE'  && (item.PRODUCT_TYPE != '' && item.PRODUCT_TYPE != undefined)">{{'Product Type' | translate}}</p> -->
                      <p *ngIf="view != 'PRODUCT' && (item.PRODUCT != '' && item.PRODUCT != undefined)">
                        {{'Product' | translate}}</p>
                      <!-- <p *ngIf="view != 'PRODUCT_CODE' && (item.PRODUCT_CODE != '' && item.PRODUCT_CODE != undefined)">{{'Product Code' | translate}}</p>
                      <p *ngIf="view != 'PRODUCT_DESC' && (item.PRODUCT_DESC != '' && item.PRODUCT_DESC != undefined)">{{'Product Description' | translate}}</p> -->
                      <p *ngIf="view != 'COLOR' && (item.COLOR != '' && item.COLOR != undefined)">
                        {{'Color' | translate}}</p>
                      <!-- <p *ngIf="view != 'COLOR_OTHER' && (item.COLOR_OTHER != '' && item.COLOR_OTHER != undefined)">{{'Color Other' | translate}}</p> -->
                      <p *ngIf="view != 'CONSTRUCTION' && (item.CONSTRUCTION != '' && item.CONSTRUCTION != undefined)">
                        {{'Construction' | translate}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_CONFIG' && (item.ORIGINAL_CONFIG != '' && item.ORIGINAL_CONFIG != undefined)">{{'Original Configuration' | translate}}</p> -->
                      <p
                        *ngIf="view != 'PRODUCT_CONFIG' && (item.PRODUCT_CONFIG != '' && item.PRODUCT_CONFIG != undefined)">
                        {{'Product Configuration' | translate}}</p>
                      <p *ngIf="view != 'DIAM' && (item.DIAM != '' && item.DIAM != undefined)">
                        {{'Diameter' | translate}}</p>
                      <p *ngIf="view != 'DIAM_UOM' && (item.DIAM_UOM != '' && item.DIAM_UOM != undefined)">
                        {{'Diameter UOM' | translate}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_LENGTH' && (item.ORIGINAL_LENGTH != '' && item.ORIGINAL_LENGTH != undefined)">{{'Original Length' | translate}}</p> -->
                      <p
                        *ngIf="view != 'CURRENT_LENGTH' && (item.CURRENT_LENGTH != '' && item.CURRENT_LENGTH != undefined)">
                        {{'Current Length' | translate}} </p>
                      <p
                        *ngIf="view != 'INSPECTED_LENGTH' && (item.INSPECTED_LENGTH != '' && item.INSPECTED_LENGTH != undefined)">
                        {{'Inspection Length' | translate}} </p>
                      <p *ngIf="view != 'LENGTH_UOM' && (item.LENGTH_UOM != '' && item.LENGTH_UOM != undefined)">
                        {{'Length UOM' | translate}}</p>
                      <p *ngIf="view != 'HAS_CHAFE' && (item.HAS_CHAFE != '' && item.HAS_CHAFE != undefined)">
                        {{'Has Chafe' | translate}}</p>
                      <p *ngIf="view != 'CHAFE_TYPE' && (item.CHAFE_TYPE != '' && item.CHAFE_TYPE != undefined)">
                        {{'Chafe Type' | translate}}</p>
                      <!-- <p *ngIf="view != 'OTHER_CHAFE' && (item.OTHER_CHAFE != '' && item.OTHER_CHAFE != undefined)">{{'Other Chafe' | translate}}</p> -->
                      <p *ngIf="view != 'IS_JACKETED' && (item.IS_JACKETED != '' && item.IS_JACKETED != undefined)">
                        {{'Is Jacketed' | translate}}</p>
                      <!-- <p *ngIf="view != 'INSTALLED_DATE' && (item.INSTALLED_DATE != '' && item.INSTALLED_DATE != undefined)">{{'Installed Date' | translate}}</p>
                      <p *ngIf="view != 'INSTALLED_STATUS' && (item.INSTALLED_STATUS != '' && item.INSTALLED_STATUS != undefined)">{{'Installed Status' | translate}}</p>
                      <p *ngIf="view != 'LOCATION_INSTALLED' && (item.LOCATION_INSTALLED != '' && item.LOCATION_INSTALLED != undefined)">{{'Location Installed' | translate}}</p>
                      <p *ngIf="view != 'INSPECTION_STATUS' && (item.INSPECTION_STATUS != '' && item.INSPECTION_STATUS != undefined)">{{'Inspection Status' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'USER_ID' && (item.USER_ID != '' && item.USER_ID != undefined)">{{'User Id' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_BY' && (item.CREATED_BY != '' && item.CREATED_BY != undefined)">{{'Created By' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_DATE' && (item.CREATED_DATE != '' && item.CREATED_DATE != undefined)">{{'Created Date' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_BY' && (item.LAST_MODIFIED_BY != '' && item.LAST_MODIFIED_BY != undefined)">{{'Modified By' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_DATE' && (item.LAST_MODIFIED_DATE != '' && item.LAST_MODIFIED_DATE != undefined)">{{'Modified Date' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'LINE_POSITION' && (item.LINE_POSITION != '' && item.LINE_POSITION != undefined)">{{'Line Position' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'PRODUCT_LOCATION' && (item.PRODUCT_LOCATION != '' && item.PRODUCT_LOCATION != undefined)">{{'Product Location' | translate}}</p> -->

                    </div>
                    <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                      <!-- <p *ngIf="view != 'INSPECTION_ID' && (item.INSPECTION_ID != '' && item.INSPECTION_ID != undefined)">{{item.INSPECTION_ID}}</p> -->
                      <!-- <p *ngIf="view != 'EXTERNAL_ID'  && (item.EXTERNAL_ID != '' && item.EXTERNAL_ID != undefined)">{{item.EXTERNAL_ID}} </p> -->
                      <!-- <p *ngIf="view != 'RPS'   && (item.RPS != '' && item.RPS != undefined)">{{item.RPS}}</p> -->
                      <!-- <p *ngIf="view != 'RFT_NUM'  && (item.RFT_NUM != '' && item.RFT_NUM != undefined)">{{item.RFT_NUM}} </p> -->
                      <!-- <p *ngIf="view != 'ACCOUNT_NAME'  && (item.ACCOUNT_NAME != '' && item.ACCOUNT_NAME != undefined)">{{item.ACCOUNT_NAME}}</p>
                      <p *ngIf="view != 'ASSET_NAME'  && (item.ASSET_NAME != '' && item.ASSET_NAME != undefined)">{{item.ASSET_NAME}}</p>
                      <p *ngIf="view != 'MANUFACTURER'  && (item.MANUFACTURER != '' && item.MANUFACTURER != undefined)">{{item.MANUFACTURER}}</p>
                      <p *ngIf="view != 'INDUSTRY'  && (item.INDUSTRY != '' && item.INDUSTRY != undefined)">{{item.INDUSTRY}}</p>
                      <p *ngIf="view != 'APPLICATION'  && (item.APPLICATION != '' && item.APPLICATION != undefined)">{{item.APPLICATION}}</p>
                      <p *ngIf="view != 'PRODUCT_TYPE'  && (item.PRODUCT_TYPE != '' && item.PRODUCT_TYPE != undefined)">{{item.PRODUCT_TYPE}}</p> -->
                      <p *ngIf="view != 'PRODUCT'  && (item.PRODUCT != '' && item.PRODUCT != undefined)">
                        {{item.PRODUCT}}</p>
                      <!-- <p *ngIf="view != 'PRODUCT_CODE'  && (item.PRODUCT_CODE != '' && item.PRODUCT_CODE != undefined)">{{item.PRODUCT_CODE}}</p>
                      <p *ngIf="view != 'PRODUCT_DESC'  && (item.PRODUCT_DESC != '' && item.PRODUCT_DESC != undefined)">{{item.PRODUCT_DESC}}</p> -->
                      <p *ngIf="view != 'COLOR'  && (item.COLOR != '' && item.COLOR != undefined)">{{item.COLOR}}</p>
                      <!-- <p *ngIf="view != 'COLOR_OTHER'  && (item.COLOR_OTHER != '' && item.COLOR_OTHER != undefined)">{{item.COLOR_OTHER}}</p> -->
                      <p *ngIf="view != 'CONSTRUCTION'  && (item.CONSTRUCTION != '' && item.CONSTRUCTION != undefined)">
                        {{item.CONSTRUCTION}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_CONFIG'  && (item.ORIGINAL_CONFIG != '' && item.ORIGINAL_CONFIG != undefined)">{{item.ORIGINAL_CONFIG}}</p> -->
                      <p
                        *ngIf="view != 'PRODUCT_CONFIG'  && (item.PRODUCT_CONFIG != '' && item.PRODUCT_CONFIG != undefined)">
                        {{item.PRODUCT_CONFIG}}</p>
                      <p *ngIf="view != 'DIAM'  && (item.DIAM != '' && item.DIAM != undefined)">{{item.DIAM}}</p>
                      <p *ngIf="view != 'DIAM_UOM'  && (item.DIAM_UOM != '' && item.DIAM_UOM != undefined)">
                        {{item.DIAM_UOM}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_LENGTH'  && (item.ORIGINAL_LENGTH != '' && item.ORIGINAL_LENGTH != undefined)">{{item.ORIGINAL_LENGTH}}</p> -->
                      <p
                        *ngIf="view != 'CURRENT_LENGTH'  && (item.CURRENT_LENGTH != '' && item.CURRENT_LENGTH != undefined)">
                        {{item.CURRENT_LENGTH}}</p>
                      <p
                        *ngIf="view != 'INSPECTED_LENGTH'  && (item.INSPECTED_LENGTH != '' && item.INSPECTED_LENGTH != undefined)">
                        {{item.INSPECTED_LENGTH}}</p>
                      <p *ngIf="view != 'LENGTH_UOM'  && (item.LENGTH_UOM != '' && item.LENGTH_UOM != undefined)">
                        {{item.LENGTH_UOM}}</p>
                      <p *ngIf="view != 'HAS_CHAFE'  && (item.HAS_CHAFE != '' && item.HAS_CHAFE != undefined)"><span
                          *ngIf="item.HAS_CHAFE == 1">{{'Yes' | translate}}</span><span
                          *ngIf="item.HAS_CHAFE == 0">{{'No' | translate}}</span></p>
                      <p *ngIf="view != 'CHAFE_TYPE'  && (item.CHAFE_TYPE != '' && item.CHAFE_TYPE != undefined)">
                        {{item.CHAFE_TYPE}}</p>
                      <!-- <p *ngIf="view != 'OTHER_CHAFE'  && (item.OTHER_CHAFE != '' && item.OTHER_CHAFE != undefined)">{{item.OTHER_CHAFE}}</p> -->
                      <p *ngIf="view != 'IS_JACKETED'  && (item.IS_JACKETED != '' && item.IS_JACKETED != undefined)">
                        <span *ngIf="item.IS_JACKETED == 1">{{'Yes' | translate}}</span><span
                          *ngIf="item.IS_JACKETED == 0">{{'No' | translate}}</span></p>
                      <!-- <p *ngIf="view != 'INSTALLED_DATE'  && (item.INSTALLED_DATE != '' && item.INSTALLED_DATE != undefined)">{{item.INSTALLED_DATE}}</p>
                      <p *ngIf="view != 'INSTALLED_STATUS'  && (item.INSTALLED_STATUS != '' && item.INSTALLED_STATUS != undefined)">{{item.INSTALLED_STATUS}}</p>
                      <p *ngIf="view != 'LOCATION_INSTALLED'  && (item.LOCATION_INSTALLED != '' && item.LOCATION_INSTALLED != undefined)">{{item.LOCATION_INSTALLED}}</p>
                      <p *ngIf="view != 'INSPECTION_STATUS'  && (item.INSPECTION_STATUS != '' && item.INSPECTION_STATUS != undefined)" [ngClass]="item.status !='Sync Faild' ? 'syncFaildState' : 'inProgressState'"> <span style="color:black">{{item.INSPECTION_STATUS}}</span></p> -->
                      <!-- <p *ngIf="view != 'USER_ID'  && (item.USER_ID != '' && item.USER_ID != undefined)">{{item.USER_ID}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_BY'  && (item.CREATED_BY != '' && item.CREATED_BY != undefined)">{{item.CREATED_BY}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_DATE'  && (item.CREATED_DATE != '' && item.CREATED_DATE != undefined)">{{item.CREATED_DATE}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_BY'  && (item.LAST_MODIFIED_BY != '' && item.LAST_MODIFIED_BY != undefined)">{{item.LAST_MODIFIED_BY}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_DATE'  && (item.LAST_MODIFIED_DATE != '' && item.LAST_MODIFIED_DATE != undefined)">{{item.LAST_MODIFIED_DATE}}</p> -->
                      <!-- <p *ngIf="view != 'LINE_POSITION'  && (item.LINE_POSITION != '' && item.LINE_POSITION != undefined)">{{item.LINE_POSITION}}</p> -->
                      <!-- <p *ngIf="view != 'PRODUCT_LOCATION'  && (item.PRODUCT_LOCATION != '' && item.PRODUCT_LOCATION != undefined)">{{item.PRODUCT_LOCATION}}</p> -->
                    </div>
                  </div>
                </div>
              </ion-text>
              <ion-button color="danger" fill="outline" *ngIf="item.SYNC_STATUS == '3'"
                (click)="getInfoMsg($event, item)">Error</ion-button>

              <ion-buttons style="float:right;padding-top:10px;">
                <ion-button style="padding-bottom: 8px;" (click)="inProgressItemTrash(i,item)">
                  <fa-icon class="icon-style" slot="end" icon="trash" style="color:rgba(224, 77, 77, 0.979)"
                    class="fa-lg"></fa-icon>
                </ion-button>&nbsp;
                <!-- <ion-button style="padding-bottom: 8px;" [disabled]="item.INSPECTION_STATUS == 'In Progress' || item.SYNC_STATUS == '2' || item.SYNC_STATUS == '1' || syncButtonClicked == true" (click)="syncInspection(item, i)"> -->
                <!-- <ion-button style="padding-bottom: 8px;" (click)="syncInspection(item, i)"> -->
                <!-- <fa-icon class="icon-style"  slot="end" icon="sync-alt" class="fa-lg"></fa-icon>
                </ion-button>&nbsp; -->
                <ion-button style="padding-bottom: 8px;" (click)="viewInProgressInsp(item)">
                  <fa-icon class="icon-style" slot="end" icon="eye" class="fa-lg"></fa-icon>
                </ion-button>
              </ion-buttons>
            </ion-label>
          </ion-item>
        </ion-item-sliding>
      </ion-list>
    </div>
    <div *ngIf='historyList && historyList.length == 0'
      style="width: 100% !important; text-align: center !important; padding-top: 30px !important">
      {{'No inspections found.' | translate}}</div>
  </div>
  <!--New Inspection div -->
  <div *ngIf="inspectionType == 'new'" style="padding:40px 60px 0 60px">
    <ion-card (click)="helpService.helpMode ? '' : plannedInspection()" class="ion-activatable"
      style="border-radius: 11px">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/inspection1.jpg" style="height:130px;" />
      <ion-label>
        <div
          style="color:rgb(12, 11, 11); font-size: 16px;background-color:rgb(173, 194, 207);height:40px;text-align: center;padding-top:9px">
          {{'Planned Inspection' | translate}}</div>
      </ion-label>
    </ion-card>
    <ion-card (click)="helpService.helpMode ? '' : adHocInspect()" class="ion-activatable" style="border-radius: 11px">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/inspection1.jpg" style="height:130px;" />
      <ion-label>
        <div
          style="color:rgb(7, 7, 7); font-size: 16px;background-color:rgb(173, 194, 207);height:40px;text-align: center;padding-top:9px">
          {{'Ad-hoc Inspection' | translate}}</div>
      </ion-label>
    </ion-card>
  </div>

  <!--History Inspection div -->
  <div *ngIf="inspectionType == 'history'" style="padding-top:2px;">

    <div style="padding-top:2px">
      <ion-list>
        <ion-item-sliding *ngFor="let item of completedArray; let i = index">
          <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);padding-left:10px;">
            <ion-label (click)="viewCompletedInsp(item)">

              <h2 style="color:rgb(37, 91, 145)">{{'Cert' | translate}} # : {{item.CERT_NAME}}</h2>

              <p *ngIf="view == 'INSPECTION_ID'">{{'Inspection Id' | translate}} : {{item.INSPECTION_ID}}</p>
              <p *ngIf="view == 'EXTERNAL_ID'">{{'External Id' | translate}} : {{item.EXTERNAL_ID}} </p>
              <p *ngIf="view == 'RPS'">{{'RPS' | translate}} : {{item.RPS}}</p>
              <p *ngIf="view == 'RFT_NUM'">{{'RFT Number' | translate}} : {{item.RFT_NUM}} </p>
              <p *ngIf="view == 'ACCOUNT_NAME'">{{'Account Id' | translate}} : {{item.ACCOUNT_NAME}}</p>
              <p *ngIf="view == 'ASSET_NAME'">{{'Asset Id' | translate}} : {{item.ASSET_NAME}}</p>
              <p *ngIf="view == 'MANUFACTURER'">{{'Manufacturer' | translate}} : {{item.MANUFACTURER}}</p>
              <p *ngIf="view == 'INDUSTRY'">{{'Industry' | translate}} : {{item.INDUSTRY}}</p>
              <p *ngIf="view == 'APPLICATION'">{{'Application' | translate}} : {{item.APPLICATION}}</p>
              <p *ngIf="view == 'PRODUCT_TYPE'">{{'Product Type' | translate}} : {{item.PRODUCT_TYPE}}</p>
              <p *ngIf="view == 'PRODUCT'">{{'Product' | translate}} : {{item.PRODUCT}}</p>
              <p *ngIf="view == 'PRODUCT_CODE'">{{'Product Code' | translate}} : {{item.PRODUCT_CODE}}</p>
              <p *ngIf="view == 'PRODUCT_DESC'">{{'Product Description' | translate}} : {{item.PRODUCT_DESC}}</p>
              <p *ngIf="view == 'COLOR'">{{'Color' | translate}} : {{item.COLOR}}</p>
              <p *ngIf="view == 'COLOR_OTHER'">{{'Color Other' | translate}} : {{item.COLOR_OTHER}}</p>
              <p *ngIf="view == 'CONSTRUCTION'">{{'Construction' | translate}} : {{item.CONSTRUCTION}}</p>
              <p *ngIf="view == 'ORIGINAL_CONFIG'">{{'Original Configuration' | translate}} : {{item.ORIGINAL_CONFIG}}
              </p>
              <p *ngIf="view == 'PRODUCT_CONFIG'">{{'Product Configuration' | translate}} : {{item.PRODUCT_CONFIG}}</p>
              <p *ngIf="view == 'DIAM'">{{'Diameter' | translate}} : {{item.DIAM}}</p>
              <p *ngIf="view == 'DIAM_UOM'">{{'Diameter UOM' | translate}} : {{item.DIAM_UOM}}</p>
              <p *ngIf="view == 'ORIGINAL_LENGTH'">{{'Original Length' | translate}} : {{item.ORIGINAL_LENGTH}}</p>
              <p *ngIf="view == 'CURRENT_LENGTH'">{{'Current Length' | translate}} : {{item.CURRENT_LENGTH}}</p>
              <p *ngIf="view == 'INSPECTED_LENGTH'">{{'Inspection Length' | translate}} : {{item.INSPECTED_LENGTH}}</p>
              <p *ngIf="view == 'LENGTH_UOM'">{{'Length UOM' | translate}} : {{item.LENGTH_UOM}}</p>
              <p *ngIf="view == 'HAS_CHAFE'">{{'Has Chafe' | translate}} : <span
                  *ngIf="item.HAS_CHAFE == 1">{{'Yes' | translate}}</span><span
                  *ngIf="item.HAS_CHAFE == 0">{{'No' | translate}}</span></p>
              <p *ngIf="view == 'CHAFE_TYPE'">{{'Chafe Type' | translate}} : {{item.CHAFE_TYPE}}</p>
              <p *ngIf="view == 'OTHER_CHAFE'">{{'Other Chafe' | translate}} : {{item.OTHER_CHAFE}}</p>
              <p *ngIf="view == 'IS_JACKETED'">{{'Is Jacketed' | translate}} : <span
                  *ngIf="item.IS_JACKETED == 1">{{'Yes' | translate}}</span><span
                  *ngIf="item.IS_JACKETED == 0">{{'No' | translate}}</span></p>
              <p *ngIf="view == 'INSTALLED_DATE'">{{'Installed Date' | translate}} : {{item.INSTALLED_DATE}}</p>
              <p *ngIf="view == 'INSTALLED_STATUS'">{{'Installed Status' | translate}} : {{item.INSTALLED_STATUS}}</p>
              <p *ngIf="view == 'LOCATION_INSTALLED'">{{'Location Installed' | translate}} : {{item.LOCATION_INSTALLED}}
              </p>
              <p *ngIf="view == 'INSPECTION_STATUS'">{{'Inspection Status' | translate}} : {{item.INSPECTION_STATUS}}
              </p>
              <p *ngIf="view == 'USER_ID'">{{'User Id' | translate}} : {{item.USER_ID}}</p>
              <p *ngIf="view == 'CREATED_BY'">{{'Created By' | translate}} : {{item.CREATED_BY}}</p>
              <p *ngIf="view == 'CREATED_DATE'">{{'Created Date' | translate}} : {{item.CREATED_DATE}}</p>
              <p *ngIf="view == 'LAST_MODIFIED_BY'">{{'Modified By' | translate}} : {{item.LAST_MODIFIED_BY}}</p>
              <p *ngIf="view == 'LAST_MODIFIED_DATE'">{{'Modified Date' | translate}} : {{item.LAST_MODIFIED_DATE}}</p>
              <p *ngIf="view == 'LINE_POSITION'">{{'Line Position' | translate}} : {{item.LINE_POSITION}}</p>
              <p *ngIf="view == 'PRODUCT_LOCATION'">{{'Product Location' | translate}} : {{item.PRODUCT_LOCATION}}</p>

            </ion-label>
            <ion-buttons (click)="icon(i)">
              <ion-button style="padding-bottom: 8px;">
                <ion-icon slot="icon-only" slot="end" [name]="checkSectionIsOpen(i) ? 'ios-arrow-up' :'ios-arrow-down'"
                  style="color:rgb(41, 40, 40)"></ion-icon>
              </ion-button>
            </ion-buttons>
          </ion-item>

          <ion-item *ngIf="checkSectionIsOpen(i)" lines="none"
            style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829);">
            <ion-label style="padding-left:20px;">
              <ion-text color="primary" *ngIf="checkSectionIsOpen(i)">
                <div>
                  <div style="display: inline-flex !important; width: 100% !important">
                    <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">

                      <!-- <p *ngIf="view != 'INSPECTION_ID' && (item.INSPECTION_ID != '' && item.INSPECTION_ID != undefined)">{{'Inspection Id' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'EXTERNAL_ID'  && (item.EXTERNAL_ID != '' && item.EXTERNAL_ID != undefined)">{{'External Id' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'RPS'  && (item.RPS != '' && item.RPS != undefined)">{{'RPS' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'RFT_NUM'  && (item.RFT_NUM != '' && item.RFT_NUM != undefined)">{{'RFT Number' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'ACCOUNT_NAME'  && (item.ACCOUNT_NAME != '' && item.ACCOUNT_NAME != undefined)">{{'Account Id' | translate}}</p>
                      <p *ngIf="view != 'ASSET_NAME'  && (item.ASSET_NAME != '' && item.ASSET_NAME != undefined)">{{'Asset Id' | translate}}</p>
                      <p *ngIf="view != 'MANUFACTURER'  && (item.MANUFACTURER != '' && item.MANUFACTURER != undefined)">{{'Manufacturer' | translate}}</p>
                      <p *ngIf="view != 'INDUSTRY'  && (item.INDUSTRY != '' && item.INDUSTRY != undefined)">{{'Industry' | translate}}</p>
                      <p *ngIf="view != 'APPLICATION'  && (item.APPLICATION != '' && item.APPLICATION != undefined)">{{'Application' | translate}}</p>
                      <p *ngIf="view != 'PRODUCT_TYPE'  && (item.PRODUCT_TYPE != '' && item.PRODUCT_TYPE != undefined)">{{'Product Type' | translate}}</p> -->
                      <p *ngIf="view != 'PRODUCT' && (item.PRODUCT != '' && item.PRODUCT != undefined)">
                        {{'Product' | translate}}</p>
                      <!-- <p *ngIf="view != 'PRODUCT_CODE' && (item.PRODUCT_CODE != '' && item.PRODUCT_CODE != undefined)">{{'Product Code' | translate}}</p>
                      <p *ngIf="view != 'PRODUCT_DESC' && (item.PRODUCT_DESC != '' && item.PRODUCT_DESC != undefined)">{{'Product Description' | translate}}</p> -->
                      <p *ngIf="view != 'COLOR' && (item.COLOR != '' && item.COLOR != undefined)">
                        {{'Color' | translate}}</p>
                      <!-- <p *ngIf="view != 'COLOR_OTHER' && (item.COLOR_OTHER != '' && item.COLOR_OTHER != undefined)">{{'Color Other' | translate}}</p> -->
                      <p *ngIf="view != 'CONSTRUCTION' && (item.CONSTRUCTION != '' && item.CONSTRUCTION != undefined)">
                        {{'Construction' | translate}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_CONFIG' && (item.ORIGINAL_CONFIG != '' && item.ORIGINAL_CONFIG != undefined)">{{'Original Configuration' | translate}}</p> -->
                      <p
                        *ngIf="view != 'PRODUCT_CONFIG' && (item.PRODUCT_CONFIG != '' && item.PRODUCT_CONFIG != undefined)">
                        {{'Product Configuration' | translate}}</p>
                      <p *ngIf="view != 'DIAM' && (item.DIAM != '' && item.DIAM != undefined)">
                        {{'Diameter' | translate}}</p>
                      <p *ngIf="view != 'DIAM_UOM' && (item.DIAM_UOM != '' && item.DIAM_UOM != undefined)">
                        {{'Diameter UOM' | translate}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_LENGTH' && (item.ORIGINAL_LENGTH != '' && item.ORIGINAL_LENGTH != undefined)">{{'Original Length' | translate}}</p> -->
                      <p
                        *ngIf="view != 'CURRENT_LENGTH' && (item.CURRENT_LENGTH != '' && item.CURRENT_LENGTH != undefined)">
                        {{'Current Length' | translate}} </p>
                      <p
                        *ngIf="view != 'INSPECTED_LENGTH' && (item.INSPECTED_LENGTH != '' && item.INSPECTED_LENGTH != undefined)">
                        {{'Inspection Length' | translate}} </p>
                      <p *ngIf="view != 'LENGTH_UOM' && (item.LENGTH_UOM != '' && item.LENGTH_UOM != undefined)">
                        {{'Length UOM' | translate}}</p>
                      <p *ngIf="view != 'HAS_CHAFE' && (item.HAS_CHAFE != '' && item.HAS_CHAFE != undefined)">
                        {{'Has Chafe' | translate}}</p>
                      <p *ngIf="view != 'CHAFE_TYPE' && (item.CHAFE_TYPE != '' && item.CHAFE_TYPE != undefined)">
                        {{'Chafe Type' | translate}}</p>
                      <!-- <p *ngIf="view != 'OTHER_CHAFE' && (item.OTHER_CHAFE != '' && item.OTHER_CHAFE != undefined)">{{'Other Chafe' | translate}}</p> -->
                      <p *ngIf="view != 'IS_JACKETED' && (item.IS_JACKETED != '' && item.IS_JACKETED != undefined)">
                        {{'Is Jacketed' | translate}}</p>
                      <!-- <p *ngIf="view != 'INSTALLED_DATE' && (item.INSTALLED_DATE != '' && item.INSTALLED_DATE != undefined)">{{'Installed Date' | translate}}</p>
                      <p *ngIf="view != 'INSTALLED_STATUS' && (item.INSTALLED_STATUS != '' && item.INSTALLED_STATUS != undefined)">{{'Installed Status' | translate}}</p>
                      <p *ngIf="view != 'LOCATION_INSTALLED' && (item.LOCATION_INSTALLED != '' && item.LOCATION_INSTALLED != undefined)">{{'Location Installed' | translate}}</p>
                      <p *ngIf="view != 'INSPECTION_STATUS' && (item.INSPECTION_STATUS != '' && item.INSPECTION_STATUS != undefined)">{{'Inspection Status' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'USER_ID' && (item.USER_ID != '' && item.USER_ID != undefined)">{{'User Id' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_BY' && (item.CREATED_BY != '' && item.CREATED_BY != undefined)">{{'Created By' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_DATE' && (item.CREATED_DATE != '' && item.CREATED_DATE != undefined)">{{'Created Date' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_BY' && (item.LAST_MODIFIED_BY != '' && item.LAST_MODIFIED_BY != undefined)">{{'Modified By' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_DATE' && (item.LAST_MODIFIED_DATE != '' && item.LAST_MODIFIED_DATE != undefined)">{{'Modified Date' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'LINE_POSITION' && (item.LINE_POSITION != '' && item.LINE_POSITION != undefined)">{{'Line Position' | translate}}</p> -->
                      <!-- <p *ngIf="view != 'PRODUCT_LOCATION' && (item.PRODUCT_LOCATION != '' && item.PRODUCT_LOCATION != undefined)">{{'Product Location' | translate}}</p> -->

                    </div>
                    <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                      <!-- <p *ngIf="view != 'INSPECTION_ID' && (item.INSPECTION_ID != '' && item.INSPECTION_ID != undefined)">{{item.INSPECTION_ID}}</p> -->
                      <!-- <p *ngIf="view != 'EXTERNAL_ID'  && (item.EXTERNAL_ID != '' && item.EXTERNAL_ID != undefined)">{{item.EXTERNAL_ID}} </p> -->
                      <!-- <p *ngIf="view != 'RPS'   && (item.RPS != '' && item.RPS != undefined)">{{item.RPS}}</p> -->
                      <!-- <p *ngIf="view != 'RFT_NUM'  && (item.RFT_NUM != '' && item.RFT_NUM != undefined)">{{item.RFT_NUM}} </p> -->
                      <!-- <p *ngIf="view != 'ACCOUNT_NAME'  && (item.ACCOUNT_NAME != '' && item.ACCOUNT_NAME != undefined)">{{item.ACCOUNT_NAME}}</p>
                      <p *ngIf="view != 'ASSET_NAME'  && (item.ASSET_NAME != '' && item.ASSET_NAME != undefined)">{{item.ASSET_NAME}}</p>
                      <p *ngIf="view != 'MANUFACTURER'  && (item.MANUFACTURER != '' && item.MANUFACTURER != undefined)">{{item.MANUFACTURER}}</p>
                      <p *ngIf="view != 'INDUSTRY'  && (item.INDUSTRY != '' && item.INDUSTRY != undefined)">{{item.INDUSTRY}}</p>
                      <p *ngIf="view != 'APPLICATION'  && (item.APPLICATION != '' && item.APPLICATION != undefined)">{{item.APPLICATION}}</p>
                      <p *ngIf="view != 'PRODUCT_TYPE'  && (item.PRODUCT_TYPE != '' && item.PRODUCT_TYPE != undefined)">{{item.PRODUCT_TYPE}}</p> -->
                      <p *ngIf="view != 'PRODUCT'  && (item.PRODUCT != '' && item.PRODUCT != undefined)">
                        {{item.PRODUCT}}</p>
                      <!-- <p *ngIf="view != 'PRODUCT_CODE'  && (item.PRODUCT_CODE != '' && item.PRODUCT_CODE != undefined)">{{item.PRODUCT_CODE}}</p>
                      <p *ngIf="view != 'PRODUCT_DESC'  && (item.PRODUCT_DESC != '' && item.PRODUCT_DESC != undefined)">{{item.PRODUCT_DESC}}</p> -->
                      <p *ngIf="view != 'COLOR'  && (item.COLOR != '' && item.COLOR != undefined)">{{item.COLOR}}</p>
                      <!-- <p *ngIf="view != 'COLOR_OTHER'  && (item.COLOR_OTHER != '' && item.COLOR_OTHER != undefined)">{{item.COLOR_OTHER}}</p> -->
                      <p *ngIf="view != 'CONSTRUCTION'  && (item.CONSTRUCTION != '' && item.CONSTRUCTION != undefined)">
                        {{item.CONSTRUCTION}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_CONFIG'  && (item.ORIGINAL_CONFIG != '' && item.ORIGINAL_CONFIG != undefined)">{{item.ORIGINAL_CONFIG}}</p> -->
                      <p
                        *ngIf="view != 'PRODUCT_CONFIG'  && (item.PRODUCT_CONFIG != '' && item.PRODUCT_CONFIG != undefined)">
                        {{item.PRODUCT_CONFIG}}</p>
                      <p *ngIf="view != 'DIAM'  && (item.DIAM != '' && item.DIAM != undefined)">{{item.DIAM}}</p>
                      <p *ngIf="view != 'DIAM_UOM'  && (item.DIAM_UOM != '' && item.DIAM_UOM != undefined)">
                        {{item.DIAM_UOM}}</p>
                      <!-- <p *ngIf="view != 'ORIGINAL_LENGTH'  && (item.ORIGINAL_LENGTH != '' && item.ORIGINAL_LENGTH != undefined)">{{item.ORIGINAL_LENGTH}}</p> -->
                      <p
                        *ngIf="view != 'CURRENT_LENGTH'  && (item.CURRENT_LENGTH != '' && item.CURRENT_LENGTH != undefined)">
                        {{item.CURRENT_LENGTH}}</p>
                      <p
                        *ngIf="view != 'INSPECTED_LENGTH'  && (item.INSPECTED_LENGTH != '' && item.INSPECTED_LENGTH != undefined)">
                        {{item.INSPECTED_LENGTH}}</p>
                      <p *ngIf="view != 'LENGTH_UOM'  && (item.LENGTH_UOM != '' && item.LENGTH_UOM != undefined)">
                        {{item.LENGTH_UOM}}</p>
                      <p *ngIf="view != 'HAS_CHAFE'  && (item.HAS_CHAFE != '' && item.HAS_CHAFE != undefined)"><span
                          *ngIf="item.HAS_CHAFE == 1">{{'Yes' | translate}}</span><span
                          *ngIf="item.HAS_CHAFE == 0">{{'No' | translate}}</span>{{item.HAS_CHAFE}}</p>
                      <p *ngIf="view != 'CHAFE_TYPE'  && (item.CHAFE_TYPE != '' && item.CHAFE_TYPE != undefined)">
                        {{item.CHAFE_TYPE}}</p>
                      <!-- <p *ngIf="view != 'OTHER_CHAFE'  && (item.OTHER_CHAFE != '' && item.OTHER_CHAFE != undefined)">{{item.OTHER_CHAFE}}</p> -->
                      <p *ngIf="view != 'IS_JACKETED'  && (item.IS_JACKETED != '' && item.IS_JACKETED != undefined)">
                        <span *ngIf="item.IS_JACKETED == 1">{{'Yes' | translate}}</span><span
                          *ngIf="item.IS_JACKETED == 0">{{'No' | translate}}</span></p>
                      <!-- <p *ngIf="view != 'INSTALLED_DATE'  && (item.INSTALLED_DATE != '' && item.INSTALLED_DATE != undefined)">{{item.INSTALLED_DATE}}</p>
                      <p *ngIf="view != 'INSTALLED_STATUS'  && (item.INSTALLED_STATUS != '' && item.INSTALLED_STATUS != undefined)">{{item.INSTALLED_STATUS}}</p>
                      <p *ngIf="view != 'LOCATION_INSTALLED'  && (item.LOCATION_INSTALLED != '' && item.LOCATION_INSTALLED != undefined)">{{item.LOCATION_INSTALLED}}</p>
                      <p *ngIf="view != 'INSPECTION_STATUS'  && (item.INSPECTION_STATUS != '' && item.INSPECTION_STATUS != undefined)" [ngClass]="item.status !='Sync Faild' ? 'syncFaildState' : 'inProgressState'"> <span style="color:black">{{item.INSPECTION_STATUS}}</span></p> -->
                      <!-- <p *ngIf="view != 'USER_ID'  && (item.USER_ID != '' && item.USER_ID != undefined)">{{item.USER_ID}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_BY'  && (item.CREATED_BY != '' && item.CREATED_BY != undefined)">{{item.CREATED_BY}}</p> -->
                      <!-- <p *ngIf="view != 'CREATED_DATE'  && (item.CREATED_DATE != '' && item.CREATED_DATE != undefined)">{{item.CREATED_DATE}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_BY'  && (item.LAST_MODIFIED_BY != '' && item.LAST_MODIFIED_BY != undefined)">{{item.LAST_MODIFIED_BY}}</p> -->
                      <!-- <p *ngIf="view != 'LAST_MODIFIED_DATE'  && (item.LAST_MODIFIED_DATE != '' && item.LAST_MODIFIED_DATE != undefined)">{{item.LAST_MODIFIED_DATE}}</p> -->
                      <!-- <p *ngIf="view != 'LINE_POSITION'  && (item.LINE_POSITION != '' && item.LINE_POSITION != undefined)">{{item.LINE_POSITION}}</p> -->
                      <!-- <p *ngIf="view != 'PRODUCT_LOCATION'  && (item.PRODUCT_LOCATION != '' && item.PRODUCT_LOCATION != undefined)">{{item.PRODUCT_LOCATION}}</p> -->
                    </div>
                  </div>
                </div>
              </ion-text>

              <ion-buttons style="float:right;padding-top:10px;">
                <ion-button style="padding-bottom: 8px;" (click)="historyItemDelete(i,item)">
                  <fa-icon class="icon-style" slot="end" icon="trash" style="color:rgba(224, 77, 77, 0.979)"
                    class="fa-lg"></fa-icon>
                </ion-button>&nbsp;
                <!-- <ion-button style="padding-bottom: 8px;" [disabled]="!syncDisable">
                    <fa-icon class="icon-style"  slot="end" icon="sync-alt" class="fa-lg"></fa-icon>
                  </ion-button>&nbsp; -->
                <ion-button style="padding-bottom: 8px;" (click)="viewCompletedInsp(item)">
                  <fa-icon class="icon-style" slot="end" icon="eye" class="fa-lg"></fa-icon>
                </ion-button>
              </ion-buttons>
            </ion-label>
          </ion-item>
        </ion-item-sliding>
      </ion-list>
    </div>





    <ion-item-sliding *ngFor="let item of measurementArray; let i = index">
      <ion-item class="ion-activatable" (click)="viewHistoricalInfo(item)">
        <ion-label>
          <h2 style="color:rgb(37, 91, 145)">{{'Cert #: 123-ABC-5436-D' | translate}}</h2>
          <h6>Type : <span style="color:rgb(121, 120, 120)">{{item.type}}</span></h6>
          <!-- <h6> {{'Manufacturer' | translate}} : <span style="color:rgb(121, 120, 120)">{{'Samson' | translate}}</span>
          </h6> -->
          <h6 *ngIf="view == 'Inspected Date'">{{'Inspected' | translate}} : <span
              style="color:rgb(121, 120, 120)">{{item.date}}</span></h6>
          <h6 *ngIf="view == 'Location'">{{'Location' | translate}} : <span
              style="color:rgb(121, 120, 120)">Texas</span>
          </h6>
        </ion-label>
        <!-- <span slot="end" style="color:rgba(146, 146, 146, 0.952);font-size:16px;">{{'Completed' | translate}}</span> -->
        <ion-icon name="ios-arrow-forward" color="medium" slot="end"></ion-icon>
      </ion-item>
      <ion-item-options side="end">
        <ion-item-option color="danger" (click)="historyItemDelete(item)">
          <ion-icon slot="icon-only" name="trash"></ion-icon>
        </ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </div>

  <!--Help fab icon -->
  <ion-fab *ngIf="helpService.helpMode" class="shake" vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button size="small" color="primary" (click)="helpService.openhelpMenu()"
      [disabled]='!helpService.helpMode'>
      <fa-icon class="icon-style" icon="arrow-left"></fa-icon>
    </ion-fab-button>
  </ion-fab>

</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>