import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestResourceAbrasionPageRoutingModule } from './guest-resource-abrasion-routing.module';

import { GuestResourceAbrasionPage } from './guest-resource-abrasion.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestResourceAbrasionPageRoutingModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestResourceAbrasionPage]
})
export class GuestResourceAbrasionPageModule {}
