<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/guest-home" (click)='back()'></ion-back-button>
    </ion-buttons>
    <ion-title>External Abrasion Comparator</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <!-- Image Section -->
  <div style="height:100%"> 

  <ion-row style="padding: 10px 17px 10px 17px;height: 100%;" *ngIf="device.platform !== 'browser'">
    <div *ngIf="helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of cut'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers
        (click)="takePicture()"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
    </div>

    <div *ngIf="!helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:100%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;height: 60%;">
      <img *ngIf="imageData==undefined || imageData==''" src="./assets/img/addImage.png"
        (click)="takePicture()"
        style="object-fit:contain;border-radius: 5px;padding-top:12px; max-height: 100%;" />
        <img [src]="imageData"
        (click)="takePicture()"
        style="object-fit:contain;border-radius: 5px;padding-top:12px; max-height: 100%;" />
    </div>
    <div *ngIf="!helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:100%;height:300px;padding:2px 2px 2px 2px;border-radius: 5px;display: flex;overflow:scroll;height: 40%;">
      <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
    </div>
  </ion-row>

  <ion-row style="padding: 10px 17px 10px 17px;height: 100%;" *ngIf="device.platform == 'browser'" >
    <input id="myInput" type="file" style="visibility:hidden; display: none;" accept="image/x-png,image/jpeg" (change)="onFileSelected($event)"/>
    <div *ngIf="helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:100%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;height: 60%;" >
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers
        (click)="takePicture()"
        style="object-fit:contain;border-radius: 5px;padding-top:12px; max-height: 100%;" />
        <img [src]=imageData tooltip="{{'Tap to take a photo of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers
        (click)="takePicture()"
        style="object-fit:contain;border-radius: 5px;padding-top:12px; max-height: 100%;" />
        
    </div>

    <div *ngIf="!helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:100%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;height: 60%;">
      <img *ngIf="imageData==undefined || imageData==''" src="./assets/img/addImage.png"
        (click)="takePicture()"
        style="object-fit:contain;border-radius: 5px;padding-top:12px; max-height: 100%;" />
        <img [src]="imageData"
        (click)="takePicture()"
        style="object-fit:contain;border-radius: 5px;padding-top:12px; max-height: 100%;" />
    </div>
    <div *ngIf="!helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:100%;height:300px;padding:2px 2px 2px 2px;border-radius: 5px;display: flex;overflow:scroll;height: 40%;">
      <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
        <img src="./assets/img/External/<EMAIL>"
        style="object-fit:contain;border-radius: 5px;padding-top:12px" />
    </div>
  </ion-row>
  </div>

</ion-content>

<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>

