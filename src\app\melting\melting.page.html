<ion-header>
  <ion-toolbar>
    <ion-title>{{'Melting Measurement' | translate}}</ion-title>
    @if(!isEmpEditConfig && !isLoggedInUserEmployee) {
      <!-- ! show Back button when this component is navigated to, for customer login-->
      <ion-buttons slot="start">
        <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
      </ion-buttons>
   } @else if (isEmpEditConfig) {
      <!-- ! showing Cancel button when this component is opened as a modal, for employee login -->
      <ion-buttons slot="secondary">
        <ion-button (click)="closeSpliceModal()">Cancel</ion-button>
      </ion-buttons>
   }
    
    <ion-buttons slot="primary">
      <ion-button color="primary" (click)="historyPopover($event)" class="help-button-style">
        <fa-icon class="icon-style-help" icon="list"></fa-icon>
      </ion-button>
      <ion-button color="primary" (click)="helpService.switchMode(); disableFormFields();" class="help-button-style">
        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:15px 10px 0px 17px">
    <label>{{'OBSERVATION_START_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <div [formGroup]="startForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [disabled]='true' [(ngModel)]="measurementStart" maxlength="18"
              placeholder="{{'OBSERVATION_START_PLACEHOLDER' | translate}}"
              (keydown)="keyPressed($event, measurementStart, 'measurementStart')" step="any" inputmode="decimal"
              [required] formControlName="helpMeasurementStartCtrl">
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement length'|translate}}" positionV="bottom"
        [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('start')"></fa-icon>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <div [formGroup]="startForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [disabled]='true' [(ngModel)]="measurementStart" maxlength="18"
              placeholder="{{'OBSERVATION_START_PLACEHOLDER' | translate}}" (change)="onChangeDisable('start')"
              (keydown)="keyPressed($event, measurementStart, 'measurementStart')" step="any" inputmode="decimal"
              [required] formControlName="measurementStartCtrl">
            <mat-error *ngIf="hasErrorStart('start')">{{startErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </div>&nbsp;
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('start')"></fa-icon>
      </div>
    </div>
  </div>

  <div style="padding-top: 15px;">
    <ion-segment [disabled]='helpService.helpMode' [(ngModel)]="fieldSegment"
      (ionChange)="helpService.segmentChanged($event)" class="ion-segment-style" mode="ios"
      expand="block">
      <ion-segment-button value="length" mode="ios" class="ion-segment-button-style">
        <ion-label>{{'Length'| translate}}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="end" mode="ios" class="ion-segment-button-style">
        <ion-label>{{'End' | translate}}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'length'">
    <label>{{'OBSERVATION_LENGTH_LABEL' | translate:{ measurementName: lengthType } }}: <span
        style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <div [formGroup]="lengthForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [disabled]='helpService.helpMode || readOnly || isStartValid'
              [(ngModel)]="measurementLength" maxlength="18"
              placeholder="{{'OBSERVATION_LENGTH_PLACEHOLDER' | translate}}"
              (keydown)="keyPressed($event, measurementLength, 'length')" step="any" inputmode="decimal"
              formControlName="helpMeasurementLengthCtrl">
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement length'|translate}}" positionV="bottom"
        [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('length')"></fa-icon>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <div [formGroup]="lengthForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="measurementLength" maxlength="18"
              placeholder="{{'OBSERVATION_LENGTH_PLACEHOLDER' | translate}}" (change)="onChangeDisable('length')"
              (keydown)="keyPressed($event, measurementLength, 'length')" step="any" inputmode="decimal" [required]
              formControlName="measurementLengthCtrl">
            <mat-error *ngIf="hasErrorStart('length')">{{lengthErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('length')"></fa-icon>
      </div>
    </div>
  </div>

  <!-- <div style="width: 100%; text-align: center;">
    <label style="font-weight: bold;margin: 0px">OR</label>
  </div> -->

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'end'">
    <label>{{'OBSERVATION_END_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <div [formGroup]="endForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="measurementEnd" maxlength="18"
              placeholder="{{'OBSERVATION_END_PLACEHOLDER' | translate}}" step="any" inputmode="decimal" [required]
              formControlName="helpMeasurementEndCtrl">
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement length'|translate}}" positionV="bottom"
        [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('end')"></fa-icon>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <div [formGroup]="endForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="measurementEnd" maxlength="18"
              placeholder="{{'OBSERVATION_END_PLACEHOLDER' | translate}}" step="any" (change)="onChangeDisable('end')"
              inputmode="decimal" (keydown)="keyPressed($event, measurementEnd, 'end')" [required]
              formControlName="measurementEndCtrl">
            <mat-error *ngIf="hasErrorStart('end')">{{endErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('end')"></fa-icon>
      </div>
    </div>
  </div>

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'end'">
    <label>{{'OBSERVATION_LENGTH_LABEL' | translate:{ measurementName: lengthType } }}:</label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput id="measLength" type="text" [disabled]='helpService.helpMode || readOnly || isStartValid'
            [(ngModel)]="measurementLength" maxlength="18" placeholder="{{'Length Value' | translate}}"
            (keydown)="keyPressed($event, measurementLength, 'length')" step="any" inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="measurementLength" maxlength="18"
            placeholder="{{'Length Value' | translate}}" (keydown)="keyPressed($event, measurementLength, 'length')"
            step="any" inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>
  </div>

  <!-- <div style="width: 100%; text-align: center;">
    <label style="font-weight: bold;margin: 0px">OR</label>
  </div> -->

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'length'">
    <label>{{'OBSERVATION_END_LABEL' | translate}}:</label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput type="text" [disabled]='helpService.helpMode || readOnly || isStartValid' [(ngModel)]="ending"
            maxlength="18" placeholder="{{'End Value' | translate}}" (keydown)="keyPressed($event, ending, 'end')"
            step="any" inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput type="text" [disabled]='true' [(ngModel)]="measurementEnd" maxlength="18"
            placeholder="{{endLabel}}" (keydown)="keyPressed($event, ending, 'end')" step="any" inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>
  </div>

  <!--Layer and location options -->
  <div style="padding:15px 10px 0px 17px">
    <div>
      <div *ngIf='legOptinsList.length > 1' size="6" style="width: 100%;">
        <label style="font-size:15px;">{{'Observation Location, Leg' | translate}}</label>
      </div>
      <div *ngIf='(helpService.helpMode || readOnly) && legOptinsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;"
        tooltip="{{'Designate a leg number to each leg of the grommet or multi-loop assembly.'|translate}}"
        positionV="bottom" positionH="left" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>

        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover"
              [(ngModel)]="selectedLocationOption"
              style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
              placeholder="{{'Select location option' | translate }}">
              <ion-select-option *ngFor="let option of locationOptionsList" value="{{option}}">{{option}}
              </ion-select-option>
            </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering  [disabled]='true'
            placeholder="{{'Select location option' | translate}}" interface="popover"
            [(value)]="selectedLocationOption">
            <mat-select-trigger *ngIf="selectedLocationOption != undefined && selectedLocationOption != ''">
              {{selectedLocationOption.item}}
              <img width="200" height="70" [src]='selectedLocationOption.img' style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of legOptinsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img width="200" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div *ngIf='!(helpService.helpMode || readOnly) && locationOptionsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;">
        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover"
              [(ngModel)]="selectedLocationOption" (ionChange)="observationChanged()"
              style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
              placeholder="{{'Select location option' | translate }}">
              <ion-select-option *ngFor="let option of locationOptionsList" value="{{option}}">{{option}}
              </ion-select-option>
            </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering 
            placeholder="{{'Select location option' | translate}}" interface="popover"
            [(value)]="selectedLocationOption">
            <mat-select-trigger *ngIf="selectedLocationOption != undefined && selectedLocationOption != ''">
              {{selectedLocationOption.item}}
              <img width="200" height="70" [src]='selectedLocationOption.img' style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of legOptinsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img width="200" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <div>
      <div *ngIf='layerOptionsList.length > 1' size="6" style="width: 100%;">
        <label style="font-size:15px;">{{'Observation Location, Layer' | translate}}</label>
      </div>
      <div *ngIf='(helpService.helpMode || readOnly) && layerOptionsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;"
        tooltip="{{'Identify which layer of the rope has been affected by the damage.'|translate}}" positionV="bottom"
        positionH="left" [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover" [(ngModel)]="selectedLayerOption"
          style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
          placeholder="{{'Select layer option' | translate }}">
          <ion-select-option *ngFor="let option of layerOptionsList" value="{{option}}">{{option}}</ion-select-option>
        </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering [disabled]='true' placeholder="{{'Select layer option' | translate}}"
            interface="popover" [(value)]="selectedLayerOption">
            <mat-select-trigger *ngIf="selectedLayerOption != undefined && selectedLayerOption != ''">
              {{selectedLayerOption.item}}
              <img *ngIf="selectedLayerOption.img !=''" width="70" height="70" [src]='selectedLayerOption.img'
                style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of layerOptionsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img *ngIf="option.img !=''" width="70" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div *ngIf='!(helpService.helpMode || readOnly) && layerOptionsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;">
        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover" [(ngModel)]="selectedLayerOption"
          (ionChange)="layerChange($event)" style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
          placeholder="{{'Select layer option' | translate }}">
          <ion-select-option *ngFor="let option of layerOptionsList" value="{{option}}">{{option}}</ion-select-option>
        </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering (selectionChange)="layerChange($event)"
            placeholder="{{'Select layer option' | translate}}" interface="popover" [(value)]="selectedLayerOption">
            <mat-select-trigger *ngIf="selectedLayerOption != undefined && selectedLayerOption != ''">
              {{selectedLayerOption.item}}
              <img *ngIf="selectedLayerOption.img !=''" width="70" height="70" [src]='selectedLayerOption.img'
                style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of layerOptionsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img *ngIf="option.img !=''" width="70" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>

  <!-- Type of Damage -->
  <!-- <div style="padding:15px 10px 0px 17px">
    <label *ngIf='selectedTypeOfDamage'>{{'Type of Damage' | translate}} : {{selectedTypeOfDamageString}}</label>
  </div> -->

  <!-- Non Load baring section -->
  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="selectedTypeOfDamage == 'nonLoad'">
    <label style="padding-left:5px;">{{'Select the Severity Level' | translate}}: <span
        style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-radio-group [(ngModel)]="damageLevel" *ngIf="helpService.helpMode">
      <ion-row tooltip="{{'Select the melting level'|translate}}" positionV="bottom" positionH="right"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="mild" color="success" [disabled]='helpService.helpMode || readOnly'>{{'Mild' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Mild' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="moderate" color="warning"
              [disabled]='helpService.helpMode || readOnly'>{{'Moderate' | translate}} </ion-radio>
            <!-- <ion-label>{{'Moderate' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="severe" color="danger" [disabled]='helpService.helpMode || readOnly'>{{'Severe' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Severe' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
      </ion-row>
    </ion-radio-group>

    <ion-radio-group [(ngModel)]="damageLevel" *ngIf="!helpService.helpMode">
      <ion-row>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="mild" color="success" [disabled]='helpService.helpMode || readOnly'>{{'Mild' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Mild' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="moderate" color="warning"
              [disabled]='helpService.helpMode || readOnly'>{{'Moderate' | translate}}</ion-radio>
            <!-- <ion-label>{{'Moderate' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="severe" color="danger" [disabled]='helpService.helpMode || readOnly'>{{'Severe' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Severe' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
      </ion-row>
    </ion-radio-group>
  </ion-grid>

  <!-- Load bearing section -->
  <div *ngIf="selectedTypeOfDamage == 'load'"
    style="border:1px solid rgb(165, 163, 163);margin-left:13px;margin-right:13px;border-radius: 5px; margin-top:15px !important;">
    <label style="font-size:15px;padding-top:5px;padding-left:5px;">{{'MELTING_CALCULATION_TITLE' | translate}}:</label>
    <div style="padding-top:5px; text-align: center;">
      <div style="display: inline-block; width: 47%; padding-left: 8px;" class="load-bearing-row">
        <div class="form-group" style="text-align: center">
          <label for="inputEmail4"
            style="font-size:14px;padding-right: 4px;padding-left: 4px;">{{'MELTING_STARANDS_PER_YARN_LABEL' | translate}}
            <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
          <div [formGroup]="yarnsPerStrandForm">
            <mat-form-field style="width: 100%;">
              <input matInput type="text" [(ngModel)]="yarnsPerStrand" maxlength="18"
                placeholder="{{'MELTING_STARANDS_PER_YARN_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('yarnsPerStrand')" inputmode="decimal"
                (keydown)="keyPressed($event, yarnsPerStrand, 'end')" [required] formControlName="yarnsPerStrandCtrl">
              <mat-error *ngIf="hasErrorStart('yarnsPerStrand')">{{yarnsPerStrandErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
          <!-- <div [formGroup]="lengthForm">
            <mat-form-field>
          <input (blur)="yarnsBlur()" type="number" class="form-control" id="inputEmail4" [(ngModel)]="yarnsPerStrand"
            placeholder="{{'Enter no of yarns' | translate}}" [disabled]='helpService.helpMode || readOnly' [required] formControlName="measurementEndCtrl">
            <mat-error *ngIf="hasErrorStart('length')">{{lengthErrorMessage}}</mat-error>
          </mat-form-field>
        </div> -->
        </div>
      </div>
      <div style="display: inline-block;">
        <span style="padding-top:30px;font-size:30px;">x</span>
      </div>
      <div style="display: inline-block; width: 47%; padding-left: 8px;" class="load-bearing-row">
        <div class="form-group" style="text-align: center">
          <label for="inputPassword4"
            style="font-size:14px;padding-right: 4px;">{{'MELTING_STARANDS_LABEL' | translate}} <span
              style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
          <div [formGroup]="noOfStrandsForm">
            <mat-form-field style="width: 100%;">
              <input matInput type="text" [(ngModel)]="noOfStrands" maxlength="18"
                placeholder="{{'MELTING_STARANDS_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('noOfStrands')" inputmode="decimal"
                (keydown)="keyPressed($event, noOfStrands, 'end')" [required] formControlName="noOfStrandsCtrl">
              <mat-error *ngIf="hasErrorStart('noOfStrands')">{{noOfStrandsCtrlErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
          <!-- <input (blur)="yarnsBlur()" type="number" class="form-control" id="inputPassword4" [(ngModel)]="noOfStrands"
            placeholder="{{'Enter no.of strands' | translate}}" [disabled]='helpService.helpMode || readOnly'> -->
        </div>
      </div>
    </div>
    <label
      style="font-size:15px;padding-top:5px;padding-left:5px;">{{'MELTING_LOSS_CALCULATION_TITLE' | translate}}:</label>
    <div style="padding-top:5px; text-align: center;">
      <div style="display: inline-block; width: 47%; padding-left: 8px;" class="load-bearing-row">
        <div class="form-group" style="text-align: center;  word-wrap: normal !important;">
          <label for="inputEmail4"
            style="font-size:14px; word-wrap: normal; white-space: normal;">{{'MELTING_PULLED_YARNS_LABEL' | translate}}
            <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
          <div [formGroup]="brokenYarnForm">
            <mat-form-field style="width: 100%;">
              <input matInput type="text" [(ngModel)]="brokenYarns" maxlength="18"
                placeholder="{{'MELTING_PULLED_YARNS_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('brokenYarn')" inputmode="decimal"
                (keydown)="keyPressed($event, brokenYarns, 'end')" [required] formControlName="brokenYarnCtrl">
              <mat-error *ngIf="hasErrorStart('brokenYarn')">{{brokenYarnErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
          <!-- <input (blur)="yarnsBlur()" type="number" class="form-control" id="inputEmail4" [(ngModel)]="brokenYarns"
            placeholder="{{'Broken yarns' | translate}}" [disabled]='helpService.helpMode || readOnly'> -->
        </div>
      </div>
      <div style="display: inline-block;">
        <span style="padding-top:30px;font-size:30px;">/</span>
      </div>
      <div style="display: inline-block; width: 47%; padding-left: 8px;" class="load-bearing-row">
        <div class="form-group" style="text-align: center">
          <label for="inputPassword4" style="font-size:14px;">{{'MELTING_TOTAL_YARNS_LABEL' | translate}}</label>
          <!-- <div [formGroup]="endForm"> -->
          <mat-form-field style="width: 100%;">
            <input matInput type="text" readonly [(ngModel)]="totalYarns" maxlength="18"
              placeholder="{{'MELTING_TOTAL_YARNS_PLACEHOLDER' | translate}}" step="any" inputmode="decimal"
              (keydown)="keyPressed($event, measurementEnd, 'end')">
          </mat-form-field>
          <!-- </div> -->
          <!-- <input readonly type="number" class="form-control" id="inputPassword4" [(ngModel)]="totalYarns"
            placeholder="{{'Total yarns' | translate}}"> -->
        </div>
      </div>
    </div>
    <div>
      <div>
        <div style="text-align: center">
          <label>{{'MELTING_AREA_EFFECTED' | translate}} </label>&nbsp;=
          <ion-chip outline="true" color="primary" style="height:25px;">
            <ion-label>{{totalAreaLoss}}</ion-label>
          </ion-chip>{{'%' | translate}}
        </div>
      </div>
    </div>
  </div>

  <!-- Support Status section -->
  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson' ">
    <label style="padding-left:5px;">{{'Pass or Fail' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-radio-group [(ngModel)]="supportStatus" *ngIf="helpService.helpMode">
      <ion-row tooltip="{{'Select if the line passes or fails the specific manufacturers inspection criteria for safe and continued use.'|translate}}" positionV="bottom" positionH="right"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="pass" [disabled]='helpService.helpMode || readOnly'>{{'Pass' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Pass' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="fail" [disabled]='helpService.helpMode || readOnly'>{{'Fail' | translate}}</ion-radio>
            <!-- <ion-label>{{'Fail' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
      </ion-row>
    </ion-radio-group>

    <ion-radio-group [(ngModel)]="supportStatus" *ngIf="!helpService.helpMode">
      <ion-row>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="pass" [disabled]='helpService.helpMode || readOnly'>{{'Pass' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Pass' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="fail" [disabled]='helpService.helpMode || readOnly'>{{'Fail' | translate}}</ion-radio>
            <!-- <ion-label>{{'Fail' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
      </ion-row>
    </ion-radio-group>
  </ion-grid>

  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="showRiskRating  && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
    <ion-row style="padding: 0px 10px 0px 17px;" *ngIf="showRiskRating  && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
      <ion-col *ngIf="damageLevel != null && damageLevel != undefined && damageLevel != '' && damageLevel != 'none'  && selectedTypeOfDamage == 'nonLoad'" style="display: inline-flex;">
        <fa-icon *ngIf="damageLevel == 'mild'" icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="damageLevel == 'moderate'" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="damageLevel == 'severe'" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <p *ngIf="damageLevel == 'mild'" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p>
        <p *ngIf="damageLevel == 'moderate'" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
        <p *ngIf="damageLevel == 'severe'" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
      </ion-col>
      <ion-col *ngIf="totalAreaLoss > 0 && selectedTypeOfDamage == 'load'" style="display: inline-flex;">
        <fa-icon *ngIf="totalAreaLoss <= 11" icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="totalAreaLoss > 11 && totalAreaLoss <= 18" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="totalAreaLoss > 18" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <p *ngIf="totalAreaLoss <= 11" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p>
        <p *ngIf="totalAreaLoss > 11 && totalAreaLoss <= 18" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
        <p *ngIf="totalAreaLoss > 18" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
      </ion-col>   
    </ion-row>
  </ion-grid>

  <!-- Observation Notes Section -->
  <div style="padding:15px 10px 0px 17px">
    <label>{{'OBSERVATION_NOTES' | translate}}:</label>
    <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
      [disabled]='helpService.helpMode || readOnly' [(ngModel)]="observationNote" (ionFocus)="onFocusUserInputField($event)"></ion-textarea>
  </div>

  <!-- Image Section -->

  <ion-row style="padding: 10px 17px 10px 17px;" *ngIf="device.platform !== 'browser'">
    <div class="img-wrap" *ngFor="let item of cameraService.editedImg; let i = index"
      style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
      <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;"
        (click)="(helpService.helpMode || readOnly) ? test() : cameraService.editImage(item.Image, i, '')" />
      <ion-icon *ngIf="item.Image!='./assets/img/samson2.png'" name="trash" class="close" slot="end" mode="md" color="danger"
        (click)="(helpService.helpMode || readOnly) ? test() : deleteImage(i)"></ion-icon>
    </div>
    <div *ngIf="helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers
        (click)="(helpService.helpMode || readOnly) ? test() : '' "
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>

    <div *ngIf="!helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png"
        (click)="(helpService.helpMode || readOnly) ? test() : captureImage()"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>
  </ion-row>

  <ion-row style="padding: 10px 17px 10px 17px;" *ngIf="device.platform == 'browser'" >
    <input id="myInput" type="file" style="visibility:hidden; display: none;" accept="image/x-png,image/jpeg" (change)="cameraService.onFileSelected($event)"/>
    <div class="img-wrap" *ngFor="let item of cameraService.editedImg; let i = index"
      style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
      <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;"
        (click)="(helpService.helpMode || readOnly) ? test() :cameraService.editImage(item.Image, i, '')" />
      <ion-icon *ngIf="item.Image!='./assets/img/samson2.png'" name="trash" class="close" slot="end" mode="md" color="danger"
        (click)="(helpService.helpMode || readOnly) ? test() : deleteImage(i)"></ion-icon>
    </div>
    <div *ngIf="helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of melting'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers
        (click)="(helpService.helpMode || readOnly) ? test() : '' "
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>

    <div *ngIf="!helpService.helpMode" [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png"
        (click)="(helpService.helpMode || readOnly) ? test() : captureImage()"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>
  </ion-row>

  <!-- Save Button -->
  <ion-fab *ngIf='!readOnly' vertical="bottom" horizontal="end" slot="fixed" [topOffset]=helpService.topOffset
    tooltip="{{'Tap to save melting measurement'|translate}}" positionV="top" positionH="right"
    [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="helpService.helpMode || readOnly ? test() : saveMeasurement()">
      <fa-icon class="icon-style-other" icon="floppy-disk" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>

</ion-content>

<!-- Footer -->
<div *ngIf='footerClose && !isEmpEditConfig'>
  <div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>