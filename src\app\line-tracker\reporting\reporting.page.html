<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Reporting</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" style="height:100%">
    <ion-card style="margin-top: 12px;" (click)="navigateToLinehistory()"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.reportingLineHistory" style="width: 100%;height: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'Line History'}} </ion-label>
    </ion-card>

    <ion-card class="ion-activatable responsive-card-style" (click)="navigateToUsagehistory()">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.reportingUsageHistory" style="width: 100%;height: 100%;" />
      <ion-label class="card-title" style="font-size: 17px;"> {{'Usage History'}} </ion-label>
    </ion-card>
  </div>

  <div *ngIf="platformId == 'electron' || device.platform == 'browser'">
    <div class="gridCon"> 
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" [routerLink]="['/maintenance']">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.reportingLineHistoryWin" class="imageTag">
          <p class="wrapperLabel">{{'Line History'}}</p>
        </div>
      </div>
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" [routerLink]="['/mooring-history']">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.reportingUsageHistoryWin" class="imageTag">
          <p class="wrapperLabel">{{'Usage History'}}</p>
        </div>
      </div>
    </div>
  </div>
</ion-content>

<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>