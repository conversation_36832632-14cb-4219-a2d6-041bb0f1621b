import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { EmailComposer } from '@awesome-cordova-plugins/email-composer/ngx';
import { FileOpenerService } from 'src/app/services/file-opener.service';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, ToastController, ModalController, LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { GenericListPage } from 'src/app/generic-list/generic-list.page';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { AppConstant } from 'src/constants/appConstants';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faChevronCircleLeft, faDownload, faDownLong, faEnvelope, faSearch, faSortDown, faTasks, faTh } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-mooring-history',
  templateUrl: './mooring-history.page.html',
  styleUrls: ['./mooring-history.page.scss'],
})
export class MooringHistoryPage implements OnInit {

  showLoading: any;
  initialData: any[] = [];
  mooringHistoryData: any[] = [];
  maxDate: any;
  eventStartDateModel: any;
  eventEndDateModel: any;
  eventDateErrorMessage: any;
  hasError = false;
  eventDateForm: FormGroup;
  currentFilePath: any;
  dataToExport;
  tempArray = [];
  objArray = {};
  assetList: any;
  selectedAsset: any;
  selectedAssetName: string = ''
  isAssetSelected: boolean = false;
  selectedAssetID: any;
  fromDateForm: FormGroup;
  toDateForm: FormGroup;
  isUtility: boolean = false;

  constructor(private unviredCordovaSDK: UnviredCordovaSDK,
    private fileOpenerService: FileOpenerService,
    private _DomSanitizationService: DomSanitizer,
    private file: File,
    private userPreferenceService: UserPreferenceService,
    public menu: MenuController,
    private helpService: HelpService,
    private toastController: ToastController,
    public dataService: DataService,
    private modalController: ModalController,
    private alertService: AlertService,
    public translate: TranslateService,
    private formBuilder: FormBuilder,
    private emailComposer: EmailComposer,
    public device: Device,
    private loadingController: LoadingController,
    public faIconsLibrary: FaIconLibrary) { 
      this.faIconsLibrary.addIcons(faSearch ,
        faChevronCircleLeft , faBars , faTh , faDownload , faEnvelope , faTasks ,
         faSortDown)
    }

  ngOnInit() {
    // this.getInitialData();
    this.eventStartDateModel = '';
    this.eventEndDateModel = '';
    const maxNewDate = new Date();
    this.maxDate = new Date(maxNewDate.getFullYear(), maxNewDate.getMonth(), maxNewDate.getDate());
    this.fromDateForm = this.formBuilder.group({
      fromDateCtrl: ['', Validators.required],
    });
    this.toDateForm = this.formBuilder.group({
      toDateCtrl: ['', Validators.required],
    });
    this.checkIfUtility();
    this.getAssetList();
  }

  async getAssetList() {
    let assetRes = await this.unviredCordovaSDK.dbExecuteStatement(`SELECT * FROM ASSET_HEADER ORDER BY NAME COLLATE NOCASE ASC`)
    if (assetRes.type == ResultType.success) {
      if (assetRes.data.length > 0) {
        this.assetList = assetRes.data;
      } else {
        this.assetList = []
      }
    } else {
      this.unviredCordovaSDK.logError("create-inspection", "filterCertificate", "Error while getting error from db" + JSON.stringify(assetRes))
    }
    this.initializePreferenceAsset();
  }

  async getInitialData() {
    // await this.presentLoading('Loading...');
    await this.getMooringData();
  }

  async presentToast() {
    const toast = await this.toastController.create({
      message: 'Please select date range to get and asset to get usage history',
      duration: 5000,
      position: 'middle'
    });
    toast.present();
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
    this.clearData();
  }

  async clearData() {
    await this.unviredCordovaSDK.dbDelete('LMD_LITE_HEADER', "")
  }


  async presentLoading(msg) {
    this.showLoading = await this.loadingController.create({
      message: msg,
      spinner: 'crescent',
      animated: true,
      showBackdrop: true,
      translucent: true
    });
    await this.showLoading.present();
  }


  icon(index) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }

  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }

  onDateChange() {
    console.log('raw ' + this.eventEndDateModel + this.eventStartDateModel);
    this.hasError = false;
    if (this.eventStartDateModel === undefined || this.eventEndDateModel === undefined) {
      this.hasError = true;
      this.eventDateErrorMessage = 'Please pick both start date and end date';
    } else {
      this.hasError = false;
      const startD = this.eventStartDateModel.format('YYYY-MM-DD');
      const endD = this.eventEndDateModel.format('YYYY-MM-DD');

      if (endD < startD) {
        this.hasError = true;
        this.eventStartDateModel = '';
        this.eventEndDateModel = '';
        this.eventDateErrorMessage = 'Select a valid date range';
      } else {
        this.filterByDate(startD, endD);
      }
    }
    // console.log('date' + date.format('YYYY-MM-DD'));
  }


  convertToCSV() {
    const arr = [];
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.mooringHistoryData.length; i++) {
      // tslint:disable-next-line:prefer-for-of
      for (let j = 0; j < this.mooringHistoryData[i].LMD_DATA.winches.length; j++) {
        this.objArray = {};
        this.objArray['eventDate'] = this.mooringHistoryData[i].LMD_DATA.eventDate ? this.mooringHistoryData[i].LMD_DATA.eventDate : '';
        this.objArray['eventType'] = this.mooringHistoryData[i].LMD_TYPE ? this.mooringHistoryData[i].LMD_TYPE : '';
        this.objArray['cert'] = this.mooringHistoryData[i].LMD_DATA.winches[j].EQUIP_CERT_NAME ? " " + this.mooringHistoryData[i].LMD_DATA.winches[j].EQUIP_CERT_NAME + " ": '';
        this.objArray['winch'] = this.mooringHistoryData[i].LMD_DATA.winches[j].EQUIP_NAME ? this.mooringHistoryData[i].LMD_DATA.winches[j].EQUIP_NAME: '';
        this.objArray['portName'] = this.mooringHistoryData[i].LMD_DATA.portName ? this.mooringHistoryData[i].LMD_DATA.portName : '';
        this.objArray['portCountry'] = this.mooringHistoryData[i].LMD_DATA.portCountry ? this.mooringHistoryData[i].LMD_DATA.portCountry : '';
        this.objArray['hrsUsed'] = this.mooringHistoryData[i].LMD_DATA.hoursUsed ? this.mooringHistoryData[i].LMD_DATA.hoursUsed : '';
        // this.objArray['assetName'] = this.mooringHistoryData[i].LMD_DATA.assetName ? this.mooringHistoryData[i].LMD_DATA.assetName : '';
        const accval = this.mooringHistoryData[i].LMD_DATA.account;
        const newAccVal = accval.replace(',', '');
        // this.objArray['account'] = newAccVal ? newAccVal : '';
        // this.objArray['eventDate'] = this.mooringHistoryData[i].LMD_DATA.eventDate ? this.mooringHistoryData[i].LMD_DATA.eventDate : '';
        // this.objArray['portCountry'] = this.mooringHistoryData[i].LMD_DATA.portCountry ? this.mooringHistoryData[i].LMD_DATA.portCountry : '';
        // this.objArray['portName'] = this.mooringHistoryData[i].LMD_DATA.portName ? this.mooringHistoryData[i].LMD_DATA.portName : '';
        // this.objArray['allFast'] = this.mooringHistoryData[i].LMD_DATA.allFast ? this.mooringHistoryData[i].LMD_DATA.allFast : '';
        // this.objArray['allLetGo'] = this.mooringHistoryData[i].LMD_DATA.allLetGo ? this.mooringHistoryData[i].LMD_DATA.allLetGo : '';
        // this.objArray['winch'] = this.mooringHistoryData[i].LMD_DATA.winch ? this.mooringHistoryData[i].LMD_DATA.winch : '';
        // this.objArray['estimatedLineLength'] = this.mooringHistoryData[i].LMD_DATA.estimatedLineLength ? this.mooringHistoryData[i].LMD_DATA.estimatedLineLength : '';
        // this.objArray['averageLoad'] = this.mooringHistoryData[i].LMD_DATA.averageLoad ? this.mooringHistoryData[i].LMD_DATA.averageLoad : '';
        // this.objArray['peakLoad'] = this.mooringHistoryData[i].LMD_DATA.peakLoad ? this.mooringHistoryData[i].LMD_DATA.peakLoad : '';
        // this.objArray['shipSide'] = this.mooringHistoryData[i].LMD_DATA.shipSide ? this.mooringHistoryData[i].LMD_DATA.shipSide : '';
        // this.objArray['severeLoadCondition'] = this.mooringHistoryData[i].LMD_DATA.severeLoadCondition ? this.mooringHistoryData[i].LMD_DATA.severeLoadCondition : '';
        // this.objArray['equip_name'] = this.mooringHistoryData[i].LMD_DATA.winches[j].EQUIP_NAME;
        // this.objArray['equip_cert_name'] = this.mooringHistoryData[i].LMD_DATA.winches[j].EQUIP_CERT_NAME;
        // this.objArray['tailLength'] = this.mooringHistoryData[i].LMD_DATA.winches[j].TAIL_LENGTH;
        // this.objArray['estimateLineLength'] = this.mooringHistoryData[i].LMD_DATA.winches[j].ESTIMATE_LINE_LENGTH;
        arr.push(this.objArray);
      }
    }
    const obj = { data: arr };
    console.log('obj' + JSON.stringify(obj.data));
    const array = typeof obj.data !== 'object' ? JSON.parse(obj.data) : obj.data;
    let str = 'Event Date, Event Type,Certificate, Line Position,Location,Country,Hours Used \r\n';

    // let str = 'Asset Name,Account,Event Date,Port Country,Port Name, All Fast,All Let Go,Winch,Estimated Line Length,Average Load,' +
    //   'Peak Load,Ship Side,Severe Load Condition,Equipment Name,Equipment Cert. Name, Tail Length, Estimated Length \r\n';

    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < array.length; i++) {
      let line = '';
      // tslint:disable-next-line: forin
      for (const index in array[i]) {
        if (line !== '') {
          line += ',';
        }
        if (line === '') {
          line += ' ';
        }
        line += array[i][index];
      }
      str += line + '\r\n';
    }
    console.log(str);
    return str;
  }

  async presentModal(title: string) {
    var tempList, pageTitle;
    switch (title) {
      case 'ASSET':
        tempList = this.assetList
        pageTitle = "Assets"
    }

    this.alertService.present().then(async () => {
      const modal = await this.modalController.create({
        component: GenericListPage,
        componentProps: { value: tempList, title: pageTitle, page: title }
      });
      await modal.present();

      modal.onDidDismiss().then(async (data) => {
        switch (title) {
          case 'ASSET':
            this.selectedAssetName = data.data.data.NAME
            this.selectedAssetID = data.data.data.ID
            this.selectedAsset = data.data.data;
            if (data.data.data.ID != '') {
              this.isAssetSelected = true;
            }
            break;
        }

      });
    })
  }

  async filterByDate(startD, endD) {


    await this.presentLoading('Loading...');


    var inputHeader = {
      START_DATE: startD,
      END_DATE: endD,
      USER_TYPE: "mooringHistory",
      ASSET_ID: this.selectedAssetID
    }

    let inputObject = {
      "INPUT_GET_LMD_LITE": [
        {
          "INPUT_GET_LMD_LITE_HEADER": inputHeader
        }]
    }
    var tempres = await this.unviredCordovaSDK.dbDelete("LMD_LITE_HEADER", '')
    var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", inputObject, AppConstant.PA_ROPE_INSPECTIONS_PA_GET_HISTORICAL_LMD, true)
    if(temp != undefined) {
      if (temp.type == ResultType.success) {
        this.getInitialData();
        if (temp.message.length > 0)
          this.alertService.showAlert("Error", temp.message)
        // await this.showLoading.dismiss();      
      } else {
        this.alertService.showAlert("Error", temp.message)
        await this.showLoading.dismiss();
      }
    } else {
      this.alertService.showAlert("Error", "No records found with the given date range. Please try with different date range")
      await this.showLoading.dismiss();
    }
  }

  composeEmail() {
    this.generateCSV('email');
    console.log("current file path" + this.currentFilePath);
  }

  async generateCSV(type: string) {
    const value = this.convertToCSV();
    console.log('formatted data' + value);
    const folderPath = this.file.dataDirectory;

    //#region "Get todays date and time"
    const currentdate = new Date();
    const datetime = currentdate.getDate() + '' + (currentdate.getMonth() + 1) + '' +
      currentdate.getFullYear() + '' + currentdate.getHours() +
      '' + currentdate.getMinutes() + '' + currentdate.getSeconds();
    //#endregion

    const fileName = `Usage-History${datetime}.csv`;
    const blob = new Blob(['\ufeff' + value], { type: 'text/csv;charset=utf-8;' });
    if(this.device.platform == "browser") {
      const a = document.createElement('a');
      const url = window.URL.createObjectURL(blob);

      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();

    } else {
      if (this.device.platform == 'Android') {
        if (type === 'export') {
          this.file.checkDir(this.file.externalRootDirectory + "Download/", 'Inspections')
            .then(_ => {
              this.file.writeFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, blob).then(response => {
                let filePath = this.file.applicationDirectory + 'www/assets';
                this.file.copyFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, this.file.dataDirectory, fileName).then(result => {
                  this.fileOpenerService.openFile(fileName, 'text/csv');
                }).catch(err => {
                })
              })
            })
            .catch(err => {
              this.file.createDir(this.file.externalRootDirectory + "Download/", 'Inspections/', false).then(result => {
                this.file.writeFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, blob).then(response => {
                  this.file.copyFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, this.file.dataDirectory, fileName).then(result => {
                    this.fileOpenerService.openFile(fileName, 'text/csv');
                  }).catch(err => {
                  })
                }).catch(err => {
                  // ACTION
                })
              })
            });
        } else {
          const mailId = await this.userPreferenceService.getUserPreference('email');
            let email = {
              to: `${mailId ? mailId : ''}`,
              cc: '',
              attachments: [
                this.currentFilePath
              ],
              subject: 'Usage History Report',
              body: 'Please find the usage history report document attached',
              isHtml: true
            };
            this.emailComposer.open(email);
        }
      } else {
        this.file.writeFile(`${folderPath}`, fileName, blob, { replace: true, append: false }).then(async (res) => {
          console.log('successfully written', res);
          if (type === 'export') {
            this.fileOpenerService.openFile(fileName, 'text/csv');
            // .then(_ => console.log('Opened successfully')).
            // catch(err => console.log('error while opening file', err));
          } else {
            const mailId = await this.userPreferenceService.getUserPreference('email');
            let email = {
              to: `${mailId ? mailId : ''}`,
              cc: '',
              attachments: [
                this.currentFilePath
              ],
              subject: 'Usage History Report',
              body: 'Please find the usage history report document attached',
              isHtml: true
            };
            this.emailComposer.open(email);
          }
        }).catch(err => console.log('error while writing to file' + err));
      }
    }

    const filePath = folderPath + fileName;
    this.currentFilePath = `${filePath}`;
  }


  async getMooringData() {
    const result = await this.unviredCordovaSDK.dbSelect('LMD_LITE_HEADER', "");
    if (result.type === ResultType.success) {
      this.initialData = result.data;
      this.mooringHistoryData = [];
      const resultData = result.data;
      for (let i = 0; i < resultData.length; i++) {
        const value = result.data[i];
        if (value.LMD_DATA != '') {
          const dataToFormat = value.LMD_DATA;
          const formatedLmdData = dataToFormat.replace(/\"/g, '"');
          const newFormatedData = JSON.parse(formatedLmdData);
          value.LMD_DATA = newFormatedData;
          this.mooringHistoryData.unshift(value);
          this.mooringHistoryData.sort(function(a,b){
            var c = new Date(a.LMD_DATA.eventDate).getTime();
            var d = new Date(b.LMD_DATA.eventDate).getTime();
            // Turn your strings into dates, and then subtract them
            // to get a value that is either negative, positive, or zero.
            return c - d;
          });
          this.mooringHistoryData.reverse()
        }
      }
      console.log('mooring data' + JSON.stringify(this.mooringHistoryData));
      await this.showLoading.dismiss();
    } else {
      await this.showLoading.dismiss();
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async initializePreferenceAsset() {
    var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    
    if (selectedAsset != undefined && selectedAsset != null && selectedAsset != '' && selectedAsset != '""') {
      selectedAsset = JSON.parse(selectedAsset)
      this.selectedAsset = selectedAsset;
      this.selectedAssetID = selectedAsset.ID
      this.selectedAssetName  = selectedAsset.NAME
    }
  }

  setChanged() {

  }

  keyPressed(event, installDate, flag) {

  }

  async checkIfUtility() {
    var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
    if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
      selectedIndustry = JSON.parse(selectedIndustry)
      if((selectedIndustry.ID.includes('Utility'))) {
        this.isUtility = true;
      }
      return
    } else {
      return
    }
  }
}
