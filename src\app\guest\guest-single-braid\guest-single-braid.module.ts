import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestSingleBraidPageRoutingModule } from './guest-single-braid-routing.module';

import { GuestSingleBraidPage } from './guest-single-braid.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestSingleBraidPageRoutingModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestSingleBraidPage]
})
export class GuestSingleBraidPageModule {}
