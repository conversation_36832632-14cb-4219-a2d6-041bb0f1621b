import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
// import { library } from '@fortawesome/fontawesome-svg-core';
import { faBars, faExchangeAlt, faGrip, faList, faSearch, faSync } from '@fortawesome/free-solid-svg-icons';
import { MenuController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { LmdService } from 'src/app/services/lmd.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';


@Component({
  selector: 'app-new-lmd',
  templateUrl: './new-lmd.page.html',
  styleUrls: ['./new-lmd.page.scss'],
})
export class NewLmdPage implements OnInit {

  showRotation: boolean = true;
  selectedIndustry: any;
  isUtility: boolean  = false;

  constructor(public router: Router,
    public helpService: HelpService,
    public menu: MenuController,
    public lmdService: LmdService,
    public dataService: DataService,
    public unviredSdk: UnviredCordovaSDK,
    public translate: TranslateService,
    public userPreferenceService: UserPreferenceService,
    public utilityService: UtilserviceService,
    public faIconLibrary: FaIconLibrary) {
      this.faIconLibrary.addIcons(faExchangeAlt ,faSync , faSearch, faGrip, faList, faBars);
     }

  async ngOnInit() { 
    await this.checkIfWorkboatUser()
  }

  async checkIfWorkboatUser() {
    var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
    if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
      selectedIndustry = JSON.parse(selectedIndustry)
      if(!selectedIndustry.ID.includes("Workboat") && (!selectedIndustry.ID.includes('Utility'))) {
        return
      }
      if((selectedIndustry.ID.includes('Utility'))) {
        this.isUtility = true;
      }
      this.showRotation = false;
      return
    } else {
      return
    }
  }

  navigateToNextStep(selectedItem) {
    var tempPages = {
      'Cropping':'cropping-lmd',
      'Repair': 'repair-lmd',
      'EndForEnd':'end-for-end-lmd',
      'Rotation':'equipment-insp-lmd',
      'EquipmentInsp':'equipment-insp-lmd'
    }
    this.utilityService.setSelectedLMDPage(selectedItem)
    this.lmdService.setReadOnlyMode(false);
    this.utilityService.setLMDEditMode(false)
    switch (selectedItem) {
      case 'Cropping':
      case 'Repair':
      case 'EndForEnd':
      case 'Rotation':
      case 'EquipmentInsp':
        if(this.isUtility && this.lmdService.getInspectionMode()) {
          this.lmdService.fromObaservations = true;
          this.router.navigate([tempPages[selectedItem]])
        } else {
          this.lmdService.fromObaservations = false;
          this.router.navigate(['work-order-details'])
        }
        break;
      case 'InstallLine':
        this.router.navigate(['install-line-lmd'])
        break;
      case 'RequestNewLine':
        this.router.navigate(['request-new-line-lmd'])
        break;
      case 'AssetActivity':
        if(this.isUtility) {
          this.lmdService.setSelectedAsset('')
          this.lmdService.setSelectedActivityFromList(false)
          this.router.navigate(['general-line-usage-details'])
        } else {
          this.deleteIotActivities()
        }
        break;

    }
  }

  async deleteIotActivities() {
    var temp = await this.unviredSdk.dbDelete("IOT_ASSET_ACTIVITY_HEADER", "");
    this.router.navigate(['asset-activity'])
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

}
