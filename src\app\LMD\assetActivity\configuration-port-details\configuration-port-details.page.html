<ion-header>
  <ion-toolbar>
    <ion-title>Existing Pattern</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: var(--ion-color-primary)" defaultHref="/home"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  @if(existingConfigurations && existingConfigurations.length > 0) {
    <p>Select pattern of lines to use</p>
  }
  <ion-accordion-group multiple="true">
    @for(options of existingConfigurations;track options.LMD_ID; let i = $index) {
      <ion-accordion [value]="options.LMD_NAME">
        <ion-item slot="header" lines="none">
          <ion-label>
            <h6>{{options.PORT_NAME}}</h6>
            <h6>{{options.LOCAL_PORT_ENTERED_DATE}}</h6>
          </ion-label>
          <ion-buttons slot="end">
            <ion-button fill="outline" (click)="copyConfig($event,i)">{{'Copy' | translate}}
              <!-- <ion-icon slot="end" name="copy-outline"></ion-icon> -->
              <ion-icon slot="end" name="copy"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>
        <div class="ion-padding" slot="content">
          @if(selectedAsset.NAME != undefined && selectedAsset.NAME != '') {
            <div style="display: flex;">
              <div style="width: 50%;"> Asset Name</div>
              <div style="width: 50%;"> {{selectedAsset.NAME}}</div>
            </div>
          }
          @if(options.PORT_COUNTRY != undefined && options.PORT_COUNTRY != '') {
            <div style="display: flex;">
              <div style="width: 50%;"> Port Country</div>
              <div style="width: 50%;">{{options.PORT_COUNTRY}}</div>
            </div>
          } 
          @if(options.PORT_BERTH != undefined && options.PORT_BERTH != '') {
            <div  style="display: flex;">
              <div style="width: 50%;"> Port Birth</div>
              <div style="width: 50%;"> {{options.PORT_BERTH}}</div>
            </div>
          } 
          @if(options.SHIP_SIDE != undefined && options.SHIP_SIDE != '') {
            <div style="display: flex;">
              <div style="width: 50%;">Ship Side</div>
              <div style="width: 50%;"> {{options.SHIP_SIDE}}</div>
            </div>
          } 
          @if(options.SHELTERED_EXPOSED_PORT != undefined && options.SHELTERED_EXPOSED_PORT != '') {
            <div style="display: flex;">
              <div style="width: 50%;">Port/Terminal Type</div>
              <div style="width: 50%;"> {{options.SHELTERED_EXPOSED_PORT}}</div>
            </div>
          }
        </div>
      </ion-accordion>
    } @empty {
      <p>If a line usage activity has not been entered in the Samson App for this port this area will appear blank until one has.</p>
    }
  </ion-accordion-group>
</ion-content>

<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
    (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>