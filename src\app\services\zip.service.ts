import { Injectable } from '@angular/core';
import { unzipSync } from 'fflate';
import { File } from '@awesome-cordova-plugins/file/ngx';

@Injectable({
  providedIn: 'root'
})
export class ZipService {

  constructor(private file: File) { }

  private ensureArrayBuffer(buffer: ArrayBufferLike): ArrayBuffer {
    return buffer as ArrayBuffer;
  }

  private async getAttachmentAsArrayBuffer(path: string): Promise<ArrayBuffer> {
    return new Promise(async (resolve, reject) => {
      // Normalize path separators for cross-platform compatibility
      const normalizedPath = path.replace(/\\/g, '/');
      const directory = normalizedPath.substring(0, normalizedPath.lastIndexOf('/'));
      const filename = normalizedPath.substring(normalizedPath.lastIndexOf('/') + 1);
      
      try {
        // Try reading as ArrayBuffer first
        const buffer = await this.file.readAsArrayBuffer(directory, filename);
        resolve(buffer);
      } catch (error) {
        try {
          // Fallback: read as binary string and convert
          const binaryString = await this.file.readAsBinaryString(directory, filename);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          resolve(bytes.buffer);
        } catch (fallbackError) {
          console.error(`Error reading file from ${path}:`, fallbackError);
          reject(fallbackError);
        }
      }
    });
  }

  private async unzipTheFileAndWriteFileContent(attachmentAsArrayBufferReceived: ArrayBuffer, targetDirectory: string, progressCallback?: (progress: any) => void): Promise<void> {
    try {
      const safeBuffer = this.ensureArrayBuffer(attachmentAsArrayBufferReceived);
      const uint8 = new Uint8Array(safeBuffer);
      const files = unzipSync(uint8);
      
      const fileNames = Object.keys(files);
      let processedFiles = 0;

      for (const [filename, content] of Object.entries(files)) {
        if (filename.endsWith('/')) continue;

        const lastSlash = filename.lastIndexOf('/');
        if (lastSlash > -1) {
          const parentDir = filename.substring(0, lastSlash);
          try {
            await this.file.createDir(targetDirectory, parentDir, true);
          } catch (dirErr) {
            // Ignore if already exists
          }
        }

        const safeContentBuffer = this.ensureArrayBuffer(content.buffer);
        try {
          await this.file.writeFile(targetDirectory, filename, safeContentBuffer, { replace: true });
        } catch (writeError) {
          console.error(`Error writing file '${filename}':`, writeError);
        }
        
        processedFiles++;
        if (progressCallback) {
          progressCallback({
            loaded: processedFiles,
            total: fileNames.length
          });
        }
      }
    } catch (error) {
      console.error('Error processing file:', error);
      throw error;
    }
  }

  async unzip(zipFilePath: string, targetDirectory: string, progressCallback?: (progress: any) => void): Promise<number> {
    try {
      console.log('Unzip started');
      const buffer = await this.getAttachmentAsArrayBuffer(zipFilePath);
      await this.unzipTheFileAndWriteFileContent(buffer, targetDirectory, progressCallback);
      console.log('Unzip complete');
      return 0; // Success
    } catch (error) {
      console.error('Unzip error:', error);
      return -1; // Failure
    }
  }

}