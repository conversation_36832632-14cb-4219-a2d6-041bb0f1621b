import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AsiEntryPageRoutingModule } from './asi-entry-routing.module';

import { AsiEntryPage } from './asi-entry.page';
import { TooltipsModule } from 'ionic4-tooltips';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from 'src/app/components/footer/footer.component';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TooltipsModule,
    TranslateModule,
    FontAwesomeModule,
    AsiEntryPageRoutingModule,
    FooterComponent
  ],
  declarations: [AsiEntryPage]
})
export class AsiEntryPageModule {}
