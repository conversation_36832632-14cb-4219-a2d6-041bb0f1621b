import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faInfoCircle, faBolt, faR, faGear, faSyncAlt, faSpinner, faTrashAlt, faEnvelope, faCloudUploadAlt, faBell, faCloudDownloadAlt, faFileLines, faCircleInfo } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-planned-inspection',
  templateUrl: './planned-inspection.page.html',
  styleUrls: ['./planned-inspection.page.scss'],
})
export class PlannedInspectionPage implements OnInit {

  obj = {
    rft: "",
    rftNo: "",
    workOrder: true,
    workOrderNo: ""
  }

  label: string = this.translate.instant('Work Order');
  placeHolder: string = this.translate.instant('Enter Work Order No');

  constructor(private menu: MenuController, public dataService: DataService, private translate: TranslateService, private router: Router, public helpService: HelpService, private faIconLibrary:FaIconLibrary) {
    this.faIconLibrary.addIcons(faInfoCircle, faBolt,faR, faGear,faSyncAlt, faSpinner, faTrashAlt, faEnvelope, faCloudUploadAlt, faBell, faCloudDownloadAlt, faFileLines, faCircleInfo);
     }

  ngOnInit() {
  }
  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }
  test() {
  }

  toggleSelection() {
    if (this.obj.workOrder == true) {
      this.label = this.translate.instant('Work Order')
      this.placeHolder = this.translate.instant('Enter Work Order No')
    } else {
      this.label = this.translate.instant('Cert No')
      this.placeHolder = this.translate.instant('Enter Certificate Number')
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  searchInspection() {
    this.router.navigateByUrl('/baseline')
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

}