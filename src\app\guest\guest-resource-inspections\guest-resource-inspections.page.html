<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
    </ion-buttons>
    <ion-title>Inspections</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding: 10px !important">
    <h1 style="color: #0057b3">Inspection and Retirement Checklist*</h1>
    <p style="font-weight:bolder !important">Any rope that has been in use for any period of time will show normal wear
      and tear. Some characteristics of a used rope will not reduce strength while others will. Below we have defined
      normal conditions that should be inspected on a regular basis</p>
    <p>If upon inspection you find any of these conditions, you must consider the following before deciding to repair or
      retire it:</p><br>
    <ul>
      <li style="color: #0057b3"><span style="color: black">the length of the rope,</span></li>
      <li style="color: #0057b3"><span style="color: black">the time it has been in service</span></li>
      <li style="color: #0057b3"><span style="color: black">the type of work it does,</span></li>
      <li style="color: #0057b3"><span style="color: black">where the damage is and</span></li>
      <li style="color: #0057b3"><span style="color: black">the extent of the damage.</span></li>
    </ul><br>
    <p>In general, it is recommended that you</p><br>
    <ul>
      <li style="color: #0057b3"><span style="color: black">Repair the rope if the observed damage in localized
          areas</span></li>
      <li style="color: #0057b3"><span style="color: black">Retire the rope if the damage is over extended areas</span>
      </li>
    </ul><br>
    <p style="font-weight:bolder !important">*REFERENCES Cordage Institute International, International Guideline
      CI2001-04, Fiber-Rope Inspection and Retirement Criteria: Guidelines to Enhanced Durability and the Safer Use of
      Rope, 200</p>
  </div>
</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>