import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { PreviousConfigurationPageRoutingModule } from './previous-configuration-routing.module';

import { PreviousConfigurationPage } from './previous-configuration.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FontAwesomeModule,
    TranslateModule,
    PreviousConfigurationPageRoutingModule,
    FooterComponent
  ],
  declarations: [PreviousConfigurationPage]
})
export class PreviousConfigurationPageModule {}
