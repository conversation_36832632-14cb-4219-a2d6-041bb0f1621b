{"name": "ROPE_INSPECTIONS", "description": "<PERSON> Rope Inspections", "version": "23.0", "ACCOUNT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ACCOUNT_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.ACCOUNT_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PHONE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACNT_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "AI_MODEL_VERSION": {"description": "AI Model version", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "AI_MODEL_VERSION_HEADER": {"className": "com.samson.ropeinspections.be.AI_MODEL_VERSION_HEADER", "header": true, "field": [{"name": "MODEL_TYPE", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "MODEL_NAME", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "MODEL_FILE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODEL_FORMAT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "APPLICATION": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "APPLICATION_HEADER": {"className": "com.samson.ropeinspections.be.APPLICATION_HEADER", "header": true, "field": [{"name": "PKUID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ID", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INDUSTRY_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "APP_SETTINGS": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "APP_SETTINGS_HEADER": {"className": "com.samson.ropeinspections.be.APP_SETTINGS_HEADER", "header": true, "field": [{"name": "KEY_FLD", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ASSET": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ASSET_HEADER": {"className": "com.samson.ropeinspections.be.ASSET_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACNT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WINCH_BRAKE_TEST_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ASSET_ACTIVITY": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ASSET_ACTIVITY_HEADER": {"className": "com.samson.ropeinspections.be.ASSET_ACTIVITY_HEADER", "header": true, "field": [{"name": "AA_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ASSET", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "AA_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_COUNTRY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "BERTH_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALL_FAST", "description": "All fast Date", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALL_LET_GO", "description": "All let go date", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIP_SIDE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SEVERE_LOADING_CONDITION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WINCH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ESTIMATED_LINE_LEN_IN_USE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAIL_USED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "AVG_LOAD", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PEAK_LOAD", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CERTIFICATE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CERTIFICATE_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.CERTIFICATE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RPS", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RFT_NUM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORK_ORDER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MANUFACTURER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INDUSTRY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPLICATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERTIFICATE_NUM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COLOR", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COLOR_OTHER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONSTRUCTION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORIGINAL_CONFIG", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_CONFIG", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIAM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIAM_UOM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORIGINAL_LENGTH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CURRENT_LENGTH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSPECTED_LENGTH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LENGTH_UOM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSPECTION_NOTES", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "HAS_CHAFE", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHAFE_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "OTHER_CHAFE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_JACKETED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSTALLED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSTALLED_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_INSTALLED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSPECTION_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_MODIFIED_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_MODIFIED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LINE_POSITION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_LOCATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_DELETED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RPS_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_SIZE_INCH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_SIZE_MM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_LEN_FEET", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_LEN_METER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPETITOR_CERT", "description": "X if yes, Null if No", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RECORD_TYPE_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RECORD_TYPE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD6", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CHAFE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CHAFE_HEADER": {"className": "com.samson.ropeinspections.be.CHAFE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "COLOR": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "COLOR_HEADER": {"className": "com.samson.ropeinspections.be.COLOR_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CONFIGURATION": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CONFIGURATION_HEADER": {"className": "com.samson.ropeinspections.be.CONFIGURATION_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CONSTRUCTION": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CONSTRUCTION_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.CONSTRUCTION_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "JACKETED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CONTACT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CONTACT_HEADER": {"className": "com.samson.ropeinspections.be.CONTACT_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ACNT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FIRST_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "STREET", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CITY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "POSTAL_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACNT_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "DAMAGE_TYPE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "DAMAGE_TYPE_HEADER": {"className": "com.samson.ropeinspections.be.DAMAGE_TYPE_HEADER", "header": true, "field": [{"name": "DAMAGE_TYPE_NAME", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "DAMAGE_TYPE_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "DIAM_UOM_CHOICE": {"description": "Diamter UOM pick list values", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "DIAM_UOM_CHOICE_HEADER": {"className": "com.samson.ropeinspections.be.DIAM_UOM_CHOICE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "UOM_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "UOM_VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SIZE_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "INTEGER"}, {"name": "UOM_ORDER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "REAL"}]}}, "DIA_UOM": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "DIA_UOM_HEADER": {"className": "com.samson.ropeinspections.be.DIA_UOM_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "UOM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "DISTANCE_FROM_EYE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "DISTANCE_FROM_EYE_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.DISTANCE_FROM_EYE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "LABEL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "VALUE", "description": "Description", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "DWNLD_TIME": {"description": "Downloaded time BE. It has to be last BE to sent", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "DWNLD_TIME_HEADER": {"className": "com.samson.ropeinspections.be.DWNLD_TIME_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "TIME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "END_TYPE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "END_TYPE_HEADER": {"className": "com.samson.ropeinspections.be.END_TYPE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ENV_CONDITIONS": {"description": "Notable Environment Conditions", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ENV_CONDITIONS_HEADER": {"className": "com.samson.ropeinspections.be.ENV_CONDITIONS_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ENV_COND_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ENV_COND_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "EQUIPMENT": {"description": "Equipment", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "EQUIPMENT_HEADER": {"className": "com.samson.ropeinspections.be.EQUIPMENT_HEADER", "header": true, "field": [{"name": "EQUIP_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "EQUIP_NAME", "description": "Equipment Detail Name", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_DESC", "description": "Description", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MANUFACTURER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DISPOSITION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSTALLED_LOCATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIP_SIDE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RECORD_TYPE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "EXISTING_LMD": {"description": "Exisitng - LMD data", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "EXISTING_LMD_HEADER": {"className": "com.samson.ropeinspections.be.EXISTING_LMD_HEADER", "header": true, "field": [{"name": "LMD_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "LMD_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERT_ID", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RPS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_COUNTRY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIP_SIDE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TIME_AT_BERTH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHELTERED_EXPOSED_PORT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIPMENT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WINCH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LINE_POSITION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAINLINE_RPS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAINLINE_CERT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAIL_RPS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_ENTERED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_DEPARTED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "HARDWARE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "HARDWARE_HEADER": {"className": "com.samson.ropeinspections.be.HARDWARE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "IMAGES": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "IMAGES_HEADER": {"className": "com.samson.ropeinspections.be.IMAGES_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "MEAS_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INDUSTRY": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INDUSTRY_HEADER": {"className": "com.samson.ropeinspections.be.INDUSTRY_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_CREATE_CASE": {"description": "Input for case in salesforce", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "INPUT_CREATE_CASE_HEADER": {"className": "com.samson.ropeinspections.be.INPUT_CREATE_CASE_HEADER", "header": true, "field": [{"name": "ID", "description": "Unique Id", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CUST_NAME", "description": "Customer Name", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "description": "Email", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION", "description": "Location Coordinates", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CASE_ID", "description": "Case number form salesforce", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTACT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTACT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "INPUT_CREATE_CASE_ATTACHMENT": {"description": "Attachment", "className": "com.samson.ropeinspections.be.INPUT_CREATE_CASE_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_GET_CONNECT_URL": {"description": "Input to fetch Icaria connect URL", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_GET_CONNECT_URL_HEADER": {"className": "com.samson.ropeinspections.be.INPUT_GET_CONNECT_URL_HEADER", "header": true, "field": [{"name": "PAGE_NAME", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_GET_INSPECTION": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_GET_INSPECTION_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.INPUT_GET_INSPECTION_HEADER", "header": true, "field": [{"name": "WORK_ORDER_ID", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CERT_NUM", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}]}}, "INPUT_GET_IOT_ASSET_ACTIVITY": {"description": "Input to fetch IoT Asset activities", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_GET_IOT_ASSET_ACTIVITY_HEADER": {"className": "com.samson.ropeinspections.be.INPUT_GET_IOT_ASSET_ACTIVITY_HEADER", "header": true, "field": [{"name": "ASSET_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ACCOUNT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_GET_LMD_LITE": {"description": "Input to fetch histoical LMDs", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_GET_LMD_LITE_HEADER": {"className": "com.samson.ropeinspections.be.INPUT_GET_LMD_LITE_HEADER", "header": true, "field": [{"name": "ASSET_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "START_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "END_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_RPS_UPDATE": {"description": "Input RPS update", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_RPS_UPDATE_HEADER": {"className": "com.samson.ropeinspections.be.INPUT_RPS_UPDATE_HEADER", "header": true, "field": [{"name": "PKUID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ASSET_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RPS_ID", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "RPS_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EVENT_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSTALL_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "END_INUSE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WINCH_TEST_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "description": "Previous RPS Status", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD6", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_SEND_REPORT_EMAIL": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_SEND_REPORT_EMAIL_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.INPUT_SEND_REPORT_EMAIL_HEADER", "header": true, "field": [{"name": "INSP_NAME", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "EMAIL_ID", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}]}}, "INSPECTION": {"description": "", "attachments": true, "onConflict": "DEVICE_WINS", "save": true, "INSPECTION_HEADER": {"description": "", "className": "com.samson.ropeinspections.be.INSPECTION_HEADER", "header": true, "field": [{"name": "INSPECTION_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "EXTERNAL_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RPS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RFT_NUM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORK_ORDER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORK_ORDER_NUM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CUSTOMER_WORK_ORDER", "description": "Customer WO number if manufacturer is not samson", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_ID", "description": "Account Name", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_ID", "description": "Asset Name", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MANUFACTURER", "description": "Manufacturer", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INDUSTRY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPLICATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERTIFICATE_NUM", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_DESC", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COLOR", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COLOR_OTHER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONSTRUCTION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORIGINAL_CONFIG", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_CONFIG", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIAM", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIAM_UOM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORIGINAL_LENGTH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "REAL"}, {"name": "CURRENT_LENGTH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "REAL"}, {"name": "INSPECTED_LENGTH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "REAL"}, {"name": "LENGTH_UOM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSPECTION_NOTES", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "HAS_CHAFE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHAFE_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "OTHER_CHAFE", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_JACKETED", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "INTEGER"}, {"name": "INSTALLED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSTALLED_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCATION_INSTALLED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSPECTION_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREATED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_MODIFIED_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_MODIFIED_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LINE_POSITION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_LOCATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_DELETED", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RPS_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSPECTION_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_UNIQUE_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "START_POINT", "description": "A or B", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_SIZE_INCH", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_SIZE_MM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_LEN_FEET", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_LEN_METER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSP_TYPE", "description": "Inspection type: ", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONFIG_REFERENCE", "description": "Unique Configuration referecne to identify for historical or completed configs", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONFIG_STATUS", "description": "For local use in client", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONSTRUCTION_CLASS", "description": "Construction class", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ADV_SUPRT_RQSTD", "description": "If user requested advance support set this flag to 1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ADV_SUPRT_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PAYMENT_DATA", "description": "Payment data", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PAYMENT_STATUS", "description": "Payment status", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSPECTION_DATE", "description": "User entered inspection date", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD1", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CUSTOM_INDUSTRY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CUSTOM_APPLICATION", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TEMP_CONSTRUCTION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TEMP_PRD_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TEMP_PRD_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD6", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD7", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD8", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "MEASUREMENT": {"description": "", "className": "com.samson.ropeinspections.be.MEASUREMENT", "field": [{"name": "MEAS_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "INSPECTION_ID", "description": "", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "MEAS_TYPE_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATA", "description": "Json string contains measurement data", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MEAS_TIMESTAMP", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MEAS_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "U_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "ANNOTATION": {"className": "com.samson.ropeinspections.be.ANNOTATION", "field": [{"name": "INSPECTION_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "MEAS_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ANNOTATION_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ATTACH_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "FILE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATA", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "U_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CONFIGURATION": {"className": "com.samson.ropeinspections.be.CONFIGURATION", "field": [{"name": "CONFIG_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "INSPECTION_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONFIG_TYPE_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONFIG_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATA", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONFIG_REFERNCE", "description": "Unique Configuration referecne to identify for historical or completed configs", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "U_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "INSPECTION_ATTACHMENT": {"description": "Attachment", "className": "com.samson.ropeinspections.be.INSPECTION_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "INSPECTION_REPORT": {"description": "Inspection Report", "attachments": true, "onConflict": "DEVICE_WINS", "save": true, "INSPECTION_REPORT_HEADER": {"className": "com.samson.ropeinspections.be.INSPECTION_REPORT_HEADER", "header": true, "field": [{"name": "INSP_ID", "description": "", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "REPORT_TYPE", "description": "Type = Standard or Advanced", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "EXT_FLD1", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXT_FLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "INSPECTION_REPORT_ATTACHMENT": {"description": "Attachment", "className": "com.samson.ropeinspections.be.INSPECTION_REPORT_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "IOT_ASSET_ACTIVITY": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "IOT_ASSET_ACTIVITY_HEADER": {"className": "com.samson.ropeinspections.be.IOT_ASSET_ACTIVITY_HEADER", "header": true, "field": [{"name": "AA_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "AA_NUMBER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TIME_MOORED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TIME_CASTOFF", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_COUNTRY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PORT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "BERTH_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "LEN_UOM": {"description": "Length UOM", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LEN_UOM_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.LEN_UOM_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "UOM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "LINE_POSITION": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LINE_POSITION_HEADER": {"className": "com.samson.ropeinspections.be.LINE_POSITION_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "LMD": {"description": "Line Maintenance", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "LMD_HEADER": {"description": "Line Maintenance Detail", "className": "com.samson.ropeinspections.be.LMD_HEADER", "header": true, "field": [{"name": "LMD_ID", "description": "LMD_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "LMD_NUMBER", "description": "LMD number after submission", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_NAME", "description": "LMD Name", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_STATUS", "description": "Status", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_TYPE", "description": "Record Type", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_DATA", "description": "Json data based on LMD Type", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DISPOSITION", "description": "Disposition Text", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRE_DEP_INSP_CMPLT", "description": "PPre deployment inspection completed", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD6", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "LMD_ATTACHMENT": {"description": "Attachment", "className": "com.samson.ropeinspections.be.LMD_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "LMD_LITE": {"description": "LMD Lite data to download historical LMDs ", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LMD_LITE_HEADER": {"className": "com.samson.ropeinspections.be.LMD_LITE_HEADER", "header": true, "field": [{"name": "LMD_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "LMD_NUMBER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LMD_DATA", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "LOC_OF_BREAK": {"description": "Location of Break", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LOC_OF_BREAK_HEADER": {"className": "com.samson.ropeinspections.be.LOC_OF_BREAK_HEADER", "header": true, "field": [{"name": "LOC_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "LOC_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "MANUFACTURER": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "MANUFACTURER_HEADER": {"className": "com.samson.ropeinspections.be.MANUFACTURER_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PAYMENT_STATUS": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PAYMENT_STATUS_HEADER": {"className": "com.samson.ropeinspections.be.PAYMENT_STATUS_HEADER", "header": true, "field": [{"name": "INSP_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PAYMENT_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PORT_COUNTRY": {"description": "Port Country data", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PORT_COUNTRY_HEADER": {"className": "com.samson.ropeinspections.be.PORT_COUNTRY_HEADER", "header": true, "field": [{"name": "COUNTRY_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "COUNTRY_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PORT_TERMINAL_TYPE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PORT_TERMINAL_TYPE_HEADER": {"className": "com.samson.ropeinspections.be.PORT_TERMINAL_TYPE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PRODUCT": {"description": "Product", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PRODUCT_HEADER": {"description": "Product Details", "className": "com.samson.ropeinspections.be.PRODUCT_HEADER", "header": true, "field": [{"name": "ID", "description": "Product Id", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "description": "Product Name", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_CODE", "description": "Product Code", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONSTRUCTION", "description": "Construction", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_JACKETED", "description": "Is Jacketed", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_FAMILY", "description": "Product family name", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_FAMILY_CODE", "description": "Product family code", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PRODUCT_LOCATION": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PRODUCT_LOCATION_HEADER": {"className": "com.samson.ropeinspections.be.PRODUCT_LOCATION_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "PRODUCT_TYPE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PRODUCT_TYPE_HEADER": {"className": "com.samson.ropeinspections.be.PRODUCT_TYPE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PRODUCT_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "QUICK_INSPECTION": {"description": "Quick Inspection", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "QUICK_INSPECTION_HEADER": {"className": "com.samson.ropeinspections.be.QUICK_INSPECTION_HEADER", "header": true, "field": [{"name": "INSP_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "INSP_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RATING", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RPS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFIELD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFIELD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFIELD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFIELD6", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "QUICK_INSPECTION_ATTACHMENT": {"description": "Attachment", "className": "com.samson.ropeinspections.be.QUICK_INSPECTION_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "REPAIR_TYPE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "REPAIR_TYPE_HEADER": {"description": "Repair type header", "className": "com.samson.ropeinspections.be.REPAIR_TYPE_HEADER", "header": true, "field": [{"name": "REPAIR_NAME", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "REPAIR_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "REQUEST_FOR_TEST": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "REQUEST_FOR_TEST_HEADER": {"className": "com.samson.ropeinspections.be.REQUEST_FOR_TEST_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "RISK_RATING": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "RISK_RATING_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.RISK_RATING_HEADER", "header": true, "field": [{"name": "PKUID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ROPE_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ROPE_LAYER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "OBSERVATION_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LEVEL_INDICATOR", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MILD_THRESHOLD", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODERATE_THRESHOLD", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SEVERE_THRESHOLD", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ROPE_CONTACT_SURFACE_RATING": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ROPE_CONTACT_SURFACE_RATING_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.ROPE_CONTACT_SURFACE_RATING_HEADER", "header": true, "field": [{"name": "SR_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "SR_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "ROPE_PRODUCT_SPEC": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "ROPE_PRODUCT_SPEC_HEADER": {"className": "com.samson.ropeinspections.be.ROPE_PRODUCT_SPEC_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONSTRUCTION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MATERIAL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MFG", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIAM_MM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORIGINAL_LENGTH_IN_METER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CURRENT_LENGTH_IN_METER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSTALL_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORKING_HRS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ROPE_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "END_IN_USE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_DETAILS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_MAINTENANCE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPLICATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIAM_IN", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORKING_OPERATIONS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRODUCT_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "END_A_HOURS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "END_B_HOURS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RECORD_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD4", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD5", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD6", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SHIP_SIDE": {"description": "Ship Side", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SHIP_SIDE_HEADER": {"className": "com.samson.ropeinspections.be.SHIP_SIDE_HEADER", "header": true, "field": [{"name": "SHIP_SIDE_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "SHIP_SIDE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SPLICE_TYPE": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SPLICE_TYPE_HEADER": {"className": "com.samson.ropeinspections.be.SPLICE_TYPE_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "USER": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "USER_HEADER": {"description": "Description", "className": "com.samson.ropeinspections.be.USER_HEADER", "header": true, "field": [{"name": "ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONATCT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "USERNAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FIRST_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LINE_TRACKER_TYPE", "description": "Line tracker program field", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ICARIA_PROGRAM", "description": "Is it Icaria user or customer for advance support impl.", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INSIGHT_AI", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD1", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFLD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "WIRE_ANOMOLIES": {"description": "Anomolies List of Wire(Multine Line Routine Insp)", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "WIRE_ANOMOLIES_HEADER": {"className": "com.samson.ropeinspections.be.WIRE_ANOMOLIES_HEADER", "header": true, "field": [{"name": "PKUID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ANOMOLY_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ANOMOLY_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ANOMOLY_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "WORK_ORDER": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "WORK_ORDER_HEADER": {"className": "com.samson.ropeinspections.be.WORK_ORDER_HEADER", "header": true, "field": [{"name": "ID", "description": "", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "WO_NUMBER", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "WO_SUBJECT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WO_DESC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ROOT_WO_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACCOUNT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CERT_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONSTRUCTION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WO_STATUS", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WO_TYPE", "description": "Work order type", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EVENT_DATE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RECORD_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSET_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "INDUSTRY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "APPLICATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DIAMETER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LEN_OF_SAMPLE_FT", "description": "Feet", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LEN_OF_SAMPLE_MT", "description": "<PERSON>er", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RFT_CASE_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RFT_CASE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RFT_CASE_SUBJECT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LENGTH_OF_SAMPLE_UOM", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RFT_STATUS", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFIELD1", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFIELD2", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTFIELD3", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "Index": []}