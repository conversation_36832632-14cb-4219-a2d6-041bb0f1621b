import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON>u<PERSON><PERSON>roller, AlertController, NavController } from '@ionic/angular';
import { AppConstant } from 'src/constants/appConstants';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faListCheck, faGrip, faEnvelope } from '@fortawesome/free-solid-svg-icons';
import { TranslateService } from '@ngx-translate/core';
import { PlatformService } from '../services/platform.service';
@Component({
  selector: 'app-new-configuration-list',
  templateUrl: './new-configuration-list.page.html',
  styleUrls: ['./new-configuration-list.page.scss'],
})
export class NewConfigurationListPage implements OnInit {

  configurations: any;
  inspectionHeader: any
  platformId: string = this.platformService.getPlatformId();

  constructor(
    public platformService: PlatformService,
    public router: Router,
    public helpService: HelpService,
    public alertService: AlertService,
    public utilityService: UtilserviceService,
    public dataService: DataService,
    public menu: MenuController,
    public alertController: AlertController,
    public navController: NavController,
    public faIconLibrary: FaIconLibrary,
    public unviredSDK: UnviredCordovaSDK,
    public translate: TranslateService,
    public device: Device) { 
      this.faIconLibrary.addIcons(faBars, faListCheck, faGrip, faEnvelope)
    }

  ngOnInit() {
    this.getSelectedInspectionHeader();
  }

  async setConfigAndNavigate(configuration) {
    // this.alertService.present().then(() => {
    //   setTimeout(async () => {
        this.inspectionHeader.CONFIG_STATUS = "" + AppConstant.ADD_MEASUREMENTS
        this.inspectionHeader.CONFIG_REFERENCE = UtilserviceService.guid();
        this.inspectionHeader.PRODUCT_CONFIG = configuration
        // this.inspectionHeader.ORIGINAL_CONFIG = configuration

        var result = await this.unviredSDK.dbUpdate("INSPECTION_HEADER", this.inspectionHeader, "INSPECTION_ID like '" + this.inspectionHeader.INSPECTION_ID + "'")
        if (result.type == ResultType.success) {
          // this.alertService.dismiss();
          this.utilityService.setSelectedInspectionHeader(this.inspectionHeader)
          this.dataService.setConfigLocationOptions(this.inspectionHeader);
          if(this.dataService.getConfigurationOption() == 'yes') {
            this.router.navigate(['inspection-configuration'])
          } else {
            if(this.device.platform == "browser") {
              this.unviredSDK.dbSaveWebData();
            }
            await this.completeConfiguration();
          }
          ;
        } else {
          this.unviredSDK.logError("new-configuration-list", "setConfigAndNavigate", "Error while fetching data " + JSON.stringify(result))
        }
    //   }, 500);
    // });
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  async getSelectedInspectionHeader() {
    this.inspectionHeader = this.utilityService.getSelectedInspectionHeader()
    var temp = await this.dataService.getSelectedHeaderFromDb(this.inspectionHeader.INSPECTION_ID)
    if (temp.type == ResultType.success) {
      if (temp.data.length > 0) {
        this.inspectionHeader = temp.data[0]
      }
    }
  }

  async getFixedBearingPoint(item) {
    const alert = await this.alertController.create({
      header: 'Fixed Bearing Point?',
      inputs: [
        {
          name: 'yes',
          type: 'radio',
          label: 'Yes',
          value: 'yes',
          checked: true
        },
        {
          name: 'no',
          type: 'radio',
          label: 'No',
          value: 'no'
        },
        // {
        //   name: 'radio2',
        //   type: 'radio',
        //   label: 'Custom url',
        //   value: 'custom'
        // },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Ok',
          role: 'OK',
          handler: () => {
            console.log('Confirm Ok');
          }
        }
      ]
    });

    await alert.present();
    await alert.onDidDismiss().then((data) => {
      console.log("DATA" + JSON.stringify(data))
      if (data.role == 'OK') {
        if (data.data.values == 'yes' || data.data.values == 'no') {
          this.setConfigAndNavigate(item)
        }
      }
    })
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  backButtonClick() {
    if (this.utilityService.getFromfromInspection()) {
      this.navController.navigateBack('/inspection')
    } else {
      this.navController.navigateBack('/inspection-home')
    }
  }

  async completeConfiguration() {
    //update inspection CONFIG_ID
    this.inspectionHeader.CONFIG_STATUS = "" + AppConstant.CONFIGURATION_COMPLETED
    if (this.dataService.measurementChanged == true) {
      this.inspectionHeader.CONFIG_REFERENCE = UtilserviceService.guid()
      this.inspectionHeader.HAS_CHAFE = 1
    }
    var result = await this.unviredSDK.dbUpdate("INSPECTION_HEADER", this.inspectionHeader, "INSPECTION_ID like '" + this.inspectionHeader.INSPECTION_ID + "'")
    if (result.type == ResultType.success) {
      this.setChafeInspectionHeader(this.inspectionHeader)
    } else {
      this.unviredSDK.logError("inspection-configuration", "completeConfiguration", "Error while updating data data " + JSON.stringify(result))
    }
  }

  async setChafeInspectionHeader(inspectionHeader) {
    if (inspectionHeader) {       
      this.utilityService.setFromBaseline(false)
      this.dataService.setLocationAndLayer(this.inspectionHeader)
      this.utilityService.setSelectedInspectionHeader(this.inspectionHeader);
      this.dataService.setCreatedHeader(this.inspectionHeader)
      this.utilityService.resetBaselineArray();
      this.alertService.dismiss();
      this.router.navigate(['observations'])
      return
    }
  }

}
