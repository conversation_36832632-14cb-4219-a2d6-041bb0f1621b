import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { NewConfigurationListPageRoutingModule } from './new-configuration-list-routing.module';

import { NewConfigurationListPage } from './new-configuration-list.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    NewConfigurationListPageRoutingModule,
    FontAwesomeModule,
    FooterComponent
  ],
  declarations: [NewConfigurationListPage]
})
export class NewConfigurationListPageModule {}
