<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="showWorkboatSettings != true">{{'AA_ROUTINE_INSPECTION_TITLE' | translate}}</ion-title>
    <ion-title *ngIf="showWorkboatSettings == true">{{'AA_CONFIGURATION_WINCH_TITLE_WORKBOAT' | translate}}</ion-title>
    <ion-buttons slot="end">
      <ion-button color="primary" (click)="helpService.switchMode()" class="help-button-style">
        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <div>
    <div *ngIf="showWorkboatSettings != true" tooltip="{{'Select type of certificate'|translate}}" positionV="top" style="background-color:  var(--ion-color-primary-contrast);"
      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
      [hideOthers]=helpService.hideOthers>
      <ion-segment style="background-color: var(--ion-color-primary-contrast);"
        [disabled]='helpService.helpMode || readOnly || selectedMainline.length > 0 || selectedCertList.length > 0'
        [(ngModel)]="fieldSegment" class="ion-segment-routine-inspection"
        mode="ios" expand="block">
        <ion-segment-button value="mains" mode="ios"
          class="ion-segment-button-style">
          <!-- ! show segment button label as Lines if industry selected in utility and show Mains for Other Industries -->
          <ion-label>{{ (selectedIndustry && selectedIndustry.toLowerCase().includes('utility')) ? 'Lines' : 'Mains'| translate}}</ion-label>
        </ion-segment-button>
        <ion-segment-button value="tails" mode="ios" *ngIf="selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')"
          class="ion-segment-button-style">
          <ion-label>{{ 'Tails' | translate}}</ion-label>
        </ion-segment-button>
      </ion-segment>
      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);">
      <ion-item *ngIf="fieldSegment == 'mains'" lines="none">
        <ion-checkbox color="primary" [indeterminate]="partitalMainsSelected" (ionChange)="onCheckedSekectAll($event)" (click)="allMainlinesSelectedFlag=true"
          [checked]="allMainsSelected"
          [disabled]='helpService.helpMode || readOnly'></ion-checkbox>
      </ion-item>
      <ion-item *ngIf="fieldSegment == 'tails'" lines="none">
        <ion-checkbox color="primary" [indeterminate]="partitalTailsSelected" (ionChange)="onCheckedSekectAll($event)" (click)="allTailsSelectedFlag=true"
          [checked]="allTailsSelected"
          [disabled]='helpService.helpMode || readOnly'></ion-checkbox>
      </ion-item>
      <label>Select All</label>
      </ion-item>
      <p *ngIf="!readOnly && (selectedMainline.length > 0 || selectedCertList.length > 0)"
        style="width: 95%; margin: auto;"> Unselect all items to choose {{ fieldSegment == 'mains' ? "Tails" : "Mains"}}</p>
    </div>
  </div>
</ion-header>

<ion-content>
  <!-- ! MAINLINE SECTION START-->
  <ion-list *ngIf="fieldSegment == 'mains'" style="padding-bottom: 50px;">
    <ion-item-sliding *ngFor="let options of mainlines;let i = index">
      <!-- ^ MAINLINE ITEMS LIST START -->
      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);"
        [ngClass]="{'my-css-class': checkDivIsOpenMainline(options) === true && mainlineSelected==i }">
        <ion-item *ngIf='!readOnly' lines="none">
          <ion-checkbox color="primary" (ionChange)="onCheckedMainline($event,options.CERTIFICATE_NUM, i,options)"
            [value]="options.CERTIFICATE_NUM"  [(ngModel)]="options.CHECKED_ITEM" [checked]="checkIfSelectedCert(options.CERTIFICATE_NUM)" (click)="itemChecked=true"
            [disabled]='helpService.helpMode || readOnly'></ion-checkbox>
        </ion-item>
        <ion-label (click)="helightItemMainline(options,i)">
          <!-- & MAINLINE ITEMS DATA FOR ANDROID AND IOS START-->
          <div *ngIf="platformId !== 'electron' && device.platform != 'browser'">
            <div style="display: block;">
              <div style="color: black; width: 100%">
                {{options.NAME}}
              </div>
              <div *ngIf="options.PRODUCT != '' || options.PRODUCT != null" style="color: grey; width: 100%">
                {{'Product' | translate}} : {{options.PRODUCT}}
              </div>
              <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                {{'Diameter' | translate}} : {{options.DIAM}}
              </div>
              <!-- ! show label Equipment for utility industry and show Winch for others -->
              <div *ngIf="selectedIndustry?.toLowerCase()?.includes('utility')" style="color: grey; width: 100%">
                {{ 'Equipment' | translate }}:{{ options.EQUIP_NAME }}
              </div>
            
              <div *ngIf="!selectedIndustry?.toLowerCase()?.includes('utility')" style="color: grey; width: 100%">
                {{ 'Winch' | translate }}:{{ options.EQUIP_NAME }}
              </div>
              
            </div>
          </div>
          <!-- &MAINLINE ITEMS DATA FOR ANDROID AND IOS END-->

          <!-- & MAINLINE ITEMS DATA FOR BROWSER AND ELECTRON START-->
          <div *ngIf="platformId == 'electron' || device.platform == 'browser'" style="display:flex;">
            <div style="display: block; width: 50%">
              <div style="color: black; width: 100%">
                {{options.NAME}}
              </div>
              <div *ngIf="options.PRODUCT != '' || options.PRODUCT != null" style="color: grey; width: 100%">
                {{'Product' | translate}} : {{options.PRODUCT}}
              </div>
              <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                {{'Diameter' | translate}} : {{options.DIAM}}
              </div>
              <!-- ! show label Equipment for utility industry and show Winch for others -->
              <div *ngIf="selectedIndustry?.toLowerCase()?.includes('utility')" style="color: grey; width: 100%">
                {{ 'Equipment' | translate }}:{{ options.EQUIP_NAME }}
              </div>
              <div *ngIf="!selectedIndustry?.toLowerCase()?.includes('utility')" style="color: grey; width: 100%">
                {{ 'Winch' | translate }}:{{ options.EQUIP_NAME }}
              </div>
            </div>
            <div style="width: 50%" *ngIf="checkIfSelectedMainline(options.CERTIFICATE_NUM)">
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">
                <span style="height: 100%; margin: auto;">{{'INSPECTION_DATE_LABEL'|translate}}</span>
                <span style="color:rgb(221, 82, 82);font-size:15px;margin: auto;"> * </span>
              </p> -->
              <div>
                <div *ngIf='helpService.helpMode'>
                  <div tooltip="{{'Select inspection date'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <mat-form-field style="width:100%">
                      <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                      <input matInput [matDatepicker]="picker" [max]="maxDate" disabled
                        [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                        placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}" (focus)="picker.open()"
                        (click)="picker.open()" readonly>
                      <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                      </mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>
                <div *ngIf='!helpService.helpMode'>
                  <div>
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput [matDatepicker]="picker" [max]="maxDate"
                          placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                          step="any" (change)="onChangeDisable('eventDate')" (dateInput)="addEvent('input', $event)"
                          (dateChange)="addEvent('change', $event)" (keydown)="keyPressed($event, 'eventDate', false)"
                          (focus)="picker.open()" (click)="picker.open()" readonly>
                        <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- & MAINLINE ITEMS DATA FOR BROWSER AND ELECTRON END-->
        </ion-label>

        <!-- & INSPECT ROPE BUTTON START -->
        <ion-buttons (click)="iconMainline(i)" tappable>
          <ion-button style="padding-bottom: 8px;" *ngIf="checkIfSelectedMainline(options.CERTIFICATE_NUM)"
            class="winch-info-button">
            <!-- & INSPECT ROPE BUTTON FOR BROWSER AND ELECTRON START -->
            <span *ngIf="platformId == 'electron' || device.platform == 'browser'"
              class="winch-info-message">Inspect Rope</span>
            <!-- & INSPECT ROPE BUTTON FOR BROWSER AND ELECTRON END -->

            <!-- & INSPECT ROPE BUTTON FOR ANDROID AND IOS START -->
            <span *ngIf="platformId !== 'electron' && device.platform != 'browser' && !checkSectionIsOpenMainline(i)"
              class="winch-info-message">Inspect Rope1</span>
            <!-- & INSPECT ROPE BUTTON FOR ANDROID AND IOS END -->

            <ion-icon slot="icon-only" slot="end"
              [name]="checkSectionIsOpenMainline(i) ? 'chevron-up-outline' :'chevron-down-outline'"
              style="color:rgb(41, 40, 40)" class="winch-info-icon"></ion-icon>
          </ion-button>
        </ion-buttons>
        <!-- & INSPECT ROPE BUTTON END -->

      </ion-item>
      <!-- ^ MAINLINE ITEMS LIST END -->

      <div *ngIf="checkSectionIsOpenMainline(i)" lines="none" class="ion-padding">
        <!-- & JACKETED FORM START -->
        <div *ngIf="options.CUSTOM_CONSTRUCTION == 'Jacketed'">
          <div>
            <div>
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">{{'End in Use'}}:
              </p> -->
              <div>
                <div>
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'Select end in use'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
                      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{'INSPECTION_END_IN_USE' | translate}}</mat-label>
                          <mat-select disableOptionCentering [disabled]='true'
                            placeholder="{{'INSPECTION_END_IN_USE' | translate}}" interface="popover"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE">
                            <mat-select-trigger
                              *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != ''">
                              <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                              {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE}}
                            </mat-select-trigger>
                            <mat-option value="A">
                              A
                            </mat-option>
                            <mat-option value="B">
                              B
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{'INSPECTION_END_IN_USE' | translate}}</mat-label>
                          <mat-select disableOptionCentering placeholder="{{'INSPECTION_END_IN_USE' | translate}}"
                            interface="popover"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE">
                            <mat-select-trigger
                              *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != ''">
                              <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                              {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE}}
                            </mat-select-trigger>
                            <mat-option value="A">
                              A
                            </mat-option>
                            <mat-option value="B">
                              B
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="platformId !== 'electron' && device.platform != 'browser'">
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">
                <span style="height: 100%; margin: auto;">{{'INSPECTION_DATE_LABEL'|translate}}</span>
                <span style="color:rgb(221, 82, 82);font-size:15px;margin: auto;"> * </span>
              </p> -->
              <div>
                <div *ngIf='helpService.helpMode'>
                  <div tooltip="{{'Select inspection date'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <mat-form-field style="width:100%">
                      <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                      <input matInput [matDatepicker]="picker" [max]="maxDate" disabled
                        [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                        placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}" (focus)="picker.open()"
                        (click)="picker.open()" readonly>
                      <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                      </mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>
                <div *ngIf='!helpService.helpMode'>
                  <div>
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput [matDatepicker]="picker" [max]="maxDate"
                          placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                          step="any" (change)="onChangeDisable('eventDate')" (dateInput)="addEvent('input', $event)"
                          (dateChange)="addEvent('change', $event)" (keydown)="keyPressed($event, 'eventDate', false)"
                          (focus)="picker.open()" (click)="picker.open()" readonly style="padding-left:14px;">
                        <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <div class="display-zone">
            <!--! JACKETED ZONE1 - START  -->
            <div [ngClass]="[(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')) ? 'zone-inner-wrapper' : 'zone-inner-utility']">
              <label class="visionLabel" *ngIf="(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility'))">{{ 'ZONE_ONE_LABEL' | translate}}:</label>
              <div style="display: flex">
                <!-- <label>{{'SMALLEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter smallest visible diameter'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_SMALLEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_SMALLEST_VISIBLE_DIAM, 'smallestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_SMALLEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_SMALLEST_VISIBLE_DIAM, 'smallestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                <div class="form-group" style="padding:10px 10px 0px 17px">
                  <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedRoutineInspectionDiamUomString}}
                  </label>
                </div>
              </div>
              <div style="display: flex">
                <!-- <label>{{'LARGEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter the largest visible diameter'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LARGEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LARGEST_VISIBLE_DIAM, 'largestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LARGEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LARGEST_VISIBLE_DIAM, 'largestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                        <mat-error
                          *ngIf="hasErrorStart('largestVisibleDiam', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{largestVisibleDiamErrorMessage}}
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                <div class="form-group" style="padding:10px 10px 0px 17px">
                  <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedRoutineInspectionDiamUomString}}
                  </label>
                </div>
              </div>
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter the cut strand count cover'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_STRAND_COUNT_COVER"
                          maxlength="18" placeholder="{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_STRAND_COUNT_COVER, 'cutStrandCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_STRAND_COUNT_COVER"
                          maxlength="18" placeholder="{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisableJacketed(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_STRAND_COUNT_COVER, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_STRAND_COUNT_COVER, 'cutStrandCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <!--! JACKETED ZONE1 - END  -->
            <!--! JACKETED ZONE2 - START  -->
            <div class="zone-inner-wrapper zone-inner-wrapper-margin" *ngIf="(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility'))">
              {{'ZONE_TWO_LABEL' | translate}}:
              <div style="display: flex">
                <!-- <label>{{'SMALLEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter the smallest visible diameter'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_SMALLEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_SMALLEST_VISIBLE_DIAM, 'smallestVisibleDiamZoneTwo')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_SMALLEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_SMALLEST_VISIBLE_DIAM, 'smallestVisibleDiamZoneTwo')"
                          step="any" inputmode="decimal" [required]>
                        <!-- <mat-error
                          *ngIf="hasErrorStart('smallestVisibleDiamZoneTwo', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{smallestVisibleDiamZoneTwoErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                <div class="form-group" style="padding:10px 10px 0px 17px">
                  <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedRoutineInspectionDiamUomString}}
                  </label>
                </div>
              </div>
              <div style="display: flex">
                <!-- <label>{{'LARGEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter the largest visible diameter'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LARGEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LARGEST_VISIBLE_DIAM, 'largestVisibleDiamZoneTwo')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LARGEST_VISIBLE_DIAM"
                          maxlength="18" placeholder="{{'LARGEST_VISIBLE_DIAMETER_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LARGEST_VISIBLE_DIAM, 'largestVisibleDiamZoneTwo')"
                          step="any" inputmode="decimal" [required]>
                        <!-- <mat-error
                          *ngIf="hasErrorStart('largestVisibleDiamZoneTwo', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{largestVisibleDiamZoneTwoErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                <div class="form-group" style="padding:10px 10px 0px 17px">
                  <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedRoutineInspectionDiamUomString}}
                  </label>
                </div>
              </div>
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter the cut strand count cover'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_STRAND_COUNT_COVER"
                          maxlength="18" placeholder="{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_STRAND_COUNT_COVER, 'cutStrandCountCoverZoneTwo')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_STRAND_COUNT_COVER"
                          maxlength="18" placeholder="{{'CUT_STRAND_COUNT_COVER_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisableJacketed(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_STRAND_COUNT_COVER, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_STRAND_COUNT_COVER, 'cutStrandCountCoverZoneTwo')"
                          step="any" inputmode="decimal" [required]>
                        <mat-error
                          *ngIf="hasErrorStart('cutStrandCountCoverZoneTwo', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{cutStrandCountCoverZoneTwoErrorMessage}}</mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <!--! JACKETED ZONE2 - END  -->
            <div class="zone-inner-wrapper-margin" [ngClass]="[(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')) ? 'zone-inner-wrapper' : 'zone-inner-utility']">
              <br>
              <div>
                <!-- <label>{{'TOTAL_WORKING_HOURS_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter total working hours'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'TOTAL_OPERATIONS_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Enter total of operations'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                        <!-- <mat-error
                          *ngIf="hasErrorStart('totalOfOperations', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{totalOfOperationsErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Select pass or fail'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering [disabled]='true'
                          placeholder="{{'PASS_FAIL_LABEL' | translate}}" interface="popover"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering placeholder="{{'PASS_FAIL_LABEL' | translate}}"
                          interface="popover"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                            {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>
          <div style="display: flex; margin-top: 5px;">
            <div style="border: solid black 0.5px;border-radius: 5px;padding: 7px;width: 100%;">
              Additional Information
              <div class="display-zone">
                <div class="additional-information-width">
                  <!-- <label>{{'NUMBER_OF_JACKETS_RUPTURE_LABEL' | translate}}: <span
                      style="color:rgb(221, 82, 82);font-size:15px;"> *
                    </span></label> -->
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'Enter the number of jackets ruptures'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
                      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'NUMBER_OF_JACKETS_RUPTURE_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text" [disabled]='true'
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES"
                            maxlength="18" placeholder="{{'NUMBER_OF_JACKETS_RUPTURE_PLACEHOLDER' | translate}}"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES, 'numberOfJacketRuptures')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'NUMBER_OF_JACKETS_RUPTURE_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES"
                            maxlength="18" placeholder="{{'NUMBER_OF_JACKETS_RUPTURE_PLACEHOLDER' | translate}}"
                            (change)="onChangeDisableJacketed(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES, 'numberOfJacketRuptures')"
                            step="any" inputmode="decimal" [required]>
                          <mat-error
                            *ngIf="hasErrorStart('numberOfJacketRuptures', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                            {{numberOfJacketRupturesErrorMessage}}</mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="additional-information-width">
                  <!-- <label>{{'NUMBER_OF_JACKETS_REPAIRS_LABEL' | translate}}: <span
                      style="color:rgb(221, 82, 82);font-size:15px;"> *
                    </span></label> -->
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'Enter the number of jackets repairs'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
                      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'NUMBER_OF_JACKETS_REPAIRS_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text" [disabled]='true'
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_REPAIR"
                            maxlength="18" placeholder="{{'NUMBER_OF_JACKETS_REPAIRS_PLACEHOLDER' | translate}}"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_REPAIR, 'numberOfJacketsRepair')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'NUMBER_OF_JACKETS_REPAIRS_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_REPAIR"
                            maxlength="18" placeholder="{{'NUMBER_OF_JACKETS_REPAIRS_PLACEHOLDER' | translate}}"
                            (change)="onChangeDisableJacketed(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_REPAIR, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_REPAIR, 'numberOfJacketsRepair')"
                            step="any" inputmode="decimal" [required]>
                          <mat-error
                            *ngIf="hasErrorStart('numberOfJacketsRepair', selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ADDITIONAL_NUMBER_OF_JACKETS_REPAIR)">
                            {{numberOfJacketsRepairErrorMessage}}</mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="display: flex; margin-top: 5px;">
            <div style="border: solid black 0.5px;border-radius: 5px;padding: 7px;width: 100%;">
              <label>{{'OBSERVATION_NOTES' | translate}}:</label>
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].NOTES"
                (ionFocus)="onFocusUserInputField($event)"></ion-textarea>
            </div>
          </div>
          <div *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showJacketedError"
            class="additional-information-width">
            <label style="color:rgb(221, 82, 82);font-size:15px;">{{'JACKETED_ERROR_MESSAGE' | translate}} </label>
          </div>
        </div>
        <!-- & JACKETED FORM END -->

        <!-- & WIRE FORM START -->
        <div *ngIf="options.CUSTOM_CONSTRUCTION == 'Wire'">
          <div>
            <div>
              <p style="margin-top: 4px;margin-bottom: 0px;">{{'End in Use'}}:
              </p>
              <div>
                <div>


                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'Select end in use'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
                      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{'INSPECTION_END_IN_USE' | translate}}</mat-label>
                          <mat-select disableOptionCentering [disabled]='true'
                            placeholder="{{'INSPECTION_END_IN_USE' | translate}}" interface="popover"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE">
                            <mat-select-trigger
                              *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != ''">
                              <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                              {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE}}
                            </mat-select-trigger>
                            <mat-option value="A">
                              A
                            </mat-option>
                            <mat-option value="B">
                              B
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{'INSPECTION_END_IN_USE' | translate}}</mat-label>
                          <mat-select disableOptionCentering placeholder="{{'INSPECTION_END_IN_USE' | translate}}"
                            interface="popover"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE">
                            <mat-select-trigger
                              *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != ''">
                              <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                              {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE}}
                            </mat-select-trigger>
                            <mat-option value="A">
                              A
                            </mat-option>
                            <mat-option value="B">
                              B
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="platformId !== 'electron' && device.platform != 'browser'">
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">
                <span style="height: 100%; margin: auto;">{{'INSPECTION_DATE_LABEL'|translate}}</span>
                <span style="color:rgb(221, 82, 82);font-size:15px;margin: auto;"> * </span>
              </p> -->
              <div>
                <div *ngIf='helpService.helpMode'>
                  <div tooltip="{{'Select inspection date'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <mat-form-field style="width:100%">
                      <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                      <input matInput [matDatepicker]="picker" [max]="maxDate" disabled
                        [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                        placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}" (focus)="picker.open()"
                        (click)="picker.open()" readonly>
                      <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                      </mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>
                <div *ngIf='!helpService.helpMode'>
                  <div>
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput [matDatepicker]="picker" [max]="maxDate"
                          placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                          step="any" (change)="onChangeDisable('eventDate')" (dateInput)="addEvent('input', $event)"
                          (dateChange)="addEvent('change', $event)" (keydown)="keyPressed($event, 'eventDate', false)"
                          (focus)="picker.open()" (click)="picker.open()" readonly>
                        <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="display-zone">
            <!--! WIRE ZONE1 - START  -->
            <div [ngClass]="[(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')) ? 'zone-inner-wrapper' : 'zone-inner-utility']">
              <label class="visionLabel" *ngIf="(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility'))">{{ 'ZONE_ONE_LABEL' | translate}}:</label>
              <div>
                <!-- <label>{{'SMALLEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'MAX_WIRE_BREAKES'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
                    [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'MAX_WIRE_BREAKES' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_MAX_WIRE_BREAKES"
                          maxlength="18" placeholder="{{'MAX_WIRE_BREAKES' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_MAX_WIRE_BREAKES, 'smallestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'MAX_WIRE_BREAKES' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_MAX_WIRE_BREAKES"
                          maxlength="18" placeholder="{{'MAX_WIRE_BREAKES' | translate}}"
                          (change)="onChangeWire(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_MAX_WIRE_BREAKES, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_MAX_WIRE_BREAKES, 'smallestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'LARGEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'WAVINESS_GAP_MEASUREMENT'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WAVINESS_GAP_MEASUREMENT' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WAVINESS_GAP_MEASUREMENT"
                          maxlength="18" placeholder="{{'WAVINESS_GAP_MEASUREMENT' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WAVINESS_GAP_MEASUREMENT, 'largestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WAVINESS_GAP_MEASUREMENT' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WAVINESS_GAP_MEASUREMENT"
                          maxlength="18" placeholder="{{'WAVINESS_GAP_MEASUREMENT' | translate}}"
                          (change)="onChangeWire(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WAVINESS_GAP_MEASUREMENT, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WAVINESS_GAP_MEASUREMENT, 'largestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'WIRE_BREAKS_IN_TERMIANTION'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION"
                          maxlength="18" placeholder="{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION, 'cutStrandCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION"
                          maxlength="18" placeholder="{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}"
                          (change)="onChangeWire(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION, 'cutStrandCountCover')"
                          step="any" inputmode="decimal" [required]>
                        <mat-error
                          *ngIf="hasErrorStart('cutStrandCountCover', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{cutStrandCountCoverErrorMessage}}
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <!--! WIRE ZONE1 - END  -->
            <!-- ! WIRE ZONE2 - START -->
            <div class="zone-inner-wrapper zone-inner-wrapper-margin " *ngIf="(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility'))">
              {{'ZONE_TWO_LABEL' | translate}}:
              <div>
                <!-- <label>{{'LARGEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'WAVINESS_GAP_MEASUREMENT'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WAVINESS_GAP_MEASUREMENT' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WAVINESS_GAP_MEASUREMENT"
                          maxlength="18" placeholder="{{'WAVINESS_GAP_MEASUREMENT' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WAVINESS_GAP_MEASUREMENT, 'largestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WAVINESS_GAP_MEASUREMENT' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WAVINESS_GAP_MEASUREMENT"
                          maxlength="18" placeholder="{{'WAVINESS_GAP_MEASUREMENT' | translate}}"
                          (change)="onChangeWire(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WAVINESS_GAP_MEASUREMENT, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WAVINESS_GAP_MEASUREMENT, 'largestVisibleDiam')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'WIRE_BREAKS_IN_TERMIANTION'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION"
                          maxlength="18" placeholder="{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION, 'cutStrandCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION"
                          maxlength="18" placeholder="{{'WIRE_BREAKS_IN_TERMIANTION' | translate}}"
                          (change)="onChangeWire(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION, 'cutStrandCountCover')"
                          step="any" inputmode="decimal" [required]>
                        <mat-error
                          *ngIf="hasErrorStart('cutStrandCountCover', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{cutStrandCountCoverErrorMessage}}
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <!-- ! WIRE ZONE2 - END -->
            <div class="zone-inner-wrapper-margin" [ngClass]="[(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')) ? 'zone-inner-wrapper' : 'zone-inner-utility']">
              <br>
              <div>
                <!-- <label>{{'TOTAL_WORKING_HOURS_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'TOTAL_WORKING_HOURS_LABEL'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_LABEL' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_LABEL' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_LABEL' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_LABEL' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                        <!-- <mat-error
                          *ngIf="hasErrorStart('totalWorkingHours', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{totalWorkingHoursErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'TOTAL_OPERATIONS_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'TOTAL_OPERATIONS_LABEL'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_LABEL' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_LABEL' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_LABEL' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_LABEL' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                        <!-- <mat-error
                          *ngIf="hasErrorStart('totalOfOperations', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{totalOfOperationsErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <!-- <label>{{'TOTAL_OPERATIONS_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Select pass or fail'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering [disabled]='true'
                          placeholder="{{'PASS_FAIL_LABEL' | translate}}" interface="popover"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering placeholder="{{'PASS_FAIL_LABEL' | translate}}"
                          interface="popover"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                            {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            style="margin-top: 5px; border: solid black 0.5px;border-radius: 5px;padding: 7px;width: 100%;">
            <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
              <div tooltip="{{'ANOMOLY_LABEL'|translate}}" positionV="bottom"
                [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                [hideOthers]=helpService.hideOthers style="width:100%">
                <div>
                  <mat-form-field style="width: 100% !important">
                    <mat-label>{{'ANOMOLY_LABEL' | translate}}</mat-label>
                    <mat-select disableOptionCentering multiple [disabled]='true'
                      placeholder="{{'ANOMOLY_LABEL' | translate}}" interface="popover" [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED">
                      <mat-select-trigger *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED != ''">
                        <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                        <div style="overflow: visible; white-space: break-spaces;">
                          <span *ngFor="let anomalyData of selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED; let x = index">{{anomalyData.ANOMOLY_NAME}}<span *ngIf="x<(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED.length -1)">,&nbsp;</span> </span>
                        </div>
                      </mat-select-trigger>
                      <div class="custom-panel">
                      <mat-option *ngFor="let anomaly of anomalyList" [value]="anomaly.ANOMOLY_NAME">
                        {{anomaly.ANOMOLY_NAME}}
                      </mat-option>
                    </div>
                    <footer>
                      <button mat-raised-button>Ok</button>
                      <button mat-button>Cancel</button>
                    </footer>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
            </div>

            <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
              <div style="width:100%">
                <div>
                  <mat-form-field style="width: 100% !important">
                    <mat-label>{{'ANOMOLY_LABEL' | translate}}</mat-label>
                    <mat-select disableOptionCentering multiple placeholder="{{'ANOMOLY_LABEL' | translate}}"
                      interface="popover" [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED"
                      (change)="onChangeWire(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED, getLIDIndexMainline(options.CERTIFICATE_NUM))">
                      <mat-select-trigger *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED != ''">
                        <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                        <div style="overflow: visible; white-space: break-spaces;">
                          <span *ngFor="let anomalyData of selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED; let x = index">{{anomalyData.ANOMOLY_NAME}}<span *ngIf="x<(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED.length -1)">,&nbsp;</span> </span>
                        </div>
                      </mat-select-trigger>
                      <!-- <div class="custom-panel"> -->
                        <mat-option *ngFor="let anomaly of anomalyList" [value]="anomaly">
                          {{anomaly.ANOMOLY_NAME}}
                        </mat-option>
                      <!-- </div> -->
                      <!-- <footer>
                        <button mat-raised-button>Ok</button>
                        <button mat-button>Cancel</button>
                      </footer> -->
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
            </div>
            <div>
              <div *ngFor="let anomalyData of selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED">
                <div *ngIf="anomalyData.ANOMOLY_TYPE == 'N'">
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                      [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{anomalyData.ANOMOLY_NAME}}</mat-label>
                          <mat-select disableOptionCentering [disabled]='true'
                            placeholder="{{anomalyData.ANOMOLY_NAME}}" interface="popover"
                            [(ngModel)]="anomalyData.ANOMOLY_VALUE">
                            <mat-select-trigger
                              *ngIf="anomalyData.ANOMOLY_VALUE != undefined && anomalyData.ANOMOLY_VALUE != ''">
                              {{anomalyData.ANOMOLY_VALUE}}
                            </mat-select-trigger>
                            <mat-option value="0">
                              0
                            </mat-option>
                            <mat-option value="1">
                              1
                            </mat-option>
                            <mat-option value="2">
                              2
                            </mat-option>
                            <mat-option value="More">
                              More
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{anomalyData.ANOMOLY_NAME}}</mat-label>
                          <mat-select disableOptionCentering placeholder="{{anomalyData.ANOMOLY_NAME}}"
                            interface="popover"
                            [(ngModel)]="anomalyData.ANOMOLY_VALUE">
                            <mat-select-trigger
                              *ngIf="anomalyData.ANOMOLY_VALUE != undefined && anomalyData.ANOMOLY_VALUE != ''">
                              <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                              {{anomalyData.ANOMOLY_VALUE}}
                            </mat-select-trigger>
                            <mat-option value="0">
                              0
                            </mat-option>
                            <mat-option value="1">
                              1
                            </mat-option>
                            <mat-option value="2">
                              2
                            </mat-option>
                            <mat-option value="More">
                              More
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="display: flex; margin-top: 5px;">
            <div style="border: solid black 0.5px;border-radius: 5px;padding: 7px;width: 100%;">
              <label>{{'OBSERVATION_NOTES' | translate}}:</label>
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].NOTES"
                (ionFocus)="onFocusUserInputField($event)"></ion-textarea>
            </div>
          </div>
          <div *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showWireError || selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ANOMOLIES_SELECTED.length > 0"
            class="additional-information-width">
            <label style="color:rgb(221, 82, 82);font-size:15px;">{{'JACKETED_ERROR_MESSAGE' | translate}} </label>
          </div>
        </div>
        <!-- & WIRE FORM END -->

        <!-- & HMPE FORM START -->
        <div *ngIf="options.CUSTOM_CONSTRUCTION == 'HMPE'">
          <div>
            <div>
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">{{'End in Use'}}:
              </p> -->
              <div>
                <div>
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'INSPECTION_END_IN_USE'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
                      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{'INSPECTION_END_IN_USE' | translate}}</mat-label>
                          <mat-select disableOptionCentering [disabled]='true'
                            placeholder="{{'INSPECTION_END_IN_USE' | translate}}" interface="popover"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE">
                            <mat-select-trigger
                              *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != ''">
                              <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                              {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE}}
                            </mat-select-trigger>
                            <mat-option value="A">
                              A
                            </mat-option>
                            <mat-option value="B">
                              B
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width: 100% !important">
                          <mat-label>{{'INSPECTION_END_IN_USE' | translate}}</mat-label>
                          <mat-select disableOptionCentering placeholder="{{'INSPECTION_END_IN_USE' | translate}}"
                            interface="popover"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE">
                            <mat-select-trigger
                              *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE != ''">
                              <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                              {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].END_IN_USE}}
                            </mat-select-trigger>
                            <mat-option value="A">
                              A
                            </mat-option>
                            <mat-option value="B">
                              B
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="platformId !== 'electron' && device.platform != 'browser'">
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">
                <span style="height: 100%; margin: auto;">{{'INSPECTION_DATE_LABEL'|translate}}</span>
                <span style="color:rgb(221, 82, 82);font-size:15px;margin: auto;"> * </span>
              </p> -->
              <div>
                <div *ngIf='helpService.helpMode'>
                  <div tooltip="{{'Select Inspection Date'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <mat-form-field style="width:100%">
                      <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                      <input matInput [matDatepicker]="picker" [max]="maxDate" disabled
                        [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                        placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}" (focus)="picker.open()"
                        (click)="picker.open()" readonly>
                      <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                      </mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>
                <div *ngIf='!helpService.helpMode'>
                  <div>
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput [matDatepicker]="picker" [max]="maxDate"
                          placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                          step="any" (change)="onChangeDisable('eventDate')" (dateInput)="addEvent('input', $event)"
                          (dateChange)="addEvent('change', $event)" (keydown)="keyPressed($event, 'eventDate', false)"
                          (focus)="picker.open()" (click)="picker.open()" readonly style="padding-left:14px;">
                        <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <div class="display-zone">
            <!-- ! HMPE ZONE1 - START -->
            <div [ngClass]="[(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')) ? 'zone-inner-wrapper' : 'zone-inner-utility']">
              <label class="visionLabel" *ngIf="(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility'))">{{ 'ZONE_ONE_LABEL' | translate}}:</label>
              <!-- & show ZONE1 lable and insight AI options if user is enabled for insight AI program - START -->
              <div *ngIf="dataService.isUserEnabledForInsightAI">
                <ion-label>Please select the type of inspection you want to conduct?</ion-label>
                <div class="insightAI">
                  <div class="insightAI-option">
                    <ion-checkbox mode="ios" justify="start" labelPlacement="end" value='VisionAI' (ionChange)="visionTypeChanged($event,i,selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)],'ZONE1')" [checked]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='VisionAI'">Insight AI</ion-checkbox>
                  </div>
                  <div class="insightAI-option">
                    <ion-checkbox mode="ios" justify="start" labelPlacement="end" value="Manual" (ionChange)="visionTypeChanged($event,i,selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)],'ZONE1')" [checked]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='Manual'">Manual</ion-checkbox>
                  </div>
                  <ion-button class="action-button" *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='VisionAI' && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI" (click)="previewInsightAIResult(i,options,'ZONE1')" style="margin-top: 10px;">RESULTS</ion-button>
                  <ion-fab-button 
                      class="action-button"
                      color="primary" 
                      size="small" 
                      (click)="openCamera(i,options,'ZONE1')" 
                      *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='VisionAI'"
                      [disabled]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION!=''">
                    <ion-icon name="camera"></ion-icon>
                  </ion-fab-button>
                </div>
                <!-- <label class="visionLabel" *ngIf="(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility'))">{{ 'ZONE_ONE_LABEL' | translate}}:</label>
                <ion-grid style="--ion-grid-padding: 0px !important;">
                  <ion-row class="ion-justify-content-between">
                    <ion-col size="12" size-md="12" size-lg="12" size-xl="12" >
                      <ion-label>Please select the type of inspection you want to conduct?</ion-label>
                    </ion-col>
                    <ion-col size="5" size-xs="6" size-md="4" size-lg="4" size-xl="3">
                      <ion-item>
                        <ion-checkbox mode="ios" justify="start" labelPlacement="end" value='VisionAI' (ionChange)="visionTypeChanged($event,i,selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)],'ZONE1')" [checked]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='VisionAI'">Insight AI</ion-checkbox>
                      </ion-item>
                    </ion-col>
                    <ion-col size="5" size-xs="6" size-md="4" size-lg="4" size-xl="3">
                      <ion-item>
                        <ion-checkbox mode="ios" justify="start" labelPlacement="end" value="Manual" (ionChange)="visionTypeChanged($event,i,selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)],'ZONE1')" [checked]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='Manual'">Manual</ion-checkbox>
                      </ion-item>
                    </ion-col>
                    <ion-col size="6" size-xs="4" size-md="6" size-lg="6" size-xl="5" style="text-align:end;">
                      <ion-button *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='VisionAI' && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSIGHT_AI" (click)="previewInsightAIResult(i,options,'ZONE1')" style="margin-top: 10px;">RESULTS</ion-button>
                    </ion-col>
                    <ion-col size="6" size-xs="2" size-md="6" size-lg="6" size-xl="1" style="text-align:end;">
                      <ion-fab-button 
                        color="primary" 
                        size="small" 
                        (click)="openCamera(i,options,'ZONE1')" 
                        *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='VisionAI'"
                        [disabled]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION!=''">
                      <ion-icon name="camera"></ion-icon>
                    </ion-fab-button>
                    </ion-col>
                  </ion-row>
                </ion-grid>
                <input id="myInput" type="file" *ngIf="device.platform == 'browser' || platformId == 'electron'" style="visibility:hidden; display: none;" accept="image/x-png,image/jpeg,image/jpg,image/png" (change)="onFileSelected($event)" (click)="onInputClick($event)"/>  -->
              </div>
              <!-- & show ZONE1 lable and insight AI options if user is enabled for insight AI program - END -->

              <!-- & show only ZONE1 label if user is not enabled for insight AI program-->
              <!-- <label class="visionLabel" *ngIf="!dataService.isUserEnabledForInsightAI && selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')">{{ 'ZONE_ONE_LABEL' | translate}}:</label> -->
              <div>
                <!-- <label>{{'SMALLEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'EXTERNAL_ABRASION_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION"
                          maxlength="1" placeholder="{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION, 'externalAbrasion')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>


                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION"
                          [readonly]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INSPECTION_TYPE=='VisionAI'"
                          maxlength="1" placeholder="{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                          (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_EXTERNAL_ABRASION, 'externalAbrasion')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              @if(!(isUtility && options.PRODUCT_TYPE === 'Conventional Fiber (Class I)' && options.CONSTRUCTION === "12-Strand")) {
                <div>
                  <!-- <label>{{'LARGEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                      style="color:rgb(221, 82, 82);font-size:15px;"> *
                    </span></label> -->
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'INTERNAL_ABRASION_PLACEHOLDER'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                      [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text" [disabled]='true'
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INTERNAL_ABRASION"
                            maxlength="1" placeholder="{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                            (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INTERNAL_ABRASION, 'internalAbrasion')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
  
                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INTERNAL_ABRASION"
                            maxlength="1" placeholder="{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                            (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INTERNAL_ABRASION, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                            (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_INTERNAL_ABRASION, 'internalAbrasion')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              }
              
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'CUT_YARN_COUNT_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_YARN_COUNT"
                          maxlength="18" placeholder="{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_YARN_COUNT, 'cutYarnCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_YARN_COUNT"
                          maxlength="18" placeholder="{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}"
                          (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_YARN_COUNT, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_CUT_YARN_COUNT, 'cutYarnCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'LENGTH_OF_GLAZING_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LENGTH_OF_GLAZING"
                          maxlength="18" placeholder="{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LENGTH_OF_GLAZING, 'lengthOfGlazing')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LENGTH_OF_GLAZING"
                          maxlength="18" placeholder="{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}"
                          (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LENGTH_OF_GLAZING, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_ONE_LENGTH_OF_GLAZING, 'lengthOfGlazing')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <!-- ! HMPE ZONE1 - END -->
            <!-- ! HMPE ZONE2 - START -->
            <div class="zone-inner-wrapper zone-inner-wrapper-margin " *ngIf="selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')">
              <label class="visionLabel">{{'ZONE_TWO_LABEL' | translate}}:</label>
              <!-- & show ZONE2 label and insight AI options if the user is enabled for insight AI program -->
              <div *ngIf="dataService.isUserEnabledForInsightAI">
                <ion-label>Please select the type of inspection you want to conduct?</ion-label>
                <div class="insightAI">
                  <div class="insightAI-option">
                    <ion-checkbox mode="ios" justify="start" labelPlacement="end" value='VisionAI' (ionChange)="visionTypeChanged($event,i,selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)],'ZONE2')" [checked]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INSPECTION_TYPE=='VisionAI'">Insight AI</ion-checkbox>
                  </div>
                  <div class="insightAI-option">
                    <ion-checkbox mode="ios" justify="start" labelPlacement="end" value="Manual" (ionChange)="visionTypeChanged($event,i,selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)],'ZONE2')" [checked]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INSPECTION_TYPE=='Manual'">Manual</ion-checkbox>
                  </div>
                  <ion-button class="action-button" *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INSPECTION_TYPE=='VisionAI' && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INSIGHT_AI" (click)="previewInsightAIResult(i,options,'ZONE2')" style="margin-top: 10px;">RESULTS</ion-button>
                  <ion-fab-button 
                        class="action-button"
                        color="primary" 
                        size="small" 
                        (click)="openCamera(i,options,'ZONE2')" 
                        *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INSPECTION_TYPE=='VisionAI'"
                        [disabled]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION!=''">
                      <ion-icon name="camera"></ion-icon>
                    </ion-fab-button>
                </div>
              </div>
              
              <!-- & show only ZONE2 label if the user is not enabled for insight AI program -->
              <div>
                <!-- <label>{{'SMALLEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'EXTERNAL_ABRASION_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION"
                          maxlength="1" placeholder="{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION, 'externalAbrasion')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION"
                          maxlength="1" placeholder="{{'EXTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                          [readonly]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INSPECTION_TYPE=='VisionAI'"
                          (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_EXTERNAL_ABRASION, 'externalAbrasion')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              @if(!(isUtility && options.PRODUCT_TYPE === 'Conventional Fiber (Class I)' && options.CONSTRUCTION === "12-Strand")) {
                <div>
                  <!-- <label>{{'LARGEST_VISIBLE_DIAMETER_LABEL' | translate}}: <span
                      style="color:rgb(221, 82, 82);font-size:15px;"> *
                    </span></label> -->
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'INTERNAL_ABRASION_PLACEHOLDER'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                      [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text" [disabled]='true'
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INTERNAL_ABRASION"
                            maxlength="1" placeholder="{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                            (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INTERNAL_ABRASION, 'internalAbrasion')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INTERNAL_ABRASION"
                            maxlength="1" placeholder="{{'INTERNAL_ABRASION_PLACEHOLDER' | translate}}"
                            (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INTERNAL_ABRASION, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                            (keydown)="keyPressedAbrasion($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_INTERNAL_ABRASION, 'internalAbrasion')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              }
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'CUT_YARN_COUNT_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_YARN_COUNT"
                          maxlength="18" placeholder="{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_YARN_COUNT, 'cutYarnCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_YARN_COUNT"
                          maxlength="18" placeholder="{{'CUT_YARN_COUNT_PLACEHOLDER' | translate}}"
                          (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_YARN_COUNT, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_CUT_YARN_COUNT, 'cutYarnCountCover')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'CUT_STRAND_COUNT_COVER_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'LENGTH_OF_GLAZING_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LENGTH_OF_GLAZING"
                          maxlength="18" placeholder="{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LENGTH_OF_GLAZING, 'lengthOfGlazing')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LENGTH_OF_GLAZING"
                          maxlength="18" placeholder="{{'LENGTH_OF_GLAZING_PLACEHOLDER' | translate}}"
                          (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LENGTH_OF_GLAZING, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].ZONE_TWO_LENGTH_OF_GLAZING, 'lengthOfGlazing')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <!-- ! HMPE ZONN2 - END -->

            <div class="zone-inner-wrapper-margin" [ngClass]="[(selectedIndustry && !selectedIndustry.toLowerCase().includes('utility')) ? 'zone-inner-wrapper' : 'zone-inner-utility']">
              <br>
              <div>
                <!-- <label>{{'TOTAL_WORKING_HOURS_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'TOTAL_WORKING_HOURS_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'TOTAL_OPERATIONS_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'TOTAL_OPERATIONS_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                        <!-- <mat-error
                          *ngIf="hasErrorStart('totalOfOperations', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{totalOfOperationsErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Select pass or fail'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering [disabled]='true'
                          placeholder="{{'PASS_FAIL_LABEL' | translate}}" interface="popover"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering placeholder="{{'PASS_FAIL_LABEL' | translate}}"
                          interface="popover"
                          [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                            {{selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>
          <div style="display: flex; margin-top: 5px;">
            <div style="border: solid black 0.5px;border-radius: 5px;padding: 7px;width: 100%;">
              <div class="display-zone">
                <div class="additional-information-width">
                  <!-- <label>{{'NUMBER_OF_JACKETS_RUPTURE_LABEL' | translate}}: <span
                      style="color:rgb(221, 82, 82);font-size:15px;"> *
                    </span></label> -->
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'TWIST_PER_METER_PLACEHOLDER'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
                      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'TWIST_PER_METER_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text" [disabled]='true'
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].TWIST_PER_METER"
                            maxlength="18" placeholder="{{'TWIST_PER_METER_PLACEHOLDER' | translate}}"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].TWIST_PER_METER, 'twistPerGear')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'TWIST_PER_METER_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].TWIST_PER_METER"
                            maxlength="18" placeholder="{{'TWIST_PER_METER_PLACEHOLDER' | translate}}"
                            (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].TWIST_PER_METER, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].TWIST_PER_METER, 'twistPerGear')"
                            step="any" inputmode="decimal" [required]>
                          <mat-error
                            *ngIf="hasErrorStart('numberOfJacketRuptures', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                            {{numberOfJacketRupturesErrorMessage}}</mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="additional-information-width">
                  <!-- <label>{{'NUMBER_OF_JACKETS_REPAIRS_LABEL' | translate}}: <span
                      style="color:rgb(221, 82, 82);font-size:15px;"> *
                    </span></label> -->
                  <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div tooltip="{{'CHAFE_GEAR_HOLE_COUNT_PLACEHOLDER'|translate}}" positionV="bottom"
                      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
                      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'CHAFE_GEAR_HOLE_COUNT_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text" [disabled]='true'
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].CHAFE_GEAR_HOLE_COUNT"
                            maxlength="18" placeholder="{{'CHAFE_GEAR_HOLE_COUNT_PLACEHOLDER' | translate}}"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].CHAFE_GEAR_HOLE_COUNT, 'chafeGearHoleCount')"
                            step="any" inputmode="decimal" [required]>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                    <div style="width:100%">
                      <div>
                        <mat-form-field style="width:100%">
                          <mat-label>{{'CHAFE_GEAR_HOLE_COUNT_PLACEHOLDER' | translate}}</mat-label>
                          <input matInput type="text"
                            [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].CHAFE_GEAR_HOLE_COUNT"
                            maxlength="18" placeholder="{{'CHAFE_GEAR_HOLE_COUNT_PLACEHOLDER' | translate}}"
                            (change)="onChangeHMPE(selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].CHAFE_GEAR_HOLE_COUNT, getLIDIndexMainline(options.CERTIFICATE_NUM))"
                            (keydown)="keyPressed($event, selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].CHAFE_GEAR_HOLE_COUNT, 'chafeGearHoleCount')"
                            step="any" inputmode="decimal" [required]>
                          <mat-error
                            *ngIf="hasErrorStart('numberOfJacketsRepair', selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].CHAFE_GEAR_HOLE_COUNT)">
                            {{numberOfJacketsRepairErrorMessage}}</mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="display: flex; margin-top: 5px;">
            <div style="border: solid black 0.5px;border-radius: 5px;padding: 7px;width: 100%;">
              <label>{{'OBSERVATION_NOTES' | translate}}:</label>
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].NOTES"
                (ionFocus)="onFocusUserInputField($event)"></ion-textarea>
            </div>
          </div>
          <div *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showHmpeError"
            class="additional-information-width">
            <label style="color:rgb(221, 82, 82);font-size:15px;">{{'JACKETED_ERROR_MESSAGE' | translate}} </label>
          </div>
          <div *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showExternalErrorMessage"
            class="additional-information-width">
            <ion-row style="padding: 0px 10px 0px 17px;">
              <ion-col style="display: inline-flex;">
                <fa-icon *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showExternalErrorMessageType == 'GREEN'" icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
                <fa-icon *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showExternalErrorMessageType == 'YELLOW'" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
                <fa-icon *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showExternalErrorMessageType == 'RED'" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
                <p *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showExternalErrorMessageType == 'GREEN'" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p> 
                <p *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showExternalErrorMessageType == 'YELLOW'" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
                <p *ngIf="selectedMainlineList[getLIDIndexMainline(options.CERTIFICATE_NUM)].showExternalErrorMessageType == 'RED'" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
              </ion-col>
        
              
            </ion-row>
          </div>
        </div>
        <!-- & HMPE FORM END -->
      </div>
    </ion-item-sliding>
  </ion-list>
  <!-- ! MAINLINE SECTION END-->

  <!-- ! TAILS SECTION START-->
  <ion-list *ngIf="fieldSegment == 'tails'" style="padding-bottom: 50px;">
    <ion-item-sliding *ngFor="let options of rpsList;let i = index">
      <!-- ^ TAILS ITEMS LIST START -->
      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);"
        [ngClass]="{'my-css-class': checkDivIsOpenCert(options) === true && tailSelected==i}">
        <ion-item *ngIf='!readOnly' lines="none">
          <ion-checkbox color="primary" (ionChange)="onCheckedCert(options.CERTIFICATE_NUM, i)"
            [value]="options.CERTIFICATE_NUM" [(ngModel)]="options.CHECKED_ITEM" [checked]="checkIfSelectedCert(options.CERTIFICATE_NUM)"
            [disabled]='helpService.helpMode || readOnly'></ion-checkbox>
        </ion-item>
        <ion-label (click)="helightItemCert(options,i)">
          <!-- & TAILS ITEMS DATA FOR ANDROID AND IOS START -->
          <div *ngIf="cordova.platformId !== 'electron' && device.platform != 'browser'">
            <div style="display: block;">
              <div style="color: black; width: 100%">
                {{options.NAME}}
              </div>
              <div *ngIf="options.PRODUCT != '' || options.PRODUCT != null" style="color: grey; width: 100%">
                {{'Product' | translate}} : {{options.PRODUCT}}
              </div>
              <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                {{'Diameter' | translate}} : {{options.DIAM}}
              </div>
              <!-- <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                {{'Winch' | translate}} : {{options.EQUIP_NAME}}
              </div> -->
            </div>
          </div>
          <!-- & TAILS ITEMS DATA FOR ANDROID AND IOS END -->

          <!-- & TAILS ITEMS DATA FOR ELECTRON AND BROWSER START -->
          <div *ngIf="cordova.platformId == 'electron' || device.platform == 'browser'" style="display:flex;">
            <div style="display: block; width: 50%">
              <div style="color: black; width: 100%">
                {{options.NAME}}
              </div>
              <div *ngIf="options.PRODUCT != '' || options.PRODUCT != null" style="color: grey; width: 100%">
                {{'Product' | translate}} : {{options.PRODUCT}}
              </div>
              <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                {{'Diameter' | translate}} : {{options.DIAM}}
              </div>
              <!-- <div *ngIf="options.DIAM != '' || options.DIAM != null" style="color: grey; width: 100%">
                {{'Winch' | translate}} : {{options.EQUIP_NAME}}
              </div> -->
            </div>
            <div style="width: 50%" *ngIf="checkIfSelectedCert(options.CERTIFICATE_NUM)">
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">
                <span style="height: 100%; margin: auto;">{{'INSPECTION_DATE_LABEL'|translate}}</span>
                <span style="color:rgb(221, 82, 82);font-size:15px;margin: auto;"> * </span>
              </p> -->
              <div>
                <div *ngIf='helpService.helpMode'>
                  <div tooltip="{{'Select Inspection Date'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <mat-form-field style="width:100%">
                      <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                      <input matInput [matDatepicker]="picker" [max]="maxDate" disabled
                        [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                        placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}" (focus)="picker.open()"
                        (click)="picker.open()" readonly>
                      <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                      </mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>
                <div *ngIf='!helpService.helpMode'>
                  <div>
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput [matDatepicker]="picker" [max]="maxDate"
                          placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}"
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                          step="any" (change)="onChangeDisable('eventDate')" (dateInput)="addEvent('input', $event)"
                          (dateChange)="addEvent('change', $event)" (keydown)="keyPressed($event, 'eventDate', false)"
                          (focus)="picker.open()" (click)="picker.open()" readonly>
                        <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- & TAILS ITEMS DATA FOR ELECTRON AND BROWSER END -->
        </ion-label>
        <ion-buttons (click)="iconCert(i)" tappable>
          <ion-button style="padding-bottom: 8px;" *ngIf="checkIfSelectedCert(options.CERTIFICATE_NUM)"
            class="winch-info-button">
            <!-- & INSPECT ROPE BUTTON FOR ELECTRON AND BROWSER START -->
            <span *ngIf="platformId == 'electron' || device.platform == 'browser'"
              class="winch-info-message">Inspect Rope</span>
            <!-- & INSPECT ROPE BUTTON FOT ELECTRON AND BROWSER END -->

            <!-- & INSPECT ROPW BUTTON FOR ANDROID AND IOS START -->
            <span *ngIf="platformId !== 'electron' && device.platform != 'browser' && !checkSectionIsOpenCert(i)"
              class="winch-info-message">Inspect Rope</span>
            <!-- & INSPECT ROPE BUTTON FOR ANDROID AND IOS END -->
            <ion-icon slot="icon-only" slot="end"
              [name]="checkSectionIsOpenCert(i) ? 'chevron-up-outline' :'chevron-down-outline'"
              style="color:rgb(41, 40, 40)" class="winch-info-icon"></ion-icon>
          </ion-button>
        </ion-buttons>

      </ion-item>
      <!-- ^ TAILS ITEMS LIST END -->

      <div *ngIf="checkSectionIsOpenCert(i)" lines="none" class="ion-padding">
        <div>
          <div>
            <div *ngIf="platformId !== 'electron' && device.platform != 'browser'">
              <!-- <p style="margin-top: 4px;margin-bottom: 0px;">
                <span style="height: 100%; margin: auto;">{{'INSPECTION_DATE_LABEL'|translate}}</span>
                <span style="color:rgb(221, 82, 82);font-size:15px;margin: auto;"> * </span>
              </p> -->
              <div>
                <div *ngIf='helpService.helpMode'>
                  <div tooltip="{{'Select Inspection Date'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <mat-form-field style="width:100%">
                      <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                      <input matInput [matDatepicker]="picker" [max]="maxDate" disabled
                        [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                        placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}" (focus)="picker.open()"
                        (click)="picker.open()" readonly>
                      <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                      </mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>
                <div *ngIf='!helpService.helpMode'>
                  <div>
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'INSPECTION_DATE_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput [matDatepicker]="picker" [max]="maxDate"
                          placeholder="{{'INSPECTION_DATE_PLACEHOLDER' | translate}}"
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].INSPECTION_DATE"
                          step="any" (change)="onChangeDisable('eventDate')" (dateInput)="addEvent('input', $event)"
                          (dateChange)="addEvent('change', $event)" (keydown)="keyPressed($event, 'eventDate', false)"
                          (focus)="picker.open()" (click)="picker.open()" readonly style="padding-left:14px;">
                        <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;">
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <div class="display-zone">
            <div class="zone-inner-wrapper-tail ">
             
              <ion-grid style="padding:15px 10px 0px 17px" >
                <label style="padding-left:5px;">{{'Bearing Point Connection' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
                <ion-radio-group [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_BEARING_POINT_CONNECTION" *ngIf="helpService.helpMode">
                  <ion-row tooltip="{{'Select if the line is damaged or undamaged'|translate}}" positionV="bottom" positionH="right"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="Undamaged" [disabled]='helpService.helpMode || readOnly'>Undamaged
                        </ion-radio>
                        <!-- <ion-label>Undamaged</ion-label> -->
                      </ion-item>
                    </ion-col>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        &nbsp;&nbsp;
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="Damaged" [disabled]='helpService.helpMode || readOnly'>Damaged</ion-radio>
                        <!-- <ion-label>Damaged</ion-label> -->
                      </ion-item>
                    </ion-col>
                  </ion-row>
                </ion-radio-group>
            
                <ion-radio-group [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_BEARING_POINT_CONNECTION" *ngIf="!helpService.helpMode" (ionChange)="onChangeTail(selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_BEARING_POINT_CONNECTION, i)">
                  <ion-row>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="Undamaged" [disabled]='helpService.helpMode || readOnly'>Undamaged
                        </ion-radio>
                        <!-- <ion-label>Undamaged</ion-label> -->
                      </ion-item>
                    </ion-col>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        &nbsp;&nbsp;
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="Damaged" [disabled]='helpService.helpMode || readOnly'>Damaged</ion-radio>
                        <!-- <ion-label>Damaged</ion-label> -->
                      </ion-item>
                    </ion-col>
                  </ion-row>
                </ion-radio-group>
              </ion-grid>

              <ion-grid style="padding:15px 10px 0px 17px" >
                <label style="padding-left:5px;">{{'Fully Cut Strands' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
                <ion-radio-group [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_FULLY_CUT_STRANDS" *ngIf="helpService.helpMode">
                  <ion-row tooltip="{{'Select if the line has fully cut strands'|translate}}" positionV="bottom" positionH="right"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="yes" [disabled]='helpService.helpMode || readOnly'>Yes
                        </ion-radio>
                        <!-- <ion-label>Yes</ion-label> -->
                      </ion-item>
                    </ion-col>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        &nbsp;&nbsp;
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="No" [disabled]='helpService.helpMode || readOnly'>No</ion-radio>
                        <!-- <ion-label>No</ion-label> -->
                      </ion-item>
                    </ion-col>
                  </ion-row>
                </ion-radio-group>
            
                <ion-radio-group [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_FULLY_CUT_STRANDS" *ngIf="!helpService.helpMode" (ionChange)="onChangeTail(selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].TAIL_FULLY_CUT_STRANDS, i)">
                  <ion-row>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="Yes" [disabled]='helpService.helpMode || readOnly'>Yes
                        </ion-radio>
                        <!-- <ion-label>Yes</ion-label> -->
                      </ion-item>
                    </ion-col>
                    <ion-col>
                      <ion-item class='ion-radio-item-style'>
                        &nbsp;&nbsp;
                        <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="No" [disabled]='helpService.helpMode || readOnly'>No</ion-radio>
                        <!-- <ion-label>No</ion-label> -->
                      </ion-item>
                    </ion-col>
                  </ion-row>
                </ion-radio-group>
              </ion-grid>

            </div>
            <div class="zone-inner-wrapper-tail zone-inner-wrapper-margin ">
              <br>
              <div>
                <!-- <label>{{'TOTAL_WORKING_HOURS_LABEL' | translate}}: <span
                    style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'TOTAL_WORKING_HOURS_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS"
                          maxlength="18" placeholder="{{'TOTAL_WORKING_HOURS_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_WORKING_HOURS, 'totalWorkingHours')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!-- <label>{{'TOTAL_OPERATIONS_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
                  </span></label> -->
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'TOTAL_OPERATIONS_PLACEHOLDER'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}"
                          (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width:100%">
                        <mat-label>{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}</mat-label>
                        <input matInput type="text" [disabled]='true'
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION"
                          maxlength="18" placeholder="{{'TOTAL_OPERATIONS_PLACEHOLDER' | translate}}"
                          (change)="onChangeDisable('start')"
                          (keydown)="keyPressed($event, selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_TOTAL_OF_OPERATION, 'totalOfOperations')"
                          step="any" inputmode="decimal" [required]>
                        <!-- <mat-error
                          *ngIf="hasErrorStart('totalOfOperations', [getLIDIndexMainline(options.CERTIFICATE_NUM)])">
                          {{totalOfOperationsErrorMessage}}</mat-error> -->
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div tooltip="{{'Select Pass or fail'|translate}}" positionV="bottom"
                    [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
                    [hideOthers]=helpService.hideOthers style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering [disabled]='true'
                          placeholder="{{'PASS_FAIL_LABEL' | translate}}" interface="popover"
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            {{selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
                  <div style="width:100%">
                    <div>
                      <mat-form-field style="width: 100% !important">
                        <mat-label>{{'PASS_FAIL_LABEL' | translate}}</mat-label>
                        <mat-select disableOptionCentering placeholder="{{'PASS_FAIL_LABEL' | translate}}"
                          interface="popover"
                          [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL">
                          <mat-select-trigger
                            *ngIf="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != undefined && selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL != ''">
                            <!-- <span *ngFor="let anomaly of anomalyList"></span>{{anomaly}} -->
                            {{selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].EXTRA_PASS_FAIL}}
                          </mat-select-trigger>
                          <mat-option value="Pass">
                            Pass
                          </mat-option>
                          <mat-option value="Fail">
                            Fail
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>
          <div style="display: flex; margin-top: 5px;">
            <div style="border: solid black 0.5px;border-radius: 5px;padding: 7px;width: 100%;">
              <label>{{'OBSERVATION_NOTES' | translate}}:</label>
              <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
                [disabled]='helpService.helpMode || readOnly'
                [(ngModel)]="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].NOTES"
                (ionFocus)="onFocusUserInputField($event)"></ion-textarea>
            </div>
          </div>
          <div *ngIf="selectedCertList[getLIDIndexCert(options.CERTIFICATE_NUM)].showTailError"
            class="additional-information-width">
            <label style="color:rgb(221, 82, 82);font-size:15px;">{{'JACKETED_ERROR_MESSAGE' | translate}} </label>
          </div>
        </div>
      </div>
    </ion-item-sliding>
  </ion-list>
  <!-- ! LINE SECTION END-->

  <!-- ! FAB BUTTON WHEN HELPMODE IS ENABLED START -->
  <ion-fab *ngIf='helpService.helpMode' vertical="bottom" horizontal="end" slot="fixed"
    [topOffset]=helpService.topOffset tooltip="{{'Tap to save and submit the inspections'|translate}}" positionV="top"
    positionH="right" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
    [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveInspections()" *ngIf='!isDivOpen'>
      <fa-icon class="icon-style-other" icon="floppy-disk" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : closeDiv()" *ngIf='isDivOpen'>
      <fa-icon class="icon-style-other" icon="arrow-right" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
  <!-- ! FAB BUTTON WHEN HELPMODE IS ENABLED END -->

  <!-- ! FAB BUTTON WHEN HELP MODE IS DISABLED START -->
  <ion-fab *ngIf='!helpService.helpMode && !readOnly' vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveInspections()" *ngIf='!isDivOpen'>
      <fa-icon class="icon-style-other" icon="floppy-disk" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : closeDiv()" *ngIf='isDivOpen'>
      <fa-icon class="icon-style-other" icon="arrow-right" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
  <!-- ! FAB BUTTON WHEN HELP MODE IS DISABLED END -->

  <!-- <ion-fab vertical="bottom" horizontal="end" slot="fixed" *ngIf='isDivOpen'>
    <ion-fab-button color="primary" (click)="closeDiv()">
      <fa-icon class="icon-style-other" icon="arrow-right" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab> -->
</ion-content>
<!-- Footer -->
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <ion-footer class="footer-style">
    <ion-grid style="text-align:center;">
      <ion-row>
        <ion-col>
          <div (click)="openMenu()">
            <fa-icon class="icon-style" icon="bars"></fa-icon>
          </div>
        </ion-col>
        <ion-col>
          <div (click)="goToLineTracker()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
            <ion-ripple-effect></ion-ripple-effect>
            <fa-icon class="icon-style" icon="list-check"></fa-icon>
          </div>
        </ion-col>
        <ion-col>
          <div style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable" (click)="gotoInspections()">
            <ion-ripple-effect></ion-ripple-effect>
            <img src="./assets/icon/Rope_Inspection_ICON_3A.png" class="bottom-bar-image-style fa-fw">
          </div>
        </ion-col>
        <ion-col>
          <div style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable" (click)="gotoResources()">
            <ion-ripple-effect></ion-ripple-effect>
            <fa-icon class="icon-style" icon="grip"></fa-icon>
          </div>
        </ion-col>
        <ion-col>
          <div style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable" (click)="gotoContact()">
            <ion-ripple-effect></ion-ripple-effect>
            <fa-icon class="icon-style" icon="envelope" class="icon-style"></fa-icon>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-footer>
</div>