import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestContactPageRoutingModule } from './guest-contact-routing.module';

import { GuestContactPage } from './guest-contact.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestContactPageRoutingModule,
    TranslateModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestContactPage]
})
export class GuestContactPageModule {}
