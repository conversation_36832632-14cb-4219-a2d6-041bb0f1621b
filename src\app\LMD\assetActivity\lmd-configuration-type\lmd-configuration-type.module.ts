import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { LmdConfigurationTypePageRoutingModule } from './lmd-configuration-type-routing.module';
import { LmdConfigurationTypePage } from './lmd-configuration-type.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from 'src/app/components/footer/footer.component';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    FontAwesomeModule,
    LmdConfigurationTypePageRoutingModule,
    FooterComponent
  ],
  declarations: [LmdConfigurationTypePage]
})
export class LmdConfigurationTypePageModule {}
