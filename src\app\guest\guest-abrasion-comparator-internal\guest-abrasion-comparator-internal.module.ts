import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestAbrasionComparatorInternalPageRoutingModule } from './guest-abrasion-comparator-internal-routing.module';

import { GuestAbrasionComparatorInternalPage } from './guest-abrasion-comparator-internal.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestAbrasionComparatorInternalPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    TooltipsModule,
    GuestFooterComponent
    
  ],
  declarations: [GuestAbrasionComparatorInternalPage]
})
export class GuestAbrasionComparatorInternalPageModule {}
