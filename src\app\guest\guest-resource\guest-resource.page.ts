import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DocumentViewer } from '@awesome-cordova-plugins/document-viewer/ngx';
import { FileOpenerService } from 'src/app/services/file-opener.service';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ZipService } from '../../services/zip.service';
import { IonSelect, MenuController, NavController, AlertController, Platform, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faTh } from '@fortawesome/free-solid-svg-icons';

declare var cordova: any;
@Component({
  selector: 'app-guest-resource',
  templateUrl: './guest-resource.page.html',
  styleUrls: ['./guest-resource.page.scss'],
})
export class GuestResourcePage implements OnInit {

  selection: any;
  hideList = true;

  customizationAlert: any;

  @ViewChild('countryList') countrySelectRef: IonSelect;
  

  constructor(private router: Router,
    public translate: TranslateService,
    public dataService: DataService,
    private menu: MenuController,
    public navCtrl: NavController,
    public alertController: AlertController,
    public helpService: HelpService,
    public plt: Platform,
    public document: DocumentViewer,
    public file: File,
    public unviredSdk: UnviredCordovaSDK,
    public network: Network,
    public modalController: ModalController,
    public alertService: AlertService,
    public fileOpenerService: FileOpenerService,
    public device: Device,
    private zipService: ZipService, 
    private faIconLibrary: FaIconLibrary) {
      this.faIconLibrary.addIcons(faBars , faTh)
     }

  ngOnInit() {
    if(this.device.platform == "Android" || cordova.platformId == "electron") { 
      this.checkAndDownloadResources()
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }
  back() {
    this.router.navigate(['guest-home']);
  }

  openAbrasion() {
    this.router.navigate(['guest-resource-abrasion'])
  }

  openInspections() {
    this.router.navigate(['guest-resource-inspections'])
  }


  openPdfExternal() {
    this.fileOpenerService.openFileGuest('Abrasion-External.pdf');
  }

  openPdfInternal() {
    this.fileOpenerService.openFileGuest('Abrasion-Internal.pdf');
  }

  openPdfSinglebraid() {
    this.fileOpenerService.openFileGuest('App_SB_Inspection_Checklist_2017.pdf');
  }

  openPdfDoublebraid() {
    this.fileOpenerService.openFileGuest('App_DB_Inspection_Checklist_2017.pdf');
  }

  gotoHome() {
      this.router.navigate(['guest-home']);
  }

  async gotoInspections() {     
    const alert = await this.alertController.create({
      message: this.translate.instant("GUEST_MESSAGE"),
      buttons: ['OK']
    });
    await alert.present();
  }

  gotoResources() {
      this.router.navigate(['guest-resource']);
  }

  gotoContact() {
      this.router.navigate(['guest-contact']);
  }

  openSpliceInstructions() {
    this.router.navigate(['guest-resources-splice-instruction']);
  }

  checkAndDownloadResources() {
    this.file.checkDir(this.file.dataDirectory, "pdf").then(() => {
      console.log("Already Downloaded")
      }).catch((err) => {
        this.showResourceDownloadAlert(true)
        console.log("Download resources")
      });
  }

  async showResourceDownloadAlert(closePage) {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Do you want download resource files?',
      buttons: [
        {
          text: 'Later',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            if(closePage == true) {
              this.router.navigateByUrl('/guest-home')
            }
          }
        }, {
          text: 'Yes',
          handler: async () => {
            this.downloadResources();
            // this.route.navigate(['user-preferences']);
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }

  async downloadResources() {
    this.showResourceDownloadLoading();
    try {
      var url = "https://sandbox.unvired.io/samson/resources.zip";
      const tempUser = await this.unviredSdk.userSettings();

      if (tempUser && tempUser.data && tempUser.data["SERVER_URL"]) {
        const tempzSettings = tempUser.data["SERVER_URL"];

        if (tempzSettings.indexOf('/UMP/') > 0) {
          url = tempzSettings.replace('/UMP/','/samson/resources.zip');
        } else if (tempzSettings.indexOf('/UMP') > 0) {
          url = tempzSettings.replace('/UMP', '/samson/resources.zip');
        }
      } else {
        console.log("SERVER_URL not found in user settings, using default URL");
        this.unviredSdk.logInfo("Resources", "downloadResources", "SERVER_URL not found, using default URL");
      }

      this.unviredSdk.logInfo("Resources", "downloadResources", 'Download URL: ' + url);
      this.unviredSdk.logInfo("Resources", "downloadResources", 'Target path: ' + this.file.dataDirectory);

      const filePath = this.file.dataDirectory + 'dummy.zip';

      // Use fetch as in ResourcePage
      const response = await fetch(url);
      if (!response.ok) throw new Error('Network response was not ok');
      const blob = await response.blob();
      await this.file.writeFile(this.file.dataDirectory, 'dummy.zip', blob, { replace: true });
      this.unviredSdk.logInfo("home", "downloadResources", 'download complete');

      this.file.checkFile(this.file.dataDirectory, "dummy.zip")
        .then(() => {
          this.zipService.unzip(this.file.dataDirectory + 'dummy.zip', this.file.dataDirectory, (progress) => {
            if (typeof progress === 'number') {
              this.unviredSdk.logInfo("home", "downloadResources", 'Unzipping, ' + Math.round(progress) + '%');
            } else if (progress && typeof progress === 'object') {
              if (typeof progress.loaded === 'number' && typeof progress.total === 'number' && progress.total > 0) {
                this.unviredSdk.logInfo("home", "downloadResources", 'Unzipping, ' + Math.round((progress.loaded / progress.total) * 100) + '%');
              } else {
                this.unviredSdk.logInfo("home", "downloadResources", 'Unzipping in progress...');
              }
            } else {
              this.unviredSdk.logInfo("home", "downloadResources", 'Unzipping in progress...');
            }
          })
          .then(async (result) => {
            try {
              await this.file.checkDir(this.file.dataDirectory, "pdf");
              this.unviredSdk.logInfo("Resource", "downloadResources", 'SUCCESS - PDF directory exists');
              this.customizationAlert.dismiss();
              this.showAlert("Resources downloaded successfully.");
            } catch (dirError) {
              if (result === 0) {
                this.unviredSdk.logInfo("Resource", "downloadResources",'SUCCESS');
                this.customizationAlert.dismiss();
                this.showAlert("Resources downloaded successfully.");
              } else {
                this.unviredSdk.logInfo("Resource", "downloadResources", 'FAILED with code: ' + result);
                this.customizationAlert.dismiss();
                this.showAlert("Failed to extract resources. Please try again.");
              }
            }
          })
          .catch(err => {
            if (this.customizationAlert) {
              this.customizationAlert.dismiss();
            }
            this.file.checkDir(this.file.dataDirectory, "pdf")
              .then(() => {
                this.unviredSdk.logInfo("Resource", "downloadResources", 'Unzip reported error but PDF directory exists');
                this.showAlert("Resources may be available. Please try using them.");
              })
              .catch(() => {
                this.showAlert("Error :" + JSON.stringify(err));
              });
          });
        })
        .catch((err) => {
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          this.showAlert("Error: Downloaded file not found. " + JSON.stringify(err));
        });
    } catch (error) {
      if (this.customizationAlert) {
        this.customizationAlert.dismiss();
      }
      this.showAlert("Error :" + JSON.stringify(error));
      this.customizationAlert.dismiss();
    }
  }

  async showResourceDownloadLoading() {
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: '<div><div class="imageStyle"><img src="./assets/img/blue-hourglass.gif"></div>' + 'Please wait while we configure resources. Do not exit this screen.</div>',
      backdropDismiss: false,
      cssClass: 'waitImage',
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredSdk.logDebug("home", "ionViewWillEnter", " Download alert presented ")
    await this.customizationAlert.onDidDismiss().then((data) => {          
    })
  }

  async showAlert(message) {
    const alert = await this.alertController.create({
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }
}
