import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, NavigationExtras } from '@angular/router';
import { MenuController, AlertController, Platform, ToastController, ActionSheetController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from '../services/alert.service';
import { CameraService } from '../services/camera.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { Camera, CameraOptions } from '@awesome-cordova-plugins/camera/ngx';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faL<PERSON><PERSON><PERSON><PERSON>, faGrip } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from '../services/platform.service';

@Component({
  selector: 'app-abrasion-comparator-external',
  templateUrl: './abrasion-comparator-external.page.html',
  styleUrls: ['./abrasion-comparator-external.page.scss'],
})
export class AbrasionComparatorExternalPage implements OnInit {
  public platformId: string = this.platformService.getPlatformId();
  imageData: any;
  component: any;

  constructor(
    public platformService: PlatformService,
    public helpService: HelpService,
    public translate: TranslateService,
    public device: Device,
    public router: Router,
    private menu: MenuController,
    public domSanitizer: DomSanitizer,
    public screenOrientation: ScreenOrientation, 
    private service: UtilserviceService,
    public cameraService: CameraService,
    public dataService: DataService,
    public alertService: AlertService,
    public alertController: AlertController,
    public platform: Platform,
    public toastController: ToastController,
    public actionSheetController: ActionSheetController,
    private camera: Camera,
    public faIconLibrary : FaIconLibrary) {

    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip)
  }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  gotoHome() {
    this.router.navigate(['home'])
  }

  async gotoInspections() {
    this.dataService.navigateToInspection();
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  back() {
    this.router.navigate(['abrasion-comparator']);
  }

  async takePicture() {
    this.imageData = await this.cameraService.takePictureComparator();
    console.log("Image data:", this.imageData);

    if (this.imageData) {
      const blob = this.cameraService.b64toBlob(this.imageData.split(',')[1], 'image/jpeg');
      const imgURL = URL.createObjectURL(blob);
      this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
    }
   }

  // async presentActionSheet() {
  //   const actionSheet = await this.actionSheetController.create({
  //     cssClass: 'my-custom-class',
  //     buttons: [{
  //       text: 'Camera',
  //       icon: 'camera',
  //       handler: () => {
  //         actionSheet.dismiss(1, 'camera');
  //       }
  //     }, {
  //       text: 'Gallery',
  //       icon: 'image',
  //       handler: () => {
  //         actionSheet.dismiss(2, 'gallery');
  //       }
  //     }]
  //   });
  //   await actionSheet.present();

  //   let dataOnDismissed = await actionSheet.onDidDismiss();
  //   return dataOnDismissed;
  // }

  onFileSelected(event) {
    console.log(event);
    var filepath = URL.createObjectURL(event.target.files[0])
    var mimeType = event.target.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      var message = "Only images are supported.";
      return;
    }
    var reader = new FileReader();
    var imagePath = event.target.files;
    reader.readAsDataURL(event.target.files[0]);
    reader.onload = (_event) => {
      let imgURL = reader.result;
      console.log(imgURL)
      this.imageData = imgURL
    }
  }

  unlockScreen() {
    this.screenOrientation.unlock();
  }

  setImageData() {
    let realData = this.cameraService.getComparator();
    let blob = this.cameraService.b64toBlob(realData, 'image/jpeg');
    var imgURL = URL.createObjectURL(blob);
    this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }


}