import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestResourceDoubleBraidPageRoutingModule } from './guest-resource-double-braid-routing.module';

import { GuestResourceDoubleBraidPage } from './guest-resource-double-braid.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestResourceDoubleBraidPageRoutingModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestResourceDoubleBraidPage]
})
export class GuestResourceDoubleBraidPageModule {}
