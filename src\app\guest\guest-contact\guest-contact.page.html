<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/guest-home" (click)='back()'></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Contact Us' |translate}}</ion-title>
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
          <fa-icon class="icon-style-help"  *ngIf='helpService.helpMode' icon="times"></fa-icon>
          <fa-icon class="icon-style-help"  *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
      </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content padding>
  <div>
    <ion-button expand="full" color="mediumBlack" (click)="contactUsPage()">{{'Contact Us' | translate}}</ion-button>
  </div>
  <div class='contact-details'>
    <h5 style="text-align: center">{{'Customer Service' | translate}}</h5>
    <div style="text-align: center">
      <span><a href="tel:13603844669 ">**************</a> | <a href="tel:18002277673 ">**************
          (ROPE)</a></span><br>
      <span>{{'Fax:' | translate}} <a href="fax:13603840572">**************</a> | <a
          href="fax:18002999246 ">**************</a></span><br>
      <span><a href="mailto:<EMAIL> ">custservsamsonrope.com</a></span>
    </div>
  </div>
</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>

<app-guest-footer *ngIf="footerClose" (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>