import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController } from '@ionic/angular';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { LmdService } from 'src/app/services/lmd.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faList, faSyncAlt, faFileAlt, faBars, faHome, faTh, faEnvelope, faTasks, faTimes } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from 'src/app/services/platform.service';
@Component({
  selector: 'app-line-tracker-home',
  templateUrl: './line-tracker-home.page.html',
  styleUrls: ['./line-tracker-home.page.scss'],
})
export class LineTrackerHomePage implements OnInit {

  selectedAsset: any;
  public platformId: string = this.platformService.getPlatformId();

  constructor(public helpService: HelpService,
    public platformService: PlatformService,
    public dataService: DataService,
    public menu: MenuController,
    private userPreferenceService: UserPreferenceService,
    private router: Router,
    public lmdService: LmdService,
    public device: Device,
    private faIconLibrary:FaIconLibrary ) {
      this.faIconLibrary.addIcons(faList, faSyncAlt, faFileAlt, faBars, faHome, faTh, faEnvelope, faTasks, faTimes)
     }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  async navigateToInventory() {
    await this.initializePreferenceAsset();
  }

  async initializePreferenceAsset() {
    var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    
    if (selectedAsset != undefined && selectedAsset != null && selectedAsset != '' && selectedAsset != '""') {
      selectedAsset = JSON.parse(selectedAsset)
      this.selectedAsset = selectedAsset;
      this.lmdService.setSelectedRPSAssetID(selectedAsset.ID)
      this.lmdService.setSelectedAsset(selectedAsset)
      this.router.navigate(['certificate-list'], { state: { AssetId: selectedAsset.ID } });
    } else {
      this.router.navigate(['/inventory'])
    }
  }

  navigateToDataEntry() {
    this.router.navigate(['data-entry'])
  }

  navigateToReporting() {
    this.router.navigate(['/reporting'])
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }
  gotoResources() {
    this.dataService.navigateToResources()
  }
  gotoContact() {
    this.dataService.navigateToContact()
  }
}
