const ropePatternOverlayImage = 'data:image/png;base64,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';

function capture(success, failure, params) {
  const overlayImg = new Image();
  overlayImg.src = ropePatternOverlayImage;
  let rectObj;
  var HIGHEST_POSSIBLE_Z_INDEX = 99999;
  let preferredWidth = params[0].width;
  let preferredHeight = params[0].height;
  var targetWidth = screen.width / 2;
  var targetHeight = screen.height / 2;
  let widthRatio = targetWidth / preferredWidth;
  let rectRatio = 0;
  console.log("widthRatio:", widthRatio);
  let heightRatio = targetHeight / preferredHeight;
  console.log("heightRatio:", heightRatio);
  // var x = window.matchMedia("(max-width: 500px)");
  currentCamera = 'environment';

  var imgWidth = targetWidth / 3;
  var imgHeight = targetHeight
  if (imgWidth < preferredWidth) {
    imgWidth = targetWidth / 2
  }
  if (imgWidth < preferredWidth) {
    imgWidth = preferredWidth
  }

  var localMediaStream;
  console.log("Width : ", window.innerWidth);
  console.log("Height : ", window.innerHeight);

  console.log("image Height : ", imgHeight);
  console.log("image Width : ", imgWidth);

  var animationFrameId = null;

  const canvasModal = document.createElement('div');
  canvasModal.style.position = 'fixed';
  canvasModal.style.top = '0'
  canvasModal.style.left = '0';
  canvasModal.style.height = '100%'
  canvasModal.style.width = '100%';
  canvasModal.style.backgroundColor = 'rgb(143, 143, 143)';
  canvasModal.style.Zindex = '9999';
  canvasModal.setAttribute('id', 'cameraModal')

  // Create the flex container
  const flexContainer = document.createElement('div');
  canvasModal.appendChild(flexContainer);
  flexContainer.id = 'flex-container';
  flexContainer.style.display = 'flex';
  flexContainer.style.backgroundColor = 'black';
  flexContainer.style.flexDirection = 'column';
  flexContainer.style.position = 'absolute';
  flexContainer.style.left = '50%';
  flexContainer.style.top = '50%';
  flexContainer.style.borderRadius = '10px';
  flexContainer.style.transform = 'translate(-50%, -50%)';

  const rowDiv1 = document.createElement('div');
  rowDiv1.style.height = '50px';
  rowDiv1.style.display = 'flex';
  rowDiv1.style.justifyContent = 'flex-end'; // Align buttons to the end
  const rowDiv2 = document.createElement('div');

  var video = document.createElement("video");
  var canvas = document.createElement("canvas");
  rowDiv2.appendChild(canvas);
  // video.width = targetWidth;
  canvas.width = targetWidth;
  canvas.height = targetHeight;
  canvas.objectFit = 'cover';


  var captureButton = document.createElement("button")
  captureButton.style.cursor = 'pointer';
  captureButton.style.height = '60px';
  captureButton.style.width = '60px';
  captureButton.style.borderRadius = '30px';
  captureButton.style.marginLeft = '5em';
  captureButton.style.marginBottom = '5px';
  captureButton.style.backgroundColor = '#ffffff';
  captureButton.style.border = '5px solid grey';
  captureButton.style.position = 'relative';

  var cancelButton = document.createElement("button");
  cancelButton.style.height = '25px';
  cancelButton.style.margin = '10px';
  cancelButton.innerHTML = '&#10005';
  cancelButton.style.color = '#ffffff';
  cancelButton.style.cursor = 'pointer';
  cancelButton.style.height = '25px';
  cancelButton.style.width = '25px';
  cancelButton.style.fontSize = '19px';
  cancelButton.style.backgroundColor = 'transparent';
  cancelButton.style.background = 'transparent';
  cancelButton.style.borderStyle = 'none';
  // cancelButton.style.color = 'white';

  var increaseRectBtn = document.createElement("button");
  increaseRectBtn.id = 'iRectBtn';
  increaseRectBtn.style.height = '25px';
  increaseRectBtn.style.margin = '10px';
  increaseRectBtn.innerHTML = '&plus;';
  increaseRectBtn.style.color = '#ffffff';
  increaseRectBtn.style.cursor = 'pointer';
  increaseRectBtn.style.height = '25px';
  increaseRectBtn.style.width = '25px';
  increaseRectBtn.style.fontSize = '19px';
  increaseRectBtn.style.backgroundColor = 'transparent';
  increaseRectBtn.style.background = 'transparent';
  // increaseRectBtn.style.borderStyle = 'none';
  increaseRectBtn.style.border = 'solid 1px white';
  increaseRectBtn.style.borderRadius = '5px';
  increaseRectBtn.title = 'Increase overlay size';


  var decreaseRectBtn = document.createElement("button");
  decreaseRectBtn.id = 'dRectBtn';
  decreaseRectBtn.style.height = '25px';
  decreaseRectBtn.style.margin = '10px';
  decreaseRectBtn.innerHTML = '&minus;';
  decreaseRectBtn.style.color = '#ffffff';
  decreaseRectBtn.style.cursor = 'pointer';
  decreaseRectBtn.style.height = '25px';
  decreaseRectBtn.style.width = '25px';
  decreaseRectBtn.style.fontSize = '19px';
  decreaseRectBtn.style.backgroundColor = 'transparent';
  decreaseRectBtn.style.background = 'transparent';
  decreaseRectBtn.style.border = 'solid 1px white';
  decreaseRectBtn.style.borderRadius = '5px';
  decreaseRectBtn.title = 'Decrease overlay size';

  var switchCamButton = document.createElement("button")
  switchCamButton.style.cursor = 'pointer';
  switchCamButton.style.height = '40px';
  switchCamButton.style.width = '40px';
  switchCamButton.style.borderRadius = '30px';
  switchCamButton.style.marginRight = '5px';
  switchCamButton.innerHTML = `<svg class="svg-icon" viewBox="0 0 20 20">
          <path d="M12.319,5.792L8.836,2.328C8.589,2.08,8.269,2.295,8.269,2.573v1.534C8.115,4.091,7.937,4.084,7.783,4.084c-2.592,0-4.7,2.097-4.7,4.676c0,1.749,0.968,3.337,2.528,4.146c0.352,0.194,0.651-0.257,0.424-0.529c-0.415-0.492-0.643-1.118-0.643-1.762c0-1.514,1.261-2.747,2.787-2.747c0.029,0,0.06,0,0.09,0.002v1.632c0,0.335,0.378,0.435,0.568,0.245l3.483-3.464C12.455,6.147,12.455,5.928,12.319,5.792 M8.938,8.67V7.554c0-0.411-0.528-0.377-0.781-0.377c-1.906,0-3.457,1.542-3.457,3.438c0,0.271,0.033,0.542,0.097,0.805C4.149,10.7,3.775,9.762,3.775,8.76c0-2.197,1.798-3.985,4.008-3.985c0.251,0,0.501,0.023,0.744,0.069c0.212,0.039,0.412-0.124,0.412-0.34v-1.1l2.646,2.633L8.938,8.67z M14.389,7.107c-0.34-0.18-0.662,0.244-0.424,0.529c0.416,0.493,0.644,1.118,0.644,1.762c0,1.515-1.272,2.747-2.798,2.747c-0.029,0-0.061,0-0.089-0.002v-1.631c0-0.354-0.382-0.419-0.558-0.246l-3.482,3.465c-0.136,0.136-0.136,0.355,0,0.49l3.482,3.465c0.189,0.186,0.568,0.096,0.568-0.245v-1.533c0.153,0.016,0.331,0.022,0.484,0.022c2.592,0,4.7-2.098,4.7-4.677C16.917,9.506,15.948,7.917,14.389,7.107 M12.217,15.238c-0.251,0-0.501-0.022-0.743-0.069c-0.212-0.039-0.411,0.125-0.411,0.341v1.101l-2.646-2.634l2.646-2.633v1.116c0,0.174,0.126,0.318,0.295,0.343c0.158,0.024,0.318,0.034,0.486,0.034c1.905,0,3.456-1.542,3.456-3.438c0-0.271-0.032-0.541-0.097-0.804c0.648,0.719,1.022,1.659,1.022,2.66C16.226,13.451,14.428,15.238,12.217,15.238"></path>
      </svg>`;
  switchCamButton.style.fontSize = '20px';
  switchCamButton.style.lineHeight = '0';
  switchCamButton.style.alignSelf = 'center';
  switchCamButton.style.backgroundColor = '#ffffff';

  // Create note (first row)
  let note = document.createElement('p');
  note.style.fontSize = '20px';
  // note.style.color = 'yellow'
  note.innerHTML = "Orient Rope to Pattern Overlay";
  note.style.color = 'white';
  note.style.textAlign = 'center';
  note.style.gridColumn = "1 / span 2"; // Centered across both columns
  note.style.marginTop = '5px'

  // Create the third row div
  const rowDiv3 = document.createElement('div');
  rowDiv3.style.display = 'grid';
  rowDiv3.style.gridTemplateRows = '40px 70px';
  rowDiv3.style.gridTemplateColumns = '90% 10%'; // Capture button (90%), Switch button (10%)
  // rowDiv3.style.gridTemplateRows = '1fr 1fr'; // Two rows

  // Style capture button (center it)
  captureButton.style.justifySelf = "center";

  // Style switch button (align it to the right)
  switchCamButton.style.justifySelf = "end";

  // Append elements
  rowDiv3.appendChild(note); // First row (spans both columns)
  rowDiv3.appendChild(captureButton); // Second row, left (centered)
  rowDiv3.appendChild(switchCamButton); // Second row, right (aligned right)
  // Create a new style element
  var styleElement = document.createElement('style');

  // Set the inner text of the style element to your CSS code
  styleElement.innerText = `
      .svg-icon {
      width: 1em;
      height: 1em;
      }
  
      .svg-icon path,
      .svg-icon polygon,
      .svg-icon rect {
      fill: #000000;
      }
  
      .svg-icon circle {
      stroke: #000000;
      stroke-width: 1;
      }`;

  // Append the style element to the head of the document
  document.head.appendChild(styleElement);

  rowDiv1.appendChild(decreaseRectBtn);// Append cancelButton to rowDiv1
  rowDiv1.appendChild(increaseRectBtn);
  rowDiv1.appendChild(cancelButton);

  rowDiv3.style.justifyContent = 'center';
  rowDiv3.style.justifyItems = 'center';

  flexContainer.appendChild(rowDiv1);
  flexContainer.appendChild(rowDiv2);
  flexContainer.appendChild(rowDiv3);
  // Stop drawing updates
  function stopDrawing() {
    cancelAnimationFrame(animationFrameId);
  }

  // function drawVideoOnCanvas() {
  //   // canvas.height= video.videoHeight;
  //   // canvas.width= video.videoWidth;
  //   var ctx = canvas.getContext("2d");
  //   ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear the canvas
  //   ctx.fillStyle = 'white';
  //   // 1. Draw the full unblurred video onto the canvas
  //   ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

  //   // 2. Calculate rectangle position
  //   const rectX = ((targetWidth - imgWidth) / 2) - (rectRatio / 4);
  //   const rectY = (targetHeight - imgHeight) / 2;
  //   const rectWidth = imgWidth + (rectRatio / 2);
  //   const rectHeight = imgHeight;
  //   // 3. Create a blurred copy of the canvas
  //   const tempCanvas = document.createElement('canvas');
  //   tempCanvas.width = canvas.width;
  //   tempCanvas.height = canvas.height;
  //   const tempCtx = tempCanvas.getContext('2d');
  //   tempCtx.drawImage(canvas, 0, 0); // Copy the current canvas state (full video)

  //   ctx.globalAlpha = 0.9;
  //   ctx.filter = 'blur(15px)';
  //   ctx.filter = 'none';
  //   ctx.globalAlpha = 1;

  //   tempCtx.globalAlpha = 0.9;
  //   tempCtx.filter = 'blur(1px)';
  //   tempCtx.fillRect(0, 0, canvas.width, canvas.height); // Apply blur to the entire temp canvas
  //   tempCtx.filter = 'none';
  //   tempCtx.globalAlpha = 1;

  //   // 4. Clear the rectangle on the blurred copy
  //   tempCtx.clearRect(rectX, rectY, rectWidth, rectHeight);

  //   // 5. Draw the blurred copy back onto the original canvas

  //   // 6. Draw the rectangle border
  //   ctx.strokeStyle = 'yellow';
  //   ctx.lineWidth = 4;
  //   // ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);
  //   ctx.drawImage(tempCanvas, 0, 0);

  //   // Draw the rectangle
  //   // ctx.strokeStyle = "yellow";
  //   // ctx.lineWidth = 2;
  //   // ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);

  //   // Calculate tile size so that only **one vertical strand** fits
  //   const w = rectWidth; // Pattern width should match rectangle with
  //   const h = w / 2; // Keep herringbone shape
  //   const h2 = h / 2;
  //   ctx.strokeStyle = "#faf555";

  //   // Fix: Align the pattern **precisely** to the left edge
  //   let x = rectX; // Start exactly from the left edge
  //   // Draw a **single vertical strand** of the herringbone pattern
  //   for (let y = rectY; y <= rectY + rectHeight; y += h) {
  //     // Left tile
  //     ctx.beginPath();
  //     ctx.moveTo(x, y);  // Aligned to the rectangle's left edge
  //     ctx.lineTo(x + h2, y - h2);
  //     ctx.lineTo(x + h, y);
  //     ctx.lineTo(x + h2, y + h2);
  //     ctx.closePath();
  //     ctx.stroke();

  //     // Right tile
  //     ctx.beginPath();
  //     ctx.moveTo(x + h2, y + h2);
  //     ctx.lineTo(x + w - h2, y - h2);
  //     ctx.lineTo(x + w, y);
  //     ctx.lineTo(x + h, y + h);
  //     ctx.closePath();
  //     ctx.stroke();
  //   }

  //   rectObj = {
  //     x: ((targetWidth - rectRatio / 2) - imgWidth) / 2,
  //     y: (targetHeight - imgHeight) / 2,
  //     height: imgWidth + (rectRatio / 2),
  //     width: imgHeight
  //   }

  //   // ctx.lineWidth = 4;
  //   // console.log("rectObject live:", rectObj)
  //   ctx.strokeRect(rectObj.x, rectObj.y, rectObj.height, rectObj.width);
  // }


  function drawVideoOnCanvas() {
    // canvas.height= video.videoHeight;
    // canvas.width= video.videoWidth;
    var ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear the canvas
    ctx.fillStyle = 'white';
    // 1. Draw the full unblurred video onto the canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // 2. Calculate rectangle position
    const rectX = ((targetWidth - imgWidth) / 2) - (rectRatio / 4);
    const rectY = (targetHeight - imgHeight) / 2;
    const rectWidth = imgWidth + (rectRatio / 2);
    const rectHeight = imgHeight;

    // 3. Create a blurred copy of the canvas
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.drawImage(canvas, 0, 0); // Copy the current canvas state (full video)

    ctx.globalAlpha = 0.9;
    ctx.filter = 'blur(15px)';
    ctx.filter = 'none';
    ctx.globalAlpha = 1;

    tempCtx.globalAlpha = 0.9;
    tempCtx.filter = 'blur(1px)';
    tempCtx.fillRect(0, 0, canvas.width, canvas.height); // Apply blur to the entire temp canvas
    tempCtx.filter = 'none';
    tempCtx.globalAlpha = 1;

    // 4. Clear the rectangle on the blurred copy
    tempCtx.clearRect(rectX, rectY, rectWidth, rectHeight);

    // 5. Draw the blurred copy back onto the original canvas
    ctx.drawImage(tempCanvas, 0, 0);

    // 6. Draw the rectangle border
    ctx.strokeStyle = 'yellow';
    ctx.lineWidth = 4;
    ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);

    // img.onload = function () {
    // ctx.drawImage(overlayImg, rectX, rectY, rectWidth, rectHeight); // Place inside the rectangle
    // ctx.strokeRect(rectObj.x, rectObj.y, rectObj.height, rectObj.width);
    // };

    ctx.globalAlpha = 0.4;  // Set opacity (0 = fully transparent, 1 = fully opaque)
    ctx.drawImage(overlayImg, rectX, rectY, rectWidth, rectHeight);
    ctx.globalAlpha = 1;

    // Define rectangle object
    rectObj = {
      x: ((targetWidth - rectRatio / 2) - imgWidth) / 2,
      y: (targetHeight - imgHeight) / 2,
      height: imgWidth + (rectRatio / 2),
      width: imgHeight
    }
    // console.log("rectObj.x + rectObj.width",rectObj.x+','+ rectObj.width)
    // console.log("canvas.width",canvas.width)
    if (rectObj.height <= canvas.width) {
        ctx.strokeRect(rectObj.x, rectObj.y, rectObj.height, rectObj.width);
    }
    // ctx.strokeRect(rectObj.x, rectObj.y, rectObj.height, rectObj.width);

  }

  switchCamButton.onclick = function () {
    if (currentCamera === 'user') {
      currentCamera = 'environment'; // Switch to back camera
    } else {
      currentCamera = 'user'; // Switch to front camera
    }

    // Stop the current stream (if any)
    localMediaStream.getTracks().forEach(function (track) {
      track.stop();
    });

    previewcamera();
  }

  // Call the draw function repeatedly to update the canvas
  function animate() {
    drawVideoOnCanvas();
    animationFrameId = requestAnimationFrame(animate);
  }
  animate();

  //   old working login start
  captureButton.onclick = function () {
    // Get the actual video and canvas dimensions
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    // Calculate aspect ratios and scaling factors
    const scaleX = videoWidth / canvasWidth;
    const scaleY = videoHeight / canvasHeight;

    console.log("Video Dimensions:", videoWidth, videoHeight);
    console.log("Canvas Dimensions:", canvasWidth, canvasHeight);
    console.log("Scaling Factors:", scaleX, scaleY);

    // Convert rectObj to match video resolution
    const videoRect = {
      x: rectObj.x * scaleX,
      y: rectObj.y * scaleY,
      width: rectObj.height * scaleX,
      height: rectObj.width * scaleY
    };

    console.log("Corrected Video Crop Area:", videoRect);

    // Create a cropped canvas that matches the video resolution
    let croppedCanvas = document.createElement("canvas");
    croppedCanvas.width = videoWidth;  // Match video width
    croppedCanvas.height = videoHeight; // Match video height
    let croppedCtx = croppedCanvas.getContext("2d");

    console.log("Upscaled Cropped Canvas Dimensions W x H:", croppedCanvas.width, croppedCanvas.height);

    // Fill croppedCanvas with the full video frame (to match video resolution)
    croppedCtx.drawImage(video, 0, 0, videoWidth, videoHeight);

    // Create another canvas for final cropping
    let finalCanvas = document.createElement("canvas");
    finalCanvas.width = videoRect.width;
    finalCanvas.height = videoRect.height;
    let finalCtx = finalCanvas.getContext("2d");

    // Crop the correct portion from the upscaled canvas
    finalCtx.drawImage(
      croppedCanvas,
      videoRect.x, videoRect.y, videoRect.width, videoRect.height, // Source (video)
      0, 0, videoRect.width, videoRect.height // Destination (cropped output)
    );

    console.log("Final Cropped Image Dimensions W x H:", finalCanvas.width, finalCanvas.height);

    // Convert to image data
    let imageData = finalCanvas.toDataURL("image/png");
    imageData = imageData.replace("data:image/png;base64,", "");

    // Hide modal and show the cropped image
    if (imageData) {
      canvasModal.style.visibility = 'hidden';
      showPreviewModal(imageData, success, failure, params, capture);
    }
  };

  cancelButton.onclick = function () {
    // styleElement.remove();
    // document.head.appendChild(styleElement);
    // canvasModal.style.display = 'none';
    // canvasModal.remove()
    stopDrawing();
    console.clear();
    cleanup();
  };

  increaseRectBtn.onclick = function () {
    if (rectObj.height < canvas.width || rectRatio == 0) {
      rectRatio += Math.min(10, canvas.width - rectObj.height);
    }
  }

  decreaseRectBtn.onclick = function () {
    if((rectObj.height-10) > preferredWidth) {
      rectRatio -= 10;
    }
  }

  function cleanup() {
    let camModal = document.getElementById('cameraModal');
    video.srcObject = null;
    video.remove()
    camModal.remove();
    styleElement.remove();
  }


  function showPreviewModal(imageData, success, failure, params, capture) {
    let modal = document.createElement("div");
    modal.id = "previewModal";
    modal.style.position = "fixed";
    modal.style.top = "0";
    modal.style.left = "0";
    modal.style.width = "100vw";
    modal.style.height = "100vh";
    modal.style.backgroundColor = 'rgb(143, 143, 143)';
    modal.style.display = "flex";
    modal.style.flexDirection = "column";
    modal.style.justifyContent = "center";
    modal.style.alignItems = "center";

    let container = document.createElement("div");
    container.style.display = "flex";
    container.style.flexDirection = "column";
    container.style.alignItems = "center";
    container.style.background = "transparent";
    container.style.padding = "10px";
    container.style.borderRadius = "10px";

    let img = document.createElement("img");
    img.src = "data:image/jpeg;base64," + imageData;
    img.style.maxWidth = "80vw";
    img.style.maxHeight = "70vh";
    img.style.borderRadius = "10px";

    let buttonBar = document.createElement("div");
    buttonBar.style.display = "flex";
    buttonBar.style.justifyContent = "space-between";
    buttonBar.style.width = "100%";
    buttonBar.style.marginTop = "10px";

    let retakeBtn = document.createElement("button");
    retakeBtn.innerText = "Retake";
    retakeBtn.style.borderRadius = "10px";
    retakeBtn.style.padding = "10px 20px";
    retakeBtn.style.marginRight = "10px";
    retakeBtn.style.cursor = "pointer";
    retakeBtn.style.backgroundColor = "black";
    retakeBtn.onclick = function () {
      document.body.removeChild(modal);
      canvasModal.style.visibility = 'visible'
      // animate()
      // capture(success, failure, params);
    };
    let acceptBtn = document.createElement("button");
    acceptBtn.innerText = "Use Photo";
    acceptBtn.style.borderRadius = "10px";
    acceptBtn.style.padding = "10px 20px";
    acceptBtn.style.cursor = "pointer";
    acceptBtn.style.backgroundColor = "black";
    acceptBtn.onclick = function () {
      document.body.removeChild(modal);
      let res = {
        "ImageData": img.src,
        "GuidelineBoxWidth": img.width,
        "GuidelineBoxHeight": img.height
      };
      if (localMediaStream.stop) {
        localMediaStream.stop();
      } else {
        localMediaStream.getTracks().forEach(track => track.stop());
      }

      // localMediaStream.getTracks().forEach(function (track) {
      //   track.stop();
      // });
      stopDrawing();
      cleanup();
      success(res);
    };

    buttonBar.appendChild(retakeBtn);
    buttonBar.appendChild(acceptBtn);
    container.appendChild(img);
    container.appendChild(buttonBar);
    modal.appendChild(container);
    document.body.appendChild(modal);
  }


  navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia || navigator.mediaDevices.getUserMedia;

  var successCallback = function (stream) {
    localMediaStream = stream;
    video.onloadedmetadata = () => {
      console.log("Camera Resolution:", video.videoWidth, "x", video.videoHeight);
    };
    if ("srcObject" in video) {
      video.srcObject = localMediaStream;
    } else {
      video.src = window.URL.createObjectURL(localMediaStream);
    }
    video.width = '100%';
    video.height = 'auto';
    video.play();
    canvasModal.style.zIndex = HIGHEST_POSSIBLE_Z_INDEX;
    document.body.appendChild(canvasModal);
  };

  var errorCallback = (error) => {
    console.log("camera inialise error:",error);
    failure(error)
  }


  function previewcamera() {
    if (navigator.getUserMedia) {

      navigator.getUserMedia({
        video: {
          facingMode: currentCamera,
          width: { min: 640, ideal: 1280, max: 1920 },
          height: { min: 480, ideal: 720, max: 1080 },
          aspectRatio: 16 / 9
        }
      },
        successCallback,
        errorCallback
      );
    } else {
      return error("camera devices not found");
    }
  }
  previewcamera();
}