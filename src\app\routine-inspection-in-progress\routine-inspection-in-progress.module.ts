import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RoutineInspectionInProgressPageRoutingModule } from './routine-inspection-in-progress-routing.module';

import { RoutineInspectionInProgressPage } from './routine-inspection-in-progress.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RoutineInspectionInProgressPageRoutingModule,
    FontAwesomeModule,
    FooterComponent
  ],
  declarations: [RoutineInspectionInProgressPage]
})
export class RoutineInspectionInProgressPageModule {}
