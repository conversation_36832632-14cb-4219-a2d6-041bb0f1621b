<ion-footer class="footer-style">
  <ion-grid style="text-align:center;">
    <ion-row>
      <ion-col>
        <div (click)="Menu.emit()">
          <fa-icon class="icon-style" icon="bars"></fa-icon>
        </div>
      </ion-col>
      <!-- <ion-col>
        <div (click)="dataService.navigateToHome()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style"  icon="home"></fa-icon>
        </div>
      </ion-col> -->
      <ion-col>
        <div (click)="LineTracker.emit()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="list-check"></fa-icon>
        </div>
      </ion-col>
      <ion-col>
        <div (click)="Inspections.emit()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/icon/Rope_Inspection_ICON_3A.png" class="bottom-bar-image-style fa-fw">
          <!-- <fa-icon class="icon-style" icon="search"></fa-icon> -->
        </div>
      </ion-col>
      <ion-col>
        <div (click)="Resources.emit()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="grip"></fa-icon>
        </div>
      </ion-col>
      <ion-col (click)="Contact.emit()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
        <!-- <div 
          > -->
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="envelope" class="icon-style"></fa-icon>
        <!-- </div> -->
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-footer>