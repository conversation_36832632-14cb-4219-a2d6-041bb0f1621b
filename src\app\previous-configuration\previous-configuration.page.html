<ion-header>
  <ion-toolbar>
    <ion-title>Previous Configurations</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <p style="padding: 15px; margin-bottom:0px;">Select Configuration to use</p>

  <ion-card>
    <ion-card-content style="padding: 0px;">
      <ion-list lines="none">
        <div *ngFor="let item of configurationList ; let i = index">

          <ion-item>
            <ion-label style="font-size:16px;">
              <span>{{item.CONFIG_NAME}}: {{item.CREATED_DATE}} </span>
            </ion-label>

            <ion-buttons (click)="expandedItem(i, item)">
              <ion-button fill="outline" style="text-transform: none;" color="darkMedium" slot="end" mode="md"
                (click)="confirmCopy(i)">
                &nbsp; {{'Copy' | translate}} &nbsp;
              </ion-button>
              <ion-button style="padding-bottom: 8px;">
                <ion-icon slot="icon-only" slot="end" [name]="checkSectionIsOpen(i) ? 'chevron-up-outline' :'chevron-down-outline'"
                  style="color:rgb(41, 40, 40)"></ion-icon>
              </ion-button>
            </ion-buttons>
          </ion-item>
          <ion-item *ngIf="checkSectionIsOpen(i)" lines="none"
            style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829);">
            <div>
              <ion-item *ngFor="let observation of getArray(i); let x = index">
                <ion-label style="font-size:16px;" *ngIf="(observation.DATA.type == 'Other')">
                  <span>{{observation.DATA.type}}: {{observation.DATA.otherData.otherDescription}}</span><br />
                  <span>{{observation.DATA.start}} - {{observation.ending}} <span>{{item.LENGTH_UOM}}</span> </span>
                </ion-label>
                <ion-label style="font-size:16px;" *ngIf="(observation.DATA.type == 'End')">
                  <span>{{observation.DATA.type}}: {{observation.DATA.otherData.endType}}</span><br />
                  <span>{{observation.DATA.otherData.measurementLength}} <span>{{item.LENGTH_UOM}}</span> </span>
                </ion-label>
                <ion-label style="font-size:16px;"
                  *ngIf="(observation.DATA.type == 'Splice' && (observation.ending != undefined && observation.ending != ''))">
                  <span>{{observation.DATA.type}}: {{observation.DATA.otherData.spliceType}}</span><br />
                  <span>{{observation.DATA.start}} - {{observation.ending}} <span>{{item.LENGTH_UOM}}</span></span>
                </ion-label>
                <ion-label style="font-size:16px;"
                  *ngIf="(observation.DATA.type == 'Splice' && (observation.ending == undefined || observation.ending == ''))">
                  <span>{{observation.DATA.type}}: {{observation.DATA.otherData.spliceType}}</span><br />
                  <span>{{observation.DATA.start}} - {{observation.ending}} <span>{{item.LENGTH_UOM}}</span> </span>
                </ion-label>
                <ion-label style="font-size:16px;"
                  *ngIf="(observation.DATA.type == 'Chafe' && (observation.ending != undefined && observation.ending != ''))">
                  <span>{{observation.DATA.type}}: {{observation.DATA.otherData.chafeType}}</span><br />
                  <span>{{observation.DATA.start}} - {{observation.ending}} <span>{{item.LENGTH_UOM}}</span> </span>
                </ion-label>
                <ion-label style="font-size:16px;"
                  *ngIf="(observation.DATA.type == 'Chafe' && (observation.ending == undefined || observation.ending == ''))">
                  <span>{{observation.DATA.type}}: {{observation.DATA.otherData.chafeType}} </span><br />
                  <span>{{observation.DATA.otherData.measurementLength}} <span>{{item.LENGTH_UOM}}</span> </span>
                </ion-label>
              </ion-item>
            </div>
          </ion-item>
        </div>
      </ion-list>
    </ion-card-content>
  </ion-card>

</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>