import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestResourceInternalPageRoutingModule } from './guest-resource-internal-routing.module';

import { GuestResourceInternalPage } from './guest-resource-internal.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestResourceInternalPageRoutingModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestResourceInternalPage]
})
export class GuestResourceInternalPageModule {}
