import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { HelpService } from 'src/app/services/help.service';

@Component({
  selector: 'app-guest-resource-external',
  templateUrl: './guest-resource-external.page.html',
  styleUrls: ['./guest-resource-external.page.scss'],
})
export class GuestResourceExternalPage implements OnInit {

  constructor(private menu: MenuController, public helpService: HelpService,
    private router: Router,
    public alertController: AlertController,
    public translate: TranslateService) { }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  gotoHome() {
    this.router.navigate(['guest-home']);
  }

  async gotoInspections() {     
    const alert = await this.alertController.create({
      message: this.translate.instant("GUEST_MESSAGE"),
      buttons: ['OK']
    });
    await alert.present();
  }

  gotoResources() {
      this.router.navigate(['guest-resource']);
  }

  gotoContact() {
      this.router.navigate(['guest-contact']);
  }


}
