import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AssetActivityPageRoutingModule } from './asset-activity-routing.module';

import { AssetActivityPage } from './asset-activity.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from 'src/app/components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    AssetActivityPageRoutingModule,
    FontAwesomeModule, TranslateModule,
    FooterComponent
  ],
  declarations: [AssetActivityPage],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA]
})
export class AssetActivityPageModule {}
