<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color:black;"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
    <ion-title>{{'Initial Condition' | translate}}</ion-title>
    <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
      <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
      <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid style="padding-left:15px;padding-top:20px;">
    <ion-row>
      <p style="font-size:14px;padding-top:5px;padding-left:5px;">{{'Which end will you be starting from?' |
        translate}}</p>
    </ion-row>
    <ion-row *ngIf='helpService.helpMode' style="padding-right:17px;padding-left:2px;"
      tooltip="{{'Select the starting end of the rope'|translate}}" positionV="bottom" [arrow]="helpService.showArrow"
      [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers mode="ios">
      <ion-segment [disabled]='helpService.helpMode' style="float:left" [(ngModel)]="segment"
        (ionChange)="segmentChanged($event)" style="padding-bottom:2px" mode="ios" expand="block">
        <ion-segment-button value="0" mode="ios">
          <ion-label>{{'A'| translate}}</ion-label>
        </ion-segment-button>
        <ion-segment-button value="10" mode="ios">
          <ion-label>{{'B' | translate}}</ion-label>
        </ion-segment-button>
        <ion-segment-button value='?' mode="ios">
          <ion-label>?</ion-label>
        </ion-segment-button>
      </ion-segment>
      <input style="margin-top:5px;" type="text" *ngIf="mode" [(ngModel)]="manualValue" maxlength="2"
        class="form-control">
    </ion-row>

    <ion-row *ngIf='!helpService.helpMode' style="padding-right:17px;padding-left:2px;">
      <ion-segment [disabled]='helpService.helpMode' style="float:left" [(ngModel)]="segment"
        (ionChange)="segmentChanged($event)" style="padding-bottom:2px" mode="ios" expand="block">
        <ion-segment-button value="0" mode="ios">
          <ion-label>{{'A'| translate}}</ion-label>
        </ion-segment-button>
        <ion-segment-button value="10" mode="ios">
          <ion-label>{{'B' | translate}}</ion-label>
        </ion-segment-button>
        <ion-segment-button value='?' mode="ios">
          <ion-label>?</ion-label>
        </ion-segment-button>
      </ion-segment>
      <input style="margin-top:5px;" type="text" *ngIf="mode" [(ngModel)]="manualValue" maxlength="2"
        class="form-control">
    </ion-row>

    <div *ngIf="product != 'AMSTEEL-BLUE' && product !='K ™ 100'" style="margin-right:15px;margin-top:10px;">
      <div *ngIf='helpService.helpMode' class="form-group" tooltip="{{'Select the severity level'|translate}}"
        positionV="bottom" positionH="right" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select external severity level' | translate}} :
        </ion-label>
       <ion-select labelPlacement="stacked"  interface="popover" [(ngModel)]="extDamageType" [disabled]='helpService.helpMode'
          style="height:44px;padding-bottom:10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
          placeholder="{{'Select severity level' | translate}}" (ionChange)="setDirtyFlag()">
          <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
          <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
          <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
          <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
        </ion-select>
      </div>

      <div *ngIf='!helpService.helpMode' class="form-group">
        <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select external severity level' | translate}} :
        </ion-label>
       <ion-select labelPlacement="stacked"  interface="popover" [(ngModel)]="extDamageType" [disabled]='helpService.helpMode'
          style="height:44px;padding-bottom:10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
          placeholder="{{'Select severity level' | translate}}" (ionChange)="setDirtyFlag()">
          <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
          <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
          <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
          <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
        </ion-select>
      </div>
    </div>

    <ion-row *ngIf="product == 'AMSTEEL-BLUE' || product =='K ™ 100'">
      <ion-col size="5">
        <p style="padding-top:30px;font-size:14px;">{{'External Rating' | translate}} &nbsp;(<span
            style="color:rgb(10, 90, 128);padding-left:1px;padding-right:1px;">{{externalRange}}</span>)</p>
      </ion-col>
      <ion-col *ngIf='helpService.helpMode' size="7" tooltip="{{'Select the external range of the rope'|translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>

        <ion-range [disabled]='helpService.helpMode' [(ngModel)]="externalRange" min="0" max={{extMaxRating}}
          color="secondary" pin="true" mode="ios" expand="block" (ionChange)="setDirtyFlag()">
          <ion-label slot="start">{{'0'|translate}}</ion-label>
          <ion-label slot="end">{{extMaxRating}}</ion-label>&nbsp;&nbsp;
        </ion-range>
      </ion-col>

      <ion-col *ngIf='!helpService.helpMode' size="7">
        <ion-range [disabled]='helpService.helpMode' [(ngModel)]="externalRange" min="0" max={{extMaxRating}}
          color="secondary" pin="true" mode="ios" expand="block" (ionChange)="setDirtyFlag()">
          <ion-label slot="start">{{'0'|translate}}</ion-label>
          <ion-label slot="end">{{extMaxRating}}</ion-label>&nbsp;&nbsp;
        </ion-range>
      </ion-col>
    </ion-row>
    <ion-row style="padding: 10px 17px 10px 17px;">
      <div class="img-wrap" *ngFor="let item of cameraService.baselineExternal; let i = index"
        style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
        <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:cover;border-radius: 5px;"
          (click)="cameraService.editImage(item.Image, i, 'external', 'baseline-ext', that, extMaxRating, externalRange, selectedProduct); setDirtyFlag()" />
        <ion-icon *ngIf="item.Image!='./assets/img/samson2.png'" name="trash" class="close" slot="end" mode="md" color="danger"
          (click)="DeleteExternalImage(i)"></ion-icon>
      </div>
      <div
        style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
        <img *ngIf='helpService.helpMode' tooltip="{{'Take a photo of rope external initial condition'|translate}}"
          positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
          [duration]="helpService.duration" [hideOthers]=helpService.hideOthers src="assets/img/addImage.png"
          (click)="helpService.helpMode ? test() : externalPicture(); setDirtyFlag() "
          style="object-fit:contain;border-radius: 5px;padding-top:52px" />
        <img *ngIf='!helpService.helpMode' src="assets/img/addImage.png"
          (click)="helpService.helpMode ? test() : externalPicture(); setDirtyFlag() "
          style="object-fit:contain;border-radius: 5px;padding-top:52px" />
      </div>
    </ion-row>

    <div *ngIf="product != 'AMSTEEL-BLUE' && product !='K ™ 100'" style="margin-right:15px;margin-top:10px;">
      <div *ngIf='helpService.helpMode' class="form-group" tooltip="{{'Select the severity level'|translate}}"
        positionV="bottom" positionH="right" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select internal severity level' | translate}} :
        </ion-label>
       <ion-select labelPlacement="stacked"  interface="popover" [(ngModel)]="intDamageType" [disabled]='helpService.helpMode'
          style="height:44px;padding-bottom:10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
          placeholder="{{'Select severity level' | translate}}" (ionChange)="setDirtyFlag()">
          <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
          <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
          <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
          <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
        </ion-select>
      </div>

      <div *ngIf='!helpService.helpMode' class="form-group">
        <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select internal severity level' | translate}} :
        </ion-label>
       <ion-select labelPlacement="stacked"  interface="popover" [(ngModel)]="intDamageType" [disabled]='helpService.helpMode'
          style="height:44px;padding-bottom:10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
          placeholder="{{'Select severity level' | translate}}" (ionChange)="setDirtyFlag()">
          <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
          <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
          <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
          <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
        </ion-select>
      </div>
    </div>
    <ion-row *ngIf="product == 'AMSTEEL-BLUE' || product =='K ™ 100'">
      <ion-col size="5">
        <p style="padding-top:30px;font-size:14px;">{{'Internal Rating' | translate}} &nbsp;(<span
            style="color:rgb(10, 90, 128);padding-left:1px;padding-right:1px;">{{internalRange}}</span>)</p>
      </ion-col>
      <ion-col *ngIf='helpService.helpMode' size="7" tooltip="{{'Select the internal range of the rope'|translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <!-- <ion-range (ionChange)="internalRangeChanged($event)" min="0" max="5" snaps="true" color="primary" mode="md">
                    <ion-label slot="start"></ion-label>
                    <ion-label slot="end" style="font-size:13px;">{{internalRange}}</ion-label>
                    &nbsp; &nbsp;&nbsp;
                    <ion-icon name="camera" slot="end" color="dark" mode="md" (click)="internalPicture()"></ion-icon>
                  </ion-range> -->
        <ion-range *ngIf="product == 'AMSTEEL-BLUE'" [disabled]='helpService.helpMode' [(ngModel)]="internalRange"
          min="0" max={{intMaxRating}} color="secondary" pin="true" mode="ios" expand="block"
          (ionChange)="setDirtyFlag()">
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{intMaxRating}}</ion-label>
          <!-- <ion-icon name="camera" slot="end" color="dark" mode="md" (click)="helpService.helpMode ? test() : internalPicture()"></ion-icon> -->
        </ion-range>
        <ion-range *ngIf="product =='K ™ 100'" [disabled]='helpService.helpMode' [(ngModel)]="internalRange" min="0"
          max={{k100MaxRating}} color="secondary" pin="true" mode="ios" expand="block" (ionChange)="setDirtyFlag()">
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{k100MaxRating}}</ion-label>
          <!-- <ion-icon name="camera" slot="end" color="dark" mode="md" (click)="helpService.helpMode ? test() : internalPicture()"></ion-icon> -->
        </ion-range>
      </ion-col>

      <ion-col *ngIf='!helpService.helpMode' size="7">
        <!-- <ion-range (ionChange)="internalRangeChanged($event)" min="0" max="5" snaps="true" color="primary" mode="md">
                    <ion-label slot="start"></ion-label>
                    <ion-label slot="end" style="font-size:13px;">{{internalRange}}</ion-label>
                    &nbsp; &nbsp;&nbsp;
                    <ion-icon name="camera" slot="end" color="dark" mode="md" (click)="internalPicture()"></ion-icon>
                  </ion-range> -->
        <ion-range *ngIf="product == 'AMSTEEL-BLUE'" [disabled]='helpService.helpMode' [(ngModel)]="internalRange"
          min="0" max={{intMaxRating}} color="secondary" pin="true" mode="ios" expand="block"
          (ionChange)="setDirtyFlag()">
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{intMaxRating}}</ion-label>
          <!-- <ion-icon name="camera" slot="end" color="dark" mode="md" (click)="helpService.helpMode ? test() : internalPicture()"></ion-icon> -->
        </ion-range>
        <ion-range *ngIf="product =='K ™ 100'" [disabled]='helpService.helpMode' [(ngModel)]="internalRange" min="0"
          max={{k100MaxRating}} color="secondary" pin="true" mode="ios" expand="block" (ionChange)="setDirtyFlag()">
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{k100MaxRating}}</ion-label>
          <!-- <ion-icon name="camera" slot="end" color="dark" mode="md" (click)="helpService.helpMode ? test() : internalPicture()"></ion-icon> -->
        </ion-range>
      </ion-col>

    </ion-row>
    <ion-row style="padding: 10px 17px 10px 17px;">
      <div class="img-wrap" *ngFor="let item of cameraService.baselineInternal; let i = index"
        style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
        <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:cover;border-radius: 5px;"
          (click)="cameraService.editImage(item.Image, i, 'internal', 'baseline-int', that, extMaxRating, internalRange, selectedProduct); setDirtyFlag()" />
        <ion-icon *ngIf="item.Image!='./assets/img/samson2.png'" name="trash" class="close" slot="end" mode="md" color="danger"
          (click)="DeleteInternalImage(i)"></ion-icon>
      </div>
      <div *ngIf='helpService.helpMode'
        style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
        <img src="./assets/img/addImage.png" tooltip="{{'Take a photo of rope internal initial condition'|translate}}"
          positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
          [duration]="helpService.duration" [hideOthers]=helpService.hideOthers
          (click)="helpService.helpMode ? test() : internalPicture(product); setDirtyFlag()"
          style="object-fit:contain;border-radius: 5px;padding-top:52px" />
      </div>

      <div *ngIf='!helpService.helpMode'
        style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
        <img src="./assets/img/addImage.png"
          (click)="helpService.helpMode ? test() : internalPicture(product); setDirtyFlag()"
          style="object-fit:contain;border-radius: 5px;padding-top:52px" />
      </div>
    </ion-row>

  </ion-grid>
</ion-content>
<!-- <div style="position: absolute; bottom: 58px; width: 100%" tooltip="{{'Tap to continue Inspection'|translate}}"
  positionV="top" [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveObservations()">
      <fa-icon class="icon-style-other" icon="arrow-right" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</div> -->
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>