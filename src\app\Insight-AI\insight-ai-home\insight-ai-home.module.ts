import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { InsightAIHomePageRoutingModule } from './insight-ai-home-routing.module';

import { InsightAIHomePage } from './insight-ai-home.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { WebView } from '@awesome-cordova-plugins/ionic-webview/ngx';
import { FooterComponent } from 'src/app/components/footer/footer.component';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    InsightAIHomePageRoutingModule,
    TranslateModule,
    FontAwesomeModule,
    ScrollingModule,
    FooterComponent
  ],
  declarations: [InsightAIHomePage],
  providers: [WebView]
})
export class InsightAIHomePageModule {}
