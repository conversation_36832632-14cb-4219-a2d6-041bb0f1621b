<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="primary">
      <ion-button color="primary" slot="end" class="icon" (click)="toggleSearch()" *ngIf="searchIcon">
        <fa-icon class="icon-style" icon="search"></fa-icon>
      </ion-button>
      <!-- <ion-button color="primary" slot="end">
        Submit
      </ion-button> -->
    </ion-buttons>
    <ion-title>Completed</ion-title>
  </ion-toolbar>
  <ion-toolbar *ngIf="searchbar" style="padding-bottom:5px;">
    <ion-searchbar class="searchBar" showCancelButton debounce="500" (ionCancel)="cancelHistoricalItem($event)" animated
      (ionInput)="searchHistoricalItem($event)">
    </ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="none">
    <ion-item-sliding *ngFor="let item of inProgressData; let i = index">

      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);">
        <ion-label style="margin-left: 10px;" (click)="navigateToDetailsPage(item)">

          <ion-label style="margin-left: 10px;" (click)="navigateToDetailsPage(item)">
            <h2
              *ngIf="(item.LMD_DATA.certNo != undefined && item.LMD_DATA.certNo != '') && item.LMD_TYPE != 'AssetActivity'"
              style="color:rgb(37, 91, 145);">{{'Cert' }} : {{item.LMD_DATA.certNo}}</h2>
            <h2
              *ngIf="(item.LMD_DATA.certNo == undefined || item.LMD_DATA.certNo == '') && item.LMD_TYPE != 'AssetActivity'"
              style="color:rgb(37, 91, 145);">{{'Asset' }} : {{item.LMD_DATA.assetName}}</h2>
            <h2
              *ngIf="(item.LMD_DATA.certNo == undefined || item.LMD_DATA.certNo == '') && item.LMD_TYPE == 'AssetActivity'"
              style="color:rgb(37, 91, 145);">{{'Asset' }} : {{item.LMD_DATA.assetName}}</h2>
            <p *ngIf="(item.LMD_DATA.certNo == undefined || item.LMD_DATA.certNo == '') && item.LMD_TYPE == 'AssetActivity'"
              style="color:rgba(104, 102, 102, 0.753);">
              <span *ngIf="item.LMD_DATA.winchType == 'equipment'">{{'Equip' }} :
                {{item.LMD_DATA.winches[0].EQUIP_NAME}}</span>
              <span *ngIf="item.LMD_DATA.winchType == 'lines'">{{'Cert' }} :
                {{item.LMD_DATA.winches[0].EQUIP_CERT_NAME}}</span>
            </p>
            <p style="color:rgba(104, 102, 102, 0.753);">Name: {{item.LMD_NAME}}</p>
            <p style="color:rgba(104, 102, 102, 0.753);">Type: {{item.LMD_TYPE}}</p>
          </ion-label>

          <!-- <p >{{'Product: ' }} : {{'AMSTEEL-BLUE'}}</p> -->
        </ion-label>
        <ion-buttons (click)="icon(i)">
          <ion-button style="padding-bottom: 8px;">
            <ion-icon slot="icon-only" slot="end" [name]="checkSectionIsOpen(i) ? 'chevron-up-outline' :'chevron-down-outline'"
              style="color:rgb(41, 40, 40)"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-item>

      <ion-item *ngIf="checkSectionIsOpen(i)" lines="none"
        style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829);">
        <ion-label style="padding-left: 20px;">
          <ion-text color="primary" *ngIf="checkSectionIsOpen(i)">
            <div *ngIf="item.LMD_TYPE === 'Cropping'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf = "item.LMD_DATA.assetName">Asset</p>
                <p *ngIf = "item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf = "item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf = "item.LMD_DATA.lengthCropped">Length Cropped</p>
                <p *ngIf = "item.LMD_DATA.endCropped">End being Repaired</p>
                <p *ngIf = "item.LMD_DATA.eventDate">Event Date</p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.totalWorkingHour}}</p>
                <p>{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p>{{item.LMD_DATA.lengthCropped}}</p>
                <p>{{item.LMD_DATA.endCropped}}</p>
                <p>{{item.LMD_DATA.eventDate}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'Repair'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf="item.LMD_DATA.endInUse">End being Repaired</p>
                <p *ngIf="item.LMD_DATA.distanceInEye">Distance From Eye</p>
                <p *ngIf="item.LMD_DATA.repairType">Repair Type</p>
                <p *ngIf="item.LMD_DATA.damageType">Reason for Repair</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.totalWorkingHour}}</p>
                <p>{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p>{{item.LMD_DATA.endInUse}}</p>
                <p>{{item.LMD_DATA.distanceInEye}}</p>
                <p>{{item.LMD_DATA.repairType}}</p>
                <p>{{item.LMD_DATA.damageType}}</p>
                <p>{{item.LMD_DATA.eventDate}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'EndForEnd'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.totalWorkingHour}}</p>
                <p>{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p>{{item.LMD_DATA.eventDate}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'InstallLine'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.selectedEquipmentName">Equipment</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.selectedEquipmentName}}</p>
                <p>{{item.LMD_DATA.eventDate}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>


            <div *ngIf="item.LMD_TYPE === 'Rotation'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf="item.LMD_DATA.fromWinch">From Winch</p>
                <p *ngIf="item.LMD_DATA.toWinch">To Winch</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.totalWorkingHour}}</p>
                <p>{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p>{{item.LMD_DATA.fromWinch}}</p>
                <p>{{item.LMD_DATA.toWinch}}</p>
                <p>{{item.LMD_DATA.eventDate}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'EquipmentInsp'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.selectedEquipmentName">Equipment</p>
                <p *ngIf="item.LMD_DATA.ropeContactSurfaceRating">Rope Contact</p>
                <p *ngIf="item.LMD_DATA.flangeSurfaceRating">Surface Rating</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.selectedEquipmentName}}</p>
                <p>{{item.LMD_DATA.ropeContactSurfaceRating}}</p>
                <p>{{item.LMD_DATA.flangeSurfaceRating}}</p>
                <p>{{item.LMD_DATA.eventDate}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'RequestNewLine'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.alternateCertNo">Alternate Cert</p>
                <p *ngIf="item.LMD_DATA.manufacturer">Manufacturer</p>
                <p *ngIf="item.LMD_DATA.productName">Product Name</p>
                <p *ngIf="item.LMD_DATA.productDesc">product Desc</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.alternateCertNo}}</p>
                <p>{{item.LMD_DATA.manufacturer}}</p>
                <p>{{item.LMD_DATA.productName}}</p>
                <p>{{item.LMD_DATA.productDesc}}</p>
                <p>{{item.LMD_DATA.eventDate}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'AssetActivity'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.account">Account</p>
                @if(!isUtility) {
                  <p *ngIf="item.LMD_DATA.portCountry">Port Country</p>
                } @else {
                  <p *ngIf="item.LMD_DATA.portCountry">Country</p>
                }
                <p *ngIf="item.LMD_DATA.portName">Port Name</p>
                <p *ngIf="item.LMD_DATA.allFast"><span *ngIf="isUtility == true">Start of Job</span><span *ngIf="isUtility == false">Port Entered</span></p>
                <p *ngIf="item.LMD_DATA.allLetGo"><span *ngIf="isUtility == true">End of Job</span><span *ngIf="isUtility == false">Port Exited</span></p>
                <p *ngIf = "item.DISPOSITION">Disposition</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p>{{item.LMD_DATA.assetName}}</p>
                <p>{{item.LMD_DATA.account}}</p>
                <p>{{item.LMD_DATA.portCountry}}</p>
                <p>{{item.LMD_DATA.portName}}</p>
                <p>{{item.LMD_DATA.allFast}}</p>
                <p>{{item.LMD_DATA.allLetGo}}</p>
                <p>{{item.DISPOSITION}}</p>
              </div>
            </div>

          </ion-text>
          <ion-buttons style="float:right;padding-top:10px;">
            <ion-button style="padding-bottom: 8px;">
              <fa-icon class="icon-style" slot="end" icon="trash" style="color:rgba(224, 77, 77, 0.979)" class="fa-lg"
                (click)="inProgressItemTrash(i,item)"></fa-icon>
            </ion-button>&nbsp;
            <ion-button style="padding-bottom: 8px;">
              <fa-icon class="icon-style" slot="end" icon="eye" class="fa-lg" (click)="navigateToDetailsPage(item)">
              </fa-icon>
            </ion-button>
          </ion-buttons>
        </ion-label>
      </ion-item>
    </ion-item-sliding>
  </ion-list>
  <div *ngIf='inProgressData && inProgressData.length == 0'
    style="width: 100% !important; text-align: center !important; padding-top: 30px !important">
    {{'No completed items found.'}}</div>
</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>