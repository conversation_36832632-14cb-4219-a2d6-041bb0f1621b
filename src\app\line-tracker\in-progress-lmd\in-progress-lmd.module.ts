import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { InProgressLmdPageRoutingModule } from './in-progress-lmd-routing.module';

import { InProgressLmdPage } from './in-progress-lmd.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from 'src/app/components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    InProgressLmdPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    FooterComponent
  ],
  declarations: [InProgressLmdPage]
})
export class InProgressLmdPageModule {}
