import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DocumentViewer } from '@awesome-cordova-plugins/document-viewer/ngx';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { PopoverController, MenuController, Platform, NavController, ModalController, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { GenericListPage } from 'src/app/generic-list/generic-list.page';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { FileTransfer, FileUploadOptions, FileTransferObject } from '@awesome-cordova-plugins/file-transfer/ngx';
import { PlatformService } from 'src/app/services/platform.service';
import { FileOpenerService } from 'src/app/services/file-opener.service';

@Component({
  selector: 'app-guest-resources-splice-instruction',
  templateUrl: './guest-resources-splice-instruction.page.html',
  styleUrls: ['./guest-resources-splice-instruction.page.scss'],
})
export class GuestResourcesSpliceInstructionPage implements OnInit {

  selection: any;
  private fileTransfer: FileTransferObject;
  platformId: string = this.platformService.getPlatformId();

  strand3 = [
    { "NAME": "Class I Eye Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "3Strand_C1_Eye_Splice_Mobile.pdf" },
    { "NAME": "Class I Long Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "3Strand_Long_Cls1.pdf" },
    { "NAME": "Class I Rope-to-Chain Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "3Strand_Rope2Chain_Cls1.pdf" }
  ]

  strand8 = [
    { "NAME": "Class I End-for-End Splice", "URL": "https://youtu.be/Q4BdJZHkuO4", "TYPE": "BOTH", "FILEPATH": "8Strand_C1_End_For_End_MOBILE.pdf" },
    { "NAME": "Class I Eye Splice", "URL": "https://youtu.be/6SsuPNi10f8", "TYPE": "BOTH", "FILEPATH": "8Strand_C1_Eye_Splice_MOBILE.pdf" },
    { "NAME": "Class II End-for-End Splice", "URL": "https://youtu.be/y6o3n2GoxSQ", "TYPE": "BOTH", "FILEPATH": "8Strand_C2_End_For_End_MOBILE.pdf" },
    { "NAME": "Class II Eye Splice", "URL": "https://youtu.be/TVrmRsxizOY", "TYPE": "BOTH", "FILEPATH": "8Strand_C2_Eye_Splice_MOBILE.pdf" }
  ]

  strand8x3 = [
    { "NAME": "Class II End-for-End Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "8x3Strand_E4E_Cls2.pdf" },
    { "NAME": "Class II Eye Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "8x3Strand_Eye_Cls2.pdf" }
  ]

  strand12 = [
    { "NAME": "Class I End-for-End Splice", "URL": "https://youtu.be/GkiF4gLdp24", "TYPE": "BOTH", "FILEPATH": "12Strand_C1_End_for_End_MOBILE.pdf" },
    { "NAME": "Class I Eye Splice", "URL": "https://youtu.be/v463wuRbxtg", "TYPE": "BOTH", "FILEPATH": "12Strand_C1_Eye_Splice_MOBILE.pdf" },
    { "NAME": "Class I Eye-and-Eye Tail Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "12Strand_C1_Eye_and_Eye_Tail_Mobile.pdf" },
    { "NAME": "Class I Whoopie Sling Modified for Tenex and Tenex-TEC", "URL": "", "TYPE": "PDF", "FILEPATH": "12Strand_C1_Whoopie_Sling_Tenex_Tenex-TEC_Mobile.pdf" },
    { "NAME": "Class II End-for-End Splice", "URL": "https://youtu.be/wEH4UJTDZsc", "TYPE": "BOTH", "FILEPATH": "12Strand_C2_End_for_End_Mobile.pdf" },
    { "NAME": "Class II End-for-End Splice Modified for Dirty, Used Rope", "URL": "", "TYPE": "PDF", "FILEPATH": "12Strand_C2_End_for_End_Dirty_Rope_Mobile.pdf" },
    { "NAME": "Class II Eye Splice", "URL": "https://youtu.be/T8ENqS6b51I", "TYPE": "BOTH", "FILEPATH": "12Strand_C2_Eye_Splice_Mobile.pdf" },
    { "NAME": "Class II Eye Splice for Hi-Tech Purseline", "URL": "", "TYPE": "PDF", "FILEPATH": "12strand_c2_hi-tech_purseline_eye_splice_web.pdf" },
    { "NAME": "Class II Tuck-Bury End-for-End Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "12Strand_C2_Tuck_Bury_End_for_End_Mobile.pdf" },
    { "NAME": "Class II Tuck-Bury End-for-End Splice for Saturn-12", "URL": "", "TYPE": "PDF", "FILEPATH": "12Strand_C2_Tuck_Bury_End_for_End_Saturn-12_Mobile.pdf" },
    { "NAME": "Class II Tuck-Bury Eye Splice", "URL": "https://youtu.be/t2DeDILC_-Y", "TYPE": "BOTH", "FILEPATH": "12Strand_C2_Tuck_Bury_Eye_Splice_Mobile.pdf" },
    { "NAME": "Class II Tuck-Bury Eye Splice for Saturn-12", "URL": "", "TYPE": "PDF", "FILEPATH": "12Strand_C2_Tuck_Bury_Eye_Splice_Saturn-12_Mobile.pdf" },
    { "NAME": "Class II Whoopie Sling Modified for AmSteel® and AmSteel®-Blue", "URL": "", "TYPE": "PDF", "FILEPATH": "12Strand_C2_Whoopie_Sling_AmSteel_AmSteel-Blue_Mobile.pdf" }
  ]

  strand16 = [
    { "NAME": "Class I Eye Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "16Strand_C1_Eye_Splice_Mobile.pdf" }
  ]

  doubleBraid = [
    { "NAME": "Class I Back Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_C1_Back_Splice_Mobile.pdf" },
    { "NAME": "Class I End-for-End Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_C1_End_for_End_Mobile.pdf" },
    { "NAME": "Class I Eye Splice", "URL": "https://youtu.be/n4tU2G31Bus", "TYPE": "BOTH", "FILEPATH": "DblBrd_C1_End_for_End_Mobile.pdf" },
    { "NAME": "Class I Eye Splice Modified for Used Rope", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_C1_End_for_End_Mobile.pdf" },
    { "NAME": "Class II End-for-End Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_C2_End_for_End_Mobile.pdf" },
    { "NAME": "Class II Eye Splice FOR AMSTEEL® II PLUS, WARPSPEED® II, AND WARPSPEED® III SD", "URL": "https://youtu.be/EgOdIMild9E", "TYPE": "BOTH", "FILEPATH": "DblBrd_C2_Eye_Splice_Mobile.pdf" },
    { "NAME": "Class II Eye Splice Modified for TS-II Turbo", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_C2_Eye_Splice_TS-II_Turbo_Mobile.pdf" },
    { "NAME": "Class II Eye Splice Modified for TS-II and TS-II Premium", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_C2_Eye_Splice_TS-II_Mobile.pdf" },
    { "NAME": "Class II Tips for Stripping the Cover", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_C2_Stripping_Cover_Mobile.pdf" },
    { "NAME": "Special Tips for Splicing Double Braid", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_SpecialTips.pdf" },
    { "NAME": "Special Tips for Splicing Double Braid Used Rope", "URL": "", "TYPE": "PDF", "FILEPATH": "DblBrd_Used_SpecialTips.pdf" }
  ]

  roundPlait = [
    { "NAME": "Class I End-for-End Splice", "URL": "", "TYPE": "PDF", "FILEPATH": "RndPlt_C1_End_for_End_Mobile.pdf" },
    { "NAME": "Class I Eye Splice", "URL": "https://youtu.be/XHHVN72MplQ", "TYPE": "BOTH", "FILEPATH": "RndPlt_C1_Eye_Splice_Mobile.pdf" }
  ]

  other = [
    { "NAME": "End-for-End Lock Stitching Procedure", "URL": "https://youtu.be/GMSyiaMfrPI", "TYPE": "BOTH", "FILEPATH": "LockStitch_Whip_Seize.pdf" },
    { "NAME": "Lock Stitching, Whipping, and Seizing Procedures", "URL": "https://youtu.be/T55K4D0kmwg", "TYPE": "BOTH", "FILEPATH": "LockStitch_Whip_Seize.pdf" },
    { "NAME": "Tools Required for Splicing", "URL": "", "TYPE": "PDF", "FILEPATH": "ToolsMaterials_Required.pdf" },
    { "NAME": "Eye Splice Lock Stitching Procedure", "URL": "https://youtu.be/T55K4D0kmwg", "TYPE": "BOTH", "FILEPATH": "lock_stitching_whipping_seizing_procedures.pdf" }
  ]

  constructor(
    public platformService: PlatformService,
    public helpService: HelpService,
    private router: Router,
    public popoverController: PopoverController,
    public inAppBrowser: InAppBrowser,
    public dataService: DataService,
    private menu: MenuController,
    public plt: Platform,
    public navCtrl: NavController,
    public document: DocumentViewer,
    public file: File,
    public unviredSdk: UnviredCordovaSDK,
    public network: Network,
    public modalController: ModalController,
    public alertService: AlertService,
    public device: Device,
    private transfer: FileTransfer,
    public translate: TranslateService,
    private alertController: AlertController,
    private fileOpenerService: FileOpenerService
  ) { 
    this.fileTransfer = this.transfer.create();
  }

  ngOnInit() {
  }

  async presentPopover(title: string) {
    var temp = [];
    var tempTitle = '';
    switch (title) {
      case "3Strand":
        temp = this.strand3;
        tempTitle = '3-Strand'
        break;
      case "8Strand":
        temp = this.strand8;
        tempTitle = '8-Strand'
        break;
      case "8x3Strand":
        temp = this.strand8x3;
        tempTitle = '8x3-Strand'
        break;
      case "12Strand":
        temp = this.strand12;
        tempTitle = '12-Strand'
        break;
      case "16Strand":
        temp = this.strand16;
        tempTitle = '16-Strand'
        break;
      case "DoubleBraid":
        temp = this.doubleBraid;
        tempTitle = 'Double Braid'
        break;
      case "RoundPlait":
        temp = this.roundPlait;
        tempTitle = 'Round Plait'
        break;
      case "Other":
        temp = this.other;
        tempTitle = 'Misc'
        break;
    }
    const popover = await this.modalController.create({
      component: GenericListPage,
      componentProps: { value: temp, title: tempTitle, page: 'RESOURCE' }
    });
    await popover.present();
    popover.onDidDismiss().then(async (data) => {
      if (data.data) {
        this.selection = data.data.data.NAME
        this.openResourceFile(title);
      }
    });
  }

  openResourceFile(title: string) {
    const item = this.getSelectedItem(title);
    if (!item) return;
    
    if (item.TYPE === 'PDF') {
      this.fileOpenerService.openFile(item.FILEPATH);
    } else if (item.TYPE === 'BOTH') {
      if (this.dataService.getNetworkStatus() === false) {
        this.fileOpenerService.openFile(item.FILEPATH);
      } else {
        const browser = this.platformId === 'electron' 
          ? this.inAppBrowser.create(item.URL, '_system', 'location=no,usewkwebview=true')
          : this.inAppBrowser.create(item.URL, '_blank');
        browser.show();
      }
    }
  }

  private getSelectedItem(title: string) {
    const arrays = {
      '3Strand': this.strand3,
      '8Strand': this.strand8,
      '8x3Strand': this.strand8x3,
      '12Strand': this.strand12,
      '16Strand': this.strand16,
      'DoubleBraid': this.doubleBraid,
      'RoundPlait': this.roundPlait,
      'Other': this.other
    };
    
    const items = arrays[title] || [];
    return items.find(i => i.NAME === this.selection);
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  navigateToResources() {
    this.navCtrl.pop();
  }

  async showErrorMessage() {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Resource file not downloaded.',
      buttons: [
        {
          text: 'Ok',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }

  gotoHome() {
    this.router.navigate(['guest-home']);
  }

async gotoInspections() {     
  const alert = await this.alertController.create({
    message: this.translate.instant("GUEST_MESSAGE"),
    buttons: ['OK']
  });
  await alert.present();
}

gotoResources() {
    this.router.navigate(['guest-resource']);
  }

gotoContact() {
    this.router.navigate(['guest-contact']);
  }

  openSpliceInstructions() {
    this.router.navigate(['guest-resources-splice-instruction']);
  }
}