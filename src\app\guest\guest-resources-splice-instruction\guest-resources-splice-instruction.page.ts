import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DocumentViewer } from '@awesome-cordova-plugins/document-viewer/ngx';
import { FileOpener } from '@awesome-cordova-plugins/file-opener/ngx';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Zip } from '@awesome-cordova-plugins/zip/ngx';
import { PopoverController, MenuController, Platform, NavController, ModalController, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { GenericListPage } from 'src/app/generic-list/generic-list.page';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { FileTransfer, FileUploadOptions, FileTransferObject } from '@awesome-cordova-plugins/file-transfer/ngx';
import { PlatformService } from 'src/app/services/platform.service';
@Component({
  selector: 'app-guest-resources-splice-instruction',
  templateUrl: './guest-resources-splice-instruction.page.html',
  styleUrls: ['./guest-resources-splice-instruction.page.scss'],
})
export class GuestResourcesSpliceInstructionPage implements OnInit {

  


  strand3 = [
    { "NAME": "Class I Eye Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class I Long Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class I Rope-to-Chain Splice", "URL": "" , "TYPE": "PDF"}
  ]

  strand8 = [
    { "NAME": "Class I End-for-End Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class I Eye Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class II End-for-End Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class II Eye Splice", "URL": "" , "TYPE": "BOTH"}

  ]

  strand8x3 = [
    { "NAME": "Class II End-for-End Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Eye Splice", "URL": "" , "TYPE": "PDF"}
  ]

  strand12 = [
    { "NAME": "Class I End-for-End Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class I Eye Splice​​", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class I Eye-and-Eye Tail Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class I Whoopie Sling Modified for Tenex and Tenex-TEC", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II End-for-End Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class II End-for-End Splice Modified for Dirty, Used Rope", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Eye Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class II Eye Splice for Hi-Tech Purseline​​", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Tuck-Bury End-for-End Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Tuck-Bury End-for-End Splice for Saturn-12", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Tuck-Bury Eye Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class II Tuck-Bury Eye Splice for Saturn-12", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Whoopie Sling Modified for AmSteel® and AmSteel®-Blue", "URL": "" , "TYPE": "PDF"}
  ]

  strand16 = [
    { "NAME": "Class I Eye Splice", "URL": "" , "TYPE": "PDF"}
  ]

  doubleBraid = [
    { "NAME": "Class I Back Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class I End-for-End Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "​​Class I ​Eye Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class I Eye Splice Modified for Used Rope", "URL": "" , "TYPE": "PDF"},
    { "NAME": "​Class II End-for-End Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Eye Splice", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Class II Eye Splice Modified for TS-II Turbo", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II ​Eye Splice Modified for TS-II and TS-II Premium", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class II Tips for Stripping the Cover", "URL": "" , "TYPE": "PDF"},
    { "NAME": "​​Special Tips for Splicing Double Braid", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Special Tips for Splicing Double Braid Used Rope", "URL": "" , "TYPE": "PDF"}
  ]

  roundPlait = [
    { "NAME": "Class I End-for-End Splice", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Class I Eye Splice", "URL": "" , "TYPE": "BOTH"}
  ]

  other = [
    { "NAME": "End-for-End Lock Stitching Procedure", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Lock Stitching, Whipping, and Seizing Procedures", "URL": "" , "TYPE": "BOTH"},
    { "NAME": "Tools Required for Splicing", "URL": "" , "TYPE": "PDF"},
    { "NAME": "Eye Splice Lock Stitching Procedure", "URL": "" , "TYPE": "BOTH"}
  ]

  selection: any;
  private fileTransfer: FileTransferObject;
  platformId: string = this.platformService.getPlatformId();

  constructor(
    public platformService: PlatformService,
    public helpService: HelpService,
    private router: Router,
    public popoverController: PopoverController,
    public inAppBrowser: InAppBrowser,
    public dataService: DataService,
    private menu: MenuController,
    public plt: Platform,
    public navCtrl: NavController,
    public document: DocumentViewer,
    public file: File,
    public unviredSdk: UnviredCordovaSDK,
    public network: Network,
    public modalController: ModalController,
    public alertService: AlertService,
    public fileOpener: FileOpener,
    public device: Device,
    private transfer: FileTransfer,
    private zip: Zip,
    public translate: TranslateService,
    private alertController: AlertController
  ) { 
    this.fileTransfer = this.transfer.create();
  }

  ngOnInit() {
  }

  async presentPopover(title: string) {
    var temp = [];
    var tempTitle = '';
    switch (title) {
      case "3Strand":
        temp = this.strand3;
        tempTitle = '3-Strand'
        break;
      case "8Strand":
        temp = this.strand8;
        tempTitle = '8-Strand'
        break;
      case "8x3Strand":
        temp = this.strand8x3;
        tempTitle = '8x3-Strand'
        break;
      case "12Strand":
        temp = this.strand12;
        tempTitle = '12-Strand'
        break;
      case "16Strand":
        temp = this.strand16;
        tempTitle = '16-Strand'
        break;
      case "DoubleBraid":
        temp = this.doubleBraid;
        tempTitle = 'Double Braid'
        break;
      case "RoundPlait":
        temp = this.roundPlait;
        tempTitle = 'Round Plait'
        break;
      case "Other":
        temp = this.other;
        tempTitle = 'Misc'
        break;


    }
    const popover = await this.modalController.create({
      component: GenericListPage,
      componentProps: { value: temp, title: tempTitle, page: 'RESOURCE' }
    });
    await popover.present();
    popover.onDidDismiss().then(async (data) => {
      if (data.data.data != '') {
        this.selection = data.data.data.NAME
        switch (title) {
          case "3Strand":
            this.open3StrandResource();
            break;
          case "8Strand":
            this.open8StrandResource();
            break;
          case "8x3Strand":
            this.open8x3StrandResource();
            break;
          case "12Strand":
            this.open12StrandResource();
            break;
          case "16Strand":
            this.open16StrandResource();
            break;
          case "DoubleBraid":
            this.openDoubleBraidResource();
            break;
          case "RoundPlait":
            this.openRoundPlaitResource();
            break;
          case "Other":
            this.openOtherResource();
            break;
        }

      }
    });
  }

  open3StrandResource() {
    switch (this.selection) {
      case "Class I Eye Splice":
        this.open3StrandEyeSplice()
        break;
      case "Class I Long Splice":
        this.open3StrandLongSplice()
        break;
      case "Class I Rope-to-Chain Splice":
        this.open3StrandChainSplice()
        break;

    }
  }

  open8StrandResource() {
    var browser = null;
    switch (this.selection) {
      case "Class I End-for-End Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open8StrandEndForEnd1()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/Q4BdJZHkuO4", '_system', 'location=no,usewkwebview=true');
            browser.show();
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/Q4BdJZHkuO4", "_blank");
          }
        }
        break;
      case "Class I Eye Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open8StrandEndSplice1()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/6SsuPNi10f8", "_system", 'location=no,usewkwebview=true');
            browser.show();
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/6SsuPNi10f8", "_blank");
          }
        }
        break;
      case "Class II End-for-End Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open8StrandEndForEnd2()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/y6o3n2GoxSQ", '_system', 'location=no,usewkwebview=true');
            browser.show();
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/y6o3n2GoxSQ", "_blank");
          }
        }
        break;

      case "Class II Eye Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open8StrandEndSplice2()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/TVrmRsxizOY", "_system", 'location=no,usewkwebview=true');
            browser.show();
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/TVrmRsxizOY", "_blank");
          }
        }
        break;

    }
    if (browser != null) {
      browser.show();
    }
  }

  open8x3StrandResource() {
    switch (this.selection) {
      case "Class II End-for-End Splice":
        this.open8x3StrandEndForEnd()
        break;
      case "Class II Eye Splice":
        this.open8x3StrandEyeSplice()
        break;
    }
  }

  open12StrandResource() {
    var browser = null;
    switch (this.selection) {
      case "Class I End-for-End Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open12StrandEndForEnd1()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/GkiF4gLdp24", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/GkiF4gLdp24", "_blank");
          }
        }
        break;
      case "Class I Eye Splice​​":

        if (this.dataService.getNetworkStatus() == false) {
          this.open12StrandEndSplice1()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/v463wuRbxtg", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/v463wuRbxtg", "_blank");
          }
        }
        break;
      case "Class I Eye-and-Eye Tail Splice":
        this.open12StrandEndForEndTailSplice()
        break;

      case "Class I Whoopie Sling Modified for Tenex and Tenex-TEC":
        this.open12StrandSlingModified()
        break;

      case "Class II End-for-End Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open12StrandEndForEndSplice2()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/wEH4UJTDZsc", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/wEH4UJTDZsc", "_blank");
          }
        }
        break;

      case "Class II End-for-End Splice Modified for Dirty, Used Rope":
        this.open12StrandEndSpliceModified2()
        break;

      case "Class II Eye Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open12StrandEndSplice2()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/T8ENqS6b51I", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/T8ENqS6b51I", "_blank");
          }
        }
        break;

      case "Class II Eye Splice for Hi-Tech Purseline":
        this.open12StrandEndSpliceHiTech2()
        break;

      case "Class II Tuck-Bury End-for-End Splice":
        this.open12StrandTuckBuryEndForEndSplice2()
        break;

      case "Class II Tuck-Bury End-for-End Splice for Saturn-12":
        this.open12StrandTuckBuryEndForEndSpliceSaturn2()
        break;

      case "Class II Tuck-Bury Eye Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.open12StrandTuckBuryEyeSplice2()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/t2DeDILC_-Y", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/t2DeDILC_-Y", "_blank");
          }
        }
        break;

      case "Class II Tuck-Bury Eye Splice for Saturn-12":
        this.open12StrandTuckBuryEyeSpliceSaturn2()
        break;

      case "Class II Whoopie Sling Modified for AmSteel® and AmSteel®-Blue":
        this.open12StrandWhoopieSlingModified2()
        break;

    }
    if (browser != null) {
      browser.show();
    }
  }

  open16StrandResource() {
    switch (this.selection) {
      case "Class I Eye Splice":
        this.open16StrandEyeSplice()
        break;
    }
  }

  openDoubleBraidResource() {
    var browser = null;
    switch (this.selection) {
      case "Class I Back Splice":
        this.openDoubleBraidBackSplice()
        break;
      case "Class I End-for-End Splice":
        this.openDoubleBraidEndForEndSplice()
        break;
      case "​​Class I ​Eye Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.openDoubleBraidEyeSplice()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/n4tU2G31Bus", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/n4tU2G31Bus", "_blank");
          }
        }
        break;
      case "Class I Eye Splice Modified for Used Rope":
        this.openDoubleBraidEyeSpliceModified()
        break;

      case "​Class II End-for-End Splice":
        this.openDoubleBraidEndForEndSplice2()
        break;
      case "Class II Eye Splice":

        if (this.dataService.getNetworkStatus() == false) {
          this.openDoubleBraidEyeSplice2()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/EgOdIMild9E", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/EgOdIMild9E", "_blank");
          }
        }
        break;
      case "Class II Eye Splice Modified for TS-II Turbo":
        this.openDoubleBraidEyeSpliceModifiedTurbo2()
        break;
      case "Class II ​Eye Splice Modified for TS-II and TS-II Premium":
        this.openDoubleBraidEyeSpliceModifiedTurboPremium2()
        break;
      case "Class II Tips for Stripping the Cover":
        this.openDoubleBraidStrippingTheCover()
        break;

      case "​​Special Tips for Splicing Double Braid":
        this.openDoubleBraidSpecialTip()
        break;

      case "Special Tips for Splicing Double Braid Used Rope":
        this.openDoubleBraidSpecialTipForSplicing()
        break;

    }
    if (browser != null) {
      browser.show();
    }
  }

  openRoundPlaitResource() {
    var browser = null;
    switch (this.selection) {
      case "Class I End-for-End Splice":
        this.openRoundPlaitEndForEndSplice()
        break;
      case "Class I Eye Splice":
        if (this.dataService.getNetworkStatus() == false) {
          this.openRoundPlaitEyeSplice()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/XHHVN72MplQ", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/XHHVN72MplQ", "_blank");
          }
        }
        break;
    }
    if (browser != null) {
      browser.show();
    }
  }

  openOtherResource() {
    var browser = null;
    switch (this.selection) {
      case "End-for-End Lock Stitching Procedure":

        if (this.dataService.getNetworkStatus() == false) {
          this.openOtherEndForEndLockStiching()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/GMSyiaMfrPI", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/GMSyiaMfrPI", "_blank");
          }
        }
        break;
      case "Lock Stitching, Whipping, and Seizing Procedures":

        if (this.dataService.getNetworkStatus() == false) {
          this.openOtherLockStiching()
        } else {
          if (this.platformId == 'electron') {
            browser = this.inAppBrowser.create("https://youtu.be/T55K4D0kmwg", "_system", 'location=no,usewkwebview=true');
          } else {
            browser = this.inAppBrowser.create("https://youtu.be/T55K4D0kmwg", "_blank");
          }
        }
        break;
      case "Tools Required for Splicing":
        this.openOtherToolesRequiredForSplicing()
        break;
      case "Eye Splice Lock Stitching Procedure":
      if (this.dataService.getNetworkStatus() == false) {
        this.openOtherEyeSpliceLockStiching()
      } else {
        if (this.platformId == 'electron') {
          browser = this.inAppBrowser.create("https://youtu.be/T55K4D0kmwg", "_system", 'location=no,usewkwebview=true');
        } else {
          browser = this.inAppBrowser.create("https://youtu.be/T55K4D0kmwg", "_blank");
        }
      }
      break;
    }
    if (browser != null) {
      browser.show();
    }
  }

  // & Eye Splice lock stitching
  openOtherEyeSpliceLockStiching() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/lock_stitching_whipping_seizing_procedures.pdf`, 'application/pdf')

    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'lock_stitching_whipping_seizing_procedures.pdf', this.file.dataDirectory, `lock_stitching_whipping_seizing_procedures.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/lock_stitching_whipping_seizing_procedures.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open3StrandEyeSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + '/3Strand_C1_Eye_Splice_Mobile.pdf', 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', '3Strand_C1_Eye_Splice_Mobile.pdf').then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + '/3Strand_C1_Eye_Splice_Mobile.pdf', 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '3Strand_C1_Eye_Splice_Mobile.pdf', this.file.dataDirectory, `3Strand_C1_Eye_Splice_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/3Strand_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open3StrandLongSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/3Strand_Long_Cls1.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `3Strand_Long_Cls1.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/3Strand_Long_Cls1.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '3Strand_Long_Cls1.pdf', this.file.dataDirectory, `3Strand_Long_Cls1.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/3Strand_Long_Cls1.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open3StrandChainSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/3Strand_Rope2Chain_Cls1.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `3Strand_Rope2Chain_Cls1.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/3Strand_Rope2Chain_Cls1.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '3Strand_Rope2Chain_Cls1.pdf', this.file.dataDirectory, `3Strand_Rope2Chain_Cls1.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/3Strand_Rope2Chain_Cls1.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open8StrandEndForEnd1() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C1_End_For_End_MOBILE.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `8Strand_C1_End_For_End_MOBILE.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C1_End_For_End_MOBILE.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '8Strand_C1_End_For_End_MOBILE.pdf', this.file.dataDirectory, `8Strand_C1_End_For_End_MOBILE.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/8Strand_C1_End_For_End_MOBILE.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open8StrandEndForEnd2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C2_End_For_End_MOBILE.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `8Strand_C2_End_For_End_MOBILE.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C2_End_For_End_MOBILE.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '8Strand_C2_End_For_End_MOBILE.pdf', this.file.dataDirectory, `8Strand_C2_End_For_End_MOBILE.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/8Strand_C2_End_For_End_MOBILE.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open8StrandEndSplice1() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C1_Eye_Splice_MOBILE.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `8Strand_C1_Eye_Splice_MOBILE.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C1_Eye_Splice_MOBILE.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '8Strand_C1_Eye_Splice_MOBILE.pdf', this.file.dataDirectory, `8Strand_C1_Eye_Splice_MOBILE.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/8Strand_C1_Eye_Splice_MOBILE.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open8StrandEndSplice2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C2_Eye_Splice_MOBILE.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `8Strand_C2_Eye_Splice_MOBILE.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8Strand_C2_Eye_Splice_MOBILE.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '8Strand_C2_Eye_Splice_MOBILE.pdf', this.file.dataDirectory, `8Strand_C2_Eye_Splice_MOBILE.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/8Strand_C2_Eye_Splice_MOBILE.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open8x3StrandEndForEnd() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8x3Strand_E4E_Cls2.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `8x3Strand_E4E_Cls2.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8x3Strand_E4E_Cls2.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '8x3Strand_E4E_Cls2.pdf', this.file.dataDirectory, `8x3Strand_E4E_Cls2.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/8x3Strand_E4E_Cls2.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open8x3StrandEyeSplice() {
    if(this.device.platform == "Android") {
        this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8x3Strand_Eye_Cls2.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `8x3Strand_Eye_Cls2.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/8x3Strand_Eye_Cls2.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '8x3Strand_Eye_Cls2.pdf', this.file.dataDirectory, `8x3Strand_Eye_Cls2.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/8x3Strand_Eye_Cls2.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandEndForEnd1() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_End_for_End_MOBILE.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C1_End_for_End_MOBILE.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_End_for_End_MOBILE.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C1_End_for_End_MOBILE.pdf', this.file.dataDirectory, `12Strand_C1_End_for_End_MOBILE.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C1_End_for_End_MOBILE.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandEndSplice1() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_Eye_Splice_MOBILE.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C1_Eye_Splice_MOBILE.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_Eye_Splice_MOBILE.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C1_Eye_Splice_MOBILE.pdf', this.file.dataDirectory, `12Strand_C1_Eye_Splice_MOBILE.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C1_Eye_Splice_MOBILE.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandEndForEndTailSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_Eye_and_Eye_Tail_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C1_Eye_and_Eye_Tail_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_Eye_and_Eye_Tail_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C1_Eye_and_Eye_Tail_Mobile.pdf', this.file.dataDirectory, `12Strand_C1_Eye_and_Eye_Tail_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C1_Eye_and_Eye_Tail_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandSlingModified() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_Whoopie_Sling_Tenex_Tenex-TEC_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C1_Whoopie_Sling_Tenex_Tenex-TEC_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C1_Whoopie_Sling_Tenex_Tenex-TEC_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C1_Whoopie_Sling_Tenex_Tenex-TEC_Mobile.pdf', this.file.dataDirectory, `12Strand_C1_Whoopie_Sling_Tenex_Tenex-TEC_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C1_Whoopie_Sling_Tenex_Tenex-TEC_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandEndForEndSplice2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_End_for_End_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_End_for_End_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_End_for_End_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_End_for_End_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_End_for_End_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_End_for_End_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandEndSpliceModified2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_End_for_End_Dirty_Rope_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_End_for_End_Dirty_Rope_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_End_for_End_Dirty_Rope_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_End_for_End_Dirty_Rope_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_End_for_End_Dirty_Rope_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_End_for_End_Dirty_Rope_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandEndSplice2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Eye_Splice_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_Eye_Splice_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Eye_Splice_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_Eye_Splice_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_Eye_Splice_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_Eye_Splice_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandEndSpliceHiTech2() {
    // if(this.device.platform == "Android") {
    //   this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_Eye_Splice_Mobile.pdf`).then(async () => {
    //     this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Eye_Splice_Mobile.pdf`, 'application/pdf')
    //     }).catch((err) => {
    //       this.showErrorMessage();
    //     });
      
    // } else {
    // let filePath = this.file.applicationDirectory + 'www/assets';
    // let fakeName = Date.now();
    //   this.file.copyFile(filePath, '8x3Strand_Eye_Cls2.pdf', this.file.dataDirectory, `8x3Strand_Eye_Cls2.pdf`).then(result => {
    //     this.fileOpener.open(result.nativeURL, 'application/pdf')
    //   }, error => {
    //     if(error.code == 12) {
    //       this.fileOpener.open( this.file.dataDirectory + `/8x3Strand_Eye_Cls2.pdf`, 'application/pdf')
    //     }
    //     console.log(JSON.stringify(error))
    //   });
    // }
  }

  open12StrandTuckBuryEndForEndSplice2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_End_for_End_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_Tuck_Bury_End_for_End_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_End_for_End_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_Tuck_Bury_End_for_End_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_Tuck_Bury_End_for_End_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_Tuck_Bury_End_for_End_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandTuckBuryEndForEndSpliceSaturn2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_End_for_End_Saturn-12_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_Tuck_Bury_End_for_End_Saturn-12_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_End_for_End_Saturn-12_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_Tuck_Bury_End_for_End_Saturn-12_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_Tuck_Bury_End_for_End_Saturn-12_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_Tuck_Bury_End_for_End_Saturn-12_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandTuckBuryEyeSplice2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_Eye_Splice_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_Tuck_Bury_Eye_Splice_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_Eye_Splice_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_Tuck_Bury_Eye_Splice_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_Tuck_Bury_Eye_Splice_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_Tuck_Bury_Eye_Splice_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandTuckBuryEyeSpliceSaturn2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_Eye_Splice_Saturn-12_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_Tuck_Bury_Eye_Splice_Saturn-12_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Tuck_Bury_Eye_Splice_Saturn-12_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_Tuck_Bury_Eye_Splice_Saturn-12_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_Tuck_Bury_Eye_Splice_Saturn-12_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_Tuck_Bury_Eye_Splice_Saturn-12_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open12StrandWhoopieSlingModified2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Whoopie_Sling_AmSteel_AmSteel-Blue_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `12Strand_C2_Whoopie_Sling_AmSteel_AmSteel-Blue_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/12Strand_C2_Whoopie_Sling_AmSteel_AmSteel-Blue_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '12Strand_C2_Whoopie_Sling_AmSteel_AmSteel-Blue_Mobile.pdf', this.file.dataDirectory, `12Strand_C2_Whoopie_Sling_AmSteel_AmSteel-Blue_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/12Strand_C2_Whoopie_Sling_AmSteel_AmSteel-Blue_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  open16StrandEyeSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/16Strand_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `16Strand_C1_Eye_Splice_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/16Strand_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, '16Strand_C1_Eye_Splice_Mobile.pdf', this.file.dataDirectory, `16Strand_C1_Eye_Splice_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/16Strand_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidBackSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_Back_Splice_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C1_Back_Splice_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_Back_Splice_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C1_Back_Splice_Mobile.pdf', this.file.dataDirectory, `DblBrd_C1_Back_Splice_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C1_Back_Splice_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidEndForEndSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C1_End_for_End_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C1_End_for_End_Mobile.pdf', this.file.dataDirectory, `DblBrd_C1_End_for_End_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidEyeSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C1_End_for_End_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C1_End_for_End_Mobile.pdf', this.file.dataDirectory, `DblBrd_C1_End_for_End_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidEyeSpliceModified() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C1_End_for_End_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C1_End_for_End_Mobile.pdf', this.file.dataDirectory, `DblBrd_C1_End_for_End_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C1_End_for_End_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidEndForEndSplice2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_End_for_End_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C2_End_for_End_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_End_for_End_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C2_End_for_End_Mobile.pdf', this.file.dataDirectory, `DblBrd_C2_End_for_End_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C2_End_for_End_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidEyeSplice2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Eye_Splice_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C2_Eye_Splice_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Eye_Splice_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C2_Eye_Splice_Mobile.pdf', this.file.dataDirectory, `DblBrd_C2_Eye_Splice_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C2_Eye_Splice_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidEyeSpliceModifiedTurbo2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Eye_Splice_TS-II_Turbo_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C2_Eye_Splice_TS-II_Turbo_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Eye_Splice_TS-II_Turbo_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C2_Eye_Splice_TS-II_Turbo_Mobile.pdf', this.file.dataDirectory, `DblBrd_C2_Eye_Splice_TS-II_Turbo_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C2_Eye_Splice_TS-II_Turbo_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidEyeSpliceModifiedTurboPremium2() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Eye_Splice_TS-II_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C2_Eye_Splice_TS-II_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Eye_Splice_TS-II_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C2_Eye_Splice_TS-II_Mobile.pdf', this.file.dataDirectory, `DblBrd_C2_Eye_Splice_TS-II_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C2_Eye_Splice_TS-II_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidStrippingTheCover() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Stripping_Cover_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_C2_Stripping_Cover_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_C2_Stripping_Cover_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_C2_Stripping_Cover_Mobile.pdf', this.file.dataDirectory, `DblBrd_C2_Stripping_Cover_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_C2_Stripping_Cover_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidSpecialTip() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_SpecialTips.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_SpecialTips.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_SpecialTips.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_SpecialTips.pdf', this.file.dataDirectory, `DblBrd_SpecialTips.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_SpecialTips.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openDoubleBraidSpecialTipForSplicing() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_Used_SpecialTips.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `DblBrd_Used_SpecialTips.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/DblBrd_Used_SpecialTips.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'DblBrd_Used_SpecialTips.pdf', this.file.dataDirectory, `DblBrd_Used_SpecialTips.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/DblBrd_Used_SpecialTips.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openRoundPlaitEndForEndSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/RndPlt_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `RndPlt_C1_End_for_End_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/RndPlt_C1_End_for_End_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'RndPlt_C1_End_for_End_Mobile.pdf', this.file.dataDirectory, `RndPlt_C1_End_for_End_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/RndPlt_C1_End_for_End_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openRoundPlaitEyeSplice() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/RndPlt_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `RndPlt_C1_Eye_Splice_Mobile.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/RndPlt_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'RndPlt_C1_Eye_Splice_Mobile.pdf', this.file.dataDirectory, `RndPlt_C1_Eye_Splice_Mobile.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/RndPlt_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openOtherEndForEndLockStiching() {
    // if(this.device.platform == "Android") {
    //   this.file.checkFile(this.file.dataDirectory + '/pdf', `RndPlt_C1_Eye_Splice_Mobile.pdf`).then(async () => {
    //     this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/RndPlt_C1_Eye_Splice_Mobile.pdf`, 'application/pdf')
    //     }).catch((err) => {
    //       this.showErrorMessage();
    //     });
      
    // } else {
    // let filePath = this.file.applicationDirectory + 'www/assets';
    // let fakeName = Date.now();
    //   this.file.copyFile(filePath, '8x3Strand_Eye_Cls2.pdf', this.file.dataDirectory, `8x3Strand_Eye_Cls2.pdf`).then(result => {
    //     this.fileOpener.open(result.nativeURL, 'application/pdf')
    //   }, error => {
    //     if(error.code == 12) {
    //       this.fileOpener.open( this.file.dataDirectory + `/8x3Strand_Eye_Cls2.pdf`, 'application/pdf')
    //     }
    //     console.log(JSON.stringify(error))
    //   });
    // }
  }

  openOtherLockStiching() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/LockStitch_Whip_Seize.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `LockStitch_Whip_Seize.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/LockStitch_Whip_Seize.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'LockStitch_Whip_Seize.pdf', this.file.dataDirectory, `LockStitch_Whip_Seize.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/LockStitch_Whip_Seize.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openOtherToolesRequiredForSplicing() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/ToolsMaterials_Required.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `ToolsMaterials_Required.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/ToolsMaterials_Required.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'ToolsMaterials_Required.pdf', this.file.dataDirectory, `ToolsMaterials_Required.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/ToolsMaterials_Required.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  navigateToResources() {
    this.navCtrl.pop();
  }

  async showErrorMessage() {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Resource file not downloaded.',
      buttons: [
        {
          text: 'Ok',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }

  gotoGuestHome() {
    this.router.navigate(['guest-home']);
}

async gotoGuestInspections() {     
  const alert = await this.alertController.create({
    message: this.translate.instant("GUEST_MESSAGE"),
    buttons: ['OK']
  });
  await alert.present();
}

gotoGuestResources() {
    this.router.navigate(['guest-resource']);
}

gotoGuestContact() {
    this.router.navigate(['guest-contact']);
}

openSpliceInstructions() {
  this.router.navigate(['guest-resources-splice-instruction']);
}

}
