import { Component, Ng<PERSON>one, OnInit } from '@angular/core';
import { AuthenticateAndActivateResultType, LoginListenerType, LoginParameters, LoginType, LogLevel, RequestType, ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstant } from 'src/constants/appConstants';
import { UserPreferenceService } from '../services/user-preference.service';
import { AlertController, MenuController, NavController } from '@ionic/angular';
import { HttpClient } from '@angular/common/http';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { Router } from '@angular/router';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { PlatformService } from '../services/platform.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {
  url: string;
  appVersion: string = "0.0.0"
  platformId: string = this.platformService.getPlatformId();
  pressTimeout: any;

  constructor(private route: Router, 
    public platformService: PlatformService,
    public userPreferenceService: UserPreferenceService, 
    public menu: MenuController, 
    private iab: InAppBrowser, 
    public alertController: AlertController, 
    private http: HttpClient, 
    public device: Device,
    private unviredCordovaSdk: UnviredCordovaSDK, 
    public navCtrl: NavController, 
    private alertService: AlertService, 
    public dataService: DataService, 
    public ngZone: NgZone, 
    public androidPermissions: AndroidPermissions) { }

  ngOnInit() {
    // Debug Electron API availability
    // console.log("Electron API available:", !!window.electronAPI);
    // console.log("Platform ID:", this.platformId);
    // console.log("Window object:", typeof window);
    // console.log("Running in Electron:", typeof window !== 'undefined' && (window as any).process && (window as any).process.type);
    // console.log("Window electronAPI object:", JSON.stringify(window.electronAPI));
    
    // Set up deep link listener for Electron platform
    if (window.electronAPI) {
      // console.log("Setting up deep link listener");
      window.electronAPI.onDeepLink(async(url:string)=>{
        // console.log("Deep link received:", url);
        if (url.includes('samson://')) {
          if (url.includes('data=')) {
            const token = url.substring(url.indexOf('?data=') + 6);
            // console.log("Samson token received:", token);
            await this.loginWithSamsonToken(token);
          } else if (url.includes('error=')) {
            const error = decodeURIComponent(url.substring(url.indexOf('?error=') + 7));
            console.log("Login error:", error);
            this.showAlert(error);
          }
        }
      });
    } else {
      console.log("Electron API not available - cannot set up deep link listener");
    }
  }



  ionViewWillEnter() {
    this.menu.enable(false, 'menu');
    this.menu.enable(false, 'helpMenu');
    this.url = AppConstant.URL;
    this.getAppVersion();
  }


  onMouseDown() {
    this.pressTimeout = setTimeout(() => {
      this.presentAlertRadio();
    }, 500); // Adjust the time as needed
  }
  
  onMouseUp() {
    clearTimeout(this.pressTimeout);
  }
  
    onTouchStart() {
      this.pressTimeout = setTimeout(() => {
        this.presentAlertRadio();
      }, 500); // Adjust the time as needed
    }
  
    onTouchEnd() {
      clearTimeout(this.pressTimeout);
    }


  customerAuth() {
    this.dataService.selectedRole = 'Customer'
    var tempUrl = this.url.replace('/UMP', '/auth/login?role=customer')

   
    if (this.platformId == 'electron') {
      // For Electron platform, open external browser for Salesforce login
      const loginURL = this.url.replace('/UMP', '/auth/login?role=customer&version=2');
      
      // console.log("Opening external browser for Salesforce login:", loginURL);
      // console.log("Current URL:", this.url);
      // console.log("Platform ID:", this.platformId);
      
      // Check if we're actually running in Electron
      const isElectron = typeof window !== 'undefined' && (window as any).process && (window as any).process.type;
      // console.log("Is Electron runtime:", isElectron);
      
      // Try to use Electron API first
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.openExternal) {
        // console.log("Using Electron API to open external browser");
        window.electronAPI.openExternal(loginURL);
      } else {
        // console.log('Electron API not available, using in-app browser');
        
        // For browser testing, try to open in new tab
        if (typeof window !== 'undefined' && !isElectron) {
          // console.log("Opening in new browser tab for testing");
          window.open(loginURL, '_blank');
        } else {
          // Fallback to in-app browser if Electron API is not available
          const browser = this.iab.create(loginURL, "_blank");
          if (browser != null) {
            browser.show();
            
            // Add error handling for the browser
            browser.on('loaderror').subscribe(event => {
              console.log("Browser load error:", event);
              if (event.code === 401) {
                this.showAlert("Authentication failed. Please check your credentials or try a different URL.");
              } else {
                this.showAlert(`Login page error: ${event.message}`);
              }
            });
          }
        }
      }
      // var decodedString = atob("********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
      // var decodedJson = JSON.parse(decodedString);
      // this.loginWithUser(decodedJson.UserName, decodedJson.Password);
     
    } else if (this.device.platform == "browser") {
      // this.loginWithBrowserUser("<EMAIL>","samsonship1")
      var decodedString = atob("********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
      var decodedJson = JSON.parse(decodedString);
      this.loginWithBrowserUser(decodedJson.UserName, decodedJson.Password);
    } 
    else {
      console.log(tempUrl)
      const browser = this.iab.create(tempUrl, "_blank");
      if (browser != null) {
        console.log("3")
        browser.show();
      }
      this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE).then(
        result => console.log('Has permission?', result.hasPermission),
        err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE)
      );

      this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE).then(
        result => console.log('Has permission?', result.hasPermission),
        err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE)
      );

      this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE).then(
        result => console.log('Has permission?', result.hasPermission),
        err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE)
      );
      this.androidPermissions.requestPermissions([this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.READ_PHONE_STATE]);
      browser.on('loadstart').subscribe(event => {
        console.log(event);
        // browser.executeScript({code: "alert('loadstart! I am an alert box!!')"});
        if (event && event.url) {
          if (event.url.includes('https://samson//')) {
            if (event.url.includes('data=')) {

              // console.log(event.url)
              // console.log(event.url.substring(event.url.indexOf('?data=') + 6))
              var decodedString = atob(event.url.substring(event.url.indexOf('?data=') + 6));
              // console.log(decodedString);
              var decodedJson = JSON.parse(decodedString);
              this.loginWithUser(decodedJson.UserName, decodedJson.Password);
            } else {
              this.showAlert(decodeURIComponent(event.url.substring(event.url.indexOf('?error=') + 7)))
            }
            browser.close();

          }
        }
        if (browser != null) {
          console.log("4")
          // browser.show();
        }
      });
      browser.on('loadstop').subscribe(event => {
        console.log(event);
      });
      // if (browser != null) {
      //   console.log("x4")
      //   browser.show();
      // }
    }

    // this.route.navigate(['home']);
  }

  sfdcAuth() {
    this.dataService.selectedRole = "Employee"
    if (this.platformId == 'electron') {

        // var decodedString = atob("******************************************************************************************************************************************************************************************************************************************");
        // var decodedJson = JSON.parse(decodedString);
        // this.loginWithUser(decodedJson.UserName, decodedJson.Password);
      // For Electron platform, open external browser for Salesforce login
      const loginURL = this.url.replace('/UMP', '/auth/login?version=2');
      
      // console.log("Opening external browser for Salesforce Employee login:", loginURL);
      // console.log("Current URL:", this.url);
      // console.log("Platform ID:", this.platformId);
      
      // Check if we're actually running in Electron
      const isElectron = typeof window !== 'undefined' && (window as any).process && (window as any).process.type;
      // console.log("Is Electron runtime:", isElectron);
      
      // Try to use Electron API first
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.openExternal) {
        // console.log("Using Electron API to open external browser");
        window.electronAPI.openExternal(loginURL);
      } else {
        // console.log('Electron API not available, using in-app browser');
        
        // For browser testing, try to open in new tab
        if (typeof window !== 'undefined' && !isElectron) {
          // console.log("Opening in new browser tab for testing");
          window.open(loginURL, '_blank');
        } else {
          // Fallback to in-app browser if Electron API is not available
          const browser = this.iab.create(loginURL, "_blank");
          if (browser != null) {
            browser.show();
            
            // Add error handling for the browser
            browser.on('loaderror').subscribe(event => {
              console.log("Browser load error:", event);
              if (event.code === 401) {
                this.showAlert("Authentication failed. Please check your credentials or try a different URL.");
              } else {
                this.showAlert(`Login page error: ${event.message}`);
              }
            });
          }
        }
      }
    }

    else if (this.device.platform == "browser") {
      this.loginWithBrowserUser("<EMAIL>","88371185c308a175ffa8bf2b14a0d03e0201a81bf6af87ed6720536262c70617")
    } else {
    var tempUrl = this.url.replace('/UMP', '/auth/login')
    console.log(tempUrl)
    const browser = this.iab.create(tempUrl, "_blank");
    if (browser != null) {
      console.log("5")
      browser.show();
    }
    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE)
    );

    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE)
    );

    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE)
    );
    this.androidPermissions.requestPermissions([this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.READ_PHONE_STATE]);
    browser.on('loadstart').subscribe(event => {
      console.log(event);
      // browser.executeScript({code: "alert('loadstart! I am an alert box!!')"});
      if (event && event.url) {
        if (event.url.includes('https://samson//')) {
          if (event.url.includes('data=')) {

            // console.log(event.url)
            console.log(event.url.substring(event.url.indexOf('?data=') + 6))
            var decodedString = atob(event.url.substring(event.url.indexOf('?data=') + 6));
            // console.log(decodedString);//get the log here
            var decodedJson = JSON.parse(decodedString);
            this.loginWithUser(decodedJson.UserName, decodedJson.Password);
          } else {
            this.showAlert(decodeURIComponent(event.url.substring(event.url.indexOf('?error=') + 7)))
          }
          browser.close();

        }
      }
      if (browser != null) {
        console.log("6")
        // browser.show();
      }
    });
    browser.on('loadstop').subscribe(event => {
      console.log(event);
    });
    // this.route.navigate(['home']);
  }
  }

  ionViewDidLeave() {
    this.menu.enable(true, 'menu');
  }

  loginWithUser(username: string, password: string) {
    console.log("(Login:login)Started Logging in...")
    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE)
    );

    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE)
    );

    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE)
    );
    this.androidPermissions.requestPermissions([this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.READ_PHONE_STATE]);
    var loginParameters = new LoginParameters()
    loginParameters.appName = "ROPE_INSPECTIONS"
    loginParameters.url = this.url
    loginParameters.company = "SAMSON"
    // loginParameters.username = username && username !='' ? username :  "MJ"
    // loginParameters.password = password && password != '' ? password : "Unvired123*"
    loginParameters.username = username
    loginParameters.password = password
    loginParameters.autoSyncTime = '10'
    loginParameters.domain = "SAMSON";
    loginParameters.loginType = LoginType.unvired
    loginParameters.isRequiredAttachmentBase64 = true
    loginParameters.demoModeRequired = true;
    loginParameters.metadataPath = './assets/metadata.json';
    loginParameters.jwtOptions = { app: 'inspections', language: 'en' };
    this.alertService.present().then(() => {
      this.unviredCordovaSdk.login(loginParameters).then(async (result) => {
        console.log("(LOGIN: Lognin)Success " + JSON.stringify(result))
        if (result.type == LoginListenerType.login_success) {
          if(this.dataService.selectedRole == "Customer")  {
            this.navCtrl.navigateRoot(['home']);
            this.alertService.dismiss();
          } else {
            var accountList = await this.unviredCordovaSdk.syncForeground(RequestType.PULL, '','', 'ROPE_INSPECTIONS_PA_GET_ACCOUNT_AND_ASSETS', true);
            if(accountList.type == ResultType.success) {
              await this.userPreferenceService.dbInsertOrUpdate('accountAssetSet', 'false');
              await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'true');
              this.navCtrl.navigateRoot(['account-setup']);
              this.alertService.dismiss();
            } else {
              this.showAlert(accountList.error);
              this.alertService.dismiss();
            }
          }
          
          // } else if(result.type == LoginListenerType.auth_activation_error) {
          //   this.showAlert(result.error)
          //   this.alertService.dismiss();
        } else if (result.type == LoginListenerType.auth_activation_required) {
          this.unviredCordovaSdk.authenticateAndActivate(loginParameters).then((result) => {
            if (result.type == AuthenticateAndActivateResultType.auth_activation_error) {
              this.showAlert(result.error)
              this.alertService.dismiss();
            } else if (result.type == AuthenticateAndActivateResultType.auth_activation_success) {
              console.log("(LOGIN: authenticateAndActivate)Success " + JSON.stringify(result))
              this.getCustomization();
            } else {
              console.log("(LOGIN: authenticateAndActivate)" + JSON.stringify(result))
            }
            // this.route.navigate(['home']);
          }, error => {
            console.log("(LOGIN: authenticateAndActivate)Error " + JSON.stringify(error))
          })
        } else {
          this.unviredCordovaSdk.authenticateAndActivate(loginParameters).then((result) => {
            console.log("(LOGIN: authenticateAndActivate)Success " + JSON.stringify(result))
            this.getCustomization();
            // this.route.navigate(['home']);
          }, error => {
            console.log("(LOGIN: authenticateAndActivate)Error " + JSON.stringify(error))
          })
        }
      }, error => {
        console.log("(LOGIN: Lognin)Error " + JSON.stringify(error))
      })
    });
  }

  login() {
    console.log("(Login:login)Started Logging in...")
    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE)
    );

    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE)
    );

    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE).then(
      result => console.log('Has permission?', result.hasPermission),
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE)
    );
    this.androidPermissions.requestPermissions([this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE, this.androidPermissions.PERMISSION.READ_PHONE_STATE]);


    console.log("(Login:login)Started Logging in...")
    var loginParameters = new LoginParameters()
    loginParameters.autoSyncTime = '10'
    loginParameters.appName = "ROPE_INSPECTIONS"
    loginParameters.url = "https://sandbox.unvired.io/UMP"
    loginParameters.company = "SAMSON"
    loginParameters.username = "MJ"
    loginParameters.password = "Unvired123*"
    loginParameters.domain = "SAMSON";
    loginParameters.loginType = LoginType.unvired
    loginParameters.isRequiredAttachmentBase64 = true
    loginParameters.demoModeRequired = true;
    loginParameters.demoData = "<?xml>"
    loginParameters.metadataPath = './assets/metadata.json';
    loginParameters.jwtOptions = { app: 'inspections', language: 'en' };
    this.alertService.present().then(() => {
      this.unviredCordovaSdk.login(loginParameters).then((result) => {
        console.log("(LOGIN: Lognin)Success " + JSON.stringify(result))
        if (result.type == LoginListenerType.login_success) {
          // this.getCustomization();
          this.navCtrl.navigateRoot(['home']);
          this.alertService.dismiss();
        } else {
          this.unviredCordovaSdk.authenticateAndActivate(loginParameters).then((result) => {
            console.log("(LOGIN: authenticateAndActivate)Success " + JSON.stringify(result))
            this.getCustomization();
            // this.route.navigate(['home']);
          }, error => {
            console.log("(LOGIN: authenticateAndActivate)Error " + JSON.stringify(error))
          })
        }
      }, error => {
        console.log("(LOGIN: Lognin)Error " + JSON.stringify(error))
      })
    });


    // this.ump.authenticateAndActivate(this.ump.loginParameters).then((result) => {
    //   console.log("(LOGIN: Lognin)Success " + JSON.stringify(result))
    // }, error => {
    //   console.log("(LOGIN: Lognin)Error " + JSON.stringify(error))
    // })


  }

  getCustomization() {
    // this.unviredCordovaSdk.syncBackground(RequestType.QUERY, "", "", AppConstant.PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON, "", "", true).then((result) => {
    //   alert(JSON.stringify(result))
    // })
    this.dataService.getCustomization();
    setTimeout(() => {
      this.ngZone.run(async () => {
        if(this.dataService.selectedRole == "Customer")  {
          this.navCtrl.navigateRoot(['home']);
          this.alertService.dismiss();
        } else {
          var accountList = await this.unviredCordovaSdk.syncForeground(RequestType.PULL, '','', 'ROPE_INSPECTIONS_PA_GET_ACCOUNT_AND_ASSETS', true);
          if(accountList.type == ResultType.success) {
            await this.userPreferenceService.dbInsertOrUpdate('accountAssetSet', 'false');
            await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'true');
            this.navCtrl.navigateRoot(['account-setup']);
            this.alertService.dismiss();
          } else {
            this.showAlert(accountList.error);
            this.alertService.dismiss();
          }
        }
      })
      
    }, 1000)
    // this.unviredCordovaSdk.syncForeground(RequestType.QUERY, "", "", "ROPE_INSPECTIONS_PA_GET_CUSTOMIZATION", true).then((result) => {    
    // console.log("(LOGIN: getCustomization())    " + JSON.stringify(result))
    // this.route.navigate(['home']);
    // this.alertService.dismiss();
    // }, error => {
    //   console.log("(LOGIN: getCustomization()) error    " + JSON.stringify(error))
    // });
  }

  async showAlert(message) {
    const alert = await this.alertController.create({
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  async presentAlertPrompt() {
    const alert = await this.alertController.create({
      header: 'Prompt!',
      inputs: [
        {
          name: 'url',
          value: 'https://sandbox.unvired.io/UMP',
          type: 'url',
          placeholder: 'Enter Url'
        },

      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Ok',
          role: 'OK',
          handler: () => {
            console.log('Confirm Ok');
          }
        }
      ]
    });

    await alert.present();
    await alert.onDidDismiss().then((data) => {
      if (data.role == 'OK') {
        this.url = data.data.values.url
        this.dataService.setLoggedInUrl(this.url)
      } else {
        this.url = AppConstant.URL;
      }
    })
  }

  async presentAlertRadio() {
    const alert = await this.alertController.create({
      header: 'This URL is to be used only for testing.  Check with your administrator for details.',
      inputs: [
        {
          name: 'radio1',
          type: 'radio',
          label: 'https://sandbox.unvired.io/UMP',
          value: 'https://sandbox.unvired.io/UMP',
          checked: true
        },
        // {
        //   name: 'radio2',
        //   type: 'radio',
        //   label: 'http://192.168.98.160:8080/UMP',
        //   value: 'http://192.168.98.160:8080/UMP'
        // },
        // {
        //   name: 'radio2',
        //   type: 'radio',
        //   label: 'Custom url',
        //   value: 'custom'
        // },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Ok',
          role: 'OK',
          handler: () => {
            console.log('Confirm Ok');
          }
        }
      ]
    });

    await alert.present();
    await alert.onDidDismiss().then((data) => {
      console.log("DATA" + JSON.stringify(data))
      if (data.role == 'OK') {
        if (data.data.values == 'custom') {
          this.presentAlertPrompt()
        } else {
          this.url = data.data.values;
        }
      }
    })
  }

  async getAppVersion() {
    // var temp = await this.unviredCordovaSdk.getVersionNumbers()
    // console.log("app Version     ++++++++"   +JSON.stringify(temp))
    // console.log("app Version     ++++++++"   +temp.data.appVersion)
    // this.appVersion = temp.data.appVersion
    this.appVersion = AppConstant.VERSION_NO + " - " + AppConstant.BUILD_DATE
  }

  guestLogin() {
    this.dataService.selectedRole = "Guest"
    // this.unviredCordovaSdk.loginWithDemoData()
    this.alertService.present().then(() => {
      this.http.get('./assets/demoData.xml', { responseType: 'text' })
        .subscribe(data => {
          console.log(data);
          console.log("(Login:login)Started Logging in...")
          var loginParameters = new LoginParameters()
          loginParameters.autoSyncTime = '10'
          loginParameters.appName = "ROPE_INSPECTIONS"
          loginParameters.url = "https://sandbox.unvired.io/UMP"
          loginParameters.company = "SAMSON"
          loginParameters.username = "MJ"
          loginParameters.password = "Unvired123*"
          loginParameters.domain = "SAMSON";
          loginParameters.loginType = LoginType.unvired
          loginParameters.isRequiredAttachmentBase64 = true
          loginParameters.demoData = data
          loginParameters.demoModeRequired = true;
          loginParameters.metadataPath = './assets/metadata.json';
          loginParameters.jwtOptions = { app: 'inspections', language: 'en' };

          this.unviredCordovaSdk.loginWithDemoData(loginParameters).then((result) => {
            console.log("(LOGIN: Lognin)Success " + JSON.stringify(result))
            this.ngZone.run(() => {
              this.alertService.changeMenuItems()
            })
            this.route.navigateByUrl('/guest-home');
            this.alertService.dismiss()
          }, error => {
            console.log("(LOGIN: Lognin)Error " + JSON.stringify(error))
            this.alertService.dismiss()
          })
        });
    });

  }

  agreement() {
    this.route.navigate(['agreement']);
  }

  loginWithBrowserUser(username: string, password: string) {
    console.log("(Login:login)Started Logging in...")
    var loginParameters = new LoginParameters()
    loginParameters.appName = "ROPE_INSPECTIONS"
    loginParameters.url = this.url
    loginParameters.company = "SAMSON"
    // loginParameters.username = username && username !='' ? username :  "MJ"
    // loginParameters.password = password && password != '' ? password : "Unvired123*"
    loginParameters.username = username
    loginParameters.password = password
    loginParameters.autoSyncTime = '10'
    loginParameters.domain = "SAMSON";
    loginParameters.cacheWebData = true;
    loginParameters.loginType = LoginType.unvired
    loginParameters.isRequiredAttachmentBase64 = true
    loginParameters.demoModeRequired = true;
    loginParameters.metadataPath = './assets/metadata.json';
    loginParameters.jwtOptions = { app: 'inspections', language: 'en' };
    this.alertService.present().then(() => {
      
      this.unviredCordovaSdk.authenticateAndActivate(loginParameters).then((result) => {
        if(result.type == AuthenticateAndActivateResultType.auth_activation_error) {
          this.unviredCordovaSdk.logError("LOGIN", "Login Browser","Authentication Error " + JSON.stringify(result))
          console.log("(LOGIN: authenticateAndActivate)Success " + JSON.stringify(result))
          this.alertService.dismiss()
        } else {
          console.log("(LOGIN: authenticateAndActivate)Success " + JSON.stringify(result))
          this.getCustomization();
        }
        // this.route.navigate(['home']);
      }, error => {
        console.log("(LOGIN: authenticateAndActivate)Error " + JSON.stringify(error))
      })

    });
  }

  async loginWithSamsonToken(token: string) {
    // console.log("Processing Samson token login:", token);
    
    try {
      // Decode the token to get user credentials
      const decodedString = atob(token);
      // console.log("Decoded string:", decodedString);
      
      const decodedJson = JSON.parse(decodedString);
      // console.log("Decoded JSON:", decodedJson);
      
      // Use the decoded credentials to login
      if (decodedJson.UserName && decodedJson.Password) {
        // console.log("Calling loginWithUser with:", decodedJson.UserName, decodedJson.Password);
        await this.loginWithUser(decodedJson.UserName, decodedJson.Password);
      } else {
        // console.error("Invalid token format - missing username or password");
        // console.error("UserName:", decodedJson.UserName);
        // console.error("Password:", decodedJson.Password);
        this.showAlert("Invalid login token received");
      }
    } catch (error) {
      console.error("Error processing Samson token:", error);
      console.error("Error details:", error instanceof Error ? error.message : String(error));
      this.showAlert("Error processing login token: " + (error instanceof Error ? error.message : String(error)));
    }
  }

}
