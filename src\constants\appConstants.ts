import { Injectable } from '@angular/core';


@Injectable()
export class AppConstant {

    static APPLICATION_NAME = "ROPE_INSPECTIONS";
    static URL = "https://live.unvired.io/UMP"
    // static SANDBOX_URL = "https://sandbox.unvired.io/UMP"
    // static URL = "https://sandbox.unvired.io/UMP"

    // static URL = 'http://192.168.98.160:8080/UMP'

    // static ML_MODEL_DOWNLOAD_URL = 'https://sandbox.unvired.io/auth/getmodel/model.json'

    static VERSION_NO = "99.99.99";
    static BUILD_DATE = "@@RELEASE_DATE@@";

    //Table names
    static TABLE_USER_HEADER = "USER_HEADER";
    static TABLE_CREATE_INSPECTION_HEADER = "CREATE_INSPECTION_HEADER";
    static TABLE_INSPECTION_HEADER = "INSPECTION_HEADER"
    static TABLE_LMD_HEADER = "LMD_HEADER"
    static TABLE_MEASUREMENT_ITEM = "MEASUREMENT"
    static TABLE_CONFIGURATION_ITEM = "CONFIGURATION"
    static TABLE_ANNOTATION = "ANNOTATION";
    static TABLE_INSPECTION_ATTACHMENT = "INSPECTION_ATTACHMENT";
    static TABLE_QUICK_INSPECTION = "QUICK_INSPECTION_HEADER";
    static TABLE_QUICK_INSPECTION_ATTACHMENT = "QUICK_INSPECTION_ATTACHMENT";

    //PA functions
    static PA_ROPE_INSPECTIONS_PA_SEARCH_INSPECTION = "ROPE_INSPECTIONS_PA_SEARCH_INSPECTION"
    static PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON = "ROPE_INSPECTIONS_PA_GET_CUSTOMIZATION"
    static PA_ROPE_INSPECTIONS_PA_CREATE_INSPECTION = "ROPE_INSPECTIONS_PA_CREATE_INSPECTION"
    static PA_ROPE_INSPECTIONS_PA_UPDATE_INSPECTION = "ROPE_INSPECTIONS_PA_UPDATE_INSPECTION"
    static PA_ROPE_INSPECTIONS_PA_CREATE_LMD = "ROPE_INSPECTIONS_PA_CREATE_LMD"
    static PA_ROPE_INSPECTIONS_PA_REFRESH_CUSTOMIZATION = "ROPE_INSPECTIONS_PA_REFRESH_CUSTOMIZATION"
    static PA_ROPE_INSPECTIONS_PA_SEND_REPORT_EMAIL = "ROPE_INSPECTIONS_PA_SEND_REPORT_EMAIL"
    static PA_ROPE_INSPECTIONS_PA_GET_HISTORICAL_LMD = "ROPE_INSPECTIONS_PA_GET_HISTORICAL_LMD"
    static ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION = "ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION";
    static ROPE_INSPECTIONS_PA_CREATE_QUICK_INSPECTION = "ROPE_INSPECTIONS_PA_CREATE_QUICK_INSPECTION"
    // static QU
    // INSPECTION_STATUS
    static IN_PROGRESS = "In Progress"
    static COMPLETED = "Completed"
    static HISTORICAL = "Historical"
    static READY = "Ready"
    static REOPENED = "ReOpened"

    // PAYMENT REQUEST STATUS
    static NOT_REQUESTED = "Not Requested"
    static REQUESTED = "Requested"
    static PROCESSING = "Processing"
    static PROCESSED = "Processed"
    
    // PAYMENT STATUS
    static STARTED = "Started"
    static ERROR = "Error"
    static PAYMENT_COMPLETED = "Completed"

    static REFRESH_TIMEOUT = 15000;
    static REFRESH_DATA_TIMEOUT = 30000;
    static REFRESH_DATA_TIMEOUT_WINDOWS = 10000

    static SELECT_CONFIGURATION = "Select Configuration"
    static ADD_MEASUREMENTS = "Add Configuration Measurements"
    static CONFIGURATION_COMPLETED = "Configuration Completed"
    static MAX_ALLOWED_IMAGE_SIZE_MB:number = 25.0;
    static MAX_ALLOWED_IMAGE_WARNING_MSG = "The image you selected exceeds the maximum allowed size of 25Mb.<br>Please either <b>reduce the image size</b> before uploading or select a different image. For furthur assistance, kindly contact Samson."
}