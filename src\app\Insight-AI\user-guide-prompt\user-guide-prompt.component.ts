import { Component, Input, OnInit } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';

@Component({
  selector: 'app-user-guide-prompt',
  templateUrl: './user-guide-prompt.component.html',
  styleUrls: ['./user-guide-prompt.component.scss'],
  standalone: true,
  imports: [IonicModule]
})
export class UserGuidePromptComponent implements OnInit {
  @Input() role: string;
  @Input() inspectionType: any;
  constructor(private modalController: ModalController) { }

  ngOnInit() { }

  dismissModal(props:any) {
    this.modalController.dismiss(props);
  }
}
