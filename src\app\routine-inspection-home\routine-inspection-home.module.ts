import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RoutineInspectionHomePageRoutingModule } from './routine-inspection-home-routing.module';

import { RoutineInspectionHomePage } from './routine-inspection-home.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RoutineInspectionHomePageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    FooterComponent
  ],
  declarations: [RoutineInspectionHomePage]
})
export class RoutineInspectionHomePageModule {}
