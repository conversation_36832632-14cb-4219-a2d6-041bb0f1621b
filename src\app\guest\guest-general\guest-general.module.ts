import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestGeneralPageRoutingModule } from './guest-general-routing.module';

import { GuestGeneralPage } from './guest-general.page';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestGeneralPageRoutingModule,
    TranslateModule,
    TooltipsModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestGeneralPage]
})
export class GuestGeneralPageModule {}
