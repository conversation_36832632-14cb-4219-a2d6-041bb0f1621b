import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class INSPECTION_ATTACHMENT extends DATA_STRUCTURE {
    UID: string;
    FILE_NAME: string;
    MIME_TYPE: string;
    URL: string;
    EXTERNAL_URL: string;
    URL_REQUIRES_AUTH: string;
    LOCAL_PATH: string;
    NO_CACHE: string;
    SERVER_TIMESTAMP: number;
    TAG1: string;
    TAG2: string;
    TAG3: string;
    TAG4: string;
    TAG5: string;
    ATTACHMENT_STATUS: string;
    AUTO_DOWNLOAD: string;
    PARAM: string;
    MESSAGE: string;
}