<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/guest-home" (click)='back()'></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Resources' | translate}}</ion-title>
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
      <span *ngIf='helpService.helpMode' style="padding-right: 5px !important; font-size: medium !important">{{'Exit Help' | translate}}</span>
      <fa-icon class="icon-style-help"  *ngIf='helpService.helpMode' icon="times"></fa-icon>
      <fa-icon class="icon-style-help"  *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
    </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'Abrasion Guide' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div>
        <ion-row>
          <div class="col6 border-style" (click)="openPdfExternal()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'External' | translate}}
              </div>
            </div>
          </div>
          <div class="col6 border-style" (click)="openPdfInternal()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Internal' | translate}}
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card>
  <ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'Inspection Checklist' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div>
        <ion-row>
          <div class="col6 border-style" (click)="openPdfSinglebraid()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Single braid' | translate}}
              </div>
            </div>
          </div>
          <div class="col6 border-style" (click)="openPdfDoublebraid()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Double braid' | translate}}
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card>
  <ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'References' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div>
        <ion-row>
          <div class="col6 border-style" (click)="openAbrasion()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Abrasion' | translate}}
              </div>
            </div>
          </div>
          <div class="col6 border-style" (click)="openInspections()">
            <div class="tile" style="line-height:unset !important;">
              <div style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important">
                {{'Inspection' | translate}}
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card>

  <ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'Splice Instructions' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div (click)="openSpliceInstructions()">
        <ion-row style="border: solid #0057b3 2px;border-radius: 5px;">
          <div style="padding-left: 5px;">
            <div style="line-height:unset !important;">
              <div
                style="text-align: center !important; padding-top: 10px!important; padding-bottom: 10px!important;">
                {{'Splice Instructions' | translate}}
              </div>
            </div>
          </div>
          <br>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card>

</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>