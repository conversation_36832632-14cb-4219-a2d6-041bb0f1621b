<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
    </ion-buttons>
    <ion-title>Automatic Entry</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <!-- <div style="width:100% !important">
    <p style="text-align: center !important;">{{'ACTIVITY_TYPE_TITLE' | translate}}</p>
    <div style="width: 100% !important;">
      <div style="width: 50%;text-align: -webkit-center; margin: auto">
        <ion-card class="card-style" (click)="navigateToAssetDetailsActivity()">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/Constructions/other.png" />
        </ion-card>
        <p>{{'ACTIVITY_ADHCO' | translate}}</p>
      </div>
    </div>
  </div> -->

  <div style="padding:3px 10px 0px 3px">
    <p style="margin-top: 8px; padding-left: 14px; margin-bottom: 0px;">{{'CROPPING_ASSET_LIST_LABEL' | translate}}</p>

    <div style="display: inline-flex; width: 100%;">
      <ion-item (click)="presentModal('ASSET')" no-lines text-wrap tappable style="width: 100% !important;"
        *ngIf="assetList && assetList.length >= 0" class="ion-item-generic-style" mode="ios">
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
          *ngIf="selectedAssetName == ''" class="drop-down-arrow  value-field">{{ 'Select Asset' | translate
            }}</div>
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
          *ngIf="selectedAssetName != ''" class="value-field">{{selectedAssetName}}</div>
        <ion-note item-right style="display:inline-flex">
          <p class="drop-down-arrow">
            <fa-icon class="icon-style" icon="sort-down"></fa-icon>
          </p>
        </ion-note>
      </ion-item>
    </div>

  </div>
  <div style="display: inline-flex;width: 100%;padding: 10px 10px 5px 15px;" (click)="refreshIot()">
    <ion-ripple-effect></ion-ripple-effect>
    <p style="width: 100%;">{{'ACTIVITY_IOT' | translate}} <span style="font-style: italic;">{{'ACTIVITY_IOT_NOTE' | translate}}</span></p>
    <div style="outline-color: rgba(0, 0, 0, 0); position: relative; right: 0.4rem;" class="ion-activatable">
      <ion-ripple-effect></ion-ripple-effect>
      <fa-icon  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" class="icon-style" icon="sync"></fa-icon>
      <ion-icon *ngIf="platformId == 'electron' || device.platform == 'browser'" slot="icon-only" name="custom-icon-refresh" style="zoom: 1;"></ion-icon>
    </div>
  </div>
  <div>
    <ion-list>
      <ion-item *ngFor="let options of productNameList" (click)="navigateToAssetDetailsActivity(options)" tappable>
        <ion-label>
          {{options.PORT_NAME}} : {{options.LOCAL_TIME_MOORED}}
        </ion-label>
      </ion-item>
    </ion-list>
  </div>
</ion-content>

<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()"
    (Inspections)="gotoInspections()" (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>