import { Compo<PERSON>, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>roller, Modal<PERSON>ontroller, NavController, PopoverController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AppConstant } from 'src/constants/appConstants';
import { AlertService } from '../services/alert.service';
import { CameraService } from '../services/camera.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { EndImagePage } from '../end-image/end-image.page';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { DomSanitizer } from '@angular/platform-browser';
import { HomePage } from '../home/<USER>';
import { SpliceImagePage } from '../splice-image/splice-image.page';
import { ChafeImagePage } from '../chafe-image/chafe-image.page';
import { ConfigurationListPage } from '../configuration-list/configuration-list.page';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faListCheck, faGrip, faEnvelope, faCircleInfo, faPencil, faEye, faTrash, faPlus } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from '../services/platform.service';

@Component({
  selector: 'app-inspection-configuration',
  templateUrl: './inspection-configuration.page.html',
  styleUrls: ['./inspection-configuration.page.scss'],
})
export class InspectionConfigurationPage implements OnInit {
  platformId: string = this.platformService.getPlatformId();
  selectedInspectionHeader: any;
  selectedUnit: any;
  configurationMeasurementList: any = [];
  product: any;
  productConfig: any;
  overallLength: any;
  labEmployeeConfigList:any[] = [];
  isLoggedInUserEmployee: boolean = false;
  // OALStartDisabled : boolean = true;
  // OALEndDIsabled : boolean = true;
  readOnly:boolean = false;
  measurementId: any = '';
  footerClose: boolean = false;
  statusReopened = AppConstant.REOPENED;
  // unviredCordovaSDK: any;
  constructor(
    public platformService: PlatformService,
    public utilityService: UtilserviceService,
    public helpService: HelpService,
    public alertController: AlertController,
    public translate: TranslateService,
    public alertService: AlertService,
    public dataService: DataService,
    public unviredSDK: UnviredCordovaSDK,
    public ngZone: NgZone,
    public menu: MenuController,
    public cameraService: CameraService,
    public navController: NavController,
    public device: Device,
    public router: Router,
    private modalController:ModalController,
    private domsanitizer:DomSanitizer,
    private _DomSanitizationService: DomSanitizer,
    private popoverController: PopoverController,
    private faIconLibrary: FaIconLibrary) {
    this.faIconLibrary.addIcons(faBars, faListCheck, faGrip, faEnvelope, faCircleInfo, faPencil, faEye, faTrash, faPlus, )
 }

  ngOnInit() {
    this.selectedInspectionHeader = this.utilityService.getSelectedInspectionHeader();
    if (this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED || this.selectedInspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
      if ((this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.READY && this.selectedInspectionHeader.SYNC_STATUS == "3") || (this.selectedInspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED && (this.selectedInspectionHeader.SYNC_STATUS == "3" || this.selectedInspectionHeader.SYNC_STATUS == "0")) ) {
        this.readOnly = false;
      } else {
        this.readOnly = true;
      }
    } else {
      this.readOnly = false
    }
    this.productConfig = this.selectedInspectionHeader.PRODUCT_CONFIG;
    this.product = this.selectedInspectionHeader.PRODUCT_TYPE;
    this.overallLength = this.selectedInspectionHeader.CURRENT_LENGTH
    this.initilizeData()
    this.dataService.measurementChanged = false;
    // this.dataService.setConfigLocationOptions(this.selectedInspectionHeader);
    window.addEventListener('keyboardDidHide', () => {
      setTimeout(() => {
        this.footerClose = false;
      },10);
      
    });
    window.addEventListener('keyboardDidShow', (event) => {
      setTimeout(()=>{
        this.footerClose = true;
      },10)
    });
  }

  test() {
  }

  ionViewWillEnter() {
    this.labEmployeeConfigList = [{ specimen: '',type: 'OAL' ,typeValue:'OAL', DATA:'', photos:[]}];

    this.labEmployeeConfigList[0].DATA = {};
    this.labEmployeeConfigList[0].DATA['id'] = UtilserviceService.guid();
    this.labEmployeeConfigList[0].DATA['start'] = '';
    this.labEmployeeConfigList[0].DATA['end'] = '';
    this.labEmployeeConfigList[0].DATA['type'] = 'OAL';
    this.isLoggedInUserEmployee =  this.dataService.selectedRole=='Employee' ? true : false;
    this.refreshData();
  }

  async refreshData() {
    this.alertService.present().then(() => {
      // setTimeout(() => {
        this.selectAllMeasurements().then(async () => {
          await this.alertService.dismiss();
        }, async error => {
          this, this.unviredSDK.logError("inspection-configuration", "ionViewWillEnter", " Error in selectAllMeasurements " + JSON.stringify(error))
          await this.alertService.dismiss();
        })
      // }, 500);//* disabled timeput by Malli to resolve screen taking time to reflect back the changes saved from configuration type
    });
    // if(this.alertService.isLoading) {
    //   this.alertService.dismiss();
    // }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  initilizeData() {
    // var temp = this.alertService.present();
    this.getSelectedInspectionHeader();
    this.cameraService.reset()
    this.selectedUnit = this.dataService.selectedUom;
  }

  async selectAllMeasurements() {
    var temp;
    this.configurationMeasurementList = [];
    var result = await this.dataService.selectAllConfigMeasurements(this.selectedInspectionHeader)
    this.alertService.dismiss();
    console.log("selectAllMeasurements ************ " + JSON.stringify(result))
    if (result.type == ResultType.success) {
      // this.configurationMeasurementList = result.data;
      temp = result.data;
      // this.configurationMeasurementList = result.data;

      var resultData = result.data;

      for (var i = 0; i < resultData.length; i++) {
        this.utilityService.setBaselineparam(JSON.parse(resultData[i].DATA))
        let baseLineObject = {}
        baseLineObject["DATA"] = JSON.parse(resultData[i].DATA)
        if (baseLineObject["DATA"].otherData) {
          baseLineObject["ending"] = baseLineObject["DATA"].otherData.ending
        }
        else {
          baseLineObject["ending"] = '-'
        }
        this.configurationMeasurementList.push(baseLineObject)
      }
      if(this.isLoggedInUserEmployee) {
        this.labEmployeeConfigList.forEach((el,ind)=>{
          if(ind!=0) {
            el.DATA='';
            el.photos = [];
          }
        })
        
        let OALObj =  this.labEmployeeConfigList[0];
        this.labEmployeeConfigList = [];
        this.labEmployeeConfigList.push(OALObj);
        this.configurationMeasurementList.forEach(async config=>{
          var type = ''
          var typeValue = ''
          // let index;
          switch(config.DATA.type) {
            case 'OAL':
              this.labEmployeeConfigList[0].DATA.id = config.DATA.id;
              if(this.labEmployeeConfigList[0].DATA.start=='' && config.DATA.start!='') {
                this.labEmployeeConfigList[0].DATA.start = config.DATA.start;
              } else if(this.labEmployeeConfigList[0].DATA.start!='' && config.DATA.start=='') {
                
              }
              if(this.labEmployeeConfigList[0].DATA.end=='' && config.DATA.end!='') {
                this.labEmployeeConfigList[0].DATA.end = config.DATA.end;
              }
              break;
            case 'End':
              switch(config.DATA.otherData.endType) {
                case 'Dissected Eye':
                case 'Spliced Eye':
                case 'Whipped':
                case 'Clamped':
                  if(config.DATA.otherData.ropeEnd=='A') {
                    type = 'Eye A'
                    typeValue = 'EyeA'
                  } else if(config.DATA.otherData.ropeEnd=='B') {
                    type = 'Eye B'
                    typeValue = 'EyeB'
                  }
                  break;
                case 'Parted End/Break':
                  type = 'Parted End/Break'
                  typeValue = 'PartedEnd'
                  break;
                case 'Cow Hitched':
                  type = 'Cow Hitched' 
                  typeValue = 'CowHitched'
                  break;
                case 'Blunt Cut/Bitter End':
                  type =  'Blunt Cut/Bitter End'
                  typeValue = 'BluntCutBitterEnd'
                  break;
                case 'Spliced Eye w/Hardware':
                  type = 'Hardware'
                  typeValue ='Hardware'
                  break; 
              }
              break;
            case 'Chafe':
              type = 'Chafe' 
              if(config.DATA.otherData.chafeFixedSliding=="sliding") {
                config.DATA.start = 0;
                config.DATA.otherData.ending = config.DATA.otherData.measurementLength;
              }
              break;
            case 'Splice':
              if(config.DATA.otherData.ropeEnd=='A') {
                type = 'Splice A'
                typeValue ='SpliceA'
              } else if(config.DATA.otherData.ropeEnd=='B') {
                type = 'Splice B'
                typeValue ='SpliceB'
              }
              break;
          }
          if(config.DATA.type != 'OAL') {
            var new_obj = { specimen: 0, type: '', typeValue: '', DATA: '', photos:[]}
            new_obj.specimen = this.labEmployeeConfigList.length
            new_obj.DATA = config.DATA;
            new_obj.type = type;
            new_obj.typeValue = typeValue;
            // for(let m=0;m<config.DATA.externalImage.length;m++) {
            //   let image = JSON.parse(JSON.stringify(config.DATA.externalImage[m]))
            //   let url:SafeUrl = await this.cameraService.getNativeURL(image.Image.changingThisBreaksApplicationSecurity);
            //   new_obj.photos.push(url);
            // };
            this.labEmployeeConfigList.push(new_obj);
          }
        });

        // if(this.labEmployeeConfigList[0].DATA=='') {
        //   this.labEmployeeConfigList[0].DATA = {};
        //   this.labEmployeeConfigList[0].DATA['id'] = UtilserviceService.guid();
        //   this.labEmployeeConfigList[0].DATA['start'] = '';
        //   this.labEmployeeConfigList[0].DATA['end'] = '';
        //   this.labEmployeeConfigList[0].DATA['type'] = 'OAL';
        // }
        var tempList  = JSON.parse(JSON.stringify(this.labEmployeeConfigList))
        var tempoal = tempList.shift();
        tempList = tempList.sort((a, b) => {
          if (a.DATA.start == null || a.DATA.start === '' || a.DATA.start == undefined) return 1;
          if (b.DATA.start == null || b.DATA.start === '' || b.DATA.start == undefined) return -1;
          if (a.DATA.start < b.DATA.start) return -1;
          if (a.DATA.start > b.DATA.start) return 1;
          if (a.DATA.start == b.DATA.start) {
            if(a.DATA.otherData.ending < b.DATA.otherData.ending) return -1;
            if(a.DATA.otherData.ending > b.DATA.otherData.ending) return 1;
          }
          return 0;
        });
        tempList.unshift(tempoal)
        this.labEmployeeConfigList = tempList
        console.log(tempList)
      }
      if(this.alertService.isLoading) {
        this.alertService.dismiss();
      }
      // console.log('Array data?////////////////////////', JSON.stringify(this.configurationMeasurementList));
      this.utilityService.clearAllData();
      this.utilityService.setAllData(this.configurationMeasurementList);
      this.labEmployeeConfigList.forEach(async el => {
        for(let m=0;m<el.DATA?.externalImage?.length;m++) {
          if( this.device.platform != 'browser') {
            let image = JSON.parse(JSON.stringify(el.DATA.externalImage[m]))
            let url =  this._DomSanitizationService.bypassSecurityTrustUrl(image.Image.changingThisBreaksApplicationSecurity);
            el.photos.push(url);
          } else {
            el.photos.push(el.DATA.externalImage[m].Image);
          }
        };
      })
      console.log(this.labEmployeeConfigList)
    }
  }

  addMeasurement() {
    this.utilityService.setConfigMeasurementEditMode(false);
    this.cameraService.setImages([], {})
    this.router.navigate(['add-configuration']);
  }

  async presentAlertConfirm() {
    const alert = await this.alertController.create({
      message: '<strong>Complete configuration?</strong>',
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {

          }
        }, {
          text: this.translate.instant('Yes'),
          handler: async () => {
            if(this.device.platform == "browser") {
              this.unviredSDK.dbSaveWebData();
            }
            if(this.isLoggedInUserEmployee) {
            }
            await this.completeConfiguration();
          }
        }
      ]
    });

    await alert.present();

  }

  async completeConfiguration() {
    //update inspection CONFIG_ID
    this.selectedInspectionHeader.CONFIG_STATUS = "" + AppConstant.CONFIGURATION_COMPLETED
    if (this.dataService.measurementChanged == true && this.selectedInspectionHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
      this.selectedInspectionHeader.CONFIG_REFERENCE = UtilserviceService.guid()
    }
    var result = await this.unviredSDK.dbUpdate("INSPECTION_HEADER", this.selectedInspectionHeader, "INSPECTION_ID like '" + this.selectedInspectionHeader.INSPECTION_ID + "'")
    if (result.type == ResultType.success) {
      // this.labEmployeeConfigList[0] //! 0th index will always be OAL
      if(this.isLoggedInUserEmployee) {
        let configJson = this.labEmployeeConfigList[0].DATA; //* saving OAL to db`
        await this.dataService.saveConfigMeasurements(configJson, this.productConfig,false,false,false);
      }
      
      this.setChafeInspectionHeader(this.selectedInspectionHeader)
    } else {
      this.unviredSDK.logError("inspection-configuration", "completeConfiguration", "Error while updating data data " + JSON.stringify(result))
    }
  }

  async setChafeInspectionHeader(inspectionHeader) {
    if (inspectionHeader) {
      var setChafe = false;
      var configRes = await this.unviredSDK.dbExecuteStatement(`SELECT * FROM CONFIGURATION WHERE INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}' AND  (CONFIG_TYPE_ID like 'Chafe' OR CONFIG_TYPE_ID like 'End')`)
      if (configRes.type == ResultType.success) {
        if (configRes.data.length > 0) {
          var tempres = configRes.data
          tempres = tempres.filter(t => t.CONFIG_TYPE_ID === 'Chafe');
          if (tempres.length > 0) {
            setChafe = true;
          } else {
            tempres = configRes.data
            tempres = tempres.filter(t => t.CONFIG_TYPE_ID === 'End');
            if (tempres.length > 0) {
              for (var index = 0; index < tempres.length; index++) {
                var tempType = JSON.parse(tempres[index].DATA)
                if (tempType.otherData.endChafe != '' && tempType.otherData.endChafe != undefined && tempType.otherData.endChafe != null) {
                  if (tempType.otherData.endChafe.toLowerCase() != 'none') {
                    setChafe = true;
                    break;
                  }
                } else {
                  setChafe = false;
                }
              }
            }
          }
          if (setChafe == true) {
            var inspHeader = await this.unviredSDK.dbSelect("INSPECTION_HEADER", `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
            if (inspHeader.type == ResultType.success) {
              if (inspHeader.data.length > 0) {
                inspHeader.data[0].HAS_CHAFE = 1
                var updateRes = await this.unviredSDK.dbUpdate("INSPECTION_HEADER", inspHeader.data[0], `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
              }
            } else {
              this.unviredSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(inspHeader))
              return
            }
            this.utilityService.setFromBaseline(false)
            this.dataService.setLocationAndLayer(inspHeader.data[0])
            this.utilityService.setSelectedInspectionHeader(inspHeader.data[0]);
            this.dataService.setCreatedHeader(inspHeader.data[0])
            this.utilityService.resetBaselineArray();
            this.alertService.dismiss();
            this.router.navigate(['observations'])
          } else {
            this.utilityService.setFromBaseline(false)
            this.dataService.setLocationAndLayer(this.selectedInspectionHeader)
            this.utilityService.setSelectedInspectionHeader(this.selectedInspectionHeader);
            this.dataService.setCreatedHeader(this.selectedInspectionHeader)
            this.utilityService.resetBaselineArray();
            this.alertService.dismiss();
            this.router.navigate(['observations'])
            return;
          }
        } else {
          this.utilityService.setFromBaseline(false)
          this.dataService.setLocationAndLayer(this.selectedInspectionHeader)
          this.utilityService.setSelectedInspectionHeader(this.selectedInspectionHeader);
          this.dataService.setCreatedHeader(this.selectedInspectionHeader)
          this.utilityService.resetBaselineArray();
          this.alertService.dismiss();
          this.router.navigate(['observations'])
          this.unviredSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(configRes))
          return
        }
      } else {
        this.unviredSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(configRes))
        return
        // var setChafe = false;
        // var endType = await this.unviredCordovaSDK.dbSelect("CONFIGURATION",`INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}' AND CONFIG_TYPE_ID like 'End'`)
        // if(endType.type == ResultType.success) {
        //   if(endType.data.length > 0) {
        //     for(var index = 0; index < endType.data.length; index++) {
        //       endType.data[index].DATA.otherData.endChafe == 'NONE'
        //       setChafe = true;
        //       break;
        //     }
        //     if(setChafe == true) {
        //       var inspHeader = await this.unviredCordovaSDK.dbSelect("INSPECTION_HEADER", `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
        //       if(inspHeader.type == ResultType.success) {
        //         if(inspHeader.data.length > 0) {
        //           inspHeader.data[0].HAS_CHAFE = 1
        //           var updateRes = await this.unviredCordovaSDK.dbUpdate("INSPECTION_HEADER", inspHeader, `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
        //         }
        //       } else {
        //         this.unviredCordovaSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(inspHeader))
        //       }    
        //     }
        //   }
        // } else {
        //   this.unviredCordovaSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(endType))
        // }  
      }
    }

  }

  async getSelectedInspectionHeader() {
    var selectedInspectionHeader = this.utilityService.getSelectedInspectionHeader()
    var temp = await this.dataService.getSelectedHeaderFromDb(selectedInspectionHeader.INSPECTION_ID)
    this.alertService.dismiss();
    if (temp.type == ResultType.success) {
      if (temp.data.length > 0) {
        this.ngZone.run(() => {
          this.selectedInspectionHeader = temp.data[0]
          this.productConfig = this.selectedInspectionHeader.PRODUCT_CONFIG;
          this.product = this.selectedInspectionHeader.PRODUCT;
          this.overallLength = this.selectedInspectionHeader.CURRENT_LENGTH;
        })
        // var tempAlert = await this.alertService.present();

        this.utilityService.setSelectedInspectionHeader(this.selectedInspectionHeader)
      }
    }
  }

  backButtonClick() {
    if(this.readOnly) {
      this.router.navigate(['observations']);
    } else {
      this.presentResetConfirm();
    }
    // if(this.utilityService.getFromfromInspection()) {
    //   this.navController.navigateBack('/inspection')
    // } else {
    //   this.navController.navigateBack('/inspection-home')
    // }
  }

  async review(item) {
    this.utilityService.setConfigMeasurementEditMode(true);
    this.utilityService.setExternalData(item);
    console.log(" ++++++++++++" + item.externalImage)
    await this.cameraService.setImages(item.externalImage, item.originalImages)

    switch (item.type) {
      case 'End':
        this.router.navigateByUrl('/end-image');
        break;
      case 'Chafe':
        this.router.navigateByUrl('/chafe-image');
        break;
      case 'Splice':
        this.router.navigateByUrl('/splice-image');
        break;
      case 'Other':
        this.router.navigateByUrl('/other-image');
        break;
    }
  }

  async resetConfirm(item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Measurement'),
      message: '<strong>' + this.translate.instant('You want to delete this measurement') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Delete'),
          handler: () => {
            this.deleteMeasurement(item);
          }
        }
      ]
    });
    await alert.present();
  }

  async presentResetConfirm() {
    const alert = await this.alertController.create({
      message: '<strong>Are you sure you want to reset configuration? all the measurement associated with the configuration will be lost.</strong>',
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {

          }
        }, {
          text: this.translate.instant('Yes'),
          handler: async () => {
            await this.updateInspectionHeader();
          }
        }
      ]
    });

    await alert.present();

  }

  async deleteMeasurement(item) {
    this.alertService.present()
    var result = await this.deleteMeasurementFromDb(item)
    if (result.type == ResultType.success) {
      await this.refreshData()
      if(this.alertService.isLoading) {
        this.alertService.dismiss();
      }
      
    } else {
      this.refreshData()
      this.alertController.dismiss();
      this.unviredSDK.logError("observations", " deleteMeasurements", JSON.stringify(result));
    }
  }

  async deleteMeasurementFromDb(item) {
    let attres = await this.unviredSDK.dbDelete("INSPECTION_ATTACHMENT", "TAG1 like '" + item.DATA.id + "'")
    let annotres = await this.unviredSDK.dbDelete("ANNOTATION", "MEAS_ID like '" + item.DATA.id + "'")
    // if(item.DATA.externalImage.length > 0) {
    //   let files=[];
    //   // files = files.concat(item.DATA.externalImage);
    //   item.DATA.externalImage.forEach(image=>{
    //     files.push(image.Image.changingThisBreaksApplicationSecurity)
    //   })
    //   // item.DATA.
    //   console.log(files);
    //   files.forEach(async file=>{
    //     await this.cameraService.deleteAttachments(file);
    //   })
    //   // files.concat(item.DATA.externalImage)
    // }
    return await this.unviredSDK.dbDelete("CONFIGURATION", "CONFIG_ID like '" + item.DATA.id + "'")
  }

  async updateInspectionHeader() {
    if (this.configurationMeasurementList != undefined && this.configurationMeasurementList.length > 0) {
      var temp = await this.alertService.present();
      for (var i = 0; i < this.configurationMeasurementList.length; i++) {
        var remp = await this.deleteMeasurementFromDb(this.configurationMeasurementList[i]);
      }
      this.alertService.dismiss();
      await this.updateInspectionInDb();
      this.navController.navigateBack('/new-configuration-list');
    } else {
      await this.updateInspectionInDb();
      this.navController.navigateBack('/new-configuration-list');
    }
  }

  async updateInspectionInDb() {
    this.selectedInspectionHeader.CONFIG_STATUS = "" + AppConstant.SELECT_CONFIGURATION
    this.selectedInspectionHeader.PRODUCT_CONFIG = ''
    var result = await this.unviredSDK.dbUpdate("INSPECTION_HEADER", this.selectedInspectionHeader, "INSPECTION_ID like '" + this.selectedInspectionHeader.INSPECTION_ID + "'")
    if (result.type == ResultType.success) {
      this.utilityService.setSelectedInspectionHeader(this.selectedInspectionHeader)
      this.dataService.setConfigLocationOptions(this.selectedInspectionHeader);
    } else {
      this.unviredSDK.logError("new-configuration-list", "setConfigAndNavigate", "Error while fetching data " + JSON.stringify(result))
    }
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async Edit(type: string, index: number, mode: string) {
    let page = '';
    let props;
    var new_obj = {specimen:  this.labEmployeeConfigList.length, type: '' ,typeValue:'', DATA:'', photos:[]};
    if (mode == 'edit') {
      if (this.labEmployeeConfigList[index].DATA != undefined && this.labEmployeeConfigList[index].DATA != '') {
        this.utilityService.setConfigMeasurementEditMode(true);
        this.utilityService.setExternalData(this.labEmployeeConfigList[index].DATA);
        if(this.platformId == 'electron') {
          let extImages = this.labEmployeeConfigList[index].DATA.externalImage;
          let orgNImages = this.labEmployeeConfigList[index].DATA.originalImages;
          await this.cameraService.setImages(extImages,orgNImages);
        } else {
          await this.cameraService.setImages(this.labEmployeeConfigList[index].DATA.externalImage, this.labEmployeeConfigList[index].DATA.originalImages);
        }
        
      } else {
        this.utilityService.setConfigMeasurementEditMode(false);
      }
    } else {
      this.utilityService.setConfigMeasurementEditMode(false);
      new_obj = {specimen:  this.labEmployeeConfigList.length, type: '' ,typeValue:'', DATA:'', photos:[]}
    }


    if (this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.selectedInspectionHeader.INSPECTION_STATUS == '' || this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.IN_PROGRESS || this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.REOPENED) {
      this.utilityService.setObservationReadFlag('gg'); //! check later if this code is necessary
    } else { //! check later if this code is necessary
      this.utilityService.setObservationReadFlag('readOnly'); //! check later if this code is necessary
    } //! check later if this code is necessary
    

    switch (type) {
      case 'EyeA':
        props = { ropeEnd: 'A', endType: 'EyeA' };
        await this.presentModal(EndImagePage, props);
        break;
      case 'EyeB':
        props = { ropeEnd: 'B', endType: 'EyeB' };
        await this.presentModal(EndImagePage, props);
        break;
      case 'SpliceA':
        props = { ropeEnd: 'A', endType: 'SpliceA' };
        await this.presentModal(SpliceImagePage, props);
        break;
      case 'SpliceB':
        props = { ropeEnd: 'B', endType: 'SpliceB' };
        await this.presentModal(SpliceImagePage, props);
        break;
      case 'Chafe':
        props = { endType: 'Chafe' };
        await this.presentModal(ChafeImagePage, props);
        break;
      case 'BluntCutBitterEnd':
        props = { endType: 'Blunt Cut/Bitter End', endTypeDisabled: true };
        await this.presentModal(EndImagePage, props);
        break;
      case 'Hardware':
        props = { endType: 'Spliced Eye w/Hardware', endTypeDisabled: true };
        await this.presentModal(EndImagePage, props);
        break;
      case 'PartedEnd':
        props = { endType: 'Parted End/Break', endTypeDisabled: true };
        await this.presentModal(EndImagePage, props);
        break;
      case 'CowHitched':
        props = { endType: 'Cow Hitched', endTypeDisabled: true };
        await this.presentModal(EndImagePage, props);
        break;
    }
    // if( new_obj.specimen > this.labEmployeeConfigList.length ) {
    //   this.labEmployeeConfigList.push(new_obj);
    // }
  }

  async presentModal(component: any, componentProps:any) {
    this.alertService.present().then(async () => {
      const modal = await this.modalController.create({
        component: component,
        componentProps: componentProps,
        cssClass : 'customConfiguration',
        backdropDismiss: false,
        showBackdrop: false
      });
      await modal.present().then(async res=>{
        if(this.alertService.isLoading){
          await this.alertService.dismiss()
        }
      });

      modal.onDidDismiss().then(async (data) => {
        console.log(data);
        this.refreshData();
      });
    })
  }

  editInspection() {
    if(this.isLoggedInUserEmployee) {
      this.router.navigate(['create-inspection']);
    }
  }

  async presentPopover(type: string, index: number, mode: string) {
    const popover = await this.popoverController.create({
      component: ConfigurationListPage,
      componentProps: { page: this, insightAiEnabled: this.dataService.isUserEnabledForInsightAI },
      event,
      showBackdrop: true,
      animated: true
    });
    await popover.present();
    popover.onDidDismiss().then(async (data) => {
      if(data.data != undefined) {
        this.Edit(data.data,index,mode)
      }
    });
  }
}
