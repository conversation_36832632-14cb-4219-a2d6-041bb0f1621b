<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'END_FOR_END_TITLE' | translate}}</ion-title>
    <ion-buttons slot="end">
      <!-- <ion-button color="primary" (click)="saveLMD()" class="help-button-style">
        <fa-icon class="icon-style-help" icon="list"></fa-icon>
      </ion-button> -->
      <ion-button color="primary" class="help-button-style">

        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"
          (click)="helpService.switchMode(); enableAllFields();"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="info-circle"
          (click)="helpService.switchMode(); disableAllFields();"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:15px 10px 0px 3px">
    <p style="margin-top: 8px; padding-left: 14px">{{'CROPPING_ASSET_LIST_LABEL' | translate}}<span
        style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></p>

    <div style="display: inline-flex; width: 100%;">
      <ion-item (click)="(helpService.helpMode || readOnly) ? test() : presentModal('ASSET')" no-lines text-wrap
        tappable style="width: 100% !important;" *ngIf="assetList && assetList.length >= 0"
        class="ion-item-generic-style" mode="ios">
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
          *ngIf="selectedAssetName == ''" class="drop-down-arrow  value-field">{{ 'Select Asset' | translate
            }}</div>
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
          *ngIf="selectedAssetName != ''" class="value-field">{{selectedAssetName}}</div>
        <ion-note item-right style="display:inline-flex">
          <p class="drop-down-arrow">
            <fa-icon class="icon-style" icon="sort-down"></fa-icon>
          </p>
        </ion-note>
      </ion-item>
    </div>

  </div>
  <div style="padding:15px 10px 0px 3px">
    <p style="margin-top: 8px;padding-left: 14px ;">{{'CROPPING_CERT_LIST_LABEL' | translate}}<span
        style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></p>
    <div style="display: inline-flex; width: 100%;">
      <ion-item
        (click)="!(helpService.helpMode || readOnly) && selectedAssetName != '' ? presentModal('CERTIFICATE') : test()"
        no-lines text-wrap tappable style="width: 100% !important;" class="ion-item-generic-style" mode="ios">
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
          *ngIf="selectedCertNo == ''" class="drop-down-arrow  value-field">{{ 'Select Certificate No' | translate
              }}</div>
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
          *ngIf="selectedCertNo != ''" class="value-field">{{selectedCertNo}}</div>
        <ion-note item-right style="display:inline-flex">
          <p class="drop-down-arrow">
            <fa-icon class="icon-style" icon="sort-down"></fa-icon>
          </p>
        </ion-note>
      </ion-item>
    </div>
  </div>
  <div style="padding:15px 10px 0px 17px">
    <label>{{'TOTAL_WORKING_HOUR_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'HELP_TEXT_ENE_FOR_END_WORKING_HOUR'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>

          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="totalWorkingHour" maxlength="18" placeholder="{{'TOTAL_WORKING_HOUR_PLACEHOLDER' | translate}}" (keydown)="keyPressed($event, tpf, false)"
            step="any" inputmode="decimal" >
          </mat-form-field>


      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <div [formGroup]="totalWorkingHourForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="totalWorkingHour" maxlength="18"
              placeholder="{{'TOTAL_WORKING_HOUR_PLACEHOLDER' | translate}}" step="any"
              (change)="onChangeDisable('totalWorkingHour')" inputmode="decimal"
              (keydown)="keyPressed($event, tpf, false)" [required] formControlName="totalWorkingHourCtrl">
            <mat-error *ngIf="hasErrorStart('totalWorkingHour')">{{totalWorkingHourErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </ion-col>&nbsp;
    </ion-row>
  </div>
  <div style="padding:15px 10px 0px 17px">
    <label>{{'TOTAL_WORKING_OPERATIONS_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'HELP_TEXT_END_FOR_END_WORKING_OPERATIONS'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <mat-form-field style="width:100%">
          <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="totalWorkingOperations"
            maxlength="18" placeholder="{{'TOTAL_WORKING_OPERATIONS_PLACEHOLDER' | translate}}"
            (keydown)="keyPressed($event, tpf, false)" step="any" inputmode="decimal">
        </mat-form-field>
      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <div [formGroup]="totalWorkingOperationsForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="totalWorkingOperations" maxlength="18"
              placeholder="{{'TOTAL_WORKING_OPERATIONS_PLACEHOLDER' | translate}}" step="any"
              (change)="onChangeDisable('totalWorkingOperations')" inputmode="decimal"
              (keydown)="keyPressed($event, tpf, false)" [required] formControlName="totalWorkingOperationsCtrl">
            <mat-error *ngIf="hasErrorStart('totalWorkingOperations')">{{totalWorkingOperationsErrorMessage}}
            </mat-error>
          </mat-form-field>
        </div>
      </ion-col>&nbsp;
    </ion-row>
  </div>
  <div style="padding:15px 10px 0px 17px">
    <label>{{'EVENT_DATE_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'HELP_TEXT_END_FOR_END_EVENT_DATE'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <mat-form-field style="width:100%">
          <input matInput [matDatepicker]="picker" [max]="maxDate" disabled [(ngModel)]="eventDate"
            placeholder="{{'EVENT_DATE_PLACEHOLDER' | translate}}">
          <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <div [formGroup]="eventDateForm">
          <mat-form-field style="width:100%">
            <input matInput  [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="eventDate"
              placeholder="{{'Please select event date' | translate}}" step="any" (click)="picker.open()"
              (keydown)="keyPressedDate($event, 'eventDate', false)" formControlName="eventDateCtrl" readonly>
            <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
      </ion-col>&nbsp;
    </ion-row>
  </div>

  <div style="padding:100px 10px 0px 17px">
  </div>
  <!-- Save Button -->
  <ion-fab *ngIf='!readOnly' vertical="bottom" horizontal="end" slot="fixed" [topOffset]=helpService.topOffset
    tooltip="{{'Tap to save cropping form'|translate}}" positionV="top" positionH="right"
    [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveLMD()">
      <fa-icon class="icon-style-other" icon="save" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>


<!-- Footer -->
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); enableAllFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>