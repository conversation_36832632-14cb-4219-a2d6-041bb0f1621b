<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Planned Inspection' | translate}}</ion-title>
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
        <span *ngIf='helpService.helpMode' style="padding-right: 5px !important; font-size: medium !important">{{'Exit Help' | translate}}</span>
        <fa-icon class="icon-style-help"  *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help"  *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
      </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="card-style-multiple">
    <ion-row>
      <ion-col>
        <ion-item class="ion-toggle-item-style">
          <ion-label>{{label}}</ion-label>
          <ion-toggle [(ngModel)]="obj.workOrder" style="zoom: 0.7;" (ionChange)="toggleSelection()"></ion-toggle>
        </ion-item>
      </ion-col>
      <ion-col>
        <div>
          <ion-input [placeholder]="placeHolder"></ion-input>
        </div>
      </ion-col>
    </ion-row>
  </ion-card>

  <ion-grid>
    <ion-row>
      <ion-col>
        <ion-button mode="ios" size="small" fill="outline" style="float:right" (click)="searchInspection()"
          [disabled]='helpService.helpMode'>
          {{'Search' | translate}}
          <ion-icon mode="ios" slot="end" name="search"></ion-icon>
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>


  <!-- <ion-card-content>
      <p>Do you know the work order number ?</p>
      <ion-radio-group [(ngModel)]="obj.workOrder">
        <ion-radio  labelPlacement="end" justify="start" color="primary" mode="md" slot="start" value="yes"></ion-radio>
        &nbsp;
        <ion-label>Yes</ion-label>
        &nbsp;&nbsp;
        <ion-radio  labelPlacement="end" justify="start" color="primary" mode="md" slot="start" value="no"></ion-radio>
        &nbsp;
        <ion-label>No</ion-label>
      </ion-radio-group>
      <div style="padding-top: 10px;" *ngIf="obj.workOrder  == 'yes'">

        <p>Work Order Number</p>
        <ion-input placeholder="{{'Enter Work Order Number' | translate}}"></ion-input>
      </div>
    </ion-card-content>
  </ion-card>

  <ion-card class="card-style-multiple">
    <ion-card-content>
      <p>Do you know the RFT ?</p>
      <ion-radio-group [(ngModel)]="obj.rft">
        <ion-radio  labelPlacement="end" justify="start" color="primary" mode="md" slot="start" value="yes"></ion-radio>
        &nbsp;
        <ion-label>Yes</ion-label>
        &nbsp;&nbsp;
        <ion-radio  labelPlacement="end" justify="start" color="primary" mode="md" slot="start" value="no"></ion-radio>
        &nbsp;
        <ion-label>No</ion-label>
      </ion-radio-group>
      <div style="padding-top: 10px;" *ngIf="obj.rft  == 'yes'">

          <p>Work RFT</p>
          <ion-input placeholder="{{'Enter RFT' | translate}}"></ion-input>
        </div>
    </ion-card-content>
  </ion-card> -->
</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>