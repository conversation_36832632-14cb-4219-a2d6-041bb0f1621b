import { Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController, NavController } from '@ionic/angular';
import { HelpService } from 'src/app/services/help.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';

@Component({
  selector: 'app-guest-new-observation',
  templateUrl: './guest-new-observation.page.html',
  styleUrls: ['./guest-new-observation.page.scss'],
})
export class GuestNewObservationPage implements OnInit {

  selectedInspectionHeader: any;
  noOptions: boolean = false;
  noOfExterals = 0;
  noOfInternals = 0;
  standard = [];

  compression = [];

  others = [];

  constructor(private router: Router, public menu: MenuController, private navCtrl: NavController, private service: UtilserviceService, private ngZone: NgZone, public helpService: HelpService) {
    this.selectedInspectionHeader = this.service.getSelectedInspectionHeader();
  }
  ionViewWillEnter() {
    this.standard = [
      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
    ];
    this.others = [
      [{ label: 'General', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }]
    ];
    const obj = this.service.getBaselineArray();
    const extcount = obj.filter(t => t.type === 'External');
    this.noOfExterals = extcount.length;
    this.noOfInternals = (obj.filter(t => t.type === 'Internal')).length;

    var externalArray = this.service.getAllData().filter(t => t.DATA.type === 'External');
    var internalArray = this.service.getAllData().filter(t => t.DATA.type === 'Internal');

    if (this.standard.length > 0) {
      this.standard[0][0].count = externalArray.length;
      this.standard[0][1].count = internalArray.length;
    }
  }

  ngOnInit() {
  }

  navigateToObservation(id) {
    switch (id) {
      case 'External':
        this.router.navigateByUrl('/guest-external');
        break;
      case 'Internal':
        this.router.navigateByUrl('/guest-internal');
        break;
    }
  }
  standardobservation(item) {
    switch (item) {
      case 'General':
        this.router.navigateByUrl('/guest-general');
        break;
    }
  }
  openMenu() {
    this.menu.toggle('menu');
  }

  gotoHome() {
    this.navCtrl.navigateRoot('/guest-home');
  }

  gotoInspections() {
    this.navCtrl.navigateRoot('/guest-inspection-home');
  }

  gotoResources() {
    this.navCtrl.navigateRoot('/guest-resource');
  }

  gotoContact() {
    this.navCtrl.navigateRoot('/guest-contact');
  }

  showOptionsPage() {
    this.router.navigateByUrl('/popup-options');
  }
}
