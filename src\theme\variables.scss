// // For information on how to create your own theme, please see:
// // // http://ionicframework.com/docs/theming/
:root {
  /** primary **/
  --ion-color-primary: #0057b3;
  --ion-color-primary-rgb: 56, 128, 255;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #3171e0;
  --ion-color-primary-tint: #4c8dff;
  --ion-color-primary-light: #476ac8;

  /** secondary **/
  --ion-color-secondary: #0cd1e8;
  --ion-color-secondary-rgb: 12, 209, 232;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #0bb8cc;
  --ion-color-secondary-tint: #24d6ea;

  /** tertiary **/
  --ion-color-tertiary: #7044ff;
  --ion-color-tertiary-rgb: 112, 68, 255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #633ce0;
  --ion-color-tertiary-tint: #7e57ff;

  /** success **/
  --ion-color-success: #10dc60;
  --ion-color-success-rgb: 16, 220, 96;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #0ec254;
  --ion-color-success-tint: #28e070;

  /** warning **/
  --ion-color-warning: #ffce00;
  --ion-color-warning-rgb: 255, 206, 0;
  --ion-color-warning-contrast: #ffffff;
  --ion-color-warning-contrast-rgb: 255, 255, 255;
  --ion-color-warning-shade: #e0b500;
  --ion-color-warning-tint: #ffd31a;

  /** danger **/
  --ion-color-danger: #f04141;
  --ion-color-danger-rgb: 245, 61, 61;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #d33939;
  --ion-color-danger-tint: #f25454;

  /** dark **/
  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 34, 34;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  /** medium **/
  --ion-color-medium: #989aa2;
  --ion-color-medium-rgb: 152, 154, 162;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #86888f;
  --ion-color-medium-tint: #a2a4ab;

  /** light **/
  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 244, 244;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;
}

ion-menu {
  --ion-background-color: var(--ion-color-primary);
  --ion-text-color: var(--ion-color-primary-contrast);

  ion-toolbar {
    --background: #003c7d;
    --ion-text-color: var(--ion-color-primary-contrast);
  }

  ion-list {
    background: transparent;
  }
}

.ion-help-menu {
  --ion-background-color: white !important;
  --ion-text-color: black !important;
  --width: 100% !important;

  ion-toolbar {
    --background: #003c7d;
    --ion-text-color: var(--ion-color-primary-contrast);
  }

  ion-list {
    background: transparent;
  }
}

.blink_me {
  animation: blinker 3s linear infinite;
}


@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.shake {
  animation: shake 2s;
  animation-iteration-count: infinite;
}

@keyframes shake {
  0% {
    transform: translate(2px, 0px) rotate(0deg);
  }

  10% {
    transform: translate(-2px, 0px) rotate(0deg);
  }

  20% {
    transform: translate(2px, 0px) rotate(0deg);
  }

  30% {
    transform: translate(-2px, 0px) rotate(0deg);
  }

  40% {
    transform: translate(0px, 0px) rotate(0deg);
  }

  50% {
    transform: translate(0px, 0px) rotate(0deg);
  }

  60% {
    transform: translate(0px, 0px) rotate(0deg);
  }

  70% {
    transform: translate(0px, 0px) rotate(0deg);
  }

  80% {
    transform: translate(0px, 0px) rotate(0deg);
  }

  90% {
    transform: translate(0px, 0px) rotate(0deg);
  }

  100% {
    transform: translate(0px, 0px) rotate(0deg);
  }
}

@font-face {
  font-family: 'San Fransico';
  src: url('../assets/fonts/HelveticaNeue-Medium.ttf');
}

* {
  font-family: 'San Fransico';
}

.ion-color-darkRed {
  --ion-color-base: #be2713;
  --ion-color-base-rgb: 255, 255, 0;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 0, 0, 0;
  --ion-color-shade: #af2b19;
  --ion-color-tint: #c9422a;
}

.ion-color-darkGreen {
  --ion-color-base: #0b6830;
  --ion-color-base-rgb: 16, 220, 96;
  --ion-color-contrast: #ffffff;
  --ion-colorcontrast-rgb: 255, 255, 255;
  --ion-color-shade: #0ec254;
  --ion-color-tint: #28e070;
}

.ion-color-dark1Green {
  --ion-color-base: #0a5226;
  --ion-color-base-rgb: 16, 220, 96;
  --ion-color-contrast: #ffffff;
  --ion-colorcontrast-rgb: 255, 255, 255;
  --ion-color-shade: #0ec254;
  --ion-color-tint: #28e070;
}

.ion-color-mediumBlack {
  --ion-color-base: #174794;
  --ion-color-base-rgb: 152, 154, 162;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255, 255, 255;
  --ion-color-shade: #466aa5;
  --ion-color-tint: #355d9c;
}

.footer-style {
  background-color: var(--ion-color-primary) !important;
  color: white !important;
  line-height: 40px !important;
  min-height: 40px !important;
  text-align: center !important;
}

ion-fab-button {
  --background: #var(--ion-color-primary) !important;
}

.ion-color-darkMedium {
  --ion-color-base: #777777;
  --ion-color-base-rgb: 152, 154, 162;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255, 255, 255;
  --ion-color-shade: #585858;
  --ion-color-tint: #505050;
}

.ion-color-darkMedium1 {
  --ion-color-base: #5c5c5c;
  --ion-color-base-rgb: 152, 154, 162;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255, 255, 255;
  --ion-color-shade: #585858;
  --ion-color-tint: #505050;
}

.ion-color-orange {
  --ion-color-base: #144a7c;
  --ion-color-base-rgb: 152, 154, 162;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255, 255, 255;
  --ion-color-shade: #df7721;
  --ion-color-tint: #e28d2c;
}

.ion-color-darkRed1 {
  --ion-color-base: #e9442f;
  --ion-color-base-rgb: 255, 255, 0;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 0, 0, 0;
  --ion-color-shade: #af2b19;
  --ion-color-tint: #c9422a;
}

.help-button-style {
  padding-right: 5px !important;
  padding-left: 5px !important;
  font-size: x-large !important;
  color: var(--ion-color-primary)
}

.icon-style-info {
  cursor: pointer;
  font-size: 2vh !important;
  color: #0057b3;
}

.ion-color-darkMedium2 {
  --ion-color-base: #4d4b4b;
  --ion-color-base-rgb: 152, 154, 162;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255, 255, 255;
  --ion-color-shade: #585858;
  --ion-color-tint: #505050;
}

.contact-details {
  position: relative;
  bottom: 15px;
  width: 100%;
  padding-right: 40px;
}

.image-style {
  height: 22px;
  width: 22px;
}

.bottom-bar-image-style {
  height: 17px !important;
  width: 16px;
  padding-bottom: 2px;
}

@media screen and (min-width: 501px) and (min-height: 501px) {
  .icon-style {
    font-size: 22px !important;
  }

  .icon-style-help {
    font-size: 2.9vh !important;
  }

  .fa-2x {
    font-size: 3em !important;
  }

  .vertical-center {
    margin: auto !important;
  }

  .image-style {
    height: 25px;
    width: 25px;
    margin-right: 31px;
    margin-left: 0px;
  }

  .bottom-bar-image-style {
    height: 22px !important;
    width: 22px;
  }
}

@media screen and (min-width: 601px) and (min-height: 501px) {
  .icon-style-other {
    font-size: 2.2vh !important;
  }

  .image-style {
    height: 25px;
    width: 25px;
    margin-right: 31px;
    margin-left: 0px;
  }

  .bottom-bar-image-style {
    height: 22px !important;
    width: 22px;
  }
}

@media screen and (min-height:600px) {
  .contact-details {
    position: relative;
    bottom: 15px;
    width: 100%;
    padding-right: 40px;
  }

  .curl {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    background:
      linear-gradient(135deg,
        #fff,
        #f3f3f3 45%,
        #ddd 50%,
        #aaa 50%,
        #bbb 56%,
        #ccc 62%,
        #f3f3f3 80%,
        #fff 100%);
    box-shadow: 0 0 10px rgba(0, 0, 0, .5);
    transition: all .5s ease;
  }

  .curl:before,
  .curl:after {
    content: '';
    position: absolute;
    z-index: -1;
    left: 12.5%;
    bottom: 5.8%;
    width: 70%;
    max-width: 300px;
    max-height: 100px;
    height: 55%;
    box-shadow: 0 12px 15px rgba(0, 0, 0, .3);
    transform: skew(-10deg) rotate(-6deg);
  }

  .curl:after {
    left: auto;
    right: 5.8%;
    bottom: auto;
    top: 14.16%;
    transform: skew(-15deg) rotate(-84deg);
  }

  .curl:hover {
    width: 240px;
    height: 240px;
  }

  .curl:hover:before,
  .curl:hover:after {
    box-shadow: 0 24px 30px rgba(0, 0, 0, .3);
  }
}


#yellow {
  // position: relative;
  position: absolute;
  bottom: inherit;
  right: inherit;
  width: 100px;
  height: 100px;
  // background: url(https://dcassetcdn.com/profile_pics/12520/12520_thumbnail_100px_201403020352.jpg) no-repeat scroll 0% 0%;
  background: repeat scroll 0% 0%;
  border: 0px none transparent;
  -webkit-transition: 3s;
  /* For Safari 3.1 to 6.0 */
  transition: width .5s, height .53s;
}

.help-block {
  z-index: 99999 !important;
}

.help-block-main {
  z-index: 88888 !important;
}

#yellow:hover {
  border: 0px none transparent;
  width: 150px;
  height: 150px;
}

#yellow:hover #red {
  opacity: 1;
  transition: opacity 0.1s;
}

// #red {
//   position: absolute;
//   background: linear-gradient(135deg, rgba(255, 255, 255, 0), rgba(243, 243, 243, 0.3) 45%, rgba(221, 221, 221, 0.3) 50%, rgb(170, 170, 170) 50%, rgb(187, 187, 187) 56%, rgb(204, 204, 204) 62%, rgb(243, 243, 243) 80%, rgb(255, 255, 255) 100%) repeat scroll 0% 0%, transparent  no-repeat scroll 0% 0%;
//   width: 100%;
//   height: 100%;
//   opacity: 0;
//   border: 0px none transparent;
//   transition: opacity 0.1s 2.9s;
// }

#red {
  position: absolute;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(243, 243, 243, 0.65) 45%, rgba(221, 221, 221, 0.3) 50%, rgb(170, 170, 170) 50%, rgb(187, 187, 187) 56%, rgb(204, 204, 204) 62%, rgb(243, 243, 243) 80%, rgb(255, 255, 255) 100%) repeat scroll 0% 0%, transparent no-repeat scroll 0% 0%;
  width: 100%;
  height: 100%;
  // opacity: 0;
  // border: 0px none transparent;
  // transition: opacity 0.1s 2.9s;
}

// #yellow:hover #red {
//   -webkit-animation: mymove 1s 1; /* Safari 4.0 - 8.0 */
//   animation: mymove 1s 1;
//   animation-iteration-count: 1;
//   animation-delay: .5s;
// }

@keyframes mymove {
  from {
    bottom: 0px;
    right: 0px;
  }

  to {
    bottom: 100%;
    right: 100%;
  }
}

#corner {
  position: absolute;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0), rgba(243, 243, 243, 0.3) 45%, rgba(221, 221, 221, 0.3) 50%, rgb(170, 170, 170) 50%, rgb(187, 187, 187) 56%, rgb(204, 204, 204) 62%, rgb(243, 243, 243) 80%, rgb(255, 255, 255) 100%) repeat scroll 0% 0%, transparent repeat scroll 0% 0%;
  width: 100%;
  height: 100%;
  opacity: 0;
  border: 0px none transparent;
  transition: opacity 0.1s 2.9s;
}

// #corner {
//   position: absolute;
//   background: linear-gradient(135deg, rgba(255, 255, 255, 0), rgba(243, 243, 243, 0.3) 45%, rgba(221, 221, 221, 0.3) 50%, rgb(170, 170, 170) 50%, rgb(187, 187, 187) 56%, rgb(204, 204, 204) 62%, rgb(243, 243, 243) 80%, rgb(255, 255, 255) 100%) repeat scroll 0% 0%, transparent repeat scroll 0% 0%;
//   width: 100%;
//   height: 100%;
//   opacity: 0;
//   border: 0px none transparent;
//   transition: opacity 0.1s 2.9s;
// }


#yellow:hover #red {
  opacity: 1;
  transition: opacity 0.1s;
}



.container {
  width: 800px;
  margin: 30px auto;
}

.btn-hvr {
  background: #e1e1e1 none repeat scroll 0 0;
  border: 0 none;
  color: #666;
  cursor: pointer;
  display: inline-block;
  line-height: 1 !important;
  margin-top: 6px;
  padding: 14px;
  text-decoration: none;
}

/* curl top left */
.hvr-curl-top-left {
  backface-visibility: hidden;
  /* hide the back side of rotate dive */
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  display: inline-block;
  position: relative;
  transform: translateZ(0px);
  vertical-align: middle;
}

.hvr-curl-top-left::before {
  background: rgba(0, 0, 0, 0) linear-gradient(135deg, white 45%, #aaaaaa 50%, #cccccc 56%, white 80%) repeat scroll 0 0;
  /*display smooth transitions between two or more specified colors */
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
  content: "";
  height: 0;
  left: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  transition-duration: 0.3s;
  /* time duration */
  transition-property: width, height;
  width: 0;
  z-index: 1000;
}

.hvr-curl-top-left:hover::before,
.hvr-curl-top-left:focus::before,
.hvr-curl-top-left:active::before {
  height: 25px;
  width: 25px;
}

/* curol top right */
.hvr-curl-top-right {
  backface-visibility: hidden;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  display: inline-block;
  position: relative;
  transform: translateZ(0px);
  vertical-align: middle;
}

.hvr-curl-top-right::before {
  background: rgba(0, 0, 0, 0) linear-gradient(225deg, white 45%, #aaaaaa 50%, #cccccc 56%, white 80%) repeat scroll 0 0;
  /*display smooth transitions between two or more specified colors */
  box-shadow: -1px 1px 1px rgba(0, 0, 0, 0.4);
  content: "";
  height: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  transition-duration: 0.3s;
  transition-property: width, height;
  width: 0;
}

.hvr-curl-top-right:hover::before,
.hvr-curl-top-right:focus::before,
.hvr-curl-top-right:active::before {
  height: 25px;
  width: 25px;
}

/* curl buttom right */
.hvr-curl-bottom-right {
  backface-visibility: hidden;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  display: inline-block;
  position: relative;
  transform: translateZ(0px);
  vertical-align: middle;
}

.hvr-curl-bottom-right::before {
  background: rgba(0, 0, 0, 0) linear-gradient(315deg, white 45%, #aaaaaa 50%, #cccccc 56%, white 80%) repeat scroll 0 0;
  /*display smooth transitions between two or more specified colors */
  bottom: 0;
  box-shadow: -1px -1px 1px rgba(0, 0, 0, 0.4);
  content: "";
  height: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  transition-duration: 0.3s;
  transition-property: width, height;
  width: 0;
}

.hvr-curl-bottom-right:hover::before,
.hvr-curl-bottom-right:focus::before,
.hvr-curl-bottom-right:active::before {
  height: 25px;
  width: 25px;
}

/*curl bottom left */
.hvr-curl-bottom-left {
  backface-visibility: hidden;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  display: inline-block;
  position: relative;
  transform: translateZ(0px);
  vertical-align: middle;
}

.hvr-curl-bottom-left::before {
  background: rgba(0, 0, 0, 0) linear-gradient(45deg, white 45%, #aaaaaa 50%, #cccccc 56%, white 80%) repeat scroll 0 0;
  /*display smooth transitions between two or more specified colors */
  bottom: 0;
  box-shadow: 1px -1px 1px rgba(0, 0, 0, 0.4);
  content: "";
  height: 0;
  left: 0;
  pointer-events: none;
  position: absolute;
  transition-duration: 0.3s;
  /* time duration */
  transition-property: width, height;
  width: 0;
}

.hvr-curl-bottom-left:hover::before,
.hvr-curl-bottom-left:focus::before,
.hvr-curl-bottom-left:active::before {
  height: 25px;
  width: 25px;
}

#overlay {
  position: fixed;
  display: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 2;
  cursor: none;
}

// ion-popover {
// 	--width: 320px;
// }

ion-popover {
  .popover-wrapper {
    .popover-content {
      width: 75vw;
      max-width: 700px;

      ion-select-popover {
        overflow: scroll !important;

        ion-list {
          ion-radio-group {
            ion-item {
              display: block !important;

            }
          }
        }
      }
    }
  }
}

ion-select {
  --placeholder-opacity: 0.5 !important;
  --placeholder-color: balck !important;
}

ion-back-button {
  --color-hover: #0057b3 !important;
}

.selectError {
  border: 1px solid rgb(221, 82, 82);
}

.selectNoError {
  border: 1px solid rgb(161, 160, 160);
}

ion-textarea {
  --padding-top: 5px;
  --padding-start: 5px;
}

mat-error {
  background: white !important;
  font-size: 14px !important;
}

// override material design input field styling for readonly mode
.mat-form-field-disabled .mat-form-field-underline {
  height: 0;
}

.mat-input-element:disabled {
  color: black !important;
  opacity: 1 !important;
}

tooltip-box:not(.customHelpBtn>tooltip-box) {
  top: unset !important;
  margin-top: 50px !important;
  position: absolute !important;
}

tooltip-box.arrow-bottom:not(.customHelpBtn>tooltip-box.arrow-bottom) {
  top: unset !important;
  margin-top: -200px !important;
  position: absolute !important;
}

.waitImage {
  img {
    width: 150px;
    display: block;
    margin: auto;
  }
}

.picker-columns {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.tooltip-selector-repair .arrow-bottom {
  top: unset !important;
  margin-top: -110px !important;
  position: absolute !important;
}

.winches-dropdown .arrow-top {
  top: unset !important;
  margin-top: 5px !important;
  position: absolute !important;
}

.tooltip-selector-winches-tail .arrow-bottom {
  top: unset !important;
  margin-top: -170px !important;
  position: absolute !important;
}

ion-label.sc-ion-select-popover-md {
  white-space: pre-line !important;
}

ion-label.sc-ion-select-popover-ios {
  white-space: pre-line !important;
}

.toastAfterHeader {
  position: fixed;
  bottom: 96px;
  --background: #FF4127;
  --border-color: #852114;
  --border-radius: 1px;
  --border-style: solid;
  --border-width: 4px;
  --button-color: #FFDD00;
  --color: #fff;
}

.picker-opts {
  max-width: 70px !important;
}

.severTooltip>tooltip-box {
  margin-top: 30px !important;
}

ion-select.areaAffectedSelect {
  margin-top: 16px !important;
  height: 44px;
  border: 1px solid rgb(185, 184, 184);
  padding: 0px 10px
}

.login-tooltip {
  width: 280px !important;
  background-color: #2f313b !important;
  font-size: 20px !important;
  max-width: 280px !important;
  margin-left: 0px !important;
  margin-left: -webkit-calc(100vw - 50vw) !important;
  margin-left: -moz-calc(100vw - 50vw) !important;
  margin-left: calc(100vw - 50vw) !important;
}

.observation-tooltip {
  background-color: #2f313b !important;
  font-size: 20px !important;
  margin-left: 80vw !important;
  // margin-left: vw !important;
  // margin-left:  calc(100vw - 20vw) !important;
}

.certToastClass {
  text-align: center !important;
}

// @media only screen and (min-width: 481px) {
//   .resultmodal .modal-wrapper {
//     height: 80% !important;
//   }
// }

@media only screen and (max-width:481px) {
  .specimenModal .modal-wrapper {
    height: 85% !important;
  }
}

.specimenModal .modal-wrapper {
  border-radius: 10px;
}

.custom-loader {
  --spinner-color: #08af08;
}

.custom-loader1 {
  --spinner-color: #ff0077;
}

.customConfiguration {
  --height: 100% !important;
  --width: 100% !important;
  box-shadow: #000000 0px 0px 29px 6px;
}

.customConfigurationCamera {
  --height: 100% !important;
  --width: 100% !important;
  .inner-scroll {
    background: transparent !important;
  }
}

ion-select.unitMeasure {
  margin-top: -5px !important;
  padding: 0px 10px
}

.circle-progress-value {
	stroke-width: 6px;
	stroke: hsl(39, 100%, 50%);
}
.circle-progress-circle {
	stroke-width: 6px;
	stroke: hsl(39, 100%, 85%);
}
.circle-progress-text {
	fill: hsl(39, 100%, 50%);
}

// !this is added to wrap the label inside ion-select-option if it's overflow
.customPopover ion-checkbox::part(label) {
  white-space: normal !important;
}

.insightAIGuidePrompt, .insightAIResultModal {
  --height: 100% !important;
  --width: 100% !important;
}