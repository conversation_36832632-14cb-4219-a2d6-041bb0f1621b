import { Component, OnInit } from '@angular/core';
import { DataService } from '../services/data.service';
import { UtilserviceService } from '../services/utilservice.service';
import { MenuController, ModalController } from '@ionic/angular';
import { HelpService } from '../services/help.service';
import { UserPreferenceService } from '../services/user-preference.service';
import { Router } from '@angular/router';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from '../services/alert.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faListCheck, faGrip, faEnvelope } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from '../services/platform.service';
@Component({
  selector: 'app-detailed-routine-inspection',
  templateUrl: './detailed-routine-inspection.page.html',
  styleUrls: ['./detailed-routine-inspection.page.scss'],
})
export class DetailedRoutineInspectionPage implements OnInit {

  selectedIndustry : any;
  isVesselMoornigOrUtility:boolean = false;
  insightAIEnabledForUser : boolean = false;
  selectedUser : any;
  isRoutineInspectionEnabled:boolean = false;
  platformId: string = this.platformService.getPlatformId();

  
  // industryList: any;
  // assetList: any;
  constructor(private route: Router,
    public platformService: PlatformService,
    public device: Device,
    private utilService: UtilserviceService,
    public menu: MenuController,
    public helpService: HelpService,
    public dataService: DataService,
    private userPreferenceService:UserPreferenceService,
    private unviredCordovaSDK : UnviredCordovaSDK,
    private alertService:AlertService,
    private translate:TranslateService,
    private faIconLibrary: FaIconLibrary,
    private modalController: ModalController
  ) { 
    this.faIconLibrary.addIcons(faBars, faListCheck, faGrip, faEnvelope)
  }

  async ngOnInit() {
    this.selectedIndustry = await this.userPreferenceService.getUserPreference("industry")
    this.selectedIndustry = this.selectedIndustry != '' ? JSON.parse(this.selectedIndustry) : '';
    console.log(this.selectedIndustry.NAME);
    this.isVesselMoornigOrUtility = (this.selectedIndustry && (this.selectedIndustry?.NAME == 'Vessel Mooring' || this.selectedIndustry?.NAME.toLowerCase().indexOf(('utility').toLowerCase()) > -1));
    this.userEnabledForInsightAI();
    this.userEnabledForRoutineInspection();
  }

  async userEnabledForRoutineInspection() {
    var userHeaderRes = await this.dataService.getUserHeader();
    if (userHeaderRes.type == ResultType.success) {
      this.selectedUser = userHeaderRes.data[0];
      if(this.selectedUser.EXTFLD2 == "true") {
        this.isRoutineInspectionEnabled = true;
      }
    } else {
      this.unviredCordovaSDK.logError("create-inspection", "getUserHeader", "Error while getting error from db" + JSON.stringify(userHeaderRes))
    }
  }

  async userEnabledForInsightAI() {
    let insightAIEnabled = await this.dataService.userEnabledForInsightAI();
    if(insightAIEnabled) {
      this.insightAIEnabledForUser = insightAIEnabled;
      // this.dataService.isUserEnabledForInsightAI = false;
      console.log(insightAIEnabled);
    }
    // var userHeaderRes = await this.dataService.getUserHeader();
    // if (userHeaderRes.type == ResultType.success) {
    //   this.selectedUser = userHeaderRes.data[0];
    //   if(this.selectedUser.ICARIA_PROGRAM == 'Premium' ) {
    //     this.isPremiumUser = true;
    //   }
    // } else {
    //   this.unviredCordovaSDK.logError("create-inspection", "getUserHeader", "Error while getting error from db" + JSON.stringify(userHeaderRes))
    // }
  }

  async detailedInspection() {
    this.dataService.setConfigurationOption();
    this.dataService.setLastUsedAccount();
    this.dataService.setLastUsedAsset();
    this.dataService.setLastUsedWorkOrder();
    this.route.navigate(['inspection-home']);
  }

  routineInspection() {
    this.dataService.setConfigurationOption();
    this.dataService.setLastUsedAccount();
    this.dataService.setLastUsedAsset();
    this.dataService.setLastUsedWorkOrder();
    this.route.navigate(['routine-inspection-home']);
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  //Exit help mode
  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  goTOVisionAI() {
    this.route.navigate(['insight-AI-home']);
  }


  // async getIndstryHeader() {
  //   let industryListRes = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INDUSTRY_HEADER ORDER BY NAME COLLATE NOCASE ASC")
  //   if (industryListRes.type == ResultType.success) {
  //     if (industryListRes.data.length > 0) {
  //       this.industryList = industryListRes.data;
  //     } else {
  //       this.industryList = [{ NAME: "", VALUE: "No Data Found" }]
  //     }
  //   } else {
  //     this.unviredCordovaSDK.logError("create-inspection", "getIndstryHeader", "Error while getting error from db" + JSON.stringify(industryListRes))
  //     this.alertService.showAlert("Error!",this.translate.instant("Error while getting data from db"));
  //   }
  //   // this.getDiaUOM();
  // }

}
