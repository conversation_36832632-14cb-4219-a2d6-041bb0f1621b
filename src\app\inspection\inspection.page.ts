import { Component, Ng<PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, NotificationListenerType, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { PopoverController, MenuController, AlertController, Platform, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AppConstant } from 'src/constants/appConstants';
import { INSPECTION_REPORT_ATTACHMENT } from 'src/models/INSPECTION_REPORT_ATTACHMENT';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { FileTransfer, FileUploadOptions, FileTransferObject } from '@awesome-cordova-plugins/file-transfer/ngx';
import { HttpClient, HttpHeaders  } from '@angular/common/http';
import { InsppopoverPage } from '../insppopover/insppopover.page';
import { TrashComponent } from '../components/trash/trash.component';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faListCheck, faGrip, faEnvelope, faMagnifyingGlass, faEye, faTrash, faTrashCan, faCircle as fasCircle, faCircleCheck, faRotateRight, faCircleInfo } from '@fortawesome/free-solid-svg-icons';
import { faCircle as farCircle } from '@fortawesome/free-regular-svg-icons';
import { UserPreferenceService } from '../services/user-preference.service';
import { FileOpenerService } from '../services/file-opener.service';
declare var cordova: any;
declare var FileOpener2: any;
declare global{
  interface Navigator{
     msSaveBlob:(blob: Blob,fileName:string) => boolean
     }
  }


@Component({
  selector: 'app-inspection',
  templateUrl: './inspection.page.html',
  styleUrls: ['./inspection.page.scss'],
})
export class InspectionPage implements OnInit {

  inspectionType;
  inProgressTrashArray: any = [];
  completedTrashArray: any = [];
  searchbar = false;
  searchIcon = true;
  title = true;
  historyList;
  tempArray = [];
  view;
  search = true;
  inspTitle = '';
  completedArray = [];
  noHistory = false;
  flag = true;
  syncDisable = false;
  dataBackupCompleted: any;
  dataBackupInProgress: any;
  syncButtonClicked: boolean = false;
  selectedUser: any;
  isLoggedInUserEmployee: boolean = false;
  constructor(
    private route: Router,
    public unviredCordovaSDK: UnviredCordovaSDK,
    public alertService: AlertService,
    private service: UtilserviceService,
    private translate: TranslateService,
    private popoverController: PopoverController,
    private menu: MenuController,
    public helpService: HelpService,
    private alertController: AlertController,
    public dataService: DataService,
    public ngZone: NgZone,
    public platform: Platform,
    public file: File,
    public device: Device,
    public transfer: FileTransfer,
    private http: HttpClient,
    public toastController: ToastController,
    public iab: InAppBrowser, 
    private screenOrientation: ScreenOrientation,
    public faIconLibrary: FaIconLibrary,
    private userPreferenceService:UserPreferenceService,
    private fileOpenerService: FileOpenerService
  ) {
      
      this.faIconLibrary.addIcons(faBars, faListCheck, faGrip, faEnvelope,faMagnifyingGlass,fasCircle,faEye, faTrash,faTrashCan, farCircle, faCircleCheck, faRotateRight, faCircleInfo)
      this.unviredCordovaSDK.registerNotifListener().subscribe(async (result) => {
      // await this.alertService.dismiss();
      this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", JSON.stringify(result));
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.dataChanged:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.dataSend:
          this.syncButtonClicked = false;
          this.refreshData();
          break;
        case NotificationListenerType.appReset:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          this.refreshData();
          // this.syncButtonClicked = false;
          // this.dataService.clearDataRefresh();         
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.infoMessage:
          this.dataService.clearDataRefresh();
          this.refreshData();
          this.handleInfoMessage(result);
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.serverError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        default:
        // this.syncButtonClicked = false;
        // this.dataService.clearDataRefresh();
        // this.refreshData();
        // break;
      }
    });

    // event.subscribe('obj', (data) => {
    //   this.historyList.unshift(data);
    //   this.popoverController.dismiss();
    // });

    // event.subscribe('obj1', (data) => {
    //   this.completedArray.unshift(data);
    //   this.popoverController.dismiss();
    // });
    this.unviredCordovaSDK.logInfo("INSPECTION","SHOWADVANCEASSISTENCEPAGE","CONSTRUCTOR")
  }

  public handleInfoMessage(result) {

    this.unviredCordovaSDK.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });

    this.showAlert(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }


  async markAsDeleted(item) {
    var whereClause = "INSPECTION_ID like '" + item.INSPECTION_ID + "'"    
    var res = await  this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, whereClause)
    // await this.unviredCordovaSDK.deleteOutBoxEntry(item.LID)
    if(res.type == ResultType.success) {
      if(res.data.length > 0) {
        item = res.data[0]
      }
    }
    item.IS_DELETED = 'X'
    return await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_INSPECTION_HEADER, item, whereClause)

  }

  async getInitialData() {
    let whrHistory = "INSPECTION_STATUS IN ('" + AppConstant.IN_PROGRESS + "', '" + AppConstant.REOPENED + "') AND IS_DELETED = 'X'"
    var result = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, whrHistory);
    if (result.type == ResultType.success) {
      console.log(JSON.stringify(result))
      this.ngZone.run(() => {
        this.inProgressTrashArray = result.data;
      })
      this.service.setTrashInspectionItems(this.inProgressTrashArray)
    }
    
    let whrCompleted = "INSPECTION_STATUS like '" + AppConstant.COMPLETED + "' AND IS_DELETED = 'X'"
    result = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, whrCompleted);
    if (result.type == ResultType.success) {
      console.log(JSON.stringify(result))
      this.ngZone.run(() => {
        this.completedTrashArray = result.data;
      })
      this.service.setHistoricalTrashInspectionItems(this.completedTrashArray)
    }
  }


  initializeItems() {
    this.historyList = this.dataBackupInProgress;
    this.completedArray = this.dataBackupCompleted;
  }

  async fetchDataFromDb(mode: string) {
    let selectedAccount = await this.userPreferenceService.getUserPreference('account');
    let selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    var whereClause = "";
    if (mode == 'in-progress') {
      if(this.isLoggedInUserEmployee) {
        whereClause = "(INSPECTION_STATUS like '" + AppConstant.IN_PROGRESS + "' OR INSPECTION_STATUS like '" + AppConstant.READY + "' OR INSPECTION_STATUS like '" + AppConstant.REOPENED +"') AND (IS_DELETED IS NULL OR IS_DELETED = '') AND ACCOUNT_ID="+`'${JSON.parse(selectedAccount).ID}'`+" AND ASSET_ID="+`'${JSON.parse(selectedAsset).ID}'`+" ORDER BY EXT_FLD2 DESC"
      } else {
        whereClause = "(INSPECTION_STATUS like '" + AppConstant.IN_PROGRESS + "' OR INSPECTION_STATUS like '" + AppConstant.READY + "' OR INSPECTION_STATUS like '" + AppConstant.REOPENED +"') AND (IS_DELETED IS NULL OR IS_DELETED = '') ORDER BY EXT_FLD2 DESC"
      }
    } else {
      if(this.isLoggedInUserEmployee) {
        whereClause = "INSPECTION_STATUS like '" + AppConstant.COMPLETED + "' AND (IS_DELETED not like 'X'  OR IS_DELETED is null) AND ACCOUNT_ID="+`'${JSON.parse(selectedAccount).ID}'`+" AND ASSET_ID="+`'${JSON.parse(selectedAsset).ID}'`+" ORDER BY EXT_FLD2 DESC"
      } else {
        whereClause = "INSPECTION_STATUS like '" + AppConstant.COMPLETED + "' AND (IS_DELETED not like 'X'  OR IS_DELETED is null)  ORDER BY EXT_FLD2 DESC"
      }
    }
    var result = await this.getSelectedFieldInDb()
    if (result.type = ResultType.success) {
      if (result.data.length > 0) {
        this.view = result.data[0].VALUE
      }
    }
    return await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, whereClause)
  }

  async ngOnInit() {
    this.isLoggedInUserEmployee =  this.dataService.selectedRole=='Employee' ? true : false;
    this.unviredCordovaSDK.logInfo("INSPECTION","SHOWADVANCEASSISTENCEPAGE","INITIATING INSPECTION PAGE")
    this.view = "APPLICATION"
    this.inspectionType = this.service.getInspState();
    if (this.inspectionType === 'open') {
      this.search = true;
      this.inspTitle = this.translate.instant('In Progress');
      await this.alertService.present()
      var tempResult = await this.fetchDataFromDb("in-progress");
      if (tempResult.type == ResultType.success) {
        this.historyList = tempResult.data;
        tempResult.data.forEach(element => {
          console.log("INSPECTION_STATUS:",element.INSPECTION_STATUS+", SYNC_STATUS:",element.SYNC_STATUS)
        });
        this.dataBackupInProgress = tempResult.data;
      }
      await this.getInitialData()
      await this.alertService.dismiss();
    } else if (this.inspectionType === 'history') {
      this.search = true;
      this.inspTitle = this.translate.instant('Completed');
      // if (this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED || this.selectedInspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
      //   if ((this.selectedInspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.selectedInspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) && this.selectedInspectionHeader.SYNC_STATUS == "3") {
      //     this.readOnly = false;
      //   } else {
      //     this.readOnly = true;
      //   }
      // } else {
      //   this.readOnly = false
      // }
      await this.alertService.present();
      var tempResult = await this.fetchDataFromDb("completed");
      if (tempResult.type == ResultType.success) {
        this.completedArray = tempResult.data;
        this.dataBackupCompleted = tempResult.data;
        for(var x = 0; x < this.completedArray.length; x++) {
          var inspectionReport = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INSPECTION_REPORT_ATTACHMENT WHERE FID IN ( SELECT LID from INSPECTION_REPORT_HEADER where INSPECTION_REPORT_HEADER.INSP_ID = '" + this.completedArray[x].INSPECTION_ID + "' AND REPORT_TYPE = 'S')")
          console.log(inspectionReport.data)
          if(inspectionReport.type == ResultType.success) {
            if(inspectionReport.data.length > 0) {
              this.completedArray[x].ATTACHMANT_REPORT = inspectionReport.data[0];
            } else {
              this.completedArray[x].ATTACHMANT_REPORT = {};
            }
          }
          var advancedInspectionReport = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INSPECTION_REPORT_ATTACHMENT WHERE FID IN ( SELECT LID from INSPECTION_REPORT_HEADER where INSPECTION_REPORT_HEADER.INSP_ID = '" + this.completedArray[x].INSPECTION_ID + "' AND REPORT_TYPE = 'A')")
          console.log(advancedInspectionReport.data)
          if(advancedInspectionReport.type == ResultType.success) {
            if(advancedInspectionReport.data.length > 0) {
              this.completedArray[x].ATTACHMANT_ADVANCED_REPORT = advancedInspectionReport.data[0];
            } else {
              this.completedArray[x].ATTACHMANT_ADVANCED_REPORT = {};
            }
          }
          var paymentStatus = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM PAYMENT_STATUS_HEADER WHERE INSP_ID = '" + this.completedArray[x].EXTERNAL_ID + "'")
          console.log(paymentStatus.data)
          if(paymentStatus.type == ResultType.success) {
            if(paymentStatus.data.length > 0) {
              this.completedArray[x].PAYMENT_STATUS = paymentStatus.data[0];
            } else {
              this.completedArray[x].PAYMENT_STATUS = {"PAYMENT_STATUS": 'Awating'};
            }
          }
        }
      }
      await this.getInitialData()
      await this.getLoggedInUser();
      await this.alertService.dismiss();
      // if(this.alertService.isLoading) {
      //   await this.alertService.dismiss();
      // }
    }
    if (this.helpService.helpMode) {
      this.search = true;
    }
    this.unviredCordovaSDK.logInfo("INSPECTION","SHOWADVANCEASSISTENCEPAGE","INITIALIZATION COMPLETE")
    this.unviredCordovaSDK.logInfo("INSPECTION","SHOWADVANCEASSISTENCEPAGE","===== MODE : " + this.inspTitle + " STATE : "  + this.service.getInspState())
    // this.dataService.inspections()
  }
  adHocInspect() {
    this.route.navigate(['newinspection']);
  }
  cancelHistoricalItem(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.initializeItems();
    }
  }
  cancelOpenItem(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.initializeItems();
    }
  }
  toggleSearch() {
    this.searchbar = !this.searchbar;
    this.searchIcon = !this.searchIcon;
    this.title = !this.title;
  }
  searchinProgressItem(ev) {
    this.initializeItems();
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.historyList = this.historyList.filter((item) => {
        return (
          (item.INSPECTION_ID && (item.INSPECTION_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)) ||
          (item.EXTERNAL_ID && item.EXTERNAL_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RPS && item.RPS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RFT_NUM && item.RFT_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.WORK_ORDER && item.WORK_ORDER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ACCOUNT_ID && item.ACCOUNT_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ASSET_ID && item.ASSET_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.MANUFACTURER && item.MANUFACTURER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INDUSTRY && item.INDUSTRY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.APPLICATION && item.APPLICATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_TYPE && item.PRODUCT_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CERTIFICATE_NUM && item.CERTIFICATE_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT && item.PRODUCT.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CODE && item.PRODUCT_CODE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_DESC && item.PRODUCT_DESC.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR && item.COLOR.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR_OTHER && item.COLOR_OTHER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CONSTRUCTION && item.CONSTRUCTION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_CONFIG && item.ORIGINAL_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CONFIG && item.PRODUCT_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM && item.DIAM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM_UOM && item.DIAM_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_LENGTH && item.ORIGINAL_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CURRENT_LENGTH && item.CURRENT_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTED_LENGTH && item.INSPECTED_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LENGTH_UOM && item.LENGTH_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_NOTES && item.INSPECTION_NOTES.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.HAS_CHAFE && item.HAS_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CHAFE_TYPE && item.CHAFE_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.OTHER_CHAFE && item.OTHER_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_JACKETED && item.IS_JACKETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_DATE && item.INSTALLED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_STATUS && item.INSTALLED_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LOCATION_INSTALLED && item.LOCATION_INSTALLED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_STATUS && item.INSPECTION_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.USER_ID && item.USER_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_BY && item.CREATED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_DATE && item.CREATED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_BY && item.LAST_MODIFIED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_DATE && item.LAST_MODIFIED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LINE_POSITION && item.LINE_POSITION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_LOCATION && item.PRODUCT_LOCATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_DELETED && item.IS_DELETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    } else {
      this.initializeItems();
    }
  }
  searchHistoricalItem(ev) {
    this.initializeItems();
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.completedArray = this.completedArray.filter((item) => {
        return (
          (item.INSPECTION_ID && item.INSPECTION_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.EXTERNAL_ID && item.EXTERNAL_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RPS && item.RPS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RFT_NUM && item.RFT_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.WORK_ORDER && item.WORK_ORDER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ACCOUNT_ID && item.ACCOUNT_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ASSET_ID && item.ASSET_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.MANUFACTURER && item.MANUFACTURER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INDUSTRY && item.INDUSTRY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.APPLICATION && item.APPLICATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_TYPE && item.PRODUCT_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CERTIFICATE_NUM && item.CERTIFICATE_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT && item.PRODUCT.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CODE && item.PRODUCT_CODE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_DESC && item.PRODUCT_DESC.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR && item.COLOR.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR_OTHER && item.COLOR_OTHER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CONSTRUCTION && item.CONSTRUCTION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_CONFIG && item.ORIGINAL_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CONFIG && item.PRODUCT_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM && item.DIAM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM_UOM && item.DIAM_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_LENGTH && item.ORIGINAL_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CURRENT_LENGTH && item.CURRENT_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTED_LENGTH && item.INSPECTED_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LENGTH_UOM && item.LENGTH_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_NOTES && item.INSPECTION_NOTES.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.HAS_CHAFE && item.HAS_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CHAFE_TYPE && item.CHAFE_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.OTHER_CHAFE && item.OTHER_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_JACKETED && item.IS_JACKETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_DATE && item.INSTALLED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_STATUS && item.INSTALLED_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LOCATION_INSTALLED && item.LOCATION_INSTALLED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_STATUS && item.INSPECTION_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.USER_ID && item.USER_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_BY && item.CREATED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_DATE && item.CREATED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_BY && item.LAST_MODIFIED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_DATE && item.LAST_MODIFIED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LINE_POSITION && item.LINE_POSITION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_LOCATION && item.PRODUCT_LOCATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_DELETED && item.IS_DELETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    }
  }
  async presentPopover($event: Event): Promise<void> {
    const popover = await this.popoverController.create({
      component: InsppopoverPage,
      componentProps: { page: this },
      event,
      showBackdrop: true,
      animated: true
    });
    await popover.present();
    const result = await popover.onDidDismiss();
    this.view = result.data.key;
    this.setSelectedFieldInDb(this.view)
  }
  async trashPopoverHistory($event: Event): Promise<void> {
    this.service.setTrashMode(false);
    const trashPopover = await this.popoverController.create({
      component: TrashComponent,
      event,
      showBackdrop: true,
      animated: true,
      cssClass: 'pop-over-style'
    });
    await trashPopover.present();
    const result = await trashPopover.onDidDismiss();
    this.completedTrashArray = this.service.getHistoricalTrashInspectionItems();
    if(result.data != undefined ) {      
      if(result.data.event == 'obj') 
      {
        this.historyList.unshift(result.data.item);
        this.popoverController.dismiss();
      } else if(result.data.event == 'obj1')  {
        this.completedArray.unshift(result.data.item);
        this.popoverController.dismiss();
      }
    }
  }
  async trashPopover($event: Event): Promise<void> {
    this.service.setTrashMode(true);
    const popover = await this.popoverController.create({
      component: TrashComponent,
      event,
      showBackdrop: true,
      animated: true,
      cssClass: 'pop-over-style'
    });
    await popover.present();
    const result = await popover.onDidDismiss();
    this.inProgressTrashArray = this.service.getTrashInspectionItems();
    if(result.data != undefined ) {      
      if(result.data.event == 'obj') 
      {
        this.historyList.unshift(result.data.item);
        this.popoverController.dismiss();
      } else if(result.data.event == 'obj1')  {
        this.completedArray.unshift(result.data.item);
        this.popoverController.dismiss();
      }
    }
    // this.view = this.service.getSelectedUserName();
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  plannedInspection() {
    this.route.navigate(['planned-inspection']);
  }

  //Exit help mode
  ionViewDidLeave() {
    this.unviredCordovaSDK.logInfo("INSPECTION","SHOWADVANCEASSISTENCEPAGE","LEAVING SCREEN    MODE : " + this.inspTitle + " STATE : "  + this.service.getInspState())
    this.helpService.exitHelpMode();

  }
  async ionViewDidEnter() {
    await this.getInitialData();
    // if(this.alertService.isLoading) {
      
    // }
    // await this.alertService.dismiss();
    if(this.alertService.isLoading) {
      await this.alertService.dismiss();
    }
    // setTimeout(async() => {
    //   await this.alertService.dismiss();
    // }, 1000);
    
  }

  async viewCompletedInsp(item) {
    this.service.setInspectionReadFlag('readOnly');
    // item.INSPECTION_STATUS = AppConstant.REOPENED;
    this.dataService.setConfigLocationOptions(item);
    this.service.setObservationReadFlag('gg');
    this.service.setSelectedInspectionHeader(item);
    this.dataService.setLocationAndLayer(item);
    this.dataService.setCreatedHeader(item)
    this.dataService.setSelectedUom(item.LENGTH_UOM)
    this.service.setFromInspection(true)
    this.service.resetBaselineArray();
    this.service.setFromBaseline(false)
    this.dataService.showEmail(true);
    if(this.alertService.isLoading) {
      await this.alertService.dismiss();
    }
    this.route.navigate(['observations']);
    // setTimeout(async () => {
      
      
    // }, 200);
  }
async viewInProgressInsp(item) {
    // await this.alertService.present();
    // this.dataService.setLocationAndLayer(item);
    // this.dataService.setConfigLocationOptions(item);this.service.setInspectionReadFlag('rr');
    this.service.setObservationReadFlag('gg');
    this.service.setSelectedInspectionHeader(item);
    this.dataService.setCreatedHeader(item)
    this.dataService.setSelectedUom(item.LENGTH_UOM)
    this.service.resetBaselineArray();
    this.service.setFromBaseline(false)
    // setTimeout(async () => {
      if (item.INSPECTION_STATUS != AppConstant.IN_PROGRESS) {
        this.service.setFromInspection(true)
        this.dataService.setConfigLocationOptions(item);
        this.dataService.setLocationAndLayer(item)
        this.route.navigate(['observations']);
        // if(this.alertService.isLoading) {
        //   await this.alertService.dismiss();
        // }
        // await this.alertService.dismiss();
      } else {
        if (item.CONFIG_STATUS == AppConstant.SELECT_CONFIGURATION) {
          this.service.setFromInspection(true)
          if(this.dataService.getConfigurationOption() == 'yes') {
            this.route.navigateByUrl('/configuration-type')
          } else {
            this.route.navigate(['new-configuration-list']);
          }
          // if(this.alertService.isLoading) {
          //   await this.alertService.dismiss();
          // }
          // await this.alertService.dismiss();
          // return
        } else if (item.CONFIG_STATUS == AppConstant.ADD_MEASUREMENTS) {
          this.service.setFromInspection(true)
          this.dataService.setConfigLocationOptions(item);
          this.route.navigate(['inspection-configuration']);
          // if(this.alertService.isLoading) {
          //   await this.alertService.dismiss();
          // }
          // await this.alertService.dismiss();
          // return
        } else {
          this.service.setFromInspection(true)
          this.dataService.setLocationAndLayer(item)
          this.dataService.setConfigLocationOptions(item);
          this.route.navigate(['observations']);
          // if(this.alertService.isLoading) {
          //   try {
          //     await this.alertService.dismiss();
          //   } catch(err) {
          //     console.log(err);
          //   }
          // }
          // // await this.alertService.dismiss();
          // return
        }
      }
    // }, 200)














    // setTimeout(async () => {
    //   if(item.INSPECTION_STATUS != AppConstant.IN_PROGRESS){
    //     this.route.navigate(['observations']);
    //     this.alertService.dismiss();
    //   } else {
    //   var whereClause = "INSPECTION_ID like '" + item.INSPECTION_ID + "' AND MEAS_TYPE_ID like 'External'"
    //   var measurements = await this.unviredCordovaSDK.dbSelect("MEASUREMENT", whereClause) 
    //     if(measurements.type == ResultType.success) {
    //       if(measurements.data.length <= 1) {
    //         if(measurements.data.length == 1) {
    //             var temp = JSON.parse(measurements.data[0].DATA)
    //             if(temp.external == 0 && (temp.externalDamageType == "" || temp.externalDamageType == null )) {
    //               this.service.setFromInspection(true)
    //               this.route.navigate(['configuration-type']);
    //               this.alertService.dismiss();
    //               return
    //             }   
    //           } else {
    //             this.service.setFromInspection(true)
    //             this.route.navigate(['configuration-type']);
    //             this.alertService.dismiss();
    //             return
    //           }
    //         } 
    //       } else {
    //         this.service.setFromInspection(true)
    //         this.route.navigate(['configuration-type']);
    //         this.unviredCordovaSDK.logError("inspection", "viewInProgressInsp" , JSON.stringify(measurements))
    //       }      
    //       var whereClause = "INSPECTION_ID like '" + item.INSPECTION_ID + "' AND MEAS_TYPE_ID like 'Internal'"
    //           var internal = await this.unviredCordovaSDK.dbSelect("MEASUREMENT", whereClause) 
    //           if(internal.type == ResultType.success) {
    //             if(internal.data.length <= 1) {
    //               if(internal.data.length == 1) {
    //                   var temp = JSON.parse(internal.data[0].DATA)
    //                   if(temp.internal == 0 && (temp.internalDamageType == "" || temp.internalDamageType == null))  {
    //                     this.service.setFromInspection(true)
    //                     this.route.navigate(['configuration-type']);
    //                     this.alertService.dismiss();
    //                     return
    //                   } else {
    //                     this.route.navigate(['observations']);
    //                     this.alertService.dismiss();
    //                     return
    //                   }  
    //                 } else {
    //                   this.service.setFromInspection(true)
    //                   this.route.navigate(['configuration-type']);
    //                   this.alertService.dismiss();
    //                   return
    //                 }
    //               } else {
    //                 this.route.navigate(['observations']);
    //                 this.alertService.dismiss();
    //                 return
    //               }
    //             } else {
    //               this.service.setFromInspection(true)
    //               this.route.navigate(['configuration-type']);
    //               this.alertService.dismiss()
    //               this.unviredCordovaSDK.logError("inspection", "viewInProgressInsp" , JSON.stringify(internal))
    //             }             
    //   }
    // }, 200);
  }
  async inProgressItemTrash(index, item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Inspection'),
      message: '<strong>' + this.translate.instant('You want to delete this Inspection') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: async () => {
            this.icon(index);
            this.service.setTrashInspectionItem(item);
            this.inProgressTrashArray = this.service.getTrashInspectionItems();
            var result = await this.markAsDeleted(item);
            this.historyList.splice(index, 1);
          }
        }
      ]
    });
    await alert.present();
  }
  async historyItemDelete(index, item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Inspection'),
      message: this.translate.instant('<strong>You want to delete this Inspection</strong>!'),
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: async () => {
            this.icon(index);
            await this.markAsDeleted(item)
            this.service.setHistoricalTrashInspectionItem(item);
            this.completedTrashArray = this.service.getHistoricalTrashInspectionItems();
            this.completedArray.splice(index, 1);
          }
        }
      ]
    });
    await alert.present();
  }
  icon(index) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }
  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }

  setSelectedFieldInDb(value: string) {
    this.unviredCordovaSDK.dbInsertOrUpdate("APP_SETTINGS_HEADER", { KEY_FLD: "SELECTED_FIELD", VALUE: value }, true)
  }

  async getSelectedFieldInDb() {
    return await this.unviredCordovaSDK.dbSelect("APP_SETTINGS_HEADER", "KEY_FLD like 'SELECTED_FIELD'")
  }

  async syncInspection(item, index) {
    if (!this.syncButtonClicked) {
      this.syncButtonClicked = true;
      //item.INSPECTION_STATUS = AppConstant.COMPLETED
      var result = await this.submitInspection(item)
      this.dataService.refreshData();
      if (result.type == ResultType.success) {
        const ind = this.tempArray.indexOf(index, 0);
        if (ind > -1) {
          this.tempArray.splice(ind, 1);
        } else {
          this.tempArray.push(index);
        }
        this.refreshData();
        this.presentToast()
        // this.historyList.splice(index, 1)
      }
    }
  }

  submitInspection(item) {
    let inputObject = {
      "INSPECTION_HEADER": item
    }
    return this.unviredCordovaSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_INSPECTION, "INSPECTION", item.LID, false)
  }

  async ionViewWillEnter() {
    this.unviredCordovaSDK.registerNotifListener().subscribe(async (result) => {
      if(this.alertService.isLoading) {
        await this.alertService.dismiss();
      }
      this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", JSON.stringify(result));
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.dataChanged:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.dataSend:
          this.syncButtonClicked = false;
          this.refreshData();
          break;
        case NotificationListenerType.appReset:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          // this.syncButtonClicked = false;
          // this.dataService.clearDataRefresh();   
          this.refreshData();     
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.infoMessage:
          this.dataService.clearDataRefresh();
          this.refreshData();
          this.handleInfoMessage(result);
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.serverError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        // default: 
        //   this.syncButtonClicked = false;
        //   this.dataService.clearDataRefresh();
        //   this.refreshData();
        //   break;
      }
    });
    await this.getSelectedField();
    await this.getLoggedInUser();
    if(this.alertService.isLoading) {
      await this.alertService.dismiss();
    }
    // await this.alertService.dismiss();
  }

  async getSelectedField() {
    var result = await this.getSelectedFieldInDb()
    if (result.type == ResultType.success) {
      if (result.data.length > 0) {
        this.view = result.data[0].VALUE
      }
    }
    if(this.alertService.isLoading) {
      await this.alertService.dismiss();
    }
    await this.alertService.dismiss();
    await this.refreshData();
  }

  async refreshData() {
    // this.presentToast()
    this.inspectionType = this.service.getInspState();
    if (this.inspectionType === 'open') {
      this.inspTitle = this.translate.instant("In Progress");
      var tempResult = await this.fetchDataFromDb("in-progress");
      if (tempResult.type == ResultType.success) {
        this.ngZone.run(() => {
          this.historyList = tempResult.data ;
          this.dataBackupInProgress = tempResult.data;
        })
      }
    } else {
      this.search = true;
      this.inspTitle = this.translate.instant('Completed');
      if(this.alertService.isLoading) {
        await this.alertService.dismiss();
      }
      await this.alertService.present();
      var tempResult = await this.fetchDataFromDb("completed");
      if (tempResult.type == ResultType.success) {
        this.completedArray = tempResult.data;
        this.dataBackupCompleted = tempResult.data;
        for(var x = 0; x < this.completedArray.length; x++) {
          var inspectionReport = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INSPECTION_REPORT_ATTACHMENT WHERE FID IN ( SELECT LID from INSPECTION_REPORT_HEADER where INSPECTION_REPORT_HEADER.INSP_ID = '" + this.completedArray[x].INSPECTION_ID + "' AND REPORT_TYPE = 'S')")
          console.log(inspectionReport.data)
          if(inspectionReport.type == ResultType.success) {
            if(inspectionReport.data.length > 0) {
              this.completedArray[x].ATTACHMANT_REPORT = inspectionReport.data[0];
            } else {
              this.completedArray[x].ATTACHMANT_REPORT = {};
            }
          }
          var advancedInspectionReport = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INSPECTION_REPORT_ATTACHMENT WHERE FID IN ( SELECT LID from INSPECTION_REPORT_HEADER where INSPECTION_REPORT_HEADER.INSP_ID = '" + this.completedArray[x].INSPECTION_ID + "' AND REPORT_TYPE = 'A')")
          console.log(advancedInspectionReport.data)
          if(advancedInspectionReport.type == ResultType.success) {
            if(advancedInspectionReport.data.length > 0) {
              this.completedArray[x].ATTACHMANT_ADVANCED_REPORT = advancedInspectionReport.data[0];
            } else {
              this.completedArray[x].ATTACHMANT_ADVANCED_REPORT = {};
            }
          }
          var paymentStatus = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM PAYMENT_STATUS_HEADER WHERE INSP_ID = '" + this.completedArray[x].EXTERNAL_ID + "'")
          console.log(paymentStatus.data)
          if(paymentStatus.type == ResultType.success) {
            if(paymentStatus.data.length > 0) {
              this.completedArray[x].PAYMENT_STATUS = paymentStatus.data[0];
            } else {
              this.completedArray[x].PAYMENT_STATUS = {"PAYMENT_STATUS": 'Awating'};
            }
          }
        }
      }
      await this.getInitialData()
      await this.getLoggedInUser();
      if(this.alertService.isLoading) {
        await this.alertService.dismiss();
      }
      await this.alertService.dismiss();
    }
  }

  async presentToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("Submitted Inspection"),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async showAlert(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }


  async getInfoMsg(event: any, object: any) {
    var that = this
    if (object.SYNC_STATUS == '3') {
      event.preventDefault();
      event.stopPropagation();
      var result = await this.unviredCordovaSDK.getInfoMessages(AppConstant.TABLE_INSPECTION_HEADER, object.LID)
      if (result.type == ResultType.success) {
        let title = "Info Message";

        if (this.platform.is("android")) {
          if (result.message != "" && result.message != "") {
            that.showAlert(title, result.message);
          } else {
            if (result.data.length > 0) {
              var message = "";
              for (var i = 0; i < result.data.length; i++) {
                message = message + result.data[i].MESSAGE + "<br>"
              }
              that.showAlert(title, message);
            } else {
              that.showAlert(title, "Error while submitting inspection");
            }
          }
        } else if (this.platform.is("ios")) {
          var message = "";
          for (var i = 0; i < result.data.length; i++) {
            message = message + result.data[i].MESSAGE + "<br>"
          }
          that.showAlert(title, message);
        } else {
          if (result.message !== null && result.message !== '') {
            var message = "";
            message = `${message}${result.message}`;
            that.showAlert(title, message.trim());
          } else {
            let resultData = result.data;
            if (resultData !== null && resultData.length > 0) {
              let messageArray: string[] = resultData.map((data: any) => {
                if (data.MESSAGE && data.MESSAGE.trim().length !== 0) {
                  return data.MESSAGE;
                }
              });
              if (messageArray.length > 0) {
                let messageToDisplay: string = messageArray.join(' ').trim();
                that.showAlert(title, messageToDisplay);
              }
            }
          }
        }
      }
      else {
        that.showAlert("Error", JSON.stringify(result));
      }

    } else if (object.SYNC_STATUS == '2') {
      that.showAlert("Info Message", "Message submitted waiting for response")
    } else if (object.SYNC_STATUS == '1') {
      that.showAlert("Info Message", "Message queued for submission")
    } else if (object.SYNC_STATUS == '0') {
      if (object.INSPECTION_STATUS == AppConstant.READY) {
        that.showAlert("Info Message", "Inspection ready for submission")
      }
    }
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  doRefresh(event) {
    this.unviredCordovaSDK.getMessages();
    setTimeout(() => {
      event.target.complete();
    }, 3000);
  }

  async retryPayment(event, inspectionItem) {

    event.preventDefault();
    event.stopPropagation();
    
    // if (this.device.platform != 'browser') {
      var url = "";
    var uri_dec = "";
    this.alertService.present();    
      var inputHeader = {
        PAGE_NAME: "Payments"
      }
      let inputObject = {
        "INPUT_GET_CONNECT_URL": [
          {
            "INPUT_GET_CONNECT_URL_HEADER": inputHeader
          }]
      }
      var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", inputObject, "ROPE_INSPECTIONS_PA_GET_CONNECT_URL", false)
      console.log("SYNC Result " + JSON.stringify(temp))
      this.unlockScreen();
      if (temp.type == ResultType.error) {
        this.alertService.showAlert("Error", temp.message)
        this.unviredCordovaSDK.logError("Contact", "send message ", JSON.stringify(temp))
        if(this.alertService.isLoading) {
          await this.alertService.dismiss();
        }
        await this.alertService.dismiss();
      } else {
        url = temp.data.url
        console.log(temp)
        var decodeString = temp.data
        var decodedJaon = JSON.parse(decodeString)
        url = decodedJaon.pageURL;
        uri_dec = url + "?inspection_id=" + inspectionItem.EXTERNAL_ID + "&customer_number=" + this.selectedUser.ID + "&Product_code=" + inspectionItem.PRODUCT_CODE;
        
      

      // var uri_dec = "http://samsonrope.biz?inspection_id=" + this.inspectionHeader.INSPECTION_ID + "&customer_number=" + this.selectedUser.ID + "&Product_code=" + this.inspectionHeader.PRODUCT;
        if (this.device.platform == "browser") {
          browser = this.iab.create(uri_dec, "_system", "");
          if (browser != null) {
            browser.show();
          }
          if(this.alertService.isLoading) {
            await this.alertService.dismiss();
          }
          browser.on('exit').subscribe(event => {
            console.log("exit " +  event)
            // this.updateInspectionPaymentStatus(this.inspectionHeader)
            // this.setPortrait()
          });
          browser.on('loadstart').subscribe(event => {
            console.log(event);
            if (event && event.url) {
              if (event.url.includes('/success.html?')) {
                setTimeout(() => {
                  browser.close();    
                }, 5000);              
              }
            }
            if (browser != null) {
              browser.show();
            }
          });
        }  else {
        var browser = this.iab.create(uri_dec, "_blank", "");              
        if (browser != null) {
          browser.show();
        }
        if(this.alertService.isLoading) {
          await this.alertService.dismiss();
        }
        await this.alertService.dismiss();
        browser.on('exit').subscribe(event => {
          console.log("exit " +  event)
          // this.updateInspectionPaymentStatus(this.inspectionHeader)
          this.setPortrait()
        });
        browser.on('loadstart').subscribe(event => {
          console.log(event);
          if (event && event.url) {
            if (event.url.includes('/success.html?')) {
              setTimeout(() => {
                browser.close();    
              }, 5000);              
            }
          }
          if (browser != null) {
            browser.show();
          }
        });
      
        if(this.alertService.isLoading) {
          await this.alertService.dismiss();
        }
        await this.alertService.dismiss();
    }
  }
  }

  async refreshPayment(event, inspectionItem) {

    event.preventDefault();
    event.stopPropagation();
    
    if (this.device.platform == 'browser') {    
        this.alertService.present();    
        var input = {
          "externalId" : inspectionItem.EXTERNAL_ID          
        }
        var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", input, "ROPE_INSPECTIONS_PA_FETCH_PAYMENT_STATUS_FOR_INSP", true)
        console.log("SYNC Result " + JSON.stringify(temp))
        if(temp != undefined) {
          if (temp.type == ResultType.error) {
            this.alertService.showAlert("Error", temp.message)
            this.unviredCordovaSDK.logError("Contact", "send message ", JSON.stringify(temp))
            if(this.alertService.isLoading) {
              await this.alertService.dismiss();
            }
            await this.alertService.dismiss();
          } else {      
            if(this.alertService.isLoading) {
              await this.alertService.dismiss();
            } 
          }
        } else {
          this.alertService.showAlert("Error", "Unable to get the Payment Status. Invalid response from server")
          if(this.alertService.isLoading) {
            
          }
          await this.alertService.dismiss();
        }
        this.refreshData();
        this.unviredCordovaSDK.dbSaveWebData()
    } else {
      this.unviredCordovaSDK.getMessages();
    }
  }

  async refreshAdancedReport(event, inspectionItem) {

    if (this.device.platform == 'browser') {
      this.alertService.present();
      var input = {
        "externalId": inspectionItem.EXTERNAL_ID,
        "report": true
      }
      var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", input, "ROPE_INSPECTIONS_PA_FETCH_PAYMENT_STATUS_FOR_INSP", true)
      console.log("SYNC Result " + JSON.stringify(temp))
      if (temp != undefined) {
        if (temp.type == ResultType.error) {
          this.alertService.showAlert("Error", temp.message)
          this.unviredCordovaSDK.logError("Contact", "send message ", JSON.stringify(temp))
          if(this.alertService.isLoading) {
            await this.alertService.dismiss();
            await this.alertService.dismiss();
          }
        } else {
          var attachmentToDownload = new INSPECTION_REPORT_ATTACHMENT();
          var inspectionReport = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INSPECTION_REPORT_ATTACHMENT WHERE FID IN ( SELECT LID from INSPECTION_REPORT_HEADER where INSPECTION_REPORT_HEADER.INSP_ID = '" + inspectionItem.INSPECTION_ID + "' AND REPORT_TYPE = 'A')")
          console.log(inspectionReport.data)
          if (inspectionReport.type == ResultType.success) {
            if (inspectionReport.data.length > 0) {
              if (this.device.platform == 'browser') {
                var userDetails = localStorage.getItem("data")
                var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token");
                var userDecodedString = atob(userDetails);
                console.log(userDecodedString);
                var decodedJson = JSON.parse(userDecodedString);
                // var headers = new HttpHeaders();
                var authdata = 'SAMSON' + '\\' + decodedJson.UserName + ":" + decodedJson.Password;
                console.log("before = " + authdata);
                authdata = btoa(authdata);
                console.log("after auth = " + authdata);
                // headers.append('Accept', 'application/octet-stream');
                // headers.append('Authorization', ' Bearer ' + tempToken);
                // let options = new RequestOptions({ headers: headers, responseType: 'blob' });
                console.log("browser type for download attachment")
                //https://sandbox.unvired.io/UMP/API/v2/applications/ROPE_INSPECTIONS/attachments/E3C5BC792F2A4C62992A76B3DBED070A?frontendUser=<EMAIL>.customer_web
                var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token")
                if (tempToken != '' && tempToken != null) {
                  var encodedString = tempToken.substring(tempToken.indexOf('.') + 1, tempToken.lastIndexOf('.'))
                  var decodedString = atob(encodedString)
                  var decodedJson = JSON.parse(decodedString)
                }
                var umpUrl = this.dataService.getLoggedInUrl()
                var requestURL = umpUrl + '/API/v2/applications/' + AppConstant.APPLICATION_NAME + '/attachments/' + inspectionReport.data[0].UID + '?frontendUser= ' + decodedJson.frontendUser;
                console.log("this.requestURL for browser type for download attachment" + requestURL);
                // this.http.get(requestURL, { headers: headers, responseType: 'blob' }).subscribe(async (data: any) => {
                //   if(this.alertService.isLoading) {
                //     await this.alertService.dismiss();
                //   }
                //   await this.alertService.dismiss();
                //   if (data.text().startsWith('<')) {
                //     this.presentReportToastBrowser("Error Downloading report");
                //   } else {
                //     if (data.status == 202) {
                //       var status = "Processing";
                //       console.log("status text = " + data.statusText + "status code = " + data.status);
                //       var responseMsg = "status text = " + data.statusText + "status code = " + data.status
                //       this.presentReportToastBrowser(responseMsg);
                //     } else if (data.status == 200) {
                //       var status = "Success";
                //       this.saveBlobAsFile(data, inspectionItem.INSPECTION_NAME + ".pdf")
                //       console.log("data.text() = " + data.text());
                //       console.log("status text = " + data.statusText + "status code = " + data.status);
                //       var responseMsg = "File is ready to download"
                //       this.presentReportToastBrowser(responseMsg);
                //     } else {
                //       var responseMsg = "status text = " + data.statusText + "status code = " + data.status
                //       this.presentReportToastBrowser(responseMsg);
                //     }
                //   }
                // },
                //   async error => {
                //     console.log("error in posting" + error.text(), error);
                //     if(this.alertService.isLoading) {
                //       await this.alertService.dismiss();
                //     }
                //     await this.alertService.dismiss();
                //   });
              }
            } else {
              if(this.alertService.isLoading) {
                await this.alertService.dismiss();
              }
              await this.alertService.dismiss();
            }
          } else {
            if(this.alertService.isLoading) {
              await this.alertService.dismiss();
            }
            await this.alertService.dismiss();
          }
          if(this.alertService.isLoading) {
            await this.alertService.dismiss();
          }
          await this.alertService.dismiss();
        }
      } else {
        this.refreshData();
        this.showMessgae(event);
        this.unviredCordovaSDK.dbSaveWebData()
      }      
    } else {
      this.unviredCordovaSDK.getMessages();
      this.showMessgae(event);
    }
  }

  setPortrait(){
    // set to portrait
    this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.PORTRAIT);
  }

  unlockScreen(){
    // allow user rotate
    this.screenOrientation.unlock();
  }

  async getLoggedInUser() {    
    var res = await this.unviredCordovaSDK.dbSelect("USER_HEADER",'')
    if(res.type == ResultType.success) {
      if(res.data.length > 0) {
        this.selectedUser = res.data[0];
      }
    }
  }

  async downloadReport(event, inspectionItem) {

    event.preventDefault();
    event.stopPropagation();
    // if (this.device.platform != 'browser') {
      this.alertService.present();
      console.log("test " + inspectionItem)
      console.log(inspectionItem)
      var attachmentToDownload = new INSPECTION_REPORT_ATTACHMENT();
      var inspectionReport = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INSPECTION_REPORT_ATTACHMENT WHERE FID IN ( SELECT LID from INSPECTION_REPORT_HEADER where INSPECTION_REPORT_HEADER.INSP_ID = '" + inspectionItem.INSPECTION_ID + "' AND REPORT_TYPE = 'S')")
      console.log(inspectionReport.data)
      if (inspectionReport.type == ResultType.success) {
        if (inspectionReport.data.length > 0) {
          if (this.device.platform == 'browser') {
            var userDetails = localStorage.getItem("data")
            var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token");
            var userDecodedString = atob(userDetails);
            console.log(userDecodedString);
            var decodedJson = JSON.parse(userDecodedString); 
            // var headers = new HttpHeaders();
            var authdata = 'SAMSON' + '\\' + decodedJson.UserName + ":" + decodedJson.Password;
            console.log("before = " + authdata);
            authdata = btoa(authdata);
            console.log("after auth = " + authdata);
            // headers.append('Accept', 'application/octet-stream');
            // headers.append('Authorization', ' Bearer ' + tempToken);
            // let options = new RequestOptions({ headers: headers, responseType: 'blob' });
            console.log("browser type for download attachment")
            //https://sandbox.unvired.io/UMP/API/v2/applications/ROPE_INSPECTIONS/attachments/E3C5BC792F2A4C62992A76B3DBED070A?frontendUser=<EMAIL>.customer_web
            var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token")
            if (tempToken != '' && tempToken != null) {
              var encodedString = tempToken.substring(tempToken.indexOf('.') + 1, tempToken.lastIndexOf('.'))
              var decodedString = atob(encodedString)
              var decodedJson = JSON.parse(decodedString)
            }
            var umpUrl = this.dataService.getLoggedInUrl()
            var requestURL =  umpUrl + '/API/v2/applications/' + AppConstant.APPLICATION_NAME + '/attachments/' + inspectionReport.data[0].UID + '?frontendUser= ' + decodedJson.frontendUser;
            console.log("this.requestURL for browser type for download attachment" + requestURL);
            // this.http.get(requestURL, { headers: headers, responseType: 'blob' }).subscribe(async (data: any) => {
            //   if(this.alertService.isLoading) {
            //     await this.alertService.dismiss();
            //   }
            //   await this.alertService.dismiss();
            //   if (data.text().startsWith('<')) {
            //       this.presentReportToastBrowser("Error Downloading report");
            //   } else {
            //     if (data.status == 202) {
            //       var status = "Processing";
            //       console.log("status text = " + data.statusText + "status code = " + data.status);
            //       var responseMsg = "status text = " + data.statusText + "status code = " + data.status
            //       this.presentReportToastBrowser(responseMsg);
            //     }
            //     if (data.status == 200) {
            //       var status = "Success";
            //       this.saveBlobAsFile(data, inspectionItem.INSPECTION_NAME + ".docx")
            //       console.log("data.text() = " + data.text());
            //       console.log("status text = " + data.statusText + "status code = " + data.status);
            //       var responseMsg = "File is ready to download"
            //       this.presentReportToastBrowser(responseMsg);
            //     }
            //   }
            // },
            //   async error => {
            //     console.log("error in posting" + error.text(), error);
            //     if(this.alertService.isLoading) {
            //       await this.alertService.dismiss();
            //     }
            //     await this.alertService.dismiss();
            //   });
          } else {

            if (inspectionReport.data[0].ATTACHMENT_STATUS != "DOWNLOADED" && inspectionReport.data[0].ATTACHMENT_STATUS != 2) {
              attachmentToDownload = inspectionReport.data[0]
              console.log("Attachment download called")
              this.presentReportToast();
              var downloadedAttachment = await this.unviredCordovaSDK.downloadAttachment("INSPECTION_REPORT_ATTACHMENT", attachmentToDownload)
              console.log(downloadedAttachment)
              try {
                if (downloadedAttachment.type == ResultType.success) {
                  if (downloadedAttachment.data.length > 0) {
                    this.openReporDtocument(downloadedAttachment.data[0]);
                  } else {
                    if (this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                    await this.alertService.dismiss();
                  }
                } else {
                  if (this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                  await this.alertService.dismiss();
                }
              } catch (error) {
                if (downloadedAttachment.type == ResultType.error) {
                  console.log("Error in downloading attachment " + downloadedAttachment.message)
                }
                console.log("Error in downloading attachment " + error)
              }
            } else {
              this.openReporDtocument(inspectionReport.data[0]);
            }
          }
        } else {
          if(this.alertService.isLoading) {
            await this.alertService.dismiss();
          }
          await this.alertService.dismiss();
        }
      } else {
        if(this.alertService.isLoading) {
          await this.alertService.dismiss();
        }
        await this.alertService.dismiss();
      }
    // }
  }

  private saveBlobAsFile(response, fileName) {
    if (response.status == 200 || response.status == 202) {
      let blob = response.blob();

      // IE10+ : (has Blob, but not a[download] or URL)
      if (navigator.msSaveBlob) {
        return navigator.msSaveBlob(blob, fileName);
      }

      var url = window.URL.createObjectURL(blob);
      var a = document.createElement("a");
      document.body.appendChild(a);
      a.href = url;
      a.download = fileName || "noname";
      a.click();
      window.URL.revokeObjectURL(url);
    } else if (response.status == 204) {
      return { "error": "Attachment is added in queue.Please retry to download." };
    }

  }

  async downloadAdancedReport(event, inspectionItem) {

    event.preventDefault();
    event.stopPropagation();
    // if(inspectionItem.ADV_SUPRT_STATUS == 'Processed') {
      this.alertService.present()
      setTimeout(async () => {      
      console.log("test " + inspectionItem)
      console.log(inspectionItem)      
      var attachmentToDownload = new INSPECTION_REPORT_ATTACHMENT();
      var inspectionReport = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM INSPECTION_REPORT_ATTACHMENT WHERE FID IN ( SELECT LID from INSPECTION_REPORT_HEADER where INSPECTION_REPORT_HEADER.INSP_ID = '" + inspectionItem.INSPECTION_ID + "' AND REPORT_TYPE = 'A')")
      console.log(inspectionReport.data)
      if(inspectionReport.type == ResultType.success) {
        if(inspectionReport.data.length > 0) {
          if (this.device.platform == 'browser') {
            var userDetails = localStorage.getItem("data")
            var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token");
            var userDecodedString = atob(userDetails);
            console.log(userDecodedString);
            var decodedJson = JSON.parse(userDecodedString); 
            // var headers = new HttpHeaders();
            var authdata = 'SAMSON' + '\\' + decodedJson.UserName + ":" + decodedJson.Password;
            console.log("before = " + authdata);
            authdata = btoa(authdata);
            console.log("after auth = " + authdata);
            // headers.append('Accept', 'application/octet-stream');
            // headers.append('Authorization', ' Bearer ' + tempToken);
            // let options = new RequestOptions({ headers: headers, responseType: ResponseContentType.Blob });
            console.log("browser type for download attachment")
            //https://sandbox.unvired.io/UMP/API/v2/applications/ROPE_INSPECTIONS/attachments/E3C5BC792F2A4C62992A76B3DBED070A?frontendUser=<EMAIL>.customer_web
            var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token")
            if (tempToken != '' && tempToken != null) {
              var encodedString = tempToken.substring(tempToken.indexOf('.') + 1, tempToken.lastIndexOf('.'))
              var decodedString = atob(encodedString)
              var decodedJson = JSON.parse(decodedString)
            }
            var umpUrl = this.dataService.getLoggedInUrl()
            var requestURL = umpUrl + '/API/v2/applications/' + AppConstant.APPLICATION_NAME + '/attachments/' + inspectionReport.data[0].UID + '?frontendUser= ' + decodedJson.frontendUser;
            console.log("this.requestURL for browser type for download attachment" + requestURL);
            // this.http.get(requestURL, { headers: headers, responseType: 'blob' }).subscribe(async (data: any) => {
            //   if(this.alertService.isLoading) {
            //     await this.alertService.dismiss();
            //   }
            //   await this.alertService.dismiss();
            //   if (data.text().startsWith('<')) {
            //       this.presentReportToastBrowser("Error Downloading report");
            //   } else {
            //     if (data.status == 202) {
            //       var status = "Processing";
            //       console.log("status text = " + data.statusText + "status code = " + data.status);
            //       var responseMsg = "status text = " + data.statusText + "status code = " + data.status
            //       this.presentReportToastBrowser(responseMsg);
            //     } else if (data.status == 200) {
            //       var status = "Success";
            //       this.saveBlobAsFile(data, inspectionItem.INSPECTION_NAME + ".pdf")
            //       console.log("data.text() = " + data.text());
            //       console.log("status text = " + data.statusText + "status code = " + data.status);
            //       var responseMsg = "File is ready to download"
            //       this.presentReportToastBrowser(responseMsg);
            //     } else {
            //       var responseMsg = "status text = " + data.statusText + "status code = " + data.status
            //       this.presentReportToastBrowser(responseMsg);
            //     }
            //   }
            // },
            //   async error => {
            //     console.log("error in posting" + error.text(), error);
            //     if(this.alertService.isLoading) {
            //       await this.alertService.dismiss();
            //     }
            //     await this.alertService.dismiss();
            //   });
          } else {
            if(inspectionReport.data[0].ATTACHMENT_STATUS != "DOWNLOADED" && inspectionReport.data[0].ATTACHMENT_STATUS != 2) {
              attachmentToDownload = inspectionReport.data[0]
              console.log("Attachment download called")
              this.presentReportToast();
              var downloadedAttachment = await this.unviredCordovaSDK.downloadAttachment("INSPECTION_REPORT_ATTACHMENT", attachmentToDownload)
              console.log(downloadedAttachment)          
              if(downloadedAttachment.type == ResultType.success) {
                if(downloadedAttachment.data.length > 0) {
                  this.openReporDtocument(downloadedAttachment.data[0]);          
                } else {
                  if(this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                  await this.alertService.dismiss();
                }
              } else {
                if(this.alertService.isLoading) {
                  await this.alertService.dismiss();
                }
                await this.alertService.dismiss();
              }
            } else {
              this.openReporDtocument(inspectionReport.data[0]);
            }
          }
        } else {
          if(this.device.platform == "browser") {
            this.refreshAdancedReport(event, inspectionItem)
          } else {
            if(this.alertService.isLoading) {
              await this.alertService.dismiss();
            }  
            await this.alertService.dismiss();      
            this.showMessgae(event);
          }
        }
      } else {        
        this.showMessgae(event);
        await this.alertService.dismiss();
      }
      if (this.device.platform != 'browser') {
        this.unviredCordovaSDK.getMessages();
        this.refreshData();
      }
    // } else {
    //   this.showMessgae(event);
    // }
  }, 1000);
  }

  async openReporDtocument(attachmentItem) {
    console.log("Opening file")
    
    try {
      const fileName = attachmentItem.fields?.FILE_NAME || attachmentItem.FILE_NAME;
      await this.fileOpenerService.openFile(fileName);
      
      if(this.alertService.isLoading) {
        await this.alertService.dismiss();
      }
    } catch (error) {
      console.log("error opening the inspection report file:", error);
      if(this.alertService.isLoading) {
        await this.alertService.dismiss();
      }
    }
  }

  async presentReportToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("Downloading Report Please Wait."),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async presentReportToastBrowser(message) {
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async showMessgae(event) {

    event.preventDefault();
    event.stopPropagation();
    const alert = await this.alertController.create({
      message: '<p>Your inspection has been submitted to Samson for Enhanced Review. Please allow for 2 business days to process the report. Your temporary report can be found in the Completed section of the app, or in Samson Connect. This report will be updated once Samson has processed your Enhanced Review request.</p>',
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
          }
        }
      ]
    });

    await alert.present();
  }

  async editInspection(item:any) {
    let whereClause = `TAG1 = '${item.INSPECTION_ID}'`;
    let inspReportRes = await this.unviredCordovaSDK.dbSelect('INSPECTION_REPORT_ATTACHMENT',whereClause);
    if(inspReportRes.type==ResultType.success) {
      if(inspReportRes.data.length>0) {
        if(item.ATTACHMANT_REPORT !=undefined && (item.ATTACHMANT_REPORT.ATTACHMENT_STATUS == 2  || item.ATTACHMANT_REPORT.ATTACHMENT_STATUS == 'DOWNLOADED')) {
          await this.reOpenInspection(item);
        } else {
          await this.alertService.showAlert("Warning!","Please wait until the report get's downloaded")
        }
      } else {
        await this.alertService.showAlert("Warning!","Please wait until the report get's downloaded")
      }
    } else {
      await this.reOpenInspection(item)
    }
    
  }

  async reOpenInspection(item) {
    this.alertService.showAlertModal("Warning!","Are you sure you want to edit this inspection?").then(async role=>{
      if(role=='continue') {
        let whereClause = `INSPECTION_REPORT_ATTACHMENT.TAG1 = '${item.INSPECTION_ID}'`;
        let inspeDelRes = await this.unviredCordovaSDK.dbDelete('INSPECTION_REPORT_ATTACHMENT',whereClause);
        if(inspeDelRes.type == ResultType.success) {
          let whereClause1 = `INSPECTION_REPORT_HEADER.INSP_ID = '${item.INSPECTION_ID}'`;
          let inspeDelRes1 = await this.unviredCordovaSDK.dbDelete('INSPECTION_REPORT_HEADER',whereClause1);
          if(inspeDelRes1.type == ResultType.success) {
            let inspObject:any;
            let inspRes1 = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER,`INSPECTION_ID='${item.INSPECTION_ID}'`)
            if(inspRes1.type == ResultType.success) {
              if(inspRes1.data.length>0) {
                let insp = inspRes1.data[0];
                insp.OBJECT_STATUS = 2;
                insp.SYNC_STATUS = 0;
                insp.INSPECTION_STATUS = AppConstant.REOPENED;
                inspObject=insp;
                let inspRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_INSPECTION_HEADER,insp,`INSPECTION_ID ='${item.INSPECTION_ID}'`);
                if(inspRes.type == ResultType.success) {
                  console.log("updated inspection header table")
                } else if(inspRes.type==ResultType.error) {
                  console.log("error updating inspection header table:",inspRes.error)
                }
              }
            } else {
              console.log("error reading inspection header table");
            }
            this.dataService.setConfigLocationOptions(inspObject);
            this.service.setObservationReadFlag('gg');
            this.service.setSelectedInspectionHeader(JSON.parse(JSON.stringify(inspObject)));
            this.dataService.setLocationAndLayer(inspObject);
            this.dataService.setCreatedHeader(inspObject)
            this.dataService.setSelectedUom(inspObject.LENGTH_UOM)
            this.service.setFromInspection(true)
            this.service.resetBaselineArray();
            this.service.setFromBaseline(false)
            
            this.route.navigate(['observations']);
          } else if(inspeDelRes1.type== ResultType.error) {
            console.log("failed to delete the inspection report attachment from database")
          }
        } else if(inspeDelRes.type == ResultType.error) {
          console.log("error deleting from INSPECTION_REPORT_ATTACHMENT table", inspeDelRes.error)
        }
        
      }
    })
  }

  isReportEmpty(obj: any): boolean {
    return Object.keys(obj).length === 0 && obj.constructor === Object;
  }
}