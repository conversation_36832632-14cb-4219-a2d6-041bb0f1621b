<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Data Entry</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" style="height:104%">
    <ion-card (click)="newLmd()"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.imageUrlInspectionNew" style="width: 100%;height: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'New'}} </ion-label>
    </ion-card>

    <ion-card (click)="openLmd()"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img [src]="dataService.imageUrlInspInProgress" style="width: 100%;height: 100%;"/>
      <!-- <ion-badge class="card-badge" slot="end" color="primary" style="font-size:17px; top: 5px;padding-top:7px">{{'0'}}</ion-badge> -->
      <ion-label class="card-title" style="font-size: 17px;"> {{'In Progress'}} </ion-label>
    </ion-card>

    <ion-card (click)="historyLmd()"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/LineTracker_DataEntry_Completed.png" style="width: 100%;height:100%;"/>
      <!-- <ion-badge class="card-badge" slot="end" color="darkGreen" style="font-size:17px;  top: 5px;padding-top:7px">10</ion-badge> -->
      <ion-label class="card-title" style="font-size: 17px;"> {{'Completed'}} </ion-label>
    </ion-card>
  </div>

  <div *ngIf="platformId == 'electron' || device.platform == 'browser'">
    <div class="gridCon"> 
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="newLmd()">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.imageUrlInspectionNewWin" class="imageTag">
          <p class="wrapperLabel">{{'New'}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="historyLmd()">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/op-imgs/Data-Entry_Completed_2024x2024.png" class="imageTag">
          <p class="wrapperLabel">{{'Completed'}}</p>
        </div>
      </div>
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="openLmd()">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.imageUrlInspInProgressWin" class="imageTag">
        <p class="wrapperLabel">{{'In Progress'}}</p>
        </div>
      </div>
    </div>
  </div>
</ion-content>

<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>