<!-- <div class="curl"></div> -->
<ion-header>
  <ion-toolbar>
    <ion-title>{{'Select Observation' | translate}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="card-style" *ngIf="standard.length > 0">
    <ion-card-header class="card-header-style">
      {{'Abrasion Rating' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div *ngIf="noOptions == false">
        <ion-row *ngFor="let row of standard">
          <div *ngFor="let item of row" class="{{item.class}} border-style" (click)='navigateToObservation(item.label)'>
            <div class="tile" style="line-height:unset !important;">
              <div
                style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
                <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                  <div *ngIf="item.icon != ''">
                    <img [src]="item.icon" style="max-width: 40px !important" />
                  </div>
                </div>
                <div style="width:62%; padding-left: 15px; margin: auto !important;">
                  {{item.label}}</div>
                <div style="width: 18% !important; float: right !important">
                  <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">
                    {{item.count}}</ion-badge>
                </div>
              </div>

            </div>
            <!-- <div *ngFor="let item of row" class="{{item.class}} border-style" (click)='presentPopover($event, item.label)'> -->
            <!-- <div class="tile">

                <div style="display:flex !important; text-align: unset !important; padding-top: 10px!important">
                    <div style="width: 40px" style="line-height:unset !important;margin-left: 10px;">
                      <img [src]="item.icon" style="width: 30px !important"/>
                    </div>
                    <div style="padding-left: 15px">
                      {{item.label}}
                    </div>
                    <div style="width: 20px !important; padding-top:8px;">
                      <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}</ion-badge>
                    </div>
                  </div>                  
              <div style="display:flex !important; text-align: unset !important">
                <div style="width: 65%;padding-top:6px">
                  {{item.label}}</div>
                <div style="padding-top:8px;">
                  <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}</ion-badge> -->
            <!-- <ion-badge color="medium" style="background-color: #457599;font-size: 15px;">{{noOfExterals}}</ion-badge> -->
            <!-- </div>
              </div> -->
            <!-- </div> -->
          </div>
          <br>
        </ion-row>
      </div>
      <div *ngIf="noOptions == true">{{'No Options found' | translate}}</div>
    </ion-card-content>
  </ion-card>
  <!-- <ion-card class="card-style">
    <ion-card-header class="card-header-style">
      Compression
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <ion-row *ngFor="let row of compression">
        <div *ngFor="let item of row" class="{{item.class}} border-style">
          <div class="tile">
            <div style="display:flex !important; text-align: unset !important">
              <div style="width: 65%">
                {{item.label}}</div>
              <div style="width:35%; padding-left: 5px">
                <ion-badge color="medium" style="background-color: #00b0ff;">4</ion-badge>
              </div>
            </div>
          </div>
        </div>
        <br>
      </ion-row>
    </ion-card-content>
  </ion-card> -->
  <ion-card ion-card class="card-style" *ngIf="others.length > 0">
    <ion-card-header class="card-header-style">
      {{'Anomaly' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div *ngIf="noOptions == false">
        <ion-row *ngFor="let row of others">
          <div *ngFor="let item of row" class="{{item.class}} border-style" (click)='standardobservation(item.label)'>
            <div class="tile" style="line-height:unset !important;">
              <div
                style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
                <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                  <img [src]="item.icon" style="max-width: 40px !important" />
                </div>
                <div style="width:80%; padding-left: 15px; margin: auto !important;">
                  {{item.label}}</div>
              </div>
            </div>
            <!-- <div class="tile" style="line-height:unset !important;">
            <img [src]="item.icon">
            {{item.label}}
          </div> -->
          </div>
          <br>
        </ion-row>
      </div>
      <div *ngIf="noOptions == true">{{'No Options found' | translate}}</div>
    </ion-card-content>
  </ion-card>

  <ion-card ion-card class="card-style" *ngIf="equipmentInsp.length > 0">
    <ion-card-header class="card-header-style">
      {{'Inspect Hardware' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div *ngIf="noOptions == false">
        <ion-row *ngFor="let row of equipmentInsp">
          <div *ngFor="let item of row" class="{{item.class}} border-style" (click)='navigateToLMD(item.label)'>
            <div class="tile" style="line-height:unset !important;">
              <div
                style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
                <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                  <fa-icon style="height: 100% !important; max-width: 40px !important; font-size: 28px;" [icon]="item.icon"></fa-icon>
                  <!-- <img [src]="item.icon" style="height: 100% !important; max-width: 40px !important" /> -->
                </div>
                <div style="width:80%; padding-left: 15px; margin: auto !important;">
                  {{item.label}}</div>
              </div>
            </div>
            <!-- <div class="tile" style="line-height:unset !important;">
            <img [src]="item.icon">
            {{item.label}}
          </div> -->
          </div>
          <br>
        </ion-row>
      </div>
      <div *ngIf="noOptions == true">{{'No Options found' | translate}}</div>
    </ion-card-content>
  </ion-card>

  <ion-card ion-card class="card-style">
    <ion-card-content class="card-content-style">
      <div *ngIf="others.length == 0 && standard.length == 0">{{'No Options found some data might be missing in the Certificate selected please check Product Type, Construction and Product Name' | translate}}</div>
    </ion-card-content>
  </ion-card>

  <!-- <ion-card ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'Anomaly' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <div *ngIf="noOptions == false">
        <ion-row *ngFor="let row of others1">
          <div *ngFor="let item of row" class="{{item.class}} border-style" (click)='standardobservation(item.label)'>
            <div class="tile" style="line-height:unset !important;">
              <div style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
                <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                  <img [src]="item.icon" style="height: 100% !important; max-width: 40px !important"/>
                </div>
                <div style="width:80%; padding-left: 15px; margin: auto !important;">
                  {{item.label}}</div>
              </div>
            </div>
             <div class="tile" style="line-height:unset !important;">
            <img [src]="item.icon">
            {{item.label}}
          </div> 
          </div>
          <br>
        </ion-row>
      </div>
      <div *ngIf="noOptions == true">{{'No Options found' | translate}}</div>
    </ion-card-content>
  </ion-card> -->
</ion-content>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>