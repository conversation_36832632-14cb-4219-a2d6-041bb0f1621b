import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class INSPECTION_HEADER extends DATA_STRUCTURE {
    INSPECTION_ID: string;
    EXTERNAL_ID: string;
    RPS: string;
    RFT_NUM: string;
    WORK_ORDER: string;
    ACCOUNT_ID: string;
    ASSET_ID: string;
    MANUFACTURER: string;
    INDUSTRY: string;
    CUSTOM_INDUSTRY: string;
    APPLICATION: string;
    CUSTOM_APPLICATION:string;
    PRODUCT_TYPE: string;
    CERTIFICATE_NUM: string;
    PRODUCT: string;
    PRODUCT_CODE: string;
    PRODUCT_DESC: string;
    COLOR: string;
    COLOR_OTHER: string;
    CONSTRUCTION_CLASS: string;
    CONSTRUCTION: string;
    ORIGINAL_CONFIG: string;
    PRODUCT_CONFIG: string;
    DIAM: string;
    DIAM_UOM: string;
    ORIGINAL_LENGTH: number;
    CURRENT_LENGTH: number;
    INSPECTED_LENGTH: number;
    LENGTH_UOM: string;
    INSPECTION_NOTES: string;
    HAS_CHAFE: number;
    CHAFE_TYPE: string;
    OTHER_CHAFE: string;
    IS_JACKETED: number;
    INSTALLED_DATE: string;
    INSTALLED_STATUS: string;
    LOCATION_INSTALLED: string;
    INSPECTION_STATUS: string;
    USER_ID: string;
    CREATED_BY: string;
    CREATED_DATE: string;
    LAST_MODIFIED_BY: string;
    LAST_MODIFIED_DATE: string;
    LINE_POSITION: string;
    PRODUCT_LOCATION: string;
    ASSET_NAME: string;
    ACCOUNT_NAME: string;
    INSPECTION_NAME: string;
    USER_UNIQUE_ID: string;
    START_POINT: string;
    CERT_NAME: string;
    PRODUCT_SIZE_INCH: string;
    PRODUCT_SIZE_MM: string;
    PRODUCT_LEN_FEET: string;
    PRODUCT_LEN_METER: string;
    WORK_ORDER_NUM: string;
    IS_DELETED: string;
    RPS_NAME: string;
    INSP_TYPE: string;
    CONFIG_STATUS: string;
    CONFIG_REFERENCE: string;
    CUSTOMER_WORK_ORDER: string;
    ADV_SUPRT_RQSTD: string;
    ADV_SUPRT_STATUS: string;
    PAYMENT_DATA: string;
    INSPECTION_DATE: string;
    EXT_FLD2: string;
    EXT_FLD3: string; //! using this flag to identify whether the inspection is created with RFT cert or not
    EXT_FLD1: string;
    EXT_FLD4: string;
}