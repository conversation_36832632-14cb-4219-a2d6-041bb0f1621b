import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class CREATE_INSPECTION_HEADER extends DATA_STRUCTURE {
    INSPECTION_ID: string;
    EXTERNAL_ID: string;
    RPS: string;
    RFT_NUM: string;
    WORK_ORDER: string;
    ACCOUNT_ID: string;
    ASSET_ID: string;
    MANUFACTURER: string;
    INDUSTRY: string;
    APPLICATION: string;
    PRODUCT_TYPE: string;
    CERTIFICATE_NUM: string;
    PRODUCT: string;
    PRODUCT_CODE: string;
    PRODUCT_DESC: string;
    COLOR: string;
    COLOR_OTHER: string;
    CONSTRUCTION: string;
    CONFIG_SUPPLIED: string;
    CONFIG_INSPECTION: string;
    DIAM: string;
    DIAM_UOM: string;
    LENGTH_SUPPLIED: number;
    LENGTH_CURRENT: number;
    LENGTH_INSPECTED: string;
    LENGTH_UOM: string;
    SAMPLE_NOTES: string;
    HAS_CHAFE: number;
    CHAFE_STANDARD: string;
    CHAFE_OTHER: string;
    IS_JACKETED: number;
    INSTALLED_DATE: string;
    INSTALLED_STATUS: string;
    LOCATION_INSTALLED: string;
    INSPECTION_STATUS: string;
    CREATED_BY: string;
    CREATED_DATE: string;
    LAST_MODIFIED_BY: string;
    LAST_MODIFIED_DATE: string;
    LINE_POSITION: string;
    PRODUCT_LOCATION: string;
}