<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Select Activity</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- <ion-card class="card-style">
    <ion-card-content>
      <div>
        <ion-row>
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('Cropping')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
              <p>Cropping </p>
            </ion-button>
          </div>
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('Repair')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
              <p>Repair</p>
            </ion-button>
          </div>
        </ion-row>

        <ion-row>
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('InstallLine')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
              <p>Install Line</p>
            </ion-button>
          </div>
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('EquipmentInsp')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
               <p>Equipment Insp.</p>
            </ion-button>
          </div>
        </ion-row>

        <ion-row>
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('RequestNewLine')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
             <p>Request  New Line</p> 
            </ion-button>
          </div>
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('AssetActivity')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
              <p>Asset Activities</p>
            </ion-button>
          </div>
        </ion-row>
        <ion-row>
  
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('EndForEnd')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
              <p>End for End</p>
            </ion-button>
          </div>
          <div class="col6 border-style">
            <ion-button expand="full" (click)="navigateToNextStep('Rotation')">
              <ion-icon class="icon" slot="start" name="cut"></ion-icon>
               <p>Rotation</p>
            </ion-button>
          </div>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card> -->

  <ion-card class="card-style">
    <ion-card-header class="card-header-style">
      {{'Maintenance' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <ion-row>
        <div class="col6 border-style" (click)="navigateToNextStep('Repair')">
          <div class="tile" style="line-height:unset !important;">
            <div
              style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
              <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                <!-- <div *ngIf="item.icon != ''"> -->
                <img src="./assets/img/Constructions/other_WHite.png"
                  style="max-width: 40px !important" />
                <!-- </div> -->
              </div>
              <div style="width:62%; padding-left: 15px; margin: auto !important;">
                {{'Repair' | translate}}</div>
              <!-- <div style="width: 18% !important; float: right !important">
                <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}
                </ion-badge>
              </div> -->
            </div>

          </div>

        </div>
        <div class="col6 border-style" (click)="navigateToNextStep('EquipmentInsp')">
          <div class="tile" style="line-height:unset !important;">
            <div
              style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
              <div style="width: 20%" style="line-height:unset !important;margin-left: 10px; font-size: 30px;">
                <!-- <div *ngIf="item.icon != ''"> -->
                <fa-icon style="max-width: 40px !important" icon="search"></fa-icon>
                <!-- </div> -->
              </div>
              <div style="width:62%; padding-left: 15px; margin: auto !important;">
                {{'Equipment Inspection' | translate}}</div>
              <!-- <div style="width: 18% !important; float: right !important">
                <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}
                </ion-badge>
              </div> -->
            </div>

          </div>

        </div>
      </ion-row>
    </ion-card-content>

    <ion-card-header class="card-header-style">
      {{'Usage Logging' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <ion-row>
        <div class="col6 border-style" (click)="navigateToNextStep('AssetActivity')">
          <div class="tile" style="line-height:unset !important;">
            <div
              style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important;cursor: pointer;">
              <div style="width: 20%" style="line-height:unset !important;margin-left: 10px; font-size: 30px;">
                <!-- <div *ngIf="item.icon != ''"> -->
                  <fa-icon style="max-width: 40px !important" icon="list"></fa-icon>
                <!-- </div> -->
              </div>
              <div style="width:62%; padding-left: 15px; margin: auto !important;">
                {{'Line Usage' | translate}}</div>
              <!-- <div style="width: 18% !important; float: right !important">
                <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}
                </ion-badge>
              </div> -->
            </div>

          </div>

        </div>
      </ion-row>
    </ion-card-content>

    <ion-card-header class="card-header-style">
      {{'Wear Zone Management' | translate}}
    </ion-card-header>
    <ion-card-content class="card-content-style">
      <ion-row>
        <div class="col6 border-style" (click)="navigateToNextStep('Cropping')">
          <div class="tile" style="line-height:unset !important;">
            <div
              style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
              <div style="width: 20%" style="line-height:unset !important;margin-left: 10px;">
                <!-- <div *ngIf="item.icon != ''"> -->
                <img src="./assets/img/icons/Cutting_WHT_1024x1024.png"
                  style="max-width: 40px !important" />
                <!-- </div> -->
              </div>
              <div style="width:62%; padding-left: 15px; margin: auto !important;">
                {{'Cropping' | translate}}</div>
              <!-- <div style="width: 18% !important; float: right !important">
                <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}
                </ion-badge>
              </div> -->
            </div>

          </div>

        </div>
        <div class="col6 border-style" (click)="navigateToNextStep('EndForEnd')">
          <div class="tile" style="line-height:unset !important;">
            <div
              style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
              <div style="width: 20%" style="line-height:unset !important;margin-left: 10px; font-size: 30px;">
                <!-- <div *ngIf="item.icon != ''"> -->
                <fa-icon style="max-width: 40px !important" icon="exchange-alt"></fa-icon>
                <!-- </div> -->
              </div>
              <div style="width:62%; padding-left: 15px; margin: auto !important;">
                {{'End for End' | translate}}</div>
              <!-- <div style="width: 18% !important; float: right !important">
                <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}
                </ion-badge>
              </div> -->
            </div>

          </div>

        </div>
      </ion-row>
      <ion-row>
        <div class="col6 border-style" (click)="navigateToNextStep('Rotation')" *ngIf="showRotation">
          <div class="tile" style="line-height:unset !important;">
            <div
              style="display:flex !important; text-align: unset !important; padding-top: 10px!important; padding-bottom: 10px!important">
              <div style="width: 20%" style="line-height:unset !important;margin-left: 10px; font-size: 30px;">
                <!-- <div *ngIf="item.icon != ''"> -->
                <fa-icon style="max-width: 40px !important" icon="sync"></fa-icon>
                <!-- </div> -->
              </div>
              <div style="width:62%; padding-left: 15px; margin: auto !important;">
                {{'Rotation' | translate}}</div>
              <!-- <div style="width: 18% !important; float: right !important">
                <ion-badge class="card-badge" color="mediumBlack" style="font-size:14px;padding-top:5px;">{{item.count}}
                </ion-badge>
              </div> -->
            </div>

          </div>

        </div>
      </ion-row>
    </ion-card-content>
  </ion-card>
</ion-content>

<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>