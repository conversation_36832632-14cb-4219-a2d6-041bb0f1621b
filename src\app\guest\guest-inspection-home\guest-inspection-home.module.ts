import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestInspectionHomePageRoutingModule } from './guest-inspection-home-routing.module';

import { GuestInspectionHomePage } from './guest-inspection-home.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestInspectionHomePageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    GuestFooterComponent
  ],
  declarations: [GuestInspectionHomePage]
})
export class GuestInspectionHomePageModule {}
