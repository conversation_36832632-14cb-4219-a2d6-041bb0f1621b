import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestResourceExternalPageRoutingModule } from './guest-resource-external-routing.module';

import { GuestResourceExternalPage } from './guest-resource-external.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';


@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestResourceExternalPageRoutingModule,
    FontAwesomeModule,
    GuestFooterComponent
    
  ],
  declarations: [GuestResourceExternalPage]
})
export class GuestResourceExternalPageModule {}
