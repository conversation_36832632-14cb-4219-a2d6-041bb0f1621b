import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestExternalPageRoutingModule } from './guest-external-routing.module';

import { GuestExternalPage } from './guest-external.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TooltipsModule } from 'ionic4-tooltips';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestExternalPageRoutingModule,
    TranslateModule,
    FontAwesomeModule,
    TooltipsModule,
    GuestFooterComponent
  ],
  declarations: [GuestExternalPage]
})
export class GuestExternalPageModule {}
