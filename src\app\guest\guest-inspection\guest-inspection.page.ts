import { Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON>over<PERSON>ontroller, MenuController, AlertController, Platform, ToastController, NavController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { TrashComponent } from 'src/app/components/trash/trash.component';
import { InsppopoverPage } from 'src/app/insppopover/insppopover.page';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { AppConstant } from 'src/constants/appConstants';

@Component({
  selector: 'app-guest-inspection',
  templateUrl: './guest-inspection.page.html',
  styleUrls: ['./guest-inspection.page.scss'],
})
export class GuestInspectionPage implements OnInit {

  inspectionType;
  inProgressTrashArray: any = [];
  completedTrashArray: any = [];
  searchbar = false;
  searchIcon = true;
  title = true;
  historyList;
  tempArray = [];
  view;
  search = true;
  inspTitle = '';
  completedArray = [];
  noHistory = false;
  flag = true;
  syncDisable = false;
  dataBackupCompleted: any;
  dataBackupInProgress: any;
  syncButtonClicked: boolean = false;

  constructor(
    private route: Router,
    public unviredCordovaSDK: UnviredCordovaSDK,
    public alertService: AlertService,
    private service: UtilserviceService,
    private translate: TranslateService,
    private popoverController: PopoverController,
    private menu: MenuController,
    public helpService: HelpService,
    private alertController: AlertController,
    public dataService: DataService,
    public ngZone: NgZone,
    public platform: Platform,
    private navCtrl: NavController,
    public toastController: ToastController) {

    // event.subscribe('obj', (data) => {
    //   this.historyList.unshift(data);
    //   this.popoverController.dismiss();
    // });

    // event.subscribe('obj1', (data) => {
    //   this.completedArray.unshift(data);
    //   this.popoverController.dismiss();
    // });

    this.view = "CONSTRUCTION"
    this.inspectionType = this.service.getInspState();
    if (this.inspectionType === 'open') {
      this.search = true;
      this.inspTitle = this.translate.instant('In Progress');
      this.alertService.present().then(async () => {
        var tempResult = await this.fetchDataFromDb("in-progress");
        if (tempResult.type == ResultType.success) {
          this.historyList = [];
          this.ngZone.run(() => {
            for (var i = 0; i < tempResult.data.length; i++) {
              if (tempResult.data[i].INSPECTION_STATUS == AppConstant.IN_PROGRESS) {
                this.historyList.push(tempResult.data[i])
              }
            }
            this.dataBackupInProgress = tempResult.data;
          })
        }
        this.getInitialData()
        this.alertService.dismiss();
      })
    } else if (this.inspectionType === 'history') {
      this.search = true;
      this.inspTitle = this.translate.instant('Completed');
      this.alertService.present().then(async () => {
        var tempResult = await this.fetchDataFromDb("completed");
        if (tempResult.type == ResultType.success) {
          this.completedArray = tempResult.data;
          this.dataBackupCompleted = tempResult.data;
        }
        this.getInitialData()
        this.alertService.dismiss();
      })
    }
    if (helpService.helpMode) {
      this.search = true;
    }
  }

  public handleInfoMessage(result) {

    this.unviredCordovaSDK.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });

    this.showAlert(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }


  async markAsDeleted(item) {
    var whereClause = "INSPECTION_ID like '" + item.INSPECTION_ID + "'"
    item.IS_DELETED = 'X'
    return await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_INSPECTION_HEADER, item, whereClause)

  }

  async getInitialData() {
    var result = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, "INSPECTION_STATUS like '" + AppConstant.IN_PROGRESS + "' AND IS_DELETED = 'X'");
    if (result.type == ResultType.success) {
      console.log(JSON.stringify(result))
      this.ngZone.run(() => {
        this.inProgressTrashArray = result.data;
      })
      this.service.setTrashInspectionItems(this.inProgressTrashArray)
    }
    result = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, "INSPECTION_STATUS like '" + AppConstant.COMPLETED + "' AND IS_DELETED = 'X'");
    if (result.type == ResultType.success) {
      console.log(JSON.stringify(result))
      this.ngZone.run(() => {
        this.completedTrashArray = result.data;
      })
      this.service.setHistoricalTrashInspectionItems(this.completedTrashArray)
    }
  }


  initializeItems() {
    this.historyList = this.dataBackupInProgress;
    this.completedArray = this.dataBackupCompleted;
  }

  async fetchDataFromDb(mode: string) {
    var whereClause = "";
    if (mode == 'in-progress') {
      whereClause = "(INSPECTION_STATUS like '" + AppConstant.IN_PROGRESS + "' OR INSPECTION_STATUS like '" + AppConstant.READY + "') AND IS_DELETED not like 'X'  OR IS_DELETED is null"
    } else {
      whereClause = "INSPECTION_STATUS like '" + AppConstant.COMPLETED + "' AND (IS_DELETED not like 'X'  OR IS_DELETED is null)"
    }
    var result = await this.getSelectedFieldInDb()
    if (result.type = ResultType.success) {
      if (result.data.length > 0) {
        this.view = result.data[0].VALUE
      }
    }
    return await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, whereClause)
  }

  ngOnInit() {
    // this.dataService.inspections()
  }
  adHocInspect() {
    this.route.navigate(['guest-new-inspection']);
  }
  cancelHistoricalItem(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.initializeItems();
    }
  }
  cancelOpenItem(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.initializeItems();
    }
  }
  toggleSearch() {
    this.searchbar = !this.searchbar;
    this.searchIcon = !this.searchIcon;
    this.title = !this.title;
  }
  searchinProgressItem(ev) {
    this.initializeItems();
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.historyList = this.historyList.filter((item) => {
        return (
          (item.INSPECTION_ID && (item.INSPECTION_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)) ||
          (item.EXTERNAL_ID && item.EXTERNAL_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RPS && item.RPS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RFT_NUM && item.RFT_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.WORK_ORDER && item.WORK_ORDER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ACCOUNT_ID && item.ACCOUNT_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ASSET_ID && item.ASSET_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.MANUFACTURER && item.MANUFACTURER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INDUSTRY && item.INDUSTRY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.APPLICATION && item.APPLICATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_TYPE && item.PRODUCT_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CERTIFICATE_NUM && item.CERTIFICATE_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT && item.PRODUCT.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CODE && item.PRODUCT_CODE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_DESC && item.PRODUCT_DESC.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR && item.COLOR.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR_OTHER && item.COLOR_OTHER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CONSTRUCTION && item.CONSTRUCTION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_CONFIG && item.ORIGINAL_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CONFIG && item.PRODUCT_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM && item.DIAM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM_UOM && item.DIAM_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_LENGTH && item.ORIGINAL_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CURRENT_LENGTH && item.CURRENT_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTED_LENGTH && item.INSPECTED_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LENGTH_UOM && item.LENGTH_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_NOTES && item.INSPECTION_NOTES.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.HAS_CHAFE && item.HAS_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CHAFE_TYPE && item.CHAFE_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.OTHER_CHAFE && item.OTHER_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_JACKETED && item.IS_JACKETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_DATE && item.INSTALLED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_STATUS && item.INSTALLED_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LOCATION_INSTALLED && item.LOCATION_INSTALLED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_STATUS && item.INSPECTION_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.USER_ID && item.USER_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_BY && item.CREATED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_DATE && item.CREATED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_BY && item.LAST_MODIFIED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_DATE && item.LAST_MODIFIED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LINE_POSITION && item.LINE_POSITION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_LOCATION && item.PRODUCT_LOCATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_DELETED && item.IS_DELETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    } else {
      this.initializeItems();
    }
  }
  searchHistoricalItem(ev) {
    this.initializeItems();
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.completedArray = this.completedArray.filter((item) => {
        return (
          (item.INSPECTION_ID && item.INSPECTION_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.EXTERNAL_ID && item.EXTERNAL_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RPS && item.RPS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.RFT_NUM && item.RFT_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.WORK_ORDER && item.WORK_ORDER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ACCOUNT_ID && item.ACCOUNT_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ASSET_ID && item.ASSET_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.MANUFACTURER && item.MANUFACTURER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INDUSTRY && item.INDUSTRY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.APPLICATION && item.APPLICATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_TYPE && item.PRODUCT_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CERTIFICATE_NUM && item.CERTIFICATE_NUM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT && item.PRODUCT.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CODE && item.PRODUCT_CODE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_DESC && item.PRODUCT_DESC.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR && item.COLOR.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.COLOR_OTHER && item.COLOR_OTHER.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CONSTRUCTION && item.CONSTRUCTION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_CONFIG && item.ORIGINAL_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_CONFIG && item.PRODUCT_CONFIG.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM && item.DIAM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.DIAM_UOM && item.DIAM_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.ORIGINAL_LENGTH && item.ORIGINAL_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CURRENT_LENGTH && item.CURRENT_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTED_LENGTH && item.INSPECTED_LENGTH.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LENGTH_UOM && item.LENGTH_UOM.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_NOTES && item.INSPECTION_NOTES.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.HAS_CHAFE && item.HAS_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CHAFE_TYPE && item.CHAFE_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.OTHER_CHAFE && item.OTHER_CHAFE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_JACKETED && item.IS_JACKETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_DATE && item.INSTALLED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSTALLED_STATUS && item.INSTALLED_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LOCATION_INSTALLED && item.LOCATION_INSTALLED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.INSPECTION_STATUS && item.INSPECTION_STATUS.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.USER_ID && item.USER_ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_BY && item.CREATED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.CREATED_DATE && item.CREATED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_BY && item.LAST_MODIFIED_BY.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LAST_MODIFIED_DATE && item.LAST_MODIFIED_DATE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LINE_POSITION && item.LINE_POSITION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.PRODUCT_LOCATION && item.PRODUCT_LOCATION.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.IS_DELETED && item.IS_DELETED.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    }
  }
  async presentPopover($event: Event): Promise<void> {
    const popover = await this.popoverController.create({
      component: InsppopoverPage,
      componentProps: { page: this },
      event,
      showBackdrop: true,
      animated: true
    });
    await popover.present();
    const result = await popover.onDidDismiss();
    this.view = result.data.key;
    this.setSelectedFieldInDb(this.view)
  }
  async trashPopoverHistory($event: Event): Promise<void> {
    this.service.setTrashMode(false);
    const popover = await this.popoverController.create({
      component: TrashComponent,
      event,
      showBackdrop: true,
      animated: true,
      cssClass: 'pop-over-style'
    });
    await popover.present();
    const result = await popover.onDidDismiss();
  }
  async trashPopover($event: Event): Promise<void> {
    this.service.setTrashMode(true);
    const popover = await this.popoverController.create({
      component: TrashComponent,
      event,
      showBackdrop: true,
      animated: true,
      cssClass: 'pop-over-style'
    });
    await popover.present();
    const result = await popover.onDidDismiss();
    // this.view = this.service.getSelectedUserName();
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  gotoHome() {
    this.navCtrl.navigateRoot('/guest-home');
  }

  gotoInspections() {
    this.navCtrl.navigateRoot('/guest-inspection-home');
  }

  gotoResources() {
    this.navCtrl.navigateRoot('/guest-resource');
  }

  gotoContact() {
    this.navCtrl.navigateRoot('/guest-contact');
  }

  plannedInspection() {
    this.route.navigate(['planned-inspection']);
  }

  //Exit help mode
  ionViewDidLeave() {
    this.helpService.exitHelpMode();

  }
  ionViewDidEnter() {
    this.getInitialData();
  }
  viewCompletedInsp(item) {
    this.service.setInspectionReadFlag('readOnly');
    this.service.setObservationReadFlag('gg');
    this.service.setSelectedInspectionHeader(item);
    this.dataService.setLocationAndLayer(item);
    this.dataService.setCreatedHeader(item)
    this.dataService.setSelectedUom(item.LENGTH_UOM)
    this.service.resetBaselineArray();
    this.service.setFromBaseline(false)
    setTimeout(() => {
      this.route.navigate(['guest-observations']);
    }, 200);
  }
  async viewInProgressInsp(item) {
    var temp = await this.alertService.present();
    this.dataService.setLocationAndLayer(item);
    this.service.setInspectionReadFlag('rr');
    this.service.setObservationReadFlag('gg');
    this.service.setSelectedInspectionHeader(item);
    this.dataService.setCreatedHeader(item)
    this.dataService.setSelectedUom(item.LENGTH_UOM)
    this.service.resetBaselineArray();
    this.service.setFromBaseline(false)
    setTimeout(async () => {
      if (item.INSPECTION_STATUS != AppConstant.IN_PROGRESS) {
        this.route.navigate(['guest-observations']);
        this.alertService.dismiss();
      } else {
        var whereClause = "INSPECTION_ID like '" + item.INSPECTION_ID + "' AND MEAS_TYPE_ID like 'External'"
        var measurements = await this.unviredCordovaSDK.dbSelect("MEASUREMENT", whereClause)
        if (measurements.type == ResultType.success) {
          if (measurements.data.length <= 1) {
            if (measurements.data.length == 1) {
              var temp = JSON.parse(measurements.data[0].DATA)
              if (temp.external == 0 && (temp.externalDamageType == "" || temp.externalDamageType == null)) {
                this.service.setFromInspection(true)
                this.route.navigate(['guest-observations']);
                this.alertService.dismiss();
                return
              }
            } else {
              this.service.setFromInspection(true)
              this.route.navigate(['guest-observations']);
              this.alertService.dismiss();
              return
            }
          }
        } else {
          this.service.setFromInspection(true)
          this.route.navigate(['guest-observations']);
          this.unviredCordovaSDK.logError("inspection", "viewInProgressInsp", JSON.stringify(measurements))
        }
        var whereClause = "INSPECTION_ID like '" + item.INSPECTION_ID + "' AND MEAS_TYPE_ID like 'Internal'"
        var internal = await this.unviredCordovaSDK.dbSelect("MEASUREMENT", whereClause)
        if (internal.type == ResultType.success) {
          if (internal.data.length <= 1) {
            if (internal.data.length == 1) {
              var temp = JSON.parse(internal.data[0].DATA)
              if (temp.internal == 0 && (temp.internalDamageType == "" || temp.internalDamageType == null)) {
                this.service.setFromInspection(true)
                this.route.navigate(['guest-observations']);
                this.alertService.dismiss();
                return
              } else {
                this.route.navigate(['guest-observations']);
                this.alertService.dismiss();
                return
              }
            } else {
              this.service.setFromInspection(true)
              this.route.navigate(['guest-observations']);
              this.alertService.dismiss();
              return
            }
          } else {
            this.route.navigate(['guest-observations']);
            this.alertService.dismiss();
            return
          }
        } else {
          this.service.setFromInspection(true)
          this.route.navigate(['guest-observations']);
          this.alertService.dismiss()
          this.unviredCordovaSDK.logError("inspection", "viewInProgressInsp", JSON.stringify(internal))
        }
      }
    }, 200);
  }
  async inProgressItemTrash(index, item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Inspection'),
      message: '<strong>' + this.translate.instant('You want to delete this Inspection') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: async () => {
            this.icon(index);
            this.service.setTrashInspectionItem(item);
            var result = await this.markAsDeleted(item);
            this.historyList.splice(index, 1);
          }
        }
      ]
    });
    await alert.present();
  }
  async historyItemDelete(index, item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Inspection'),
      message: this.translate.instant('<strong>You want to delete this Inspection</strong>!'),
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: async () => {
            this.icon(index);
            await this.markAsDeleted(item)
            this.service.setHistoricalTrashInspectionItem(item);
            this.completedArray.splice(index, 1);
          }
        }
      ]
    });
    await alert.present();
  }
  icon(index) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }
  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }

  setSelectedFieldInDb(value: string) {
    this.unviredCordovaSDK.dbInsertOrUpdate("APP_SETTINGS_HEADER", { KEY_FLD: "SELECTED_FIELD", VALUE: value }, true)
  }

  async getSelectedFieldInDb() {
    return await this.unviredCordovaSDK.dbSelect("APP_SETTINGS_HEADER", "KEY_FLD like 'SELECTED_FIELD'")
  }

  async syncInspection(item, index) {
    if (!this.syncButtonClicked) {
      this.syncButtonClicked = true;
      //item.INSPECTION_STATUS = AppConstant.COMPLETED
      var result = await this.submitInspection(item)
      this.dataService.refreshData();
      if (result.type == ResultType.success) {
        const ind = this.tempArray.indexOf(index, 0);
        if (ind > -1) {
          this.tempArray.splice(ind, 1);
        } else {
          this.tempArray.push(index);
        }
        this.refreshData();
        this.presentToast()
        // this.historyList.splice(index, 1)
      }
    }
  }

  submitInspection(item) {
    let inputObject = {
      "INSPECTION_HEADER": item
    }
    return this.unviredCordovaSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_INSPECTION, "INSPECTION", item.LID, false)
  }

  async ionViewWillEnter() {
    this.getSelectedField();
  }

  async getSelectedField() {
    var result = await this.getSelectedFieldInDb()
    if (result.type == ResultType.success) {
      if (result.data.length > 0) {
        this.view = result.data[0].VALUE
      }
    }
    this.alertService.dismiss();
    this.refreshData();
  }

  async refreshData() {
    // this.presentToast()
    this.historyList = [];
    var tempAl = await this.alertService.present()
    setTimeout(async () => {
      var tempResult = await this.fetchDataFromDb("in-progress");
      if (tempResult.type == ResultType.success) {
        this.historyList = [];
        this.ngZone.run(() => {
          for (var i = 0; i < tempResult.data.length; i++) {
            if (tempResult.data[i].INSPECTION_STATUS == AppConstant.IN_PROGRESS) {
              this.historyList.push(tempResult.data[i])
            }
          }
          // this.historyList = tempResult.data;
          this.dataBackupInProgress = tempResult.data;

        })
      }
      this.alertService.dismiss();
    }, 500);

  }

  async presentToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("Submitted Inspection"),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async showAlert(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }


  async getInfoMsg(event: any, object: any) {
    var that = this
    if (object.SYNC_STATUS == '3') {
      event.preventDefault();
      event.stopPropagation();
      var result = await this.unviredCordovaSDK.getInfoMessages(AppConstant.TABLE_INSPECTION_HEADER, object.LID)
      if (result.type == ResultType.success) {
        let title = "Info Message";

        if (this.platform.is("android")) {
          if (result.message != "" && result.message != "") {
            that.showAlert(title, result.message);
          } else {
            if (result.data.length > 0) {
              var message = "";
              for (var i = 0; i < result.data.length; i++) {
                message = message + result.data[i].MESSAGE + "<br>"
              }
              that.showAlert(title, message);
            } else {
              that.showAlert(title, "Error while submitting inspection");
            }
          }
        } else if (this.platform.is("ios")) {
          var message = "";
          for (var i = 0; i < result.data.length; i++) {
            message = message + result.data[i].MESSAGE + "<br>"
          }
          that.showAlert(title, message);
        } else {
          if (result.message !== null && result.message !== '') {
            var message = "";
            message = `${message}${result.message}`;
            that.showAlert(title, message.trim());
          } else {
            let resultData = result.data;
            if (resultData !== null && resultData.length > 0) {
              let messageArray: string[] = resultData.map((data: any) => {
                if (data.MESSAGE && data.MESSAGE.trim().length !== 0) {
                  return data.MESSAGE;
                }
              });
              if (messageArray.length > 0) {
                let messageToDisplay: string = messageArray.join(' ').trim();
                that.showAlert(title, messageToDisplay);
              }
            }
          }
        }
      }
      else {
        that.showAlert("Error", JSON.stringify(result));
      }

    } else if (object.SYNC_STATUS == '2') {
      that.showAlert("Info Message", "Message submitted waiting for response")
    } else if (object.SYNC_STATUS == '1') {
      that.showAlert("Info Message", "Message queued for submission")
    } else if (object.SYNC_STATUS == '0') {
      if (object.INSPECTION_STATUS == AppConstant.READY) {
        that.showAlert("Info Message", "Inspection ready for submission")
      }
    }
  }

}
