import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { EmailComposer } from '@awesome-cordova-plugins/email-composer/ngx';
import { FileOpenerService } from 'src/app/services/file-opener.service';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, ToastController, LoadingController, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { GenericListPage } from 'src/app/generic-list/generic-list.page';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { AppConstant } from 'src/constants/appConstants';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { FilePath } from '@awesome-cordova-plugins/file-path/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { MY_FORMATS } from 'src/models/DATE_FORMAT';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faChevronCircleLeft, faDownload, faEnvelope, faTasks, faTh } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-maintenance',
  templateUrl: './maintenance.page.html',
  styleUrls: ['./maintenance.page.scss'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})
export class MaintenancePage implements OnInit {

  maintData: any[] = [];
  initialData: any[] = [];
  searchbar = false;
  title = true;
  searchIcon = true;
  maxDate: any;
  eventStartDateModel: any;
  eventEndDateModel: any;
  public showLoading = null;
  private blob: Blob;
  eventDateErrorMessage: any;
  hasError = false;
  eventDateForm: FormGroup;
  currentFilePath: any;
  dataToExport;
  tempArray = [];
  assetList: any;
  selectedAsset: any;
  selectedAssetName: string = ''
  isAssetSelected: boolean = false;
  selectedAssetID: any;
  fromDateForm: FormGroup;
  toDateForm: FormGroup;
  isUtility: boolean = false;

  constructor(public formBuilder: FormBuilder,
    private unviredCordovaSDK: UnviredCordovaSDK,
    private fileOpenerService: FileOpenerService,
    private file: File,
    private userPreferenceService: UserPreferenceService,
    public menu: MenuController,
    public dataService: DataService,
    private router: Router,
    private helpService: HelpService,
    private toastController: ToastController,
    private emailComposer: EmailComposer,
    private loadingController: LoadingController,
    private filePath: FilePath,
    private modalController: ModalController,
    private alertService: AlertService,
    public translate: TranslateService,
    public device: Device,
    private iab: InAppBrowser,
    public faIconLibrary: FaIconLibrary
  ) { 
    this.faIconLibrary.addIcons(faChevronCircleLeft , faEnvelope , faDownload, faBars, faTasks, faTh)
  }

  ngOnInit() {
    // this.loadData();
    this.eventStartDateModel = '';
    this.eventEndDateModel = '';
    const maxNewDate = new Date();
    this.maxDate = new Date(maxNewDate.getFullYear(), maxNewDate.getMonth(), maxNewDate.getDate());
    this.fromDateForm = this.formBuilder.group({
      fromDateCtrl: ['', Validators.required],
    });
    this.toDateForm = this.formBuilder.group({
      toDateCtrl: ['', Validators.required],
    });
    this.checkIfUtility();
    this.getAssetList();
  }

  async getAssetList() {
    let assetRes = await this.unviredCordovaSDK.dbExecuteStatement(`SELECT * FROM ASSET_HEADER ORDER BY NAME COLLATE NOCASE ASC`)
    if (assetRes.type == ResultType.success) {
      if (assetRes.data.length > 0) {
        this.assetList = assetRes.data;
      } else {
        this.assetList = []
      }
    } else {
      this.unviredCordovaSDK.logError("create-inspection", "filterCertificate", "Error while getting error from db" + JSON.stringify(assetRes))
    }
    this.initializePreferenceAsset();
  }


  icon(index) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }

  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }

  async loadData() {
    // await this.presentLoading('Loading...');
    this.getMaintenanceData();
  }

  async presentToast() {
    const toast = await this.toastController.create({
      message: 'Please select a date range and asset to get line history',
      duration: 5000,
      position: 'middle'
    });
    toast.present();
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
    this.clearData();
  }

  async clearData() {
    await this.unviredCordovaSDK.dbDelete('LMD_LITE_HEADER', "")
  }

  async presentLoading(msg) {
    this.showLoading = await this.loadingController.create({
      message: msg,
      spinner: 'crescent',
      animated: true,
      showBackdrop: true,
      translucent: true
    });
    await this.showLoading.present();
  }
  async getInitialData() {
    this.maintData = this.initialData;
  }

  async filterByDate(startD, endD) {
    await this.presentLoading('Loading...');
    // console.log('filter date value' + filterValue);
    // if (startD !== '') {
    //   this.maintData = this.initialData.filter((item) => {
    //     return (
    //       (item.LMD_DATA.eventDate.toString() >= startD && item.LMD_DATA.eventDate.toString() <= endD)
    //     );
    //   });
    //   console.log('filtered data' + JSON.stringify(this.maintData));
    // } else {
    //   this.getInitialData();
    // }

    var inputHeader = {
      START_DATE: startD,
      END_DATE: endD,
      USER_TYPE: "maintenance",
      ASSET_ID: this.selectedAssetID
    }

    let inputObject = {
      "INPUT_GET_LMD_LITE": [
        {
          "INPUT_GET_LMD_LITE_HEADER": inputHeader
        }]
    }
    var tempres = await this.unviredCordovaSDK.dbDelete("LMD_LITE_HEADER", '')
    var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", inputObject, AppConstant.PA_ROPE_INSPECTIONS_PA_GET_HISTORICAL_LMD, true)
    if(temp != undefined) {
      if (temp.type == ResultType.success) {
        this.loadData();
        if (temp.message.length > 0)
          this.alertService.showAlert("Error", temp.message)
      } else {
        this.alertService.showAlert("Error", temp.message)
        await this.showLoading.dismiss();
      }
    } else {
      this.alertService.showAlert("Error", "No records found with the given date range. Please try with different date range")
      await this.showLoading.dismiss();
    }

  }

  async presentModal(title: string) {
    var tempList, pageTitle;
    switch (title) {
      case 'ASSET':
        tempList = this.assetList
        pageTitle = "Assets"
    }

    this.alertService.present().then(async () => {
      const modal = await this.modalController.create({
        component: GenericListPage,
        componentProps: { value: tempList, title: pageTitle, page: title }
      });
      await modal.present();

      modal.onDidDismiss().then(async (data) => {
        if(data?.data) {
          switch (title) {
            case 'ASSET':
              this.selectedAssetName = data.data.data.NAME
              this.selectedAssetID = data.data.data.ID
              this.selectedAsset = data.data.data;
              if (data.data.data.ID != '') {
                this.isAssetSelected = true;
              }
              break;
          }
        }
      });
    })
  }


  onDateChange() {
    console.log('raw ' + this.eventEndDateModel + this.eventStartDateModel);
    this.hasError = false;
    if (this.eventStartDateModel === undefined || this.eventEndDateModel === undefined) {
      this.hasError = true;
      this.eventDateErrorMessage = 'Please pick both start date and end date';
    } else {
      this.hasError = false;
      const startD = this.eventStartDateModel.format('YYYY-MM-DD');
      const endD = this.eventEndDateModel.format('YYYY-MM-DD');

      if (endD < startD) {
        this.hasError = true;
        this.eventStartDateModel = '';
        this.eventEndDateModel = '';
        this.eventDateErrorMessage = 'Select a valid date range';
      } else {
        this.filterByDate(startD, endD);
      }
    }
    // console.log('date' + date.format('YYYY-MM-DD'));
  }
  composeEmail() {
    this.generateCSV('email');
  }

  async generateCSV(type: string) {
    const value = this.convertToCSV();
    // const value = 'a,b,c,d';
    console.log('formatted data' + value);
    const folderPath = this.file.dataDirectory;
    // this.filePath.resolveNativePath(folderPath).then((res) => console.log('filepathresolver' + res)).
    //   catch(err => console.log(err));

    //#region "Get todays date and time"
    const currentdate = new Date();
    const datetime = currentdate.getDate() + '' + (currentdate.getMonth() + 1) + '' +
      currentdate.getFullYear() + '' + currentdate.getHours() +
      '' + currentdate.getMinutes() + '' + currentdate.getSeconds();
    //#endregion

    const fileName = `Line-History${datetime}.csv`;
    const blob = new Blob(['\ufeff' + value], { type: 'text/csv;charset=utf-8;' });
    if(this.device.platform == "browser") {
      const a = document.createElement('a');
      const url = window.URL.createObjectURL(blob);

      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();

    } else {
      if (this.device.platform == 'Android') {
        if (type === 'export') {
          this.file.checkDir(this.file.externalRootDirectory + "Download/", 'Inspections')
            .then(_ => {
              this.file.writeFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, blob).then(response => {
                let filePath = this.file.applicationDirectory + 'www/assets';
                this.file.copyFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, this.file.dataDirectory, fileName).then(result => {
                  this.fileOpenerService.openFile(fileName, 'text/csv');    
                }).catch(err => {
                })
              })
            })
            .catch(err => {
              this.file.createDir(this.file.externalRootDirectory + "Download/", 'Inspections/', false).then(result => {
                this.file.writeFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, blob).then(response => {
                  this.file.copyFile(this.file.externalRootDirectory + "Download/" + "Inspections/", fileName, this.file.dataDirectory, fileName).then(result => {
                    this.fileOpenerService.openFile(fileName, 'text/csv');
                  }).catch(err => {
                  })
                }).catch(err => {
                  // ACTION
                })
              })
            });
        } else {
          const mailId = await this.userPreferenceService.getUserPreference('email');
          const email = {
            to: `${mailId ? mailId : ''}`,
            cc: '',
            attachments: [
              this.currentFilePath
            ],
            subject:  'Line History Report', //'Maintenance report from ' + this.eventStartDateModel.format('YYYY-MM-DD') + ' to ' + this.eventEndDateModel.format('YYYY-MM-DD'),
            body: 'Please find the line history report document attached',
            isHtml: true
          };
          this.emailComposer.open(email);
        }
      } else {
        this.file.writeFile(`${folderPath}`, fileName, blob, { replace: true, append: false }).then(async (res) => {
          console.log('successfully written', res);
          if (type === 'export') {
            this.fileOpenerService.openFile(fileName, 'text/csv');
            // .then(_ => console.log('Opened successfully')).
            // catch(err => console.log('error while opening file', err));
          } else {
            console.log('current file path' + this.currentFilePath);
            const mailId = await this.userPreferenceService.getUserPreference('email');
            const email = {
              to: `${mailId ? mailId : ''}`,
              cc: '',
              attachments: [
                this.currentFilePath
              ],
              subject:  'Line History Report', //'Maintenance report from ' + this.eventStartDateModel.format('YYYY-MM-DD') + ' to ' + this.eventEndDateModel.format('YYYY-MM-DD'),
              body: 'Please find the line history report document attached',
              isHtml: true
            };
            this.emailComposer.open(email);
          }
        }).catch(err => console.log('error while writing to file' + err));
      }
    }

    const filePath = folderPath + fileName;
    this.currentFilePath = `${filePath}`;
  }

  convertToCSV() {
    const arr = [];
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.maintData.length; i++) {
      const objArray = {};
      objArray['eventDate'] = this.maintData[i].LMD_DATA.eventDate ? this.maintData[i].LMD_DATA.eventDate : '';
      objArray['eventType'] = this.maintData[i].LMD_TYPE ? this.maintData[i].LMD_TYPE : '';
      if(this.maintData[i].LMD_DATA.certNo != '' && this.maintData[i].LMD_DATA.certNo != null && this.maintData[i].LMD_DATA.certNo != undefined) {
        objArray['certNo'] = " " + this.maintData[i].LMD_DATA.certNo + " "
      } else if(this.maintData[i].LMD_DATA.alternateCertNo != '' && this.maintData[i].LMD_DATA.alternateCertNo != null && this.maintData[i].LMD_DATA.alternateCertNo != undefined) {
        objArray['certNo'] = " " + this.maintData[i].LMD_DATA.alternateCertNo + " "
      } else {
        objArray['certNo'] = '' 
      }
      // objArray['selectedEquipmentName'] = this.maintData[i].LMD_DATA.selectedEquipmentName ? this.maintData[i].LMD_DATA.selectedEquipmentName : '';
      objArray['supportStatus'] = this.maintData[i].LMD_DATA.supportStatus ? this.maintData[i].LMD_DATA.supportStatus : '';
      // objArray['workOrder'] = this.maintData[i].LMD_DATA.workOrder ? this.maintData[i].LMD_DATA.workOrder : '';
      // objArray['internalWorkOrder'] = this.maintData[i].LMD_DATA.internalWorkOrder ? this.maintData[i].LMD_DATA.internalWorkOrder : '';
      // objArray['assetName'] = this.maintData[i].LMD_DATA.assetName ? this.maintData[i].LMD_DATA.assetName : '';
      // objArray['lmdType'] = this.maintData[i].LMD_TYPE ? this.maintData[i].LMD_TYPE : '';
      // objArray['eventDate'] = this.maintData[i].LMD_DATA.eventDate ? this.maintData[i].LMD_DATA.eventDate : '';
      // objArray['totalWorkingHour'] = this.maintData[i].LMD_DATA.totalWorkingHour ? this.maintData[i].LMD_DATA.totalWorkingHour : '';
      // objArray['totalWorkingOperation'] = this.maintData[i].LMD_DATA.totalWorkingOperation ? this.maintData[i].LMD_DATA.totalWorkingOperation : '';
      // objArray['endInUse'] = this.maintData[i].LMD_DATA.endInUse ? this.maintData[i].LMD_DATA.endInUse : '';
      // objArray['distanceInEye'] = this.maintData[i].LMD_DATA.distanceInEye ? this.maintData[i].LMD_DATA.distanceInEye : '';
      // objArray['repairType'] = this.maintData[i].LMD_DATA.repairType ? this.maintData[i].LMD_DATA.repairType : '';
      // objArray['damageType'] = this.maintData[i].LMD_DATA.damageType ? this.maintData[i].LMD_DATA.damageType : '';
      // objArray['ropeContact'] = this.maintData[i].LMD_DATA.ropeContact ? this.maintData[i].LMD_DATA.ropeContact : '';
      // objArray['surfaceRating'] = this.maintData[i].LMD_DATA.surfaceRating ? this.maintData[i].LMD_DATA.surfaceRating : '';
      // objArray['scoring'] = this.maintData[i].LMD_DATA.scoring ? this.maintData[i].LMD_DATA.scoring : '';
      // objArray['pitting'] = this.maintData[i].LMD_DATA.pitting ? this.maintData[i].LMD_DATA.pitting : '';
      // objArray['mobility'] = this.maintData[i].LMD_DATA.mobility ? this.maintData[i].LMD_DATA.mobility : '';
      // objArray['selectedEquipmentName'] = this.maintData[i].LMD_DATA.selectedEquipmentName ? this.maintData[i].LMD_DATA.selectedEquipmentName : '';
      // objArray['alternateCertNo'] = this.maintData[i].LMD_DATA.alternateCertNo ? this.maintData[i].LMD_DATA.alternateCertNo : '';
      // objArray['manufacturer'] = this.maintData[i].LMD_DATA.manufacturer ? this.maintData[i].LMD_DATA.manufacturer : '';
      // objArray['productName'] = this.maintData[i].LMD_DATA.productName ? this.maintData[i].LMD_DATA.productName : '';
      // objArray['productDesc'] = this.maintData[i].LMD_DATA.productDesc ? this.maintData[i].LMD_DATA.productDesc : '';
      // objArray['certDate'] = this.maintData[i].LMD_DATA.certDate ? this.maintData[i].LMD_DATA.certDate : '';
      // objArray['fromWinch'] = this.maintData[i].LMD_DATA.fromWinch ? this.maintData[i].LMD_DATA.fromWinch : '';
      // objArray['toWinch'] = this.maintData[i].LMD_DATA.toWinch ? this.maintData[i].LMD_DATA.toWinch : '';
      arr.push(objArray);
    }
    const obj = { data: arr };
    console.log('obj' + JSON.stringify(obj.data));
    const array = typeof obj.data !== 'object' ? JSON.parse(obj.data) : obj.data;
    // tslint:disable-next-line:max-line-length
    let str = 'Event Data, Event Type, Certificate, Support Status \r\n';

    // let str = 'Work Order,Internal Work Order,Asset Name,LMD Type,Event Data,Total Working Hours,Total Working Operation,End being Repaired,Distance From Eye,Repair Type,Reason for Repair,Rope Contact,Surface Rating,' +
    //   'Scoring,Pitting,Mobility,Selected Equipment Name,Alternate Cert. No,Manufacturer,Product Name,Product Description,Certificate Date,From Winch,To Winch \r\n';

    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < array.length; i++) {
      let line = '';
      // tslint:disable-next-line: forin
      for (const index in array[i]) {
        if (line !== '') {
          line += ',';
        }
        if (line === '') {
          line += ' ';
        }
        line += array[i][index];
      }
      str += line + '\r\n';
    }
    console.log(str);
    return str;
  }

  async getMaintenanceData() {

    const result = await this.unviredCordovaSDK.dbSelect('LMD_LITE_HEADER', '');
    if (result.type === ResultType.success) {
      this.initialData = result.data;
      this.maintData = [];
      const resultData = result.data;
      for (let i = 0; i < resultData.length; i++) {
        const value = result.data[i];
        if (value.LMD_DATA != '') {
          const dataToFormat = value.LMD_DATA;
          const formatedLmdData = dataToFormat.replace(/\"/g, '"');
          const newFormatedData = JSON.parse(formatedLmdData);
          value.LMD_DATA = newFormatedData;
          this.maintData.unshift(value);
          this.maintData.sort(function(a,b){
            var c = new Date(a.LMD_DATA.eventDate).getTime();
            var d = new Date(b.LMD_DATA.eventDate).getTime();
            // Turn your strings into dates, and then subtract them
            // to get a value that is either negative, positive, or zero.
            return c - d;
          });
          this.maintData.reverse()
        }
      }
      await this.showLoading.dismiss();
      console.log('maindata' + JSON.stringify(this.maintData));
    } else {
      console.log('no data found');
      await this.showLoading.dismiss();
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async initializePreferenceAsset() {
    var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    
    if (selectedAsset != undefined && selectedAsset != null && selectedAsset != '' && selectedAsset != '""') {
      selectedAsset = JSON.parse(selectedAsset)
      this.selectedAsset = selectedAsset;
      this.selectedAssetID = selectedAsset.ID
      this.selectedAssetName  = selectedAsset.NAME
    }
  }

  setChanged() {

  }

  keyPressed(event, installDate, flag) {

  }

  async checkIfUtility() {
    var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
    if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
      selectedIndustry = JSON.parse(selectedIndustry)
      if((selectedIndustry.ID.includes('Utility'))) {
        this.isUtility = true;
      }
      return
    } else {
      return
    }
  }


}
