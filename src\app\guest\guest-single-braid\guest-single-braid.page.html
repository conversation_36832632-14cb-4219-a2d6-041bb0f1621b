<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
    </ion-buttons>
    <ion-title>Single braid</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <iframe src="../assets/pdf/App_SB_Inspection_Checklist_2017.pdf"></iframe>
</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>