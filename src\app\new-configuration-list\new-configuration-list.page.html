<ion-header>
  <ion-toolbar>
    <ion-title>Identify Configuration</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <!-- <div class="inspectionHome"> -->
  <!-- UNCOMMENT FOR IPAD IF NEEDED-->
  <div *ngIf="platformId !== 'electron' && device.platform !== 'browser'">

    <!-- <ion-card style="height:25%;border-radius: 10px;" (click)="helpService.helpMode ? '' : setConfigAndNavigate()"
      class="ion-activatable" *ngFor="let configuration of configurations">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="../../assets/img/line.jpg" />
      <ion-label class="card-title" style="font-size: 17px;"> {{configuration.NAME}} </ion-label>
    </ion-card> -->

    <ion-card (click)="helpService.helpMode ? '' : setConfigAndNavigate('Cut Length')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/cutLength.png" style="width: 100% !important"/>
      <ion-label class="card-title" style="font-size: 17px;margin: 5px;"> {{'Cut Length' |translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : setConfigAndNavigate('Eye and Eye')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/EyeAndEye.png" style="width: 100% !important"/>
      <ion-label class="card-title" style="font-size: 17px; margin: 5px;"> {{'Eye and Eye' |translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : setConfigAndNavigate('Eye One End')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/EyeOneEnd.png" style="width: 100% !important"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'Eye One End' |translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : getFixedBearingPoint('Grommet')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/grommet.png" style="width: 100% !important"/>
      <ion-label class="card-title" style="font-size: 17px; margin: 5px;"> {{'Grommet' | translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : getFixedBearingPoint('Splice Centered Grommet')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/spliceCenterGrommet.png" style="width: 100% !important" />
      <ion-label class="card-title" style="font-size: 17px;"> {{'Splice Centered Grommet' | translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : setConfigAndNavigate('Strop')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/strop.png" style="width: 100% !important"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'Strop' | translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : setConfigAndNavigate('Soft Shackle')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/SoftShackle.png" style="padding-bottom: 12px; width: 100% !important" />
      <ion-label class="card-title" style="font-size: 17px;"> {{'Soft Shackle' | translate}} </ion-label>
    </ion-card>

    <ion-card (click)="helpService.helpMode ? '' : setConfigAndNavigate('Multi-part Rope Sling')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/Multi-partRopeSlingr.png" style="width: 100% !important"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'Multi-part Rope Sling' | translate}} </ion-label>
    </ion-card>

    <!-- & FUSE MOBILE -->
    <ion-card (click)="helpService.helpMode ? '' : setConfigAndNavigate('Fuse')"
      class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <img src="./assets/img/Fuse.png" style="width: 100% !important"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'Fuse' | translate}} </ion-label>
    </ion-card>
  </div>


  <div *ngIf="platformId == 'electron' || device.platform == 'browser'">
    <div class="gridCon">
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : setConfigAndNavigate('Cut Length')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_Cut_Length_BLUE_2024x2024.png"
            class="imageTag">
          <p class="wrapperLabel">{{'Cut Length' | translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : setConfigAndNavigate('Eye and Eye')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_Eye_and_Eye_BLUE_2024x2024.png"
            class="imageTag">
          <p class="wrapperLabel">{{'Eye and Eye' | translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : setConfigAndNavigate('Eye One End')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_Eye_One_End_BLUE_2024x2024.png"
            class="imageTag">
          <p class="wrapperLabel">{{'Eye One End' | translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : getFixedBearingPoint('Grommet')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_Grommet_BLUE_2024x2024.png" class="imageTag">
          <p class="wrapperLabel">{{'Grommet' | translate}}</p>
        </div>

        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : getFixedBearingPoint('Splice Centered Grommet')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_Splice_Centered_Grommet_BLUE_2024x2024.png"
            class="imageTag">
          <p class="wrapperLabel">{{'Splice Centered Grommet' | translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : setConfigAndNavigate('Strop')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_Strop_BLUE_2024x2024.png" class="imageTag">
          <p class="wrapperLabel">{{'Strop' | translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : setConfigAndNavigate('Soft Shackle')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_Soft_Shackle_BLUE_2024x2024.png"
            class="imageTag">
          <p class="wrapperLabel">{{'Soft Shackle' | translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : setConfigAndNavigate('Multi-part Rope Sling')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_MultiPart_Sling_BLUE_2024x2024.png"
            class="imageTag">
          <p class="wrapperLabel">{{'Multi-part Rope Sling' | translate}}</p>
        </div>
        <!-- & FUSE DESKTOP-->
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : setConfigAndNavigate('Fuse')">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/conf-imgs/CONFIGURATIONS_FUSE_BLUE_2024x2024.png"
            class="imageTag">
          <p class="wrapperLabel">{{'Fuse' | translate}}</p>
        </div>
      </div>
    </div>
  </div>

</ion-content>

<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>