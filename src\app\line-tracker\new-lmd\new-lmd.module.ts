import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { NewLmdPageRoutingModule } from './new-lmd-routing.module';

import { NewLmdPage } from './new-lmd.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from 'src/app/components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    NewLmdPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    FooterComponent
  ],
  declarations: [NewLmdPage]
})
export class NewLmdPageModule {}
