import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { InspectionHomePageRoutingModule } from './inspection-home-routing.module';

import { InspectionHomePage } from './inspection-home.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    InspectionHomePageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    FooterComponent
  ],
  declarations: [InspectionHomePage]
})
export class InspectionHomePageModule {}
