import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class LMD_HEADER extends DATA_STRUCTURE {
    LMD_ID: string;
    LMD_TYPE: string;
    LMD_DATA: string;
    LMD_STATUS: string;
    LMD_NAME: string;
    PRE_DEP_INSP_CMPLT: string;
    EXTFLD1 : string; //! using this field to set inspection ID for specimen in observations page in Employee login and specimens are treated as LMD
    EXTFLD2 : string; //! using it as U_MODE to identify whether the LMD is added or updated when inspection is reopened and submitted
    EXTFLD3 : string;
    EXTFLD4 : string;
    EXTFLD5 : string;
    EXTFLD6 : string;
}