import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { IonInfiniteScroll, NavController, MenuController, LoadingController } from '@ionic/angular';
import { DataService } from 'src/app/services/data.service';
import { LmdService } from 'src/app/services/lmd.service';

@Component({
  selector: 'app-inventory',
  templateUrl: './inventory.page.html',
  styleUrls: ['./inventory.page.scss'],
})
export class InventoryPage implements OnInit {

  tempArray = [];
  inventoryData = [];
  certificateData = [];
  initialData = [];
  tempId = [];
  searchbar = false;
  title = true;
  searchIcon = true;
  public showLoading = null;
  @ViewChild(IonInfiniteScroll) infiniteScroll: IonInfiniteScroll;
  constructor(
    private unviredCordovaSDK: UnviredCordovaSDK, 
    public navCtrl: NavController, 
    public lmdService: LmdService, 
    public menu: MenuController, 
    public dataService: DataService, 
    private router: Router, 
    private loadingController: LoadingController,
    public faIconLibrary: FaIconLibrary) { 
      this.faIconLibrary.addIcons(faSearch)
    }

  ngOnInit() {
    this.loadData();
  }
  async loadData() {
    await this.presentLoading('Loading...');
    this.getAsset();
    this.infiniteScroll.disabled = false;
  }

  scrolling(event) {
    setTimeout(() => {
      console.log('Done');
      event.target.complete();

      // App logic to determine if all data is loaded
      // and disable the infinite scroll
      if (this.inventoryData.length === 1000) {
        event.target.disabled = true;
      }
    }, 500);
  }

  async presentLoading(msg) {
    this.showLoading = await this.loadingController.create({
      message: msg,
      spinner: 'crescent',
      animated: true,
      showBackdrop: true,
      translucent: true
    });
    await this.showLoading.present();
  }

  toggleSearch() {
    this.searchbar = !this.searchbar;
    this.searchIcon = !this.searchIcon;
    this.title = !this.title;
  }

  searchAsset(ev) {
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.inventoryData = this.initialData.filter((item) => {
        return (
          (item.ID && item.ID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.NAME && item.NAME.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    }
    else {
      this.getInitialData();
    }
  }

  cancelsearchAsset(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.getInitialData();
    }
  }

  async getInitialData() {
    await this.presentLoading('Loading...');
    this.inventoryData = this.initialData;
    await this.showLoading.dismiss();
  }

  async openCertificatesList(assetID, item) {
    await this.presentLoading('Loading...');
    this.lmdService.setSelectedRPSAssetID(assetID)
    this.lmdService.setSelectedAsset(item)    
    this.router.navigate(['certificate-list'], { state: { AssetId: assetID } }); 
    await this.showLoading.dismiss();   
  }

  async getAsset() {
    const result = await this.unviredCordovaSDK.dbSelect('ASSET_HEADER', '');
    if (result.type === ResultType.success) {
      // console.log('asset header '+JSON.stringify(result.data));
      this.initialData = result.data;
      this.inventoryData = result.data;
      if (this.initialData.length === 1) {
        this.openCertificatesList(this.initialData[0].ID, this.initialData[0]);
      }
      await this.showLoading.dismiss();
    }
    else {
      alert('no data found');
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  backButtonClick() {
    this.navCtrl.pop()
  }

}
