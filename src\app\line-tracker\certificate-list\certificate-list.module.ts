import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CertificateListPageRoutingModule } from './certificate-list-routing.module';

import { CertificateListPage } from './certificate-list.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { FooterComponent } from 'src/app/components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    CertificateListPageRoutingModule,
    FontAwesomeModule,
    MatButtonModule,
    MatButtonToggleModule,
    FooterComponent
  ],
  declarations: [CertificateListPage]
})
export class CertificateListPageModule {}
