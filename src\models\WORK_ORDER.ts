import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class WORK_ORDER_HEADER extends DATA_STRUCTURE {
    ID: string;
    WO_NUMBER: string;
    WO_SUBJECT: string;
    WO_TYPE: string;
    EVENT_DATE: string;
    WO_DESC: string;
    WO_STATUS: string;
    ROOT_WO_ID: string;
    ACCOUNT : string;
    ACCOUNT_NAME: string;
    CERT_ID :string;
    CERT_NAME :string;
    CONSTRUCTION :string;
    RECORD_TYPE : string;
    ASSET_ID :string;
    INDUSTRY :string;
    APPLICATION :string;
    DIAMETER :string;
    LEN_OF_SAMPLE_FT :string;
    LEN_OF_SAMPLE_MT :string;
    RFT_CASE_ID :string;
    RFT_CASE_NAME :string;
    RFT_CASE_SUBJECT :string;
    LENGTH_OF_SAMPLE_UOM :string;
    RFT_STATUS :string;
    EXTFIELD1 :string;
    EXTFIELD2 :string;
    EXTFIELD3 :string;
}