<ion-header>
  <ion-toolbar>
    <ion-title>{{'Internal Abrasion' | translate}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" (click)="back()"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button color="primary" (click)="historyPopover($event)" class="help-button-style">
        <fa-icon class="icon-style-help" icon="list"></fa-icon>
      </ion-button>
      <ion-button color="primary" (click)="helpService.switchMode()" class="help-button-style">

        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="form-group" style="padding:15px 0px 0px 17px">
    <label>{{'Length UOM' | translate}} : {{dataService.selectedUomString}} </label>
  </div>
  <!-- <div id="overlay" style="display: block" *ngIf='readOnly'></div> -->
  <div class="form-group" style="padding:15px 10px 0px 17px">
    <label>{{'Enter Location' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;">
        * </span> :</label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col
        tooltip="{{'Describe the location of the internal abrasion. For example, 2-4 feet from end A.'|translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <input type="text" [disabled]='helpService.helpMode || readOnly' [(ngModel)]="start" maxlength="50"
          [ngClass]="externalStart ? 'form-control' :'form-control'" placeholder="{{'Enter value here' | translate}}"
          (change)="setChanged()">
      </ion-col>&nbsp;
    </ion-row>

    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <input type="text" [disabled]='helpService.helpMode || readOnly' [(ngModel)]="start" maxlength="50"
          [ngClass]="externalStart ? 'form-control' :'form-control'" placeholder="{{'Enter value here' | translate}}"
          (change)="setChanged()">
      </ion-col>&nbsp;
    </ion-row>
  </div>

  <div class="form-group" style="padding:15px 10px 0px 17px">
    <label>{{'Enter Notes' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;">
        * </span> :</label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'Describe the internal abrasion.'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <input type="text" [disabled]='helpService.helpMode || readOnly' [(ngModel)]="notes" maxlength="255"
          [ngClass]="externalStart ? 'form-control' :'form-control'"
          placeholder="{{'Describe the internal abrasion.' | translate}}" (change)="setChanged()">
      </ion-col>&nbsp;
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <input type="text" [disabled]='helpService.helpMode || readOnly' [(ngModel)]="notes" maxlength="255"
          [ngClass]="externalStart ? 'form-control' :'form-control'"
          placeholder="{{'Describe the internal abrasion.' | translate}}" (change)="setChanged()">
      </ion-col>&nbsp;
    </ion-row>
  </div>



  <div>
    <div *ngIf='helpService.helpMode' style="padding:0px 10px 0px 17px" class="form-group"
      tooltip="{{'Select the severity level'|translate}}" positionV="bottom" positionH="right"
      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
      [hideOthers]=helpService.hideOthers>
      <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select the severity level' | translate}} :</ion-label>
      <ion-select labelPlacement="stacked" interface="popover" [(ngModel)]="damageType" [disabled]='helpService.helpMode || readOnly'
        style="height:44px;padding-bottom:10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
        placeholder="{{'Select severity type' | translate}}" (ionChange)="setChanged()">
        <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
        <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
        <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
        <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
      </ion-select>
    </div>

    <div *ngIf='!helpService.helpMode' style="padding:0px 10px 0px 17px" class="form-group">
      <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select the severity level' | translate}} :</ion-label>
      <ion-select labelPlacement="stacked" interface="popover" [(ngModel)]="damageType" [disabled]='helpService.helpMode || readOnly'
        style="height:44px;padding-bottom:10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
        placeholder="{{'Select severity type' | translate}}" (ionChange)="setChanged()">
        <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
        <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
        <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
        <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
      </ion-select>
    </div>
  </div>


  <ion-grid>

    <ion-row>
      <ion-col *ngIf='layerOptionsList.length > 1' size="6" style="padding-left:11px;padding-top:14px;">
        <ion-label style="font-size:15px;">{{'Observation Location, Layer' | translate}}</ion-label>
      </ion-col>
      <ion-col *ngIf='(helpService.helpMode || readOnly) && layerOptionsList.length > 1' size="6"
        style="padding-right:10px;"
        tooltip="{{'Identify which layer of the rope has been affected by the damage.'|translate}}" positionV="bottom"
        [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <ion-select [disabled]='helpService.helpMode || readOnly' labelPlacement="stacked" interface="popover" [(ngModel)]="layerOptions"
          style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
          placeholder="{{'Select layer option' | translate }}" (ionChange)="setChanged()">
          <ion-select-option *ngFor="let option of layerOptionsList" value="{{option}}">{{option}}</ion-select-option>
        </ion-select>
      </ion-col>

      <ion-col *ngIf='!(helpService.helpMode || readOnly) && layerOptionsList.length > 1' size="6"
        style="padding-right:10px;">
        <ion-select [disabled]='helpService.helpMode || readOnly' labelPlacement="stacked" interface="popover" [(ngModel)]="layerOptions"
          style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
          placeholder="{{'Select layer option' | translate }}" (ionChange)="setChanged()">
          <ion-select-option *ngFor="let option of layerOptionsList" value="{{option}}">{{option}}</ion-select-option>
        </ion-select>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ion-row style="padding: 10px 17px 10px 17px;">
    <div class="img-wrap" *ngFor="let item of cameraService.editedImg; let i = index"
      style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
      <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;"
        (click)="(helpService.helpMode || readOnly) ? test() : cameraService.editImage(item.Image, i, 'internal','', that, maxRating, internalRange, 'AMSTEEL-BLUE', true); setChanged()" />
      <ion-icon *ngIf="item.Image!='./assets/img/samson2.png'" name="trash" class="close" slot="end" mode="md" color="danger"
        (click)="(helpService.helpMode || readOnly) ? test() : deleteImage(i)"></ion-icon>
    </div>
    <div *ngIf='helpService.helpMode' [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of rope internal condition'|translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers
        (click)="(helpService.helpMode || readOnly) ? test() :  cameraStarted = true; cameraService.takePicture('','internal', that, maxRating, internalRange, 'AMSTEEL-BLUE', true); setChanged()"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>

    <div *ngIf='!helpService.helpMode' [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png"
        (click)="(helpService.helpMode || readOnly) ? test() :  cameraStarted = true; cameraService.takePicture('','internal', that, maxRating, internalRange, 'AMSTEEL-BLUE', true); setChanged()"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>
  </ion-row>
  <ion-fab *ngIf='!readOnly' vertical="bottom" horizontal="end" slot="fixed"
    tooltip="{{'Tap to save internal abrasion measurement'|translate}}" [topOffset]=helpService.topOffset positionV="top"
    positionH="right" [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="(helpService.helpMode || readOnly) ? test() : save()">
      <fa-icon class="icon-style-other" icon="save" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>

  <app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>
</div>