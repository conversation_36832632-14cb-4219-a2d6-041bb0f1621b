import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { NewObservationPageRoutingModule } from './new-observation-routing.module';

import { NewObservationPage } from './new-observation.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { FooterComponent } from '../components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FontAwesomeModule,
    TranslateModule,
    NewObservationPageRoutingModule,
    FooterComponent
  ],
  declarations: [NewObservationPage]
})
export class NewObservationPageModule {}
