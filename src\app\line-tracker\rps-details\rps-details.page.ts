import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController, AlertController, MenuController, LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { GenericListPage } from 'src/app/generic-list/generic-list.page';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { LmdService } from 'src/app/services/lmd.service';
import { PresentToastService } from 'src/app/services/present-toast.service';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faGrip, faSave, faSortDown } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-rps-details',
  templateUrl: './rps-details.page.html',
  styleUrls: ['./rps-details.page.scss'],
})
export class RpsDetailsPage implements OnInit {

  certNum;
  certName;
  rpsData = [];
  public showLoading = null;
  ropeStatus: any;
  syncButtonClicked = false;
  form: FormGroup;
  eventDateForm: FormGroup;
  eventDate: any = '';
  selectedAssetId: any = '';
  maxDate: any;
  submit: boolean = true;
  installDateForm: FormGroup;
  installDate: any = '';
  winchBrakeTestDateForm: FormGroup;
  winchBreakTestDate: any = '';
  equipmentList: any;
  selectedEquipmentId: any = '';
  selectedEquipmentDesc: any = '';
  selectedEquipmentType: any = '';
  selectedEquipment: any;
  showOperation: boolean = false;
  isVesselMooring: boolean = false;

  constructor(private formBuilder: FormBuilder,
      public alertService: AlertService,
      private toast: PresentToastService,
      public modalController: ModalController,
      public translate: TranslateService,
      public alertController: AlertController,
      public userPreferenceService: UserPreferenceService,
      public lmdService: LmdService,
      private unviredCordovaSDK: UnviredCordovaSDK,
      public menu: MenuController,
      public dataService: DataService,
      private router: Router,
      private loadingController: LoadingController,
      private faIconLibrary: FaIconLibrary) {
         this.faIconLibrary.addIcons(faSave,faGrip, faSortDown);
     }

  async ngOnInit() {
    var maxNewDate = new Date();
    this.maxDate = new Date(maxNewDate.getFullYear(), maxNewDate.getMonth(), maxNewDate.getDate());
    this.selectedAssetId = this.lmdService.getSelectedRPSAssetID()
    this.certNum = this.router.getCurrentNavigation().extras.state['CertificateNum'];
    this.certName = this.router.getCurrentNavigation().extras.state['CertificateName'];
    console.log('received asset' + this.certNum);
    this.loadData();
    this.createForm();
    this.getWinches();
    this.eventDateForm = this.formBuilder.group({
      eventDateCtrl: ['', Validators.required],
    });
    this.installDateForm = this.formBuilder.group({
      installDateCtrl: ['', Validators.required],
    });

    this.winchBrakeTestDateForm = this.formBuilder.group({
      winchBrakTestDateCtlr: [''],
    })
    await this.checkIfWorkboatUser()
  }

  async checkIfWorkboatUser() {
    const selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
    if (!selectedIndustry || selectedIndustry === 'undefined' || selectedIndustry === '""') {
      return;
    }
    try {
      const industryData = JSON.parse(selectedIndustry);
      if (industryData?.ID?.includes("Workboat")) {
        this.showOperation = true;
      }
      if (industryData?.NAME?.toLowerCase().includes("mooring")) {
        this.isVesselMooring = true;
      }
    } catch (error) {
      console.error("Invalid JSON format:", error);
    }
  }
  

  createForm() {
    this.form = this.formBuilder.group({
      status: [null]
    });
  }

  async loadData() {
    await this.presentLoading('Loading...');
    this.getRpsDetails(this.certNum);
  }

  async presentLoading(msg) {
    this.showLoading = await this.loadingController.create({
      message: msg,
      spinner: 'crescent',
      animated: true,
      showBackdrop: true,
      translucent: true
    });
    await this.showLoading.present();
  }

  setChanged() {
    this.submit = true
  }

  statusChanged(RPSData: any, index: any) {
    if(RPSData.ROPE_STATUS == "Retired" || RPSData.ROPE_STATUS == 'Spare') {
      if(this.rpsData[index].equipment != undefined && this.rpsData[index].equipment != '') {
        this.showAlert(index)
      }
    } else {
      this.rpsData[index].ROPE_STATUS = RPSData.ROPE_STATUS
    }
  }

  async showAlert(index) {
    console.log(index)
    var confAlert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      message: '<strong>' + this.translate.instant('Status has been udated the associated equipment will be cleared. Do you want to continue?') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Continue'),
          role: 'continue',
          handler: () => {
            this.rpsData[index].equipment = ''
            this.rpsData[index].equipHeader = undefined
          }
        },
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          handler: () => {
            this.rpsData[index].RPSData.ROPE_STATUS = this.rpsData[index].ROPE_STATUS
          }
        }
      ]
    });
    await confAlert.present();
  }

  async showErrorAlert() {
    var confAlert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      message: '<strong>' + this.translate.instant('Please update the status to add an equipment.') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Ok'),
          role: 'continue',
          handler: () => {
            
          }
        }
      ]
    });
    await confAlert.present();
  }

  async getRpsDetails(certNum) {
    const result = await this.unviredCordovaSDK.dbSelect('ROPE_PRODUCT_SPEC_HEADER', 'CERT LIKE  \'' + certNum + '\'');
    if (result.type === ResultType.success) {
      const resultData = result.data;
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < resultData.length; i++) {
        const equipId = resultData[i].EQUIP_DETAILS;
        const equpData = await this.unviredCordovaSDK.dbSelect('EQUIPMENT_HEADER', `EQUIP_ID = '${equipId}'`);
        if (equpData.type === ResultType.success) {
          const resData = equpData.data;
          console.log('equipment details' + JSON.stringify(resData));
          let instDate = '';
          if(resultData[i].INSTALL_DATE != null && resultData[i].INSTALL_DATE != undefined && resultData[i].INSTALL_DATE != '') {
            instDate = resultData[i].INSTALL_DATE;
          }

          let winchTestDataData;
          if(resultData[i].ASSET!=null && resultData[i].ASSET!='') {
            let response = await this.unviredCordovaSDK.dbSelect('ASSET_HEADER', `ID = '${resultData[i].ASSET}'`);
            if(response.type == ResultType.success) {
              if(response.data.length > 0) {
                winchTestDataData = response.data[0]
              }
            } else if(response.type == ResultType.error) {
              console.log('Error while fetching asset data')
              this.unviredCordovaSDK.logError('rps-details', 'getRpsDetails', 'Error while fetching asset data' + JSON.stringify(response))
            }
          }
          // tslint:disable-next-line:max-line-length
          if (resData.length === 0) {
            const equipdata = '';
            if(this.isVesselMooring) {
              this.rpsData.push({ RPSData: resultData[i], equipment: '', eventDate: '', installDate: instDate, equipHeader: {}, ROPE_STATUS: resultData[i].ROPE_STATUS,  ROPE_STATUS_PREV: resultData[i].ROPE_STATUS, WINCH_TEST_DATE: winchTestDataData.WINCH_BRAKE_TEST_DATE });
            } else {
              this.rpsData.push({ RPSData: resultData[i], equipment: '', eventDate: '', installDate: instDate, equipHeader: {}, ROPE_STATUS: resultData[i].ROPE_STATUS,  ROPE_STATUS_PREV: resultData[i].ROPE_STATUS});
            }
          } else {
            let equipType = '';
            if (resData[0].EQUIP_TYPE !== undefined) {
              equipType = resData[0].EQUIP_TYPE;
            } else {
              equipType = '';
            }
            const formatedData = resData[0].EQUIP_NAME + ' (' + equipType + ')';
            if(this.isVesselMooring) {
              this.rpsData.push({ RPSData: resultData[i], equipment: formatedData, eventDate: '', installDate: instDate, equipHeader: resData[0], ROPE_STATUS: resultData[i].ROPE_STATUS,  ROPE_STATUS_PREV: resultData[i].ROPE_STATUS, WINCH_TEST_DATE: winchTestDataData.WINCH_BRAKE_TEST_DATE });
            } else {
              this.rpsData.push({ RPSData: resultData[i], equipment: formatedData, eventDate: '', installDate: instDate, equipHeader: resData[0], ROPE_STATUS: resultData[i].ROPE_STATUS,  ROPE_STATUS_PREV: resultData[i].ROPE_STATUS});
            }
            console.log('rps data' + JSON.stringify(this.rpsData));
            // this.objArray['EQUIP_DETAILS'] = formatedData ? formatedData : '';
          }
        } else {
          console.log('no data found');
        }
      }
      // this.rpsData = result.data;
      await this.showLoading.dismiss();
      console.log('Rps data' + JSON.stringify(result.data));
    } else {
      console.log('No rps data found');
    }
  }

  async updateStatus(id, status, date, item) {
    if (date == '' || date == undefined || status == '' || status == undefined) {
      this.alertService.showAlert("", "Please select status and date to update")
      return;
    }
    if(this.submit == false) {
      this.alertService.showAlert("", "Already updated please change values to update")
      return;
    }
    this.alertService.present();
    // const status = this.form.get('status').value;
    console.log('on change rope status' + this.form.get('status').value);

    console.log('on change rope status' + JSON.stringify(item));

    if (!this.syncButtonClicked) {
      this.syncButtonClicked = true;
      let obj = {
        "INPUT_RPS_UPDATE": [{
          "INPUT_RPS_UPDATE_HEADER": {
            'RPS_ID': id,
            'RPS_STATUS': status,
            'EXTFLD1': item.ROPE_STATUS_PREV,
            'PKUID': UtilserviceService.guid(),
            'ASSET_ID': this.selectedAssetId,
            'EVENT_DATE': typeof date == "object" ? date.format('YYYY-MM-DD') : (typeof date == "string" ? date : ''),
            'EQUIP_ID': item.equipHeader ? item.equipHeader.EQUIP_ID : '',
            'EQUIP_NAME': item.equipHeader ? item.equipHeader.EQUIP_NAME : '',
            'END_INUSE': item.RPSData.END_IN_USE,
            'INSTALL_DATE': typeof item.installDate == "object" ? item.installDate.format('YYYY-MM-DD') : (typeof item.installDate == "string" ? item.installDate : ''),
            "WINCH_TEST_DATE": ''
          }
        }]
      };
      if(this.isVesselMooring) {
        obj.INPUT_RPS_UPDATE[0].INPUT_RPS_UPDATE_HEADER.WINCH_TEST_DATE = item.WINCH_TEST_DATE==null ? '' : typeof item.WINCH_TEST_DATE == "object" ? item.WINCH_TEST_DATE.format('YYYY-MM-DD') : (typeof item.WINCH_TEST_DATE == "string" ? item.WINCH_TEST_DATE : '')
      } else {
        obj.INPUT_RPS_UPDATE[0].INPUT_RPS_UPDATE_HEADER.WINCH_TEST_DATE = ''
      }
      console.log('obj' + JSON.stringify(obj));
      // tslint:disable-next-line: max-line-length
      const result = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, '', obj, 'ROPE_INSPECTIONS_PA_UPDATE_RPS_STATUS', true);
      console.log('SYnc result' + JSON.stringify(result));
      this.syncButtonClicked = false;
      if(result != undefined) {
        if (result.type === ResultType.error) {
          this.syncButtonClicked = false;
          this.alertService.dismiss();
          this.alertService.showAlert("Error", result.message);
          this.submit = true
        } else {
          this.syncButtonClicked = false;
          this.submit = false
          this.alertService.dismiss();
          this.toast.presentToast('Updated successfully');
        }
      } else {
        this.alertService.dismiss();
      }
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async getWinches() {
    let equipmentRes = await this.unviredCordovaSDK.dbExecuteStatement(`SELECT * FROM EQUIPMENT_HEADER WHERE ASSET_ID = '${this.selectedAssetId}' AND EQUIP_TYPE like '%Winch%' ORDER BY EQUIP_NAME COLLATE NOCASE ASC`)
    if (equipmentRes.type == ResultType.success) {
      if (equipmentRes.data.length > 0) {
        this.equipmentList = equipmentRes.data;
      } else {
        this.equipmentList = []
      }
    } else {
      this.unviredCordovaSDK.logError("rps-details", "getWinches", "Error while getting error from db" + JSON.stringify(this.equipmentList))
    }
  }

  async presentModal(title: string, index) {
    if(this.rpsData[index].RPSData.ROPE_STATUS != "Retired" && this.rpsData[index].RPSData.ROPE_STATUS != 'Spare') {
      var tempList, pageTitle;
      switch (title) {
        case 'EQUIPMENT':
          tempList = this.equipmentList
          pageTitle = this.translate.instant("Equipments")
          break;
      }

      this.alertService.present().then(async () => {
        const modal = await this.modalController.create({
          component: GenericListPage,
          componentProps: { value: tempList, title: pageTitle, page: title }
        });
        await modal.present();

        modal.onDidDismiss().then(async (data) => {
          switch (title) {
            case 'EQUIPMENT':
              this.selectedEquipmentDesc = data.data.data.EQUIP_NAME
              this.selectedEquipmentType = data.data.data.EQUIP_TYPE
              this.selectedEquipmentId = data.data.data.EQUIP_ID
              this.selectedEquipment = data.data.data;
              this.rpsData[index].equipHeader = this.selectedEquipment
              if (this.selectedEquipment.EQUIP_TYPE !== undefined) {
                this.rpsData[index].equipment = this.selectedEquipment.EQUIP_NAME + ' (' + this.selectedEquipment.EQUIP_TYPE + ')';              
              } else {
                this.rpsData[index].equipment = this.selectedEquipment.EQUIP_NAME;
              }           
              break;
          }

        });
      })
    } else {
      this.showErrorAlert()
    }
  }
}
