import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestResourcesSpliceInstructionPageRoutingModule } from './guest-resources-splice-instruction-routing.module';

import { GuestResourcesSpliceInstructionPage } from './guest-resources-splice-instruction.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestResourcesSpliceInstructionPageRoutingModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestResourcesSpliceInstructionPage]
})
export class GuestResourcesSpliceInstructionPageModule {}
