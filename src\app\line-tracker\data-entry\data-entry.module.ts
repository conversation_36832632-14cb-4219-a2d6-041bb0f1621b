import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { DataEntryPageRoutingModule } from './data-entry-routing.module';

import { DataEntryPage } from './data-entry.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from 'src/app/components/footer/footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    DataEntryPageRoutingModule,
    FontAwesomeModule,
    FooterComponent
  ],
  declarations: [DataEntryPage]
})
export class DataEntryPageModule {}
