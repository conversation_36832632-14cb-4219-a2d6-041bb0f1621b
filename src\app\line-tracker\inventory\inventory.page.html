<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
    <ion-buttons slot="primary">
      <ion-button color="primary" slot="end" class="icon" (click)="toggleSearch()" *ngIf="searchIcon">
        <fa-icon class="icon-style" icon="search"></fa-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Inventory</ion-title>
  </ion-toolbar>
  <ion-toolbar *ngIf="searchbar" style="padding-bottom:5px;">
    <ion-searchbar class="searchBar" showCancelButton debounce="500" (ionCancel)="cancelsearchAsset($event)" animated
      (ionInput)="searchAsset($event)">
    </ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="none">
    <ion-item-sliding *ngFor="let item of inventoryData;let i=index">
      <ion-item (click)="openCertificatesList(item.ID, item)" style="border-top:1px solid rgba(199, 199, 199, 0.753);">
        <ion-label style="margin-left: 10px;">
          {{item.NAME}}
        </ion-label>

      </ion-item>

    </ion-item-sliding>
  </ion-list>
  <ion-infinite-scroll threshold="100px" (ionInfinite)="scrolling($event)">
    <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="Loading more data...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>