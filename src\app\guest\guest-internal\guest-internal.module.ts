import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestInternalPageRoutingModule } from './guest-internal-routing.module';

import { GuestInternalPage } from './guest-internal.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TooltipsModule } from 'ionic4-tooltips';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestInternalPageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    TooltipsModule,
    GuestFooterComponent
  ],
  declarations: [GuestInternalPage]
})
export class GuestInternalPageModule {}
