import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class ASSET_ACTIVITY_HEADER extends DATA_STRUCTURE {
    AA_ID: string;
    ASSET: string;
    ASSET_NAME: string;
    ACCOUNT: string;
    ACCOUNT_NAME: string;
    PORT_COUNTRY: string;
    PORT_NAME: string;
    BERTH_NAME: string;
    ALL_FAST: string;
    ALL_LET_GO: string;
    SHIP_SIDE: string;
    AA_STATUS: string;
    SEVERE_LOADING_CONDITION: string;
    WINCH: string;
    ESTIMATED_LINE_LEN_IN_USE: string;
    TAIL_USED: string;
    AVG_AND_PEAK_LOAD: string;
    // AA_EVENT_DATE: string;
}