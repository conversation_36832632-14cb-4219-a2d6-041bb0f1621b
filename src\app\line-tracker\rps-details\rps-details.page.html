<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>RPS Details</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:10px 10px 0px 10px" *ngIf="rpsData.length > 0">
    <ion-item-sliding class="card" style="padding:10px 10px 0px 10px; margin-bottom: 10px;"
      *ngFor="let item of rpsData; let i = index">
      <!-- <form [formGroup]="form" (ngSubmit)="updateStatus()"> -->
      <ion-item *ngIf="certName !== undefined && certName !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Certificate Number'}}: <span
              style="font-size:15px;color:rgb(133, 128, 128)">{{certName}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item *ngIf="item.RPSData.CONSTRUCTION !== undefined && item.RPSData.CONSTRUCTION !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Construction'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.CONSTRUCTION}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item *ngIf="item.RPSData.MATERIAL !== undefined && item.RPSData.MATERIAL !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Material'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.MATERIAL}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item *ngIf="item.RPSData.MFG !== undefined && item.RPSData.MFG !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'MFG'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.MFG}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item *ngIf="item.RPSData.NAME !== undefined && item.RPSData.NAME !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'RPS #'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.NAME}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item *ngIf="item.RPSData.PRODUCT_NAME !== undefined && item.RPSData.PRODUCT_NAME !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Product Name'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.PRODUCT_NAME}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item *ngIf="item.RPSData.DIAM_MM !== undefined && item.RPSData.DIAM_MM !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Diameter (mm)'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.DIAM_MM}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item
        *ngIf="item.RPSData.ORIGINAL_LENGTH_IN_METER !== undefined && item.RPSData.ORIGINAL_LENGTH_IN_METER !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black">
            {{'Original Length (meters)'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.ORIGINAL_LENGTH_IN_METER}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item
        *ngIf="item.RPSData.CURRENT_LENGTH_IN_METER !== undefined && item.RPSData.CURRENT_LENGTH_IN_METER !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black">
            {{'Current Length (meters)'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.CURRENT_LENGTH_IN_METER}}</span></p>
        </ion-label>
      </ion-item>
      <!-- <ion-item *ngIf="item.RPSData.INSTALL_DATE !== undefined && item.RPSData.INSTALL_DATE !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Install Date'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.INSTALL_DATE}}</span></p>
        </ion-label>
      </ion-item> -->
      <div style="padding:15px 10px 0px 17px">
        <label>{{'Install Date' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
        <ion-row>
          <ion-col>
            <div [formGroup]="installDateForm">
              <mat-form-field style="width:100%">
                <input matInput [matDatepicker]="installDatePicker" [max]="maxDate" [(ngModel)]="item.installDate"
                  formControlName="installDateCtrl" [value]="item.installDate" (change)="setChanged()" (dateChange)="setChanged()"
                  placeholder="{{'EVENT_DATE_PLACEHOLDER' | translate}}" step="any" [required]
                  (keydown)="keyPressed($event, 'installDate', false)" (click)="installDatePicker.open()" readonly>
                <mat-datepicker-toggle matSuffix [for]="installDatePicker" style="font-size: x-large;"></mat-datepicker-toggle>
                <mat-datepicker #installDatePicker (selectedChanged)="setChanged()"></mat-datepicker>
              </mat-form-field>
            </div>
          </ion-col>&nbsp;
        </ion-row>
      </div>
      <ion-item *ngIf="item.RPSData.WORKING_HRS !== undefined && item.RPSData.WORKING_HRS !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Working Hours'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.WORKING_HRS}}</span></p>
        </ion-label>
      </ion-item>
      <ion-item *ngIf="item.RPSData.EXTFLD2 !== undefined && item.RPSData.EXTFLD2 !== 'null' && showOperation == true">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Operations'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.EXTFLD2}}</span></p>
        </ion-label>
      </ion-item>
      <!-- <ion-item *ngIf="item.RPSData.END_IN_USE !== undefined && item.RPSData.END_IN_USE !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'End in Use'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.END_IN_USE}}</span></p>
        </ion-label>
      </ion-item> -->

      <div style="Width: 100%; padding-top: 15px">
        <p style="margin-top: 4px;padding-left: 14px ;margin-bottom: 0px;">{{'End in Use'}}:
        </p>
        <div *ngIf="item.RPSData.END_IN_USE !== undefined && item.RPSData.END_IN_USE !== 'null'">
          <div>
            <ion-item>
              <ion-select
                style="max-width: 100% !important; width: 100% !important; padding: 0px 5px 0px 0px !important;"
                [(ngModel)]='item.RPSData.END_IN_USE' labelPlacement="stacked" interface="popover"
                placeholder="{{'WINCHES_END_TYPE_PLACEHOLDER' | translate}}">
                <ion-select-option value="A">A</ion-select-option>
                <ion-select-option value="B">B</ion-select-option>
              </ion-select>
            </ion-item>
          </div>
        </div>
      </div>
      <!-- <ion-item *ngIf="item.RPSData.EQUIP_DETAILS !== undefined && item.RPSData.EQUIP_DETAILS !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Equipment Detail'}}: <span
              style="font-size:14px;color:rgb(133, 127, 127)">{{item.equipment}}</span></p>
        </ion-label>
      </ion-item> -->
      <div style="width: 100%; padding-top: 15px">
        <p style="width: 100%; padding-left:16px" >{{'Equipment Detail'}}: </p>
        <ion-item
          (click)="presentModal('EQUIPMENT', i)"
          no-lines text-wrap tappable style="width: 100% !important;" *ngIf="equipmentList && equipmentList.length >= 0"
          class="ion-item-generic-style" mode="ios">
          <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
            *ngIf="item.equipment == ''" class="drop-down-arrow  value-field">{{ 'Select Equipment' | translate
                }}</div>
          <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
            *ngIf="item.equipment != ''" class="value-field">{{item.equipment}}</div>
          <ion-note item-right style="display:inline-flex">
            <p class="drop-down-arrow">
              <fa-icon class="icon-style" icon="sort-down"></fa-icon>
            </p>
          </ion-note>
        </ion-item>
      </div>
      @if(isVesselMooring) {
        <div style="padding:15px 10px 0px 17px">
          <label>{{'Winch Brake Test Date' | translate}}</label>
          <ion-row>
            <ion-col>
              <div [formGroup]="winchBrakeTestDateForm">
                <mat-form-field style="width:100%">
                  <input matInput [matDatepicker]="winchBrakeTestDatePicker" [max]="maxDate" [(ngModel)]="item.WINCH_TEST_DATE"
                    formControlName="winchBrakTestDateCtlr" [value]="item.WINCH_TEST_DATE"
                    placeholder="{{'Select Winch Brake Test Date' | translate}}" step="any"
                    (keydown)="keyPressed($event, 'WINCH_TEST_DATE', false)" (click)="winchBrakeTestDatePicker.open()" readonly>
                  <mat-datepicker-toggle matSuffix [for]="winchBrakeTestDatePicker" style="font-size: x-large;"></mat-datepicker-toggle>
                  <mat-datepicker #winchBrakeTestDatePicker (selectedChanged)="setChanged()"></mat-datepicker>
                </mat-form-field>
              </div>
            </ion-col>&nbsp;
          </ion-row>
        </div>
      }
      
      <ion-item *ngIf="item.RPSData.LAST_MAINTENANCE !== undefined && item.RPSData.LAST_MAINTENANCE !== 'null'">
        <ion-label style="font-size:16px">
          <p style="font-size:16px;color:black"> {{'Last Maintenance'}}:
            <span style="font-size:14px;color:rgb(133, 127, 127)">{{item.RPSData.LAST_MAINTENANCE}}</span></p>
        </ion-label>
      </ion-item>
      <div style="padding:15px 10px 0px 0px">
        <ion-label style="font-size:16px">
          <p style="font-size:16px; color: black; padding-left: 17px;">{{' Installation Status'}}:<span
              style="color:rgb(221, 82, 82);font-size:15px;"> * </span></p>
        </ion-label>
        <ion-item>
          <ion-select
            style="color: rgb(133, 127, 127);max-width: 100% !important;width: 100% !important;padding-left: 0px;"
            [(ngModel)]='item.RPSData.ROPE_STATUS' labelPlacement="stacked" interface="popover" (ngModelChange)="setChanged(); statusChanged(item.RPSData, i)">
            <ion-select-option value="Spare">Spare</ion-select-option>
            <!-- <ion-select-option value="Warehouse">Warehouse</ion-select-option> -->
            <ion-select-option value="Installed">Installed</ion-select-option>
            <ion-select-option value="Retired">Retired</ion-select-option>
          </ion-select>
        </ion-item>
      </div>
      <div style="padding:15px 10px 0px 17px">
        <label>{{'EVENT_DATE_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
        <ion-row>
          <ion-col>
            <div [formGroup]="eventDateForm">
              <mat-form-field style="width:100%">
                <input matInput [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="item.eventDate"
                  formControlName="eventDateCtrl" [value]="item.eventDate" (change)="setChanged()" (dateChange)="setChanged()"
                  placeholder="{{'EVENT_DATE_PLACEHOLDER' | translate}}" step="any" [required]
                  (keydown)="keyPressed($event, 'eventDate', false)" (click)="picker.open()" readonly>
                <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
                <mat-datepicker #picker (selectedChanged)="setChanged()"></mat-datepicker>
                <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
              </mat-form-field>
            </div>
          </ion-col>&nbsp;
        </ion-row>
      </div>
      <ion-button color="primary" (click)="updateStatus(item.RPSData.ID, item.RPSData.ROPE_STATUS, item.eventDate, item)">
        Update Status</ion-button>
      <!-- </form> -->
    </ion-item-sliding>
  </div>

  <div style="padding:40px 10px 0px 10px; text-align: center;" *ngIf="rpsData.length == 0">
    <p>
      No RPS found for this Certificate
    </p>
  </div>
</ion-content>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>