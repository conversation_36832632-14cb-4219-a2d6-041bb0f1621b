import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
// import { library } from '@fortawesome/fontawesome-svg-core';
import { faEye, faSearch, faSortDown, faTrash } from '@fortawesome/free-solid-svg-icons';
import { MenuController, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { LmdService } from 'src/app/services/lmd.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { UserPreferenceService } from 'src/app/services/user-preference.service';
@Component({
  selector: 'app-completed-lmd',
  templateUrl: './completed-lmd.page.html',
  styleUrls: ['./completed-lmd.page.scss'],
})
export class CompletedLmdPage implements OnInit {

  searchbar = false;
  searchIcon = true;
  inProgressData = [];
  initialData = [];
  title = true;
  tempArray = [];

  isUtility: boolean = false;

  constructor(private router: Router,
    public helpService: HelpService,
    public menu: MenuController,
    public dataService: DataService,
    public lmdService: LmdService,
    public translate: TranslateService,
    public alertController: AlertController,
    public utilityService: UtilserviceService,
    public userPreferenceService: UserPreferenceService,
    private unviredCordovaSDK: UnviredCordovaSDK,
    public faiconlibrary: FaIconLibrary) {
      this.faiconlibrary.addIcons(faSearch,faTrash,faEye,faSortDown)
    }

  ngOnInit() {
    this.getInitialData();
    this.checkIfUtility();
  }

  toggleSearch() {
    this.searchbar = !this.searchbar;
    this.searchIcon = !this.searchIcon;
    this.title = !this.title;
  }
  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }
  showDetails() {
    this.router.navigate(['/lmd-details']);
  }
  icon(index) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }

  cancelHistoricalItem(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.initializeItems();
    }
  }
  searchHistoricalItem(ev) {
    // this.getInitialData();
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.inProgressData = this.initialData.filter((item) => {
        return (
          (item.LID && item.LID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LMD_DATA.cert && item.LMD_DATA.cert.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LMD_DATA.totalWorkingHour && item.LMD_DATA.totalWorkingHour.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    }
    else {
      this.getInitialData();
    }
  }

  initializeItems() {
    this.inProgressData = this.initialData;
  }
  async getInitialData() {
    const result = await this.unviredCordovaSDK.dbSelect("LMD_HEADER", "LMD_STATUS = 'Completed' AND LMD_TYPE != 'RoutineInspection'");
    if (result.type === ResultType.success) {
      this.initialData = result.data;
      console.log('db data' + JSON.stringify(this.initialData));
      this.inProgressData = [];
      const resultData = result.data;

      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < resultData.length; i++) {
        const value = result.data[i];
        const dataToFormat = value.LMD_DATA;
        const formatedLmdData = dataToFormat.replace(/\"/g, '"');
        const newFormatedData = JSON.parse(formatedLmdData);
        value.LMD_DATA = newFormatedData;
        this.inProgressData.unshift(value);
      }
    } else {
      alert('No data found');
    }
  }

  navigateToDetailsPage(item) {
    this.lmdService.setSelectedAsset(item)
    this.lmdService.setSelectedAssetActivity(item)
    this.lmdService.setSelectedActivityFromList(false)
    this.utilityService.setSelectedLMD(item);
    this.lmdService.setIsFromInProgress(false)
    this.lmdService.setIsFromCompleted(true);
    this.utilityService.setLMDEditMode(true);
    this.lmdService.setReadOnlyMode(true)
    this.utilityService.setSelectedLMD(item);
    switch (item.LMD_TYPE) {
      case 'Cropping':
        this.router.navigate(['cropping-lmd']);
        break;
      case 'Repair':
        this.router.navigate(['repair-lmd']);
        break;
      case 'EndForEnd':
        this.router.navigate(['end-for-end-lmd']);
        break;
      case 'EquipmentInsp':
        this.router.navigate(['equipment-insp-lmd']);
        break;
      case 'Rotation':
        this.router.navigate(['rotation-lmd']);
        break;
      case 'InstallLine':
        this.router.navigate(['install-line-lmd']);
        break;
      case 'RequestNewLine':
        this.router.navigate(['request-new-line-lmd']);
        break;
      case 'AssetActivity':
        if (item.LMD_DATA.isGeneral == true) {
          this.router.navigate(['general-line-usage-details']);
        } else {
          this.router.navigate(['asset-activity-details']);
        }
        break;
    }

  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async inProgressItemTrash(index, item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Lmd'),
      message: '<strong>' + this.translate.instant('You want to delete this entry') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: async () => {
            this.icon(index);
            var result = await this.markAsDeleted(item);
            this.inProgressData.splice(index, 1);
            this.getInitialData();
          }
        }
      ]
    });
    await alert.present();
  }

  async markAsDeleted(item) {
    var whereClause = "LMD_ID like '" + item.LMD_ID + "'"
    return await this.unviredCordovaSDK.dbDelete("LMD_HEADER", whereClause)
  }

  async checkIfUtility() {
    var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
    if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
      selectedIndustry = JSON.parse(selectedIndustry)
      if((selectedIndustry.ID.includes('Utility'))) {
        this.isUtility = true;
      }
      return
    } else {
      return
    }
  }
}
