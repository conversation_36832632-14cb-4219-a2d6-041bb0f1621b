<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'GENERAL_LINE_USAGE_TITLE' | translate}}</ion-title>
    <ion-buttons slot="end">
      <!-- <ion-button color="primary" (click)="saveLMD()" class="help-button-style">
        <fa-icon class="icon-style-help" icon="list"></fa-icon>
      </ion-button> -->
      <ion-button color="primary" class="help-button-style">

        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"
          (click)="helpService.switchMode(); enableAllFields();"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="info-circle"
          (click)="helpService.switchMode(); disableAllFields();"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:15px 10px 0px 3px" *ngIf="!fromList">
    <p style="margin-top: 8px; padding-left: 14px">{{'CROPPING_ASSET_LIST_LABEL' | translate}} <span
        style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></p>

    <div style="width: 100%;">
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_ASSET_ACTIVITY_ASSET_LIST'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <ion-item (click)="(helpService.helpMode || readOnly) ? test() : presentModal('ASSET')" no-lines text-wrap
            tappable style="width: 100% !important;" *ngIf="assetList && assetList.length >= 0"
            class="ion-item-generic-style" mode="ios">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="selectedAssetName == ''" class="drop-down-arrow  value-field">{{ 'Select Asset' | translate
            }}</div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="selectedAssetName != ''" class="value-field">{{selectedAssetName}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <ion-item (click)="(helpService.helpMode || readOnly) ? test() : presentModal('ASSET')" no-lines text-wrap
            tappable style="width: 100% !important;" *ngIf="assetList && assetList.length >= 0"
            class="ion-item-generic-style" mode="ios">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="selectedAssetName == ''" class="drop-down-arrow  value-field">{{ 'Select Asset' | translate
            }}</div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="selectedAssetName != ''" class="value-field">{{selectedAssetName}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
        </ion-col>
      </ion-row>
    </div>

  </div>

  <div style="padding:15px 10px 0px 3px" *ngIf="!fromList">
    <p style="margin-top: 8px; padding-left: 14px">{{'COUNTRY' | translate}} <span
        style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></p>

    <div style="width: 100%;">
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_ASSET_ACTIVITY_PORT_COUNTRY'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <ion-item (click)="(helpService.helpMode || readOnly) ? test() : presentModal('COUNTRY')" no-lines text-wrap
            tappable style="width: 100% !important;" *ngIf="portCountryList && portCountryList.length >= 0"
            class="ion-item-generic-style" mode="ios">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="portCountry == ''" class="drop-down-arrow  value-field">{{ 'COUNTRY_PLACEHOLDER' | translate}}
            </div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="portCountry != ''" class="value-field">{{portCountry}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <ion-item (click)="(helpService.helpMode || readOnly) ? test() : presentModal('COUNTRY')" no-lines text-wrap
            tappable style="width: 100% !important;" *ngIf="portCountryList && portCountryList.length >= 0"
            class="ion-item-generic-style" mode="ios">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="portCountry == ''" class="drop-down-arrow  value-field">{{ 'COUNTRY_PLACEHOLDER' | translate}}
            </div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="portCountry != ''" class="value-field">{{portCountry}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
        </ion-col>
      </ion-row>
    </div>
  </div>
  <div *ngIf="isUtility == false" style="padding:15px 10px 0px 17px">
    <label>{{'LOCATION' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'HELP_TEXT_ASSET_ACTIVITY_PORT_NAME'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <mat-form-field style="width:100%">
          <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="portName" maxlength="225"
            placeholder="{{'LOCATION_PLACEHOLDER' | translate}}">
        </mat-form-field>
      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <div [formGroup]="portNameForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="portName" maxlength="225"
              placeholder="{{'LOCATION_PLACEHOLDER' | translate}}" step="any" (change)="onChangeDisable('portName')"
              [required] formControlName="portNameCtrl">
            <mat-error *ngIf="hasErrorStart('portName')">{{portNameErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </ion-col>&nbsp;
    </ion-row>
  </div>

  <div style="padding:15px 10px 0px 17px">
    <label>{{'START_OF_JOB' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-row *ngIf='helpService.helpMode'>


      <!-- <ion-col tooltip="{{'HELP_TEXT_ASSET_ACTIVITY_PORT_ENTERED'|translate}}" positionV="bottom"
      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
      [hideOthers]=helpService.hideOthers style='width: 50%;'>
      <mat-form-field style="width:100%">
        <input matInput disabled [matDatepicker]="picker" [max]="maxDate"
          placeholder="{{'START_OF_JOB_PLACEHOLDER' | translate}}" [(ngModel)]="eventDate" disabled>
        <mat-datepicker-toggle disabled matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
    </ion-col> -->


    <ion-col style='width: 50%;'>
      <div [formGroup]="portEnteredForm">
        <mat-form-field style="width:100%">
          <input disabled matInput [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="portEntered"
            placeholder="{{'START_OF_JOB_PLACEHOLDER' | translate}}" step="any"
            (keydown)="keyPressed($event, 'eventDate', false)" (change)="onChangeDisable('portEntered')"
            (dateInput)="addEvent('portEntered', $event)" (dateChange)="addEvent('change', $event)"
            formControlName="portEnteredCtrl" (focus)="picker.open()" (click)="picker.open()" readonly>
          <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
          <mat-datepicker disabled #picker></mat-datepicker>
        </mat-form-field>
      </div>
    </ion-col>



   
      <ion-col style='width: 50%;'>
        <mat-form-field style="width:100%" id="time-input">
          <input disabled matInput placeholder="Select Time" [value]="portEnteredTime ? (portEnteredTime | date: 'hh:mm a') : ''">
          <fa-icon matSuffix icon="clock" style="margin-right:5px"></fa-icon>
        </mat-form-field>
        <ion-popover  disabled trigger="time-input" triggerAction="click" backdropDismiss="true" [keepContentsMounted]="true">
          <ng-template>
            <ion-datetime  #datetime id="datetime" presentation="time" displayFormat="HH:mm a" locale="en-US" [value]="currentTime"
            (ionChange)="change($event,'portEnteredTime')">
            <ion-buttons slot="buttons">
              <ion-button color="danger" (click)="datetime.cancel(true)">Cancel</ion-button>
              <ion-button color="primary" (click)="confirm($event,datetime,'portEnteredTime')">Done</ion-button>
            </ion-buttons>
          </ion-datetime>
          </ng-template>
        </ion-popover>
      </ion-col>
    </ion-row>


    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col style='width: 50%;'>
        <div [formGroup]="portEnteredForm">
          <mat-form-field style="width:100%">
            <input matInput [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="portEntered"
              placeholder="{{'START_OF_JOB_PLACEHOLDER' | translate}}" step="any"
              (keydown)="keyPressed($event, 'eventDate', false)" (change)="onChangeDisable('portEntered')"
              (dateInput)="addEvent('portEntered', $event)" (dateChange)="addEvent('change', $event)"
              formControlName="portEnteredCtrl" (focus)="picker.open()" (click)="picker.open()" readonly>
            <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
      </ion-col>

      <ion-col style='width: 50%;' >
        <mat-form-field style="width:100%" id="time-input">
          <input  matInput placeholder="Select Time" [value]="portEnteredTime ? (portEnteredTime | date: 'hh:mm a') : ''" 
          [disabled]='helpService.helpMode || readOnly ||selectedAssetName == "" || selectedAssetName == null || selectedAssetName == undefined'>
          <fa-icon matSuffix icon="clock" style="margin-right:5px" ></fa-icon>
        </mat-form-field>
        <ion-popover trigger="time-input" triggerAction="click" backdropDismiss="true" [keepContentsMounted]="true">
          <ng-template>
            <ion-datetime #datetime id="datetime" presentation="time" displayFormat="HH:mm a" locale="en-US" [value]="currentTime"
            (ionChange)="change($event,'portEnteredTime')"   >
            <ion-buttons slot="buttons">
              <ion-button color="danger" (click)="datetime.cancel(true)">Cancel</ion-button>
              <ion-button color="primary" (click)="confirm($event,datetime,'portEnteredTime')">Done</ion-button>
            </ion-buttons>
          </ion-datetime>
          </ng-template>
        </ion-popover>
      </ion-col>
    </ion-row>
  </div>

  <div style="padding:15px 10px 0px 17px">
    <label>{{'END_OF_JOB' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-row *ngIf='helpService.helpMode'>

      <ion-col tooltip="{{'HELP_TEXT_ASSET_ACTIVITY_PORT_EXITED'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style='width: 50%;'>
        <mat-form-field style="width:100%">
          <input matInput disabled [matDatepicker]="picker" [max]="maxDate"
            placeholder="{{'END_OF_JOB_PLACEHOLDER' | translate}}" [(ngModel)]="portExited" disabled>
          <mat-datepicker-toggle disabled matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </ion-col>
      

      
      <ion-col style='width: 50%;'>
        <mat-form-field style="width:100%" id="time-input1">
          <input disabled matInput placeholder="Select Time" [value]="portExitedTime ? (portExitedTime | date: 'hh:mm a') : ''">
          <fa-icon matSuffix icon="clock"></fa-icon>
        </mat-form-field>
        <p *ngIf="!portExitedTimeValid" style="color:red;font-size: 15px;">Enter valid time</p>
        <ion-popover disabled trigger="time-input1" triggerAction="click" backdropDismiss="true" [keepContentsMounted]="true">
          <ng-template>
            <ion-datetime #datetime1 id="datetime1" presentation="time" displayFormat="HH:mm a" locale="en-US" [value]="currentTime"(ionChange)="change($event,'portExitedTime')">
            <ion-buttons slot="buttons">
              <ion-button color="danger" (click)="datetime1.cancel(true)">Cancel</ion-button>
              <ion-button color="primary" (click)="confirm($event,datetime1,'portExitedTime')">Done</ion-button>
            </ion-buttons>
          </ion-datetime>
          </ng-template>
        </ion-popover>
      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col style='width: 50%;'>
        <div [formGroup]="portExitedForm">
          <mat-form-field style="width:100%">
            <input matInput [matDatepicker]="picker" [max]="maxDate" [min]="portEntered" [(ngModel)]="portExited" placeholder="{{'END_OF_JOB_PLACEHOLDER' | translate}}" step="any"
              (keydown)="keyPressed($event, 'eventDate', false)" (change)="onChangeDisable('portExited')" (dateInput)="addEvent('portExited', $event)" (dateChange)="addEvent('portExited', $event)"
            formControlName="portExitedCtrl" (focus)="picker.open()" (click)="picker.open()" readonly>
            <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
      </ion-col>
      <ion-col style='width: 50%;' >
        <mat-form-field style="width:100%" id="time-input1">
          <input matInput placeholder="Select Time" [value]="portExitedTime ? (portExitedTime | date: 'hh:mm a') : ''" 
          [disabled]='helpService.helpMode || readOnly ||selectedAssetName == "" || selectedAssetName == null || selectedAssetName == undefined'>
          <fa-icon matSuffix icon="clock"></fa-icon>
        </mat-form-field>
        <p *ngIf="!portExitedTimeValid" style="color:red;font-size: 15px;">Enter valid time</p>
        <ion-popover trigger="time-input1" triggerAction="click" backdropDismiss="true" [keepContentsMounted]="true">
          <ng-template>
            <ion-datetime #datetime1 id="datetime1" presentation="time" displayFormat="HH:mm a" locale="en-US" [value]="currentTime"(ionChange)="change($event,'portExitedTime')">
            <ion-buttons slot="buttons">
              <ion-button color="danger" (click)="datetime1.cancel(true)">Cancel</ion-button>
              <ion-button color="primary" (click)="confirm($event,datetime1,'portExitedTime')">Done</ion-button>
            </ion-buttons>
          </ion-datetime>
          </ng-template>
        </ion-popover>
      </ion-col>
    </ion-row>
  </div>

  <div style="padding:15px 10px 0px 17px" *ngIf="isUtility == false">
    <label (click)="switchMode()" class="severTooltip" style="display: inline-flex; width: 100%;">{{'SEVERE_LOADING_CONDITION_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span>
        <fa-icon (click)="switchMode()" style="margin-left:10px;" tooltip="Documents environmental conditions that cause above normal tension to mooring lines in use" 
            event="click" 
            positionV="bottom"
            hideOthers="true" 
            arrow duration
            class="icon-style-info"
            icon="info-circle">
      </fa-icon>
    </label>
    
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'HELP_TEXT_ASSET_ACTIVITY_WEATHER_CONDITION'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <ion-radio-group [(ngModel)]="severeWeatherCondition">
          <ion-row class="ion-radio-row">
            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio mode="md" item-left labelPlacement="end" slot="start" value="yes" [disabled]='helpService.helpMode || readOnly'></ion-radio>
                <ion-label>{{'Yes' | translate}}</ion-label>
              </ion-item>
            </ion-col>

            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio mode="md" item-left labelPlacement="end" slot="start" value="no" [disabled]='helpService.helpMode || readOnly'></ion-radio>
                <ion-label>{{'No' | translate}}</ion-label>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-radio-group>
      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <ion-radio-group [(ngModel)]="severeWeatherCondition"  (ionChange)="resetEnvCondition($event)">
          <ion-row class="ion-radio-row">
            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio mode="md" item-left  labelPlacement="end" slot="start" value="yes"
                  [disabled]='helpService.helpMode || readOnly || selectedAssetName ==""'></ion-radio>
                <ion-label>{{'Yes' | translate}}</ion-label>
              </ion-item>
            </ion-col>

            <ion-col>
              <ion-item class='ion-radio-item-style'>
                <ion-radio mode="md" item-left labelPlacement="end" slot="start" value="no"
                  [disabled]='helpService.helpMode || readOnly || selectedAssetName ==""'></ion-radio>
                <ion-label>{{'No' | translate}}</ion-label>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-radio-group>
      </ion-col>&nbsp;
    </ion-row>
  </div>
  <div style="padding:15px 10px 0px 17px;" *ngIf="severeWeatherCondition == 'yes'">
    <p>{{'Conditions' | translate}} <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></p>
    <div style="display: inline-flex;width: 100%;">
      <ion-select [disabled]='helpService.helpMode || readOnly'
        style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px;width: 100%;"
        placeholder="{{'Select Condition' | translate}}" labelPlacement="stacked"  interface="popover" [interfaceOptions]="{ cssClass: 'customPopover' }" [(ngModel)]="envConditionType" [multiple]="true" (ionChange)="handleChange($event)"
        (ionCancel)="handleCancel()"
        (ionDismiss)="handleDismiss()">
        <ion-select-option *ngFor="let envCondition of envConditions" value="{{envCondition.ENV_COND_NAME}}"><span
            *ngIf="envCondition.ENV_COND_NAME ==''">{{envCondition.ID}}</span><span
            *ngIf="envCondition.ENV_COND_NAME !=''">{{envCondition.ENV_COND_NAME}}</span>
        </ion-select-option>
      </ion-select>
      <div style="padding: 10px;">
        <fa-icon class="icon-style-help" icon="times" (click)="clearEnvCondition()"></fa-icon>
      </div>
    </div>
  </div>


  <div *ngIf="severeWeatherCondition == 'yes' && severeWeatherConditionOther == true" style="padding:15px 10px 0px 17px">
    <label>{{'Weather Condition' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'HELP_TEXT_ASSET_ACTIVITY_WEATHER_CONDITION_OTHER'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <mat-form-field style="width:100%">
          <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="otherCondition" maxlength="25"
            placeholder="{{'Weather Condition' | translate}}">
        </mat-form-field>
      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <div [formGroup]="otherConditionForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="otherCondition" maxlength="25"
              placeholder="{{'Weather Condition' | translate}}" step="any" (change)="onChangeDisable('otherCondition')"
              [required] formControlName="otherConditionCtrl">
            <mat-error *ngIf="hasErrorStart('otherCondition')">{{otherConditionErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </ion-col>&nbsp;
    </ion-row>
  </div>

  <div style="padding:100px 10px 0px 17px">
  </div>
  <!-- Save Button -->
  <ion-fab vertical="bottom" horizontal="end" slot="fixed" *ngIf='showSave != true'>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveAndContinue()">
      <fa-icon class="icon-style-other" icon="arrow-right" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>

  <ion-fab vertical="bottom" horizontal="end" slot="fixed" *ngIf='showSave == true && !readOnly'>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveWorkboat()">
      <fa-icon class="icon-style-other" icon="save" style="font-size:20px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>



<!-- Footer -->
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); enableAllFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()"
    (Inspections)="gotoInspections()" (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>