<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
    <ion-title>Certificates</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="none">
    <ion-item-sliding *ngFor="let item of certificateData; let i = index">

      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);"
        (click)="showRpdDetails(item.CERTIFICATE_NUM, item.NAME)">

        <ion-label style="margin-left: 10px;">
          <h2 style="color:rgb(23, 23, 24); font-weight: bold;">Certificate: {{item.NAME}}</h2>
          <p style="color:rgba(104, 102, 102, 0.753);">Product: {{item.PRODUCT}}</p>
        </ion-label>
      </ion-item>
    </ion-item-sliding>
  </ion-list>
  <div *ngIf='certificateData && certificateData.length == 0'
    style="width: 100% !important; text-align: center !important; padding-top: 30px !important">
    {{'No certificates found for this asset.'}}
  </div>
  
  <ion-fab vertical="bottom" horizontal="end">
    <ion-fab-button color="primary">
      <fa-icon class="icon-style" icon="chevron-circle-left" style="font-size: 25px;" class="icon-style">
      </fa-icon>
    </ion-fab-button>
  
    <ion-fab-list side="top" style="margin-bottom: 60px;">
      <ion-fab-button (click)="generateCSV('export')" data-desc="Export" color="primary"
        *ngIf="certificateData.length > 0">
        <fa-icon class="icon-style" icon="download" style="font-size: 16px;" class="icon-style"></fa-icon>
      </ion-fab-button>
  
      <ion-fab-button *ngIf="device.platform != 'browser' && certificateData.length > 0" data-desc="Email"
        (click)="composeEmail()" color="primary">
        <fa-icon class="icon-style" icon="envelope" style="font-size: 16px;" class="icon-style"></fa-icon>
  
      </ion-fab-button>
      <ion-fab-button (click)="naviateToRequestNewLine()" color="primary" data-desc="Request New Line">
        <fa-icon class="icon-style" icon="plus" style="font-size: 16px;" class="icon-style"></fa-icon>
      </ion-fab-button>
  
    </ion-fab-list>
  </ion-fab>
</ion-content>

<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
