import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestResourceInspectionsPageRoutingModule } from './guest-resource-inspections-routing.module';

import { GuestResourceInspectionsPage } from './guest-resource-inspections.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestResourceInspectionsPageRoutingModule,
    FontAwesomeModule,
    GuestFooterComponent
  ],
  declarations: [GuestResourceInspectionsPage]
})
export class GuestResourceInspectionsPageModule {}
