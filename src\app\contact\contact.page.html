<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="back()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Contact Us' |translate}}</ion-title>
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
             <fa-icon class="icon-style-help"  *ngIf='helpService.helpMode' icon="times"></fa-icon>
            <fa-icon class="icon-style-help"  *ngIf='!helpService.helpMode' icon="info-circle"></fa-icon>
        </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <form id="contact-form" method="post" action="contact.php" role="form">

    <div class="messages"></div>

    <div class="controls">

      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="form_name">{{'Name' |translate}} *</label>
            <input [(ngModel)]="name" type="text" name="name" class="form-control"
              placeholder="{{'Please enter your name' | translate}} *">
            <div class="help-block with-errors"></div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="form_email">{{'Email' | translate}} *</label>
            <input [(ngModel)]="email" type="email" name="email" class="form-control"
              placeholder="{{'Please enter your email' | translate}} *">
            <div class="help-block with-errors"></div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <div class="form-group">
            <label for="form_message">{{'Message' | translate}} *</label>
            <textarea [(ngModel)]="message" name="message" class="form-control"
              placeholder="{{'Message for me' | translate}} *" rows="2"></textarea>
            <div class="help-block with-errors"></div>
          </div>
        </div>
        <div class="col-12 col-md-12 col-sm-12">
          <!-- <div class="row"> -->
            <input id="myInput" type="file" *ngIf="device.platform == 'browser' || platformId == 'electron'" style="visibility:hidden; display: none;" accept="image/x-png,image/jpeg,image/jpg,image/png" (change)="onFileSelected($event)" (click)="onInputClick($event)"/>
            <input id="myInputFile" *ngIf="device.platform == 'browser' || platformId == 'electron'" type="file" style="visibility:hidden; display: none;" 
            accept="image/x-png,image/jpeg,image/jpg,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain" (change)="onFileSelected($event)" (click)="onInputClick($event)" />
            
            <!-- <input id="myInputFile" *ngIf="device.platform == 'browser' || platformId == 'electron'" type="file" style="visibility:hidden; display: none;" 
            accept="image/x-png,image/jpeg,image/jpg,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain" /> -->
            <div class="img-wrap" *ngFor="let file of files; let i = index" style="width:50%;height:230px;border:1px solid grey;border-radius:5px;">
              <img class="image" [src]='(checkFileType(file.type)==1)?file.url:(checkFileType(file.type)==2)?"./assets/img/pdfThumb.png":(checkFileType(file.type)==3)?"./assets/img/docThumb.png":"./assets/img/excelThumb.png"' 
                    style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;" (click)="openFile(file)"/>
              <div *ngIf="(checkFileType(file.type)>1)" class="fileTitle">
                <p style="font-size:14px;">{{ file.title }}</p>
              </div>
              <ion-icon name="trash" class="close" slot="end" mode="md" color="danger"(click)="deleteFile(i)"></ion-icon>
            </div>
            <div class="img-wrap" style="width:50%;height:230px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
              <div class="addBtn">
                <ion-button (click)="presentActionSheet()"><ion-icon name="add-outline" color="white"></ion-icon> Add Files</ion-button>
              </div>
            </div>
          <!-- </div> -->
        </div>
        <div class="col-md-12">
          <input type="submit" style="float:right" class="btn btn-primary btn-send" (click)="askToTurnOnGPS()"
            value="{{'Send message' | translate}}">
        </div>
      </div>
    </div>
  </form>
  <div class='contact-details'>
    <h5 style="text-align: center">{{'Customer Service' | translate}}</h5>
    <div style="text-align: center">
      <span><a href="tel:13603844669 ">**************</a> | <a href="tel:18002277673 ">**************
          (ROPE)</a></span><br>
      <span>{{'Fax:' | translate}} <a href="fax:13603840572">**************</a> | <a
          href="fax:18002999246 ">**************</a></span><br>
      <span><a href="mailto:<EMAIL> ">custserv&#64;samsonrope.com</a></span>
    </div>
  </div>
</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>