import { Component, Ng<PERSON>one, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Menu<PERSON>ontroller, AlertController } from '@ionic/angular';
import { DataService } from '../services/data.service';
import { LmdService } from '../services/lmd.service';
import { UtilserviceService } from '../services/utilservice.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faGrip, faListCheck, faSearch } from '@fortawesome/free-solid-svg-icons';
import { UserPreferenceService } from '../services/user-preference.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-new-observation',
  templateUrl: './new-observation.page.html',
  styleUrls: ['./new-observation.page.scss'],
})
export class NewObservationPage implements OnInit {

  selectedInspectionHeader: any;
  noOptions: boolean = false;
  noOfExterals = 0;
  noOfInternals = 0;
  standard = [
    // [{label:'External', class:'col8'}, {label:'Internal', class:'col4'}], 
    // [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon : './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
  ];

  compression = [

  ];

  others = [
    // [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon : './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
    // [{ label: 'Discoloration', class: 'col6', icon : './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon : './assets/img/icons/Contamination_WHT_1024x1024.png' }],
    // [{ label: 'Pulled Strands/Yarns', class: 'col6', icon : './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }, { label: 'Twist', class: 'col6', icon : './assets/img/icons/Twist_WHT_1024x1024.png' }],
    // [{ label: 'Inconsistent Diameter', class: 'col6',  icon : './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Wire Breaks', class: 'col6', icon: 'unlink' }],
    // [{ label: 'Corrosion', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }, { label: 'Basket Deformation', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
    // [{ label: 'Bird Caging', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }, { label: 'Protrusion', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
    // [{ label: 'Heat Damage', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }, { label: 'Parting', class: 'col6', icon : './assets/img/icons/Parted_WHT_1024x1024.png' }],
    // [{ label: 'Compression', class: 'col6', icon : './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Seizing', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
    // [{ label: 'Waviness', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }, { label: 'Kinking', class: 'col6', icon : './assets/img/icons/Kinking_WHT_1024x1024.png'}],
    // [{ label: 'Flattening', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
  ];

  equipmentInsp = [
    [{ label: 'Equipment Inspection', class: 'col6', icon: 'search' }],
  ]
  selectedIndustry: any;
  isUtility: boolean=false;

  // others1 = this.others = [
  //   [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
  //   [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
  //   [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
  //   [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
  //   [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
  // ];

  constructor(private router: Router,
    public unviredSDK: UnviredCordovaSDK,
    public faIconLibrary: FaIconLibrary,
    public dataService: DataService,
    public lmdService: LmdService,
    public menu: MenuController,
    private service: UtilserviceService,
    private ngZone: NgZone,
    public alertController: AlertController,
    private userPreferenceService:UserPreferenceService,
    private translate: TranslateService) {
    this.selectedInspectionHeader = this.service.getSelectedInspectionHeader();
    this.faIconLibrary.addIcons(faBars, faEnvelope, faGrip, faListCheck, faSearch)
  }
  async ionViewWillEnter() {
    this.selectedIndustry = await this.userPreferenceService.getUserPreference("industry")
    this.selectedIndustry = this.selectedIndustry != '' ? JSON.parse(this.selectedIndustry) : '';
    this.isUtility = this.selectedIndustry && this.selectedIndustry?.ID?.includes('Utility');
    this.initializeAnomaly();
    const obj = this.service.getBaselineArray();
    const extcount = obj.filter(t => t.type === 'External');
    this.noOfExterals = extcount.length;
    this.noOfInternals = (obj.filter(t => t.type === 'Internal')).length;

    var externalArray = this.service.getAllData().filter(t => t.DATA.type === 'External');
    var internalArray = this.service.getAllData().filter(t => t.DATA.type === 'Internal');

    if (this.standard.length > 0) {
      this.standard[0][0].count = externalArray.length;
      if(!(this.isUtility && this.selectedInspectionHeader.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && this.selectedInspectionHeader.CONSTRUCTION == "12-Strand")) {
        this.standard[0][1].count = internalArray.length;
      }
    }
  }

  initializeAnomaly() {
    if (this.selectedInspectionHeader) {
      if (this.selectedInspectionHeader.PRODUCT == undefined || this.selectedInspectionHeader.PRODUCT == null || this.selectedInspectionHeader.PRODUCT == '') {
        this.selectedInspectionHeader.PRODUCT = ''
        // this.showDataMissingAlert('Product Name')
        // return;
      }
      if(this.selectedInspectionHeader.CONSTRUCTION == undefined || this.selectedInspectionHeader.CONSTRUCTION == '' || this.selectedInspectionHeader.CONSTRUCTION == null) {
        if ((!(this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Link-It').toLowerCase()) > -1) &&
        !(this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Link It').toLowerCase()) > -1) &&
        !(this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('LinkIt').toLowerCase()) > -1))) {
          this.showDataMissingAlert('Construction')
          return;
        }
      }
      if(this.selectedInspectionHeader.PRODUCT_TYPE == undefined || this.selectedInspectionHeader.PRODUCT_TYPE == null || this.selectedInspectionHeader.PRODUCT_TYPE == '') {
        this.selectedInspectionHeader.PRODUCT_TYPE = ''
        // this.showDataMissingAlert('Product Type')
        // return;
      }
      
      if (this.selectedInspectionHeader.PRODUCT_TYPE && this.selectedInspectionHeader.PRODUCT_TYPE != '') {
        if ((this.selectedInspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Conventional Fiber (Class I)').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('HMSF (Class II)').toLowerCase()) > -1)) {
          if (this.selectedInspectionHeader.CONSTRUCTION && this.selectedInspectionHeader.CONSTRUCTION != '') {
            if ((this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('3-Strand').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6-Strand').toLowerCase()) > -1) ||
              ((this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand').toLowerCase()) > -1) && (this.selectedInspectionHeader.CONSTRUCTION == '8-Strand')) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12-Strand').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('16-Strand').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cover Only').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Round Plait').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Solid Braid').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x36 IWRC EEIPS').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x37 IWRC').toLowerCase()) > -1)) {
              if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  }
                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];

                  } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];

                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];

                  }

                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }
              } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  }

                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  }
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
                (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  }

                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  }

                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG == 'TBD') || (this.selectedInspectionHeader.PRODUCT_CONFIG == 'Other') || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
                (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  }

                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  } else {
                    this.standard = [
                      [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                    ];
                    this.others = [
                      [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                      [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                      [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                      [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                      [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                      [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                    ];
                  }

                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }
            } else if ((this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Multi-core').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Parallel Fiber core').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Other').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Multi-Core').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Parallel Fiber Core').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Core Dependent Double Braid').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cork Line').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('HMSF').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Lead Line').toLowerCase()) > -1)) {
              if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }
              } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
                (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }
              } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG == 'TBD') || (this.selectedInspectionHeader.PRODUCT_CONFIG == 'Other') || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
                (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }
              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }
            } else if (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Single Layer').toLowerCase()) > -1) {
              if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
                (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
                if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                  this.others = [
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                    [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                    [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                    [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                    [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }
              } else if (this.selectedInspectionHeader.PRODUCT_CONFIG == 'TBD') {
                this.others = [
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                  [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                  [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                  [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                  [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
                ];
              }
            } else {
              this.standard = [
                [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
              ];
              this.others = [
                [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
              ];
            }
          } else {
            this.standard = [
              [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
            ];
            this.others = [
              [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
              [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
              [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
              [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
              [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
              [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
            ];
          }
        } else if ((this.selectedInspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Steel').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Hybrid SWR/Synthetic').toLowerCase()) > -1) ||
        (this.selectedInspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Steel Wire and Hybrid SWR/Synthetic').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Wire').toLowerCase()) > -1) ||
        this.selectedInspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Steel Wire').toLowerCase()) > -1) {
          if ((this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Multi-core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Parallel Fiber core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Other').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Multi-Core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Parallel Fiber Core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Core Dependent Double Braid').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cork Line').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('HMSF').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Lead Line').toLowerCase()) > -1)||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6X36 IWRC EEIPS').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6X36 IWRC').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6X36SES(37) + IWRC').toLowerCase()) > -1)) {
            if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                this.others = [
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                  [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                  [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                  [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                  [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
                ];
              } else {
                // this.standard = [
                //   [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                // ];
                // this.others = [
                //   [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                //   [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                //   [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                //   [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                //   [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                //   [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                // ];
                this.others = [
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                  [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                  [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                  [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                  [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
                ];
              }
            } else {
              if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                this.others = [
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                  [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                  [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                  [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                  [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
                ];
              } else {
                // this.standard = [
                //   [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                // ];
                // this.others = [
                //   [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                //   [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                //   [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                //   [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                //   [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                //   [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                // ];
                this.others = [
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                  [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                  [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                  [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                  [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
                ];
              }
            }
          } else {
            this.others = [
              [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
              [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
              [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
              [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
              [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
            ];
          }
        } else {
          this.standard = [
            [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
          ];
          this.others = [
            [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
            [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
            [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
            [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
            [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
            [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
          ];
        }
      } else {
        if (this.selectedInspectionHeader.CONSTRUCTION && this.selectedInspectionHeader.CONSTRUCTION != '') {
          if ((this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('3-Strand').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6-Strand').toLowerCase()) > -1) ||
            ((this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand').toLowerCase()) > -1) && (this.selectedInspectionHeader.CONSTRUCTION == '8-Strand')) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12-Strand').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('16-Strand').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cover Only').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Round Plait').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Solid Braid').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x36 IWRC EEIPS').toLowerCase()) > -1) || (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x37 IWRC').toLowerCase()) > -1)) {
            if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }
              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];

                } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];

                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];

                }

              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }
            } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }
              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }

            } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }

            } else if (this.selectedInspectionHeader.PRODUCT_CONFIG == 'TBD' || this.selectedInspectionHeader.PRODUCT_CONFIG == 'Other') {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
                  (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                } else {
                  this.standard = [
                    [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                  ];
                  this.others = [
                    [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                    [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                    [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                    [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                    [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                    [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                  ];
                }

              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }

            } else {
              this.standard = [
                [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
              ];
              this.others = [
                [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
              ];
            }
          } else if ((this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Multi-core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Parallel Fiber core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Other').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Multi-Core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Parallel Fiber Core').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Core Dependent Double Braid').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cork Line').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('HMSF').toLowerCase()) > -1) ||
            (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Lead Line').toLowerCase()) > -1)) {
            if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }

            } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }
            } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }
            } else if ((this.selectedInspectionHeader.PRODUCT_CONFIG == 'TBD') || (this.selectedInspectionHeader.PRODUCT_CONFIG == 'Other') || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 1) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }
            } else {
              this.standard = [
                [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
              ];
              this.others = [
                [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
              ];
            }
          } else if (this.selectedInspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Single Layer').toLowerCase()) > -1) {
            if ((this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (this.selectedInspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (this.selectedInspectionHeader.HAS_CHAFE == 0) {
                this.others = [
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                  [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                  [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                  [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                  [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
                ];
              } else {
                this.standard = [
                  [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
                ];
                this.others = [
                  [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
                  [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
                  [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
                  [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
                  [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
                  [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
                ];
              }
            } else if (this.selectedInspectionHeader.PRODUCT_CONFIG == 'TBD') {
              this.others = [
                [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
                [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
                [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
                [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
                [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
              ];
            }
          } else {
            this.others = [
              [{ label: 'Inconsistent Diameter', class: 'col6', icon: '' }, { label: 'Wire Breaks', class: 'col6', icon: '' }],
              [{ label: 'Waviness', class: 'col6', icon: '' }, { label: 'Corrosion', class: 'col6', icon: '' }],
              [{ label: 'Basket Deformation', class: 'col6', icon: '' }, { label: 'Kinking', class: 'col6', icon: '' }],
              [{ label: 'Bird Caging', class: 'col6', icon: '' }, { label: 'Protrusion', class: 'col6', icon: '' }],
              [{ label: 'Flattening', class: 'col6', icon: '' }, { label: 'Heat Damage', class: 'col6', icon: '' }]
            ];
          }
        } else {
          this.standard = [
            [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
          ];
          this.others = [
            [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
            [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
            [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
            [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
            [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
            [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
          ];
        }
      }
    } else {
      this.standard = [
        [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
      ];
      this.others = [
        [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
        [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
        [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
        [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
        [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
        [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
      ];
    }

    if ((this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Link-It').toLowerCase()) > -1) ||
    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('Link It').toLowerCase()) > -1) ||
    (this.selectedInspectionHeader.PRODUCT.toLowerCase().indexOf(('LinkIt').toLowerCase()) > -1)) {
      this.standard = [
        [{ label: 'External', class: 'col6', icon: './assets/img/icons/Inspection_EXTERNAL_Wht_1024x1024.png', size: 0, count: 0 }, { label: 'Internal', class: 'col6', icon: './assets/img/icons/Inspection_INTERNAL_Wht_1024x1024.png', size: 0, count: 0 }]
      ];
      this.others = [
        [{ label: 'Cuts', class: 'col6', icon: './assets/img/icons/Cutting_WHT_1024x1024.png' }, { label: 'Melting', class: 'col6', icon: './assets/img/icons/Melting_WHT_Flame_1024x1024.png' }],
        [{ label: 'Discoloration', class: 'col6', icon: './assets/img/icons/Discoloration_WHT_1024x1024.png' }, { label: 'Contamination', class: 'col6', icon: './assets/img/icons/Contamination_WHT_1024x1024.png' }],
        [{ label: 'Compression', class: 'col6', icon: './assets/img/icons/Compression_WHT_1024x1024.png' }, { label: 'Pulled Strands/Yarns', class: 'col6', icon: './assets/img/icons/Pulled_Strand_WHT_1024x1024.png' }],
        [{ label: 'Twist', class: 'col6', icon: './assets/img/icons/Twist_WHT_1024x1024.png' }, { label: 'Kinked Yarns', class: 'col6', icon: './assets/img/icons/Kinking_WHT_1024x1024.png' }],
        [{ label: 'Inconsistent Diameter', class: 'col6', icon: './assets/img/icons/Inconsistent_Diameter_WHT_1024x1024.png' }, { label: 'Parting', class: 'col6', icon: './assets/img/icons/Parted_WHT_1024x1024.png' }],
        [{ label: 'Linear Damage', class: 'col6', icon: './assets/img/icons/Linear_Damage_WHT_1024x1024.png' }]
      ];
    } 

    // ! if the selected industry is utility then show Repair option in the grid to conduct a repair
    if(this.isUtility) {
      if(this.others[this.others.length - 1].length < 2) {
        this.others[this.others.length-1].push({ label: 'Repair', class: 'col6', icon: './assets/img/Constructions/other_WHite.png' });
      } else {
        this.others.push([{ label: 'Repair', class: 'col6', icon: './assets/img/Constructions/other_WHite.png' }]);
      }
    }

    // ! hide internal abrasion for Tenex ropes of Utility industry
    if(this.isUtility && this.selectedInspectionHeader.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && this.selectedInspectionHeader.CONSTRUCTION == "12-Strand") {
      // this.selectedProduct = "TENEX"
      // this.product = 'TENEX'
      // this.maxRating = '4';
      this.standard = this.standard.map(group => group.filter(item => !item.label.includes('Internal')));
    } 
  }

  async showDataMissingAlert(field) {
    const alert = await this.alertController.create({
      backdropDismiss: true,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: 'Warning',
      message: 'The certificate is not complete ' + field + ' is missing please contact your administrator',
      buttons: ['OK']
    });
    await alert.present();
  }

  ngOnInit() {
  }

  navigateToObservation(id) {
    switch (id) {
      case 'External':
        this.router.navigateByUrl('/external-abrasion');
        break;
      case 'Internal':
        this.router.navigateByUrl('/internal-abrasion');
        break;
    }
  }
  standardobservation(item) {
    switch (item) {
      case 'Cuts':
        this.router.navigateByUrl('/cuts');
        break;
      case 'Melting':
        this.router.navigateByUrl('/melting');
        break;
      case 'Discoloration':
        this.router.navigateByUrl('/discoloration');
        break;
      case 'Bird Caging':
        this.router.navigateByUrl('/caging');
        break;
      case 'Contamination':
        this.router.navigateByUrl('/contamination');
        break;
      case 'Parting':
      case 'Parted':
        this.router.navigateByUrl('/parting');
        break;
      case 'Twist':
        this.router.navigateByUrl('/twist');
        break;
      case 'Diameter':
      case 'Inconsistent Diameter':
        this.router.navigateByUrl('/inconsistent-diameter');
        break;
      case 'Compression':
        this.router.navigateByUrl('/compression');
        break;
      case 'Wire Breaks':
        this.router.navigateByUrl('/wire-breaks');
        break;
      case 'Corrosion':
        this.router.navigateByUrl('/corrosion');
        break;
      case 'Kinking':
      case 'Kinked Yarns':
        this.router.navigateByUrl('/kinking');
        break;
      case 'Basket Deformation':
        this.router.navigateByUrl('/basket-deformation');
        break;
      case 'Protrusion':
        this.router.navigateByUrl('/protrusion');
        break;
      case 'Heat Damage':
        this.router.navigateByUrl('/heat-damage');
        break;
      case 'Waviness':
        this.router.navigateByUrl('/waviness');
        break;
      case 'Flattening':
        this.router.navigateByUrl('/flattening');
        break;
      case 'Pulled Strands/Yarns':
        this.router.navigateByUrl('/pulled');
        break;
      case 'Linear Damage':
        this.router.navigateByUrl('/linear-damage');
        break;
      case 'Repair':
        this.presentAlertLmd()
        break;
    }
  }
  openMenu() {
    this.menu.toggle('menu');
  }

  showOptionsPage() {
    this.router.navigateByUrl('/popup-options');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async navigateToLMD(selectedItem) {
    this.service.setSelectedLMDPage(selectedItem)
    this.lmdService.setReadOnlyMode(false);
    this.service.setLMDEditMode(false)
    this.lmdService.setInspectionMode(true);
    this.lmdService.setInspection(this.selectedInspectionHeader)
    var workorder = {};
    if(this.selectedInspectionHeader.WORK_ORDER != '' && this.selectedInspectionHeader.WORK_ORDER != null && this.selectedInspectionHeader.WORK_ORDER != undefined) {
      var tempWo = await this.unviredSDK.dbSelect("WORK_ORDER_HEADER", `ID = '${this.selectedInspectionHeader.WORK_ORDER}'`)
      if(tempWo.type == ResultType.success) {
        if (tempWo.data.length > 0) {
          workorder = tempWo.data[0]
        } else {
          workorder =  {
            "WO_NUMBER" : this.selectedInspectionHeader.WORK_ORDER,
            "WO_INTERNAL" : this.selectedInspectionHeader.WORK_ORDER,
            "ID" : this.selectedInspectionHeader.WORK_ORDER
          }
        } 
      } else {
        workorder =  {
          "WO_NUMBER" : this.selectedInspectionHeader.WORK_ORDER,
          "WO_INTERNAL" : this.selectedInspectionHeader.WORK_ORDER,
          "ID" : this.selectedInspectionHeader.WORK_ORDER
        }
      }      
    } else if (this.selectedInspectionHeader.CUSTOMER_WORK_ORDER != '' && this.selectedInspectionHeader.CUSTOMER_WORK_ORDER != null && this.selectedInspectionHeader.CUSTOMER_WORK_ORDER != undefined) {
      workorder =  {
        "WO_NUMBER" : this.selectedInspectionHeader.CUSTOMER_WORK_ORDER,
        "WO_INTERNAL" : this.selectedInspectionHeader.CUSTOMER_WORK_ORDER,
        "ID" : this.selectedInspectionHeader.WORK_ORDER
      }
    }
    this.lmdService.setSelectedLMDWorkorder(workorder)
    this.lmdService.setIsFromCompleted(false);
    switch (selectedItem) {
      case 'Equipment Inspection':
        this.router.navigate(['equipment-insp-lmd'])
        break;
    }
  }

    async presentAlertLmd() {
      const alert = await this.alertController.create({
        message: '<strong>Would you like to document Repair?</strong>',
        buttons: [
          {
            text: this.translate.instant('No'),
            role: 'cancel',
            cssClass: 'secondary',
            handler: (blah) => {
  
            }
          }, {
            text: this.translate.instant('Yes'),
            handler: async () => {
              await this.navigateToNewLMD();
            }
          }
        ]
      });
  
      await alert.present();
    }
  
    async navigateToNewLMD() {
      this.lmdService.setReadOnlyMode(false);
      this.service.setLMDEditMode(false)
      this.lmdService.setInspectionMode(true);
      this.lmdService.setInspection(this.selectedInspectionHeader)
      var workorder = {};
      if(this.selectedInspectionHeader.WORK_ORDER != '' && this.selectedInspectionHeader.WORK_ORDER != null && this.selectedInspectionHeader.WORK_ORDER != undefined) {
        var tempWo = await this.unviredSDK.dbSelect("WORK_ORDER_HEADER", `ID = '${this.selectedInspectionHeader.WORK_ORDER}'`)
        if(tempWo.type == ResultType.success) {
          if (tempWo.data.length > 0) {
            workorder = tempWo.data[0]
          } else {
            workorder =  {
              "WO_NUMBER" : this.selectedInspectionHeader.WORK_ORDER,
              "WO_INTERNAL" : this.selectedInspectionHeader.WORK_ORDER,
              "ID" : this.selectedInspectionHeader.WORK_ORDER
            }
          } 
        } else {
          workorder =  {
            "WO_NUMBER" : this.selectedInspectionHeader.WORK_ORDER,
            "WO_INTERNAL" : this.selectedInspectionHeader.WORK_ORDER,
            "ID" : this.selectedInspectionHeader.WORK_ORDER
          }
        }      
      } else if (this.selectedInspectionHeader.CUSTOMER_WORK_ORDER != '' && this.selectedInspectionHeader.CUSTOMER_WORK_ORDER != null && this.selectedInspectionHeader.CUSTOMER_WORK_ORDER != undefined) {
        workorder =  {
          "WO_NUMBER" : this.selectedInspectionHeader.CUSTOMER_WORK_ORDER,
          "WO_INTERNAL" : this.selectedInspectionHeader.CUSTOMER_WORK_ORDER,
          "ID" : this.selectedInspectionHeader.WORK_ORDER
        }
      }
      this.lmdService.setSelectedLMDWorkorder(workorder)
      this.lmdService.setIsFromCompleted(false);
      this.lmdService.selectedInspection = this.selectedInspectionHeader;
      this.lmdService.selectedCertNo = this.selectedInspectionHeader.CERTIFICATE_NUM
      // this.router.navigate(['new-lmd']);
      this.lmdService.fromObaservations = true;
      // this.ut.setSelectedLMDPage(selectedItem)
      this.lmdService.setReadOnlyMode(false);
      this.router.navigate(['repair-lmd'])
         
    }

}
