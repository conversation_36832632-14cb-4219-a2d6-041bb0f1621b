import { Component, OnInit,NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { <PERSON>u<PERSON><PERSON>roller, AlertController, ToastController } from '@ionic/angular';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { UnviredCordovaSDK, ResultType, RequestType, NotificationListenerType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { LocationAccuracy } from '@awesome-cordova-plugins/location-accuracy/ngx';
import { INPUT_CREATE_CASE_HEADER } from '../../models/INPUT_CREATE_CASE_HEADER';
import { INPUT_CREATE_CASE_ATTACHMENT } from '../../models/INPUT_CREATE_CASE_ATTACHMENT';
import { AlertService } from '../services/alert.service';
import { Platform } from '@ionic/angular';
import { DataService } from '../services/data.service';
import { ActionSheetController } from '@ionic/angular';
import { Chooser, ChooserOptions } from '@awesome-cordova-plugins/chooser/ngx';
import { Camera,CameraOptions } from '@awesome-cordova-plugins/camera/ngx';
import { File } from "@awesome-cordova-plugins/file/ngx";
import { FilePath } from '@awesome-cordova-plugins/file-path/ngx';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FileAttachmentService } from '../services/file-attachment.service';
import { FileOpenerService } from '../services/file-opener.service';
import { UserPreferenceService } from '../services/user-preference.service';
import { faBars, faHome, faSortDown , faDownload , faTh, faEnvelope, faTasks, faTimes, faWindowRestore } from '@fortawesome/free-solid-svg-icons';
import { library } from '@fortawesome/fontawesome-svg-core';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { PlatformService } from '../services/platform.service';

declare var cordova: any;
@Component({
  selector: 'app-contact',
  templateUrl: './contact.page.html',
  styleUrls: ['./contact.page.scss'],
})
export class ContactPage implements OnInit {
  platformId: string = this.platformService.getPlatformId();
  private win: any = window;
  footerClose = true;
  name = '';
  email = '';
  message = '';
  images:any[]=[];
  files:any[]=[];
  userHeader: any;
  constructor(private router: Router,
    private platformService: PlatformService,
    public dataService: DataService,
    private menu: MenuController,
    public helpService: HelpService,
    private service: UtilserviceService,
    private geolocation: Geolocation,
    public unviredCordovaSdk: UnviredCordovaSDK,
    public alertService: AlertService,
    public alertController: AlertController,
    public platform: Platform,
    public toastController: ToastController,
    private actionSheetController:ActionSheetController,
    private fileChooser:Chooser,
    private camera:Camera,
    private zone:NgZone,
    private file:File,
    private filePath:FilePath,
    private _DomSanitizationService: DomSanitizer,
    private translate:TranslateService,
    public device:Device,
    private locationAccuracy:LocationAccuracy,
    private fileAttachService:FileAttachmentService,
    private userPreferenceService: UserPreferenceService,
    public faIconLibrary: FaIconLibrary,
    private fileOpenerService: FileOpenerService) { 
      this.faIconLibrary.addIcons(faDownload , faSortDown , faBars , faTasks , faTh, faEnvelope )
     }

  ngOnInit() {
    console.log(this.device.version);
    window.addEventListener('keyboardDidHide', () => {
      this.footerClose = true;
    });
    window.addEventListener('keyboardWillShow', (event) => {
      this.footerClose = false;
      document.activeElement.scrollIntoView()
    });

    this.unviredCordovaSdk.registerNotifListener().subscribe(async (result) => {
      this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", JSON.stringify(result));
      console.log("Result account setup "
        + result.type)
      console.log("notification data:",result);
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          // this.dataService.clearRefreshDownloadComplete();
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "DATA RECIVED");
          break;
        case NotificationListenerType.dataChanged:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "DATA CHANGED");
          if(cordova.platformId=='electron') {
            if(result.data.length>0 && result.data[0].CASE_ID!="" && result.data[0].CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("CONTACT", "dataChanged", "dataChanged" + result.data[0]);
              await this.presentToast(`Message has been sent. Case created with Id:${result.data[0].CASE_ID}`);
            }
          } else if(this.device.platform =='iOS') {
            if(result.data.length>0 && result.data[0].fields.CASE_ID!="" && result.data[0].fields.CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("CONTACT", "dataChanged", "dataChanged" + result.data[0]);
              await this.presentToast(`Message has been sent. Case created with Id:${result.data[0].fields.CASE_ID}`);
            }
          } else {
            if(result.data.length>0 && result.data[0].hasOwnProperty('INPUT_CREATE_CASE_HEADER') && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!=null && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!="")  {
              this.unviredCordovaSdk.logDebug("CONTACT", "dataChanged", "dataChanged" + result.data[0]);
              await this.presentToast(`Message has been sent. Case created with Id:${result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID}`);
            }
          }
          break;
        case NotificationListenerType.dataSend:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "DATA SENT");
          break;
        case NotificationListenerType.appReset:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "APP RESET");
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "attachmentDownloadSuccess");
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "attachmentDownloadError");
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "IMCOMING DATA PROCESSING FINISHED");
          if(cordova.platformId=='electron') {
            if(result.data.length>0 && result.data[0].CASE_ID!="" && result.data[0].CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("CONTACT", "incomingDataProcessingFinished", "incomingDataProcessingFinished" + result.data[0]);
              await this.presentToast(`Message has been sent. Case created with Id:${result.data[0].CASE_ID}`);
            }
          } else if(this.device.platform =='iOS') {
            if(result.data.length>0 && result.data[0].fields.CASE_ID!="" && result.data[0].fields.CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("CONTACT", "incomingDataProcessingFinished", "incomingDataProcessingFinished" + result.data[0]);
              await this.presentToast(`Message has been sent. Case created with Id:${result.data[0].fields.CASE_ID}`);
            }
          } else {
            if(result.data.length>0 && result.data[0].hasOwnProperty('INPUT_CREATE_CASE_HEADER') && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!=null && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!="")  {
              this.unviredCordovaSdk.logDebug("CONTACT", "incomingDataProcessingFinished", "incomingDataProcessingFinished" + result.data[0]);
              await this.presentToast(`Message has been sent. Case created with Id:${result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID}`);
            }
          }
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "attachmentDownloadWaiting");
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.infoMessage:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "infoMessage");
          this.dataService.clearDataRefresh();
          this.handleInfoMessage(result)
          break;
        case NotificationListenerType.serverError:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "serverError");
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.unviredCordovaSdk.logInfo("CONTACT", "registerNotifListener", "attachmentDownloadCompleted");
          this.dataService.clearDataRefresh();
          break;
        default:
          this.dataService.clearDataRefresh();
          break;
      }
    });
  }

  public handleInfoMessage(result) {

    this.unviredCordovaSdk.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });
    this.showAlertError(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  goToLineTracker() {
    if (this.name !== '' || this.email !== '' || this.message != '') {
      this.service.menuAlert('lineTracker', 'line-tracker-home')
    } else {
      this.dataService.navigateToLineTracker(this)
    }
  }

  gotoHome() {
    if (this.name !== '' || this.email !== '' || this.message != '') {
      this.service.menuAlert('home', 'home')
    } else {
      this.router.navigate(['home']);
    }
  }

  gotoInspections() {
    if (this.name !== '' || this.email !== '' || this.message != '') {
      this.service.menuAlert('inspections', 'inspection-home')
    } else {
      this.router.navigate(['inspection-home']);
    }
  }

  gotoResources() {
    if (this.name !== '' || this.email !== '' || this.message != '') {
      this.service.menuAlert('resources', 'resource')
    } else {
      this.router.navigate(['resource']);
    }
  }

  gotoContact() {
    if (this.name !== '' || this.email !== '' || this.message != '') {
      this.service.menuAlert('contact', 'contact')
    } else {
      this.router.navigate(['contact']);
    }
  }
  back() {
    this.router.navigate(['home']);
  }

  async askToTurnOnGPS() {
    if (this.name == "" || this.email == "" || this.message == "" || this.name == undefined || this.email == undefined || this.message == undefined) {
      this.showAlert("Please fill all fields to send message")
      return;
    } else if (this.email != "") {
      var EMAIL_REGEX = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (EMAIL_REGEX.test(this.email) == false) {
        this.showAlert("Please enter valid email id")
        return
      }
    }
    var waitAlert = await this.alertService.present()
    // this.locationAccuracy.request(this.locationAccuracy.REQUEST_PRIORITY_HIGH_ACCURACY).then(()=>{
    //   this.sendMessage(true)
    // }).catch((error)=>{
    //   this.sendMessage(false)
    // })
    await this.sendMessageToServer(0, 0)
  }

  // async sendMessage(sendLocation) {
  //   if (sendLocation == true) {
  //     var location = this.geolocation.getCurrentPosition().then(async res=>{
  //       await this.sendMessageToServer(res.coords.latitude, res.coords.longitude)
  //     })
  //     // console.log(" LOCATION " + location.coords.latitude + " ," + location.coords.longitude)
  //   } else {
  //     await this.sendMessageToServer(0, 0)
  //   }
  // }

  getBrowser() {
    if ((navigator.userAgent.indexOf("Opera") || navigator.userAgent.indexOf('OPR')) != -1) {
      console.log('Opera');
      return 'Opera';
    } else if (navigator.userAgent.indexOf("Edg") != -1) {
      console.log('Edge');
      return 'Edge';
    } else if (navigator.userAgent.indexOf("Chrome") != -1) {
      console.log('Chrome');
      return 'Chrome';
    } else if (navigator.userAgent.indexOf("Safari") != -1) {
      console.log('Safari');
      return 'Safari';
    } else if (navigator.userAgent.indexOf("Firefox") != -1) {
      console.log('Firefox');
      return 'Firefox';
    } else if ((navigator.userAgent.indexOf("MSIE") != -1)) { //IF IE > 10
      console.log('IE');
      return 'IE';
    } else {
      console.log('unknown');
      return 'Unknown';
    }
  }

  async getUserHeader() {
    var userHeaderRes = await this.dataService.getUserHeader();
    if (userHeaderRes.type == ResultType.success) {
      this.userHeader = userHeaderRes.data[0];
    } else {
      this.unviredCordovaSdk.logInfo("CONTACT", "getUserHeader", "Error while getting error from db" + JSON.stringify(userHeaderRes))
    }
  }

  async sendMessageToServer(lat, long) {
    var selectedAccount = await this.userPreferenceService.getUserPreference('account');
    var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
    let currentBrowser = this.getBrowser()
    await this.getUserHeader();
    var caseHeader = new INPUT_CREATE_CASE_HEADER()
    caseHeader.ID = UtilserviceService.guid();
    caseHeader.CUST_NAME = this.name;
    caseHeader.EMAIL = this.email;
    caseHeader.MESSAGE = this.message;
    caseHeader.LOCATION = lat + " ," + long
    caseHeader.CASE_ID = "";
    caseHeader.ACCOUNT_ID = JSON.parse(selectedAccount).ID;
    caseHeader.ACCOUNT_NAME = JSON.parse(selectedAccount).NAME;
    caseHeader.ASSET_ID  = JSON.parse(selectedAsset).ID;
    caseHeader.ASSET_NAME  = JSON.parse(selectedAsset).NAME;
    caseHeader.EXTFLD1 = currentBrowser;
    caseHeader.CONTACT_ID = this.userHeader.CONATCT_ID;
    caseHeader.CONTACT_NAME = "";
    var insertRec = await this.unviredCordovaSdk.dbInsert("INPUT_CREATE_CASE_HEADER", caseHeader, true)
    if (insertRec.type == ResultType.success) {
      // var path = await this.unviredCordovaSdk.getLogFilePath()
      var insertedCase = await this.unviredCordovaSdk.dbSelect("INPUT_CREATE_CASE_HEADER", "ID Like '" + caseHeader.ID + "'")
      if (insertedCase.type == ResultType.success) {
      }
      let inputObject = {
        "INPUT_CREATE_CASE_HEADER": insertedCase.data[0]
      }
      // create attachment items
      for(let file of this.files) {
        await this.fileAttachService.createAttachmentItem(insertedCase.data[0].LID,file);
      }
      if(this.device.platform=='browser') {
        var temp = await this.unviredCordovaSdk.syncForeground(RequestType.RQST, inputObject, "", "ROPE_INSPECTIONS_PA_CREATE_CASE",true )
      } else {
        var temp = await this.unviredCordovaSdk.syncBackground(RequestType.RQST, inputObject, "", "ROPE_INSPECTIONS_PA_CREATE_CASE","INPUT_CREATE_CASE",insertedCase.data[0].LID,false)
      }
      console.log("SYNC Result " + JSON.stringify(temp))
      if (temp.type == ResultType.error) {
        this.alertService.dismiss();
        this.showAlert(temp.message)
        this.unviredCordovaSdk.logError("Contact", "send message ", JSON.stringify(temp))
      } else {
        this.name = "";
        this.email = "";
        this.message = "";
        this.files=[];
        if (this.device.platform != 'browser') {
          await this.unviredCordovaSdk.sendLogToServer().then(async (result) => {
            console.log('upload result: ', result);
          }).catch(() => {
          });
        }
      }
      await this.alertService.dismiss();
      if(this.device.platform=='browser') {
        await this.presentToast('Message has been sent.');
      }
      // await this.presentToast();
    } else {
      this.unviredCordovaSdk.logError("Contact", "send message", JSON.stringify(insertRec))
    }
  }

  async createAttachmentItem(lid: string, path: string) {
    var fileName = path.substring(path.lastIndexOf('/') + 1)
    var attachmentObject = new INPUT_CREATE_CASE_ATTACHMENT()
    attachmentObject.FID = lid
    attachmentObject.UID = UtilserviceService.guid();
    attachmentObject.EXTERNAL_URL = "";
    attachmentObject.FILE_NAME = fileName;
    attachmentObject.LOCAL_PATH = path;
    attachmentObject.ATTACHMENT_STATUS = "SAVED_FOR_UPLOAD"
    console.log("ATTACHMENT OBJECT" + JSON.stringify(attachmentObject))
    return await this.unviredCordovaSdk.createAttachmentItem("INPUT_CREATE_CASE_ATTACHMENT", attachmentObject)
  }

  async showAlert(message) {
    const alert = await this.alertController.create({
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  async showAlertError(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  async presentToast(data) {
    this.unviredCordovaSdk.logInfo("Home", "preasenting toast", data)
    const toast = await this.toastController.create({
      message: data,
      duration: 5000,
      position: 'middle',
      color: 'dark'
    });
    toast.present();
  }

  async presentActionSheet() {
    const actionSheet = await this.actionSheetController.create({
      cssClass: 'my-custom-class',
      mode :"ios",
      buttons: [{
        text: 'Camera',
        role: 'Camera',
        handler: async () => {
          if(this.device.platform=='Android') {
            await this.alertService.present();
            const options: CameraOptions = {
              quality: 50,
              destinationType: this.camera.DestinationType.DATA_URL,
              sourceType: this.camera.PictureSourceType.CAMERA,
              encodingType: this.camera.EncodingType.JPEG,
              mediaType: this.camera.MediaType.PICTURE
            }
            this.camera.getPicture(options).then(async (imageData) => {
              let image = 'data:image/jpeg;base64,' + imageData;

              this.zone.run(async () => {
                let UUID = 'image-' + (new Date().getTime()).toString(16);
                let blob = this.b64toBlob(imageData.split(',')[1], 'image/jpeg');
                this.writePicture(blob,UUID+'.jpeg','image/jpeg');  
              })
            }).catch(async err=>{
              await this.alertService.dismiss()
              console.log("error:",err);
            });
            
          } else if(this.platformId == 'electron') {
            await this.alertService.present();
            const options: CameraOptions = {
              quality: 50,
              destinationType: this.camera.DestinationType.DATA_URL,
              sourceType: this.camera.PictureSourceType.CAMERA,
              encodingType: this.camera.EncodingType.JPEG,
              mediaType: this.camera.MediaType.PICTURE
            }
            this.camera.getPicture(options).then((imageData) => {
              this.zone.run(async () => {
                let UUID = 'image-' + (new Date().getTime()).toString(16);
                let blob = this.b64toBlob(imageData.split(',')[1], 'image/jpeg');
                this.writePicture(blob,UUID+'.jpeg','image/jpeg');
              })
            }).catch(async err=>{
              await this.alertService.dismiss()
              console.log("error:",err);
            });
          } else if(this.device.platform=='browser') {
            const options: CameraOptions = {
              quality: 50,
              destinationType: this.camera.DestinationType.DATA_URL,
              sourceType: this.camera.PictureSourceType.CAMERA,
              encodingType: this.camera.EncodingType.JPEG,
              mediaType: this.camera.MediaType.PICTURE
            }
            this.camera.getPicture(options).then((imageData) => {
              let base64Image = imageData;
              this.zone.run(async () => {
                let UUID = 'image-' + (new Date().getTime()).toString(16);
                this.files.push({url:base64Image,title:UUID+'.jpeg',type:'image/png'})
              })
            }).catch(err=>{
              console.log("error:",err);
            });
          } else {
            await this.alertService.present();
            const options: CameraOptions = {
              quality: 50,
              destinationType: this.camera.DestinationType.DATA_URL,
              sourceType: this.camera.PictureSourceType.CAMERA,
              encodingType: this.camera.EncodingType.JPEG,
              mediaType: this.camera.MediaType.PICTURE
            }
            this.camera.getPicture(options).then(async (imageData) => {
              this.zone.run(async () => {
                let UUID = 'image-' + (new Date().getTime()).toString(16);
                imageData = imageData.split(',')[1];
                let blob = this.b64toBlob(imageData, 'image/jpeg');
                await this.writePicture(blob,UUID+'.jpeg','image/jpeg');
              })
            }).catch(async err=>{
              await this.alertService.dismiss()
              console.log("error: ",err);
            });
          }
          await actionSheet.dismiss('','Camera');
        }
      }, {
        text: 'Gallery',
        role: "Gallery",
        handler: async () => {
          if(this.device.platform=='Android') {
            await this.alertService.present();
            const options: CameraOptions = {
              quality: 50,
              destinationType: this.camera.DestinationType.DATA_URL,
              sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
              encodingType: this.camera.EncodingType.JPEG,
              mediaType: this.camera.MediaType.PICTURE
            }
            this.camera.getPicture(options).then(async (imageData) => {
              this.zone.run(async () => {
                let UUID = 'image-' + (new Date().getTime()).toString(16);
                let blob = this.b64toBlob(imageData.split(',')[1], 'image/jpeg');
                this.writePicture(blob,UUID+'.jpeg','image/jpeg');
              })
            }, async (err) => {
              await this.alertService.dismiss();
              console.log("error:",err);
            });
          } else if(this.device.platform=='iOS') {
            await this.alertService.present();
            const options: CameraOptions = {
              quality: 50,
              destinationType: this.camera.DestinationType.DATA_URL,
              sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
              encodingType: this.camera.EncodingType.JPEG,
              mediaType: this.camera.MediaType.PICTURE
            }
            this.camera.getPicture(options).then(async (imageData) => {
              this.zone.run(async () => {
                let UUID = 'image-' + (new Date().getTime()).toString(16);
                imageData = imageData.split(',')[1];
                let blob = this.b64toBlob(imageData, 'image/jpeg');
                await this.writePicture(blob,UUID+'.jpeg','image/jpeg');
              })
            }, async (err) => {
              await this.alertService.dismiss();
              console.log("error:",err);
            });
          } else if(this.platformId == 'electron') {
            let input = document.getElementById('myInput');
            if (input) {
              input.setAttribute('accept', 'image/*');
              input.click();
            }
          } else {
            let input=document.getElementById('myInput').click();
          }
          await actionSheet.dismiss('','Gallery');
        }
      }, {
        text: 'File',
        role: "File",
        handler: async () => {
          if(this.device.platform=='Android') {
            await this.alertService.present();
            await actionSheet.dismiss('','File');
            let options: ChooserOptions = {
              mimeTypes: 'image/png,image/jpeg,application/pdf,application/msword,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv,text/plain',
            };
            try {
              await this.fileChooser.getFile(options).then(async (file: any) => {
                console.log(file)
                if (file) {
                  // let blob=new Blob([new Uint8Array(file.data, file.data.byteOffset, file.data.length)]);
                  let blob = new Blob([new Uint8Array(file.data, file.data.byteOffset, file.data.length)], { type: file.mediaType });
                  const dataURL = file.dataURI;

                  this.files.push({
                    url: (this.checkFileType(file.mediaType) < 2)
                      ? this._DomSanitizationService.bypassSecurityTrustResourceUrl(dataURL)
                      : dataURL,
                    title: this.checkFileType(file.mediaType) > 1 ? file.name : '',
                    type: file.mediaType
                  });
                  

                  await this.alertService.dismiss();
                  // this.writePicture(blob,file.name,file.mediaType);
                } else {
                  await this.alertService.dismiss();
                }
              }).catch(async (error: any) =>{
                console.log("file select error Android:",error);
              } );
            } catch (error) {
              console.log("file select error Android: error:", error);
            }
          } else if(this.device.platform=='iOS') {
            await this.alertService.present();
            await actionSheet.dismiss('','File');
            let options:any = 'image/png,image/jpeg,application/pdf,application/msword,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv,text/plain';
            await this.fileChooser.getFile(options).then(async (file:any) => {
              if(file) {
                let blob=new Blob([new Uint8Array(file.data, file.data.byteOffset, file.data.length)]);
                await this.writePicture(blob,file.name,file.mediaType);
              } else {
                await this.alertService.dismiss();
              }
            }).catch(async (error: any) => {
              console.log("file select error iOS:",error)
            });
          } else if(this.device.platform=='browser') {
            await actionSheet.dismiss('','File');
            document.getElementById('myInputFile').click();
          } else if(this.platformId == 'electron') {
            await actionSheet.dismiss('','File');
            document.getElementById('myInputFile').click();
          }
        }
      }]
    });
    await actionSheet.present();
    let dataOnDismissed = await actionSheet.onDidDismiss().then(async (dismissed) => {
      if(dismissed.role==undefined || dismissed.role==null || dismissed.role=='backdrop') {
        await this.alertService.dismiss()
      }
      console.log(dismissed);
    })
  }

  b64toBlob(b64Data, contentType) {
    contentType = contentType || '';
    var sliceSize = 512;
    var byteCharacters = atob(b64Data);
    var byteArrays = [];

    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);
      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      var byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    var blob = new Blob(byteArrays, {type: contentType});
    return blob;
  }

  async writePicture(blob,fileName,fileType) {
    console.log('Blob Type:', blob.type);
    console.log('Blob Size:', blob.size);
    if(this.device.platform=='Android') {
      const dirPath = this.file.externalRootDirectory + "Download/Inspections/";
      const fullPath = dirPath + fileName;
      console.log("Checking directory:", dirPath);
      await this.file.checkDir(this.file.externalRootDirectory + "Download/", 'Inspections')
        .then(async _ => {
          console.log("Directory exists, writing file:", fullPath);
          await this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/" , fileName, blob,{ replace:true }).then(response => {
            this.files.push({url:(this.checkFileType(fileType)<2)?this.normalizeURL(this.file.externalRootDirectory + "Download/Inspections/"+ fileName):this.file.externalRootDirectory + "Download/Inspections/"+ fileName,title:this.checkFileType(fileType)>1?fileName:'',type:fileType});
          }).catch(err => {
            console.log("error:", err);
          })
        })
        .catch(async err => {
          await this.file.createDir(this.file.externalRootDirectory + "Download/", 'Inspections/', false).then(async result => {
            console.log("Directory created, attempting to write file at:", fullPath);
            await this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/", fileName, blob,{ replace:true }).then(response => {
              this.files.push({url:(this.checkFileType(fileType)<2)?this.normalizeURL(this.file.externalRootDirectory + "Download/Inspections/"+ fileName):this.file.externalRootDirectory + "Download/Inspections/"+ fileName,title:this.checkFileType(fileType)>1?fileName:'',type:fileType});
            }).catch(err => {
              console.log("error:", err);
            })
          })
        });
    } else if(this.device.platform=='iOS') {
        await this.file.writeFile(this.file.documentsDirectory,fileName,blob,{ replace:true }).then(res=>{
          this.files.push({url:(['image/png','image/jpg','image/jpeg'].includes(fileType))?this.normalizeURL(this.file.documentsDirectory+fileName):this.file.documentsDirectory+fileName,fileName,title:this.checkFileType(fileType)>1?fileName:'',type: fileType});
        }).catch(err=>{
          console.log(err)
        });
    } else if(this.platformId == 'electron') {
      this.file.writeFile(this.file.dataDirectory, fileName, blob, { replace: true }).then(response => {
        this.files.push({ url: (this.checkFileType(fileType)>1)?this.file.dataDirectory+ fileName:this.normalizeURL(this.file.dataDirectory+ fileName),title:fileName, type: fileType });
      }).catch(err => {
      })
    }
    await this.alertService.dismiss();
  }

  normalizeURL(url: string) {
    if (url == null || url.length == 0) {
      return ""
    }
    if (url.startsWith("http://")) return url;
    var updatedUrl = url
    if (this.platform.is('ios')) {
      let stringParts = updatedUrl.split('/Documents/')
      if (stringParts.length > 1) {
        let suffix = stringParts[1]
        updatedUrl = this.file.documentsDirectory + suffix
      }
    }
    var fixedURL = this.win.Ionic.WebView.convertFileSrc(updatedUrl)
    fixedURL = this._DomSanitizationService.bypassSecurityTrustUrl(fixedURL)
    return fixedURL
  }

  async deleteFile(index) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant("Delete"),
      message: '<strong>' + this.translate.instant('You want to delete this file') + '</strong>!',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Okay'),
          handler: () => {
            this.files.splice(index,1)
          }
        }
      ]
    });
    await alert.present();
  }

  checkFileType(type) {
    const signatures = {
      'image/png':1, //.png
      'image/jpeg':1, //.jpeg
      'image/jpg':1, //.jpg
      'application/pdf': 2, //.pdf
      'application/msword': 3, //.doc
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 3, //.docx
      'application/vnd.ms-excel': 4, //.xls
      "text/csv": 4, //.csv
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":4 , //.xlsx
      "text/plain" : 5 //.txt
    };
    if(type in signatures) {
      return signatures[type];
    } else {
      return 0;
    }
  }

  async openFile(file) {
    if(this.checkFileType(file.type)>1){
      if (this.device.platform != 'browser') {
        const fileName = file.title || file.url.split('/').pop() || file.url.split('\\').pop();
        await this.fileOpenerService.openFile(fileName).catch(() => {
          console.log("error opening file");
        });
      } else {
        let link=document.createElement('a');
        link.download = file.title;
        link.target = "blank";
        link.href=file.url;
        document.body.appendChild(link);
        link.click();
        link.remove()
      }
    }
  }

  onInputClick(event) {
    event.target.value = ''
  }

  async onFileSelected(event) {
    await this.alertService.present();
    if(event.target.files.length>0) {
      let mimeType = event.target.files[0].type;
      if (this.checkFileType(mimeType) < 1) {
        await this.alertService.dismiss();
        await this.alertService.showAlert('Warning',"this file is not supported");
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onload =(_event) => {
          let fileURL = reader.result;
          this.zone.run(async () => {
            if(this.platformId == 'electron') {
              var realData = (<string>reader.result).split(',')[1];
              let blob=this.b64toBlob(realData,mimeType)
              this.writePicture(blob,event.target.files[0].name,mimeType)
              await this.alertService.dismiss()
            } else if(this.device.platform=='browser') {
              await this.alertService.dismiss()
              this.files.push({ url: fileURL ,title:event.target.files[0].name, type: event.target.files[0].type });
            }
          });
        }
      }
    }
  }
}