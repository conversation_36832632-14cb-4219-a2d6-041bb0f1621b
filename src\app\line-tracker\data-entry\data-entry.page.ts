import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController } from '@ionic/angular';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { LmdService } from '../../services/lmd.service';
import { PlatformService } from 'src/app/services/platform.service';
@Component({
  selector: 'app-data-entry',
  templateUrl: './data-entry.page.html',
  styleUrls: ['./data-entry.page.scss'],
})
export class DataEntryPage implements OnInit {
  public platformId: string = this.platformService.getPlatformId();

  constructor(
    public platformService: PlatformService,
    public helpService: HelpService,
    public dataService: DataService,
    public menu: MenuController,
    public router: Router,
    public device: Device,
    private lmdService: LmdService
  ) { }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
    this.dataService.setLMDSelectedUom();
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  newLmd() {
    this.lmdService.setInspectionMode(false);
    this.router.navigate(['new-lmd']);
  }

  openLmd() {
    this.router.navigate(['in-progress-lmd']);
  }

  historyLmd() {
    this.router.navigate(['completed-lmd']);
  }

}
