/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

@import "../node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css";



/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
// @import "@ionic/angular/css/palettes/dark.system.css";

.pop-over-style .popover-content {
     --width: 300px !important;
    --min-width: 300px !important;

}

ion-popover:not(.splice-type-popover) {
  --width: 300px !important;
  --min-width: 300px !important;
}

.splice-type-popover {
  --width: fit-content !important;
  --min-width: fit-content !important;
}

ion-select-popover {
  --width: 300px !important;
  --min-width: 300px !important;
}
// .cdk-overlay-pane {
//    min-width: 90% !important;
// }

.mat-select-panel {
    min-width: 100% !important;
}


// fix for the issue : https://intranet.indience.in:8443/samson/inspections-src/-/issues/1
ion-popover [popover]:not(:popover-open):not(dialog[open]) {
  display: contents;
}

.__cropro_ > div  {
  margin: 100px 20px !important
}

// mat-form-field > div {
//   background-color: transparent !important;
// }
// mat-form-field > div:hover {
//   background-color: transparent !important;
// }
.mdc-text-field--filled:not(.mdc-text-field--disabled) {
 background-color: transparent !important;
}
.mdc-text-field--filled.mdc-text-field--disabled{
  background-color: transparent !important;
}

.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay {
  opacity: 0 !important;
}
.mat-mdc-form-field-focus-overlay {
  background-color: transparent !important;
}

.ion-segment-style {
  padding-bottom:2px !important; 
  width: 50% !important; 
  margin:auto !important;
  background-color: #0057b3 !important;
}

.ion-segment-routine-inspection {
  padding-bottom:2px !important; 
  width: 95% !important; 
  margin:auto !important;
  background-color: #0057b3 !important;
}

.ion-segment-button-style {
  // width: 50% !important; 
  --color-checked: #0057b3 !important;
  --color: white !important;
  --color-focused: white !important;
  --color-hover: white !important;

}