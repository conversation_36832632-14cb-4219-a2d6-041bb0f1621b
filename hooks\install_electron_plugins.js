#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

module.exports = function(context) {
    if (context.opts.platforms.includes('electron')) {
        const projectRoot = context.opts.projectRoot;
        const electronWwwPath = path.join(projectRoot, 'platforms', 'electron', 'www');
        
        console.log('Installing Electron plugin dependencies...');
        
        const plugins = [
            '../../../plugins/cordova-plugin-unvired-logger/src/electron',
            '../../../plugins/cordova-plugin-unvired-device/src/electron',
            '../../../plugins/cordova-plugin-unvired-electron-db/src/electron',
            '../../../plugins/cordova-plugin-file/src/electron',
        ];
        
        try {
            // Install cordova-plugin-file from GitHub before changing directory
            if (!plugins.includes('../../../plugins/cordova-plugin-file/src/electron')) {
                console.log('Installing cordova-plugin-file from GitHub...');
                execSync('cordova plugin add https://github.com/srinidhirao/cordova-plugin-file', { stdio: 'inherit' });
            }
            process.chdir(electronWwwPath);

            plugins.forEach(plugin => {
                console.log(`Installing ${plugin}...`);
                execSync(`npm install ${plugin}`, { stdio: 'inherit' });
            });

            process.chdir(projectRoot);
            console.log('Electron plugin dependencies installed successfully.');
        } catch (error) {
            console.error('Error installing Electron plugin dependencies:', error.message);
        }
    }
};