import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GuestResourcePageRoutingModule } from './guest-resource-routing.module';

import { GuestResourcePage } from './guest-resource.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { GuestFooterComponent } from '../guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GuestResourcePageRoutingModule,
    FontAwesomeModule,
    TranslateModule,
    GuestFooterComponent
  ],
  declarations: [GuestResourcePage]
})
export class GuestResourcePageModule {}
