<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start" style="position: relative;">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>
    <ion-title>{{'INSPECTION_TYPE_TITLE' | translate}}</ion-title>
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
      <fa-icon class="icon-style-help"  *ngIf='helpService.helpMode' icon="times"></fa-icon>
      <fa-icon class="icon-style-help"  *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
    </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-slides #slides scrollbar='false' style="height: 100%;">
    
    <ion-slide>
      <div style="width:100% !important;height: 100%;
      position: relative;
      width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
      justify-self: center;
      align-content: center;
      align-items: center;
      flex-direction: column;">
        <p style="padding: 0px 10%" align="center">{{'INSPECTION_TYPE_QUESTION' | translate}}</p>
        <div style="width: 100% !important;">
          <div style="width: 50%; text-align: center; margin: auto">
            <ion-card class="responsive-card-style" (click)="setModeAndCreateInspection('planned');">
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/Planned.png" />
            </ion-card>
            <p>{{'Yes' | translate}}</p>
          </div>
          <div style="width: 50%; text-align: center; margin: auto">
            <ion-card class="responsive-card-style" (click)="setModeAndCreateInspection('adhoc');">
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/Unplanned.png" />
            </ion-card>
            <p>{{'No' | translate}}</p>
          </div>
          <!-- <div style="width: 50%;text-align: -webkit-center; margin: auto">
            <ion-card class="card-style" (click)="setModeAndContinue('planned');">
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/Planned.png" />
            </ion-card>
            <p>{{'Yes' | translate}}</p>
          </div>
          <div style="width: 50%;text-align: -webkit-center; margin: auto">
            <ion-card class="card-style" (click)="setModeAndContinue('adhoc');">
              <ion-ripple-effect></ion-ripple-effect>
              <img src="./assets/img/Unplanned.png" />
            </ion-card>
            <p>{{'No' | translate}}</p>
          </div> -->
        </div>
      </div>
    </ion-slide>
  </ion-slides>

</ion-content>

<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>

<app-footer *ngIf='footerClose' (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
    (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
