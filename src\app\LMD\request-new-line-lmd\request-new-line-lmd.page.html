<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)="backButtonClicked()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'REQUEST_NEW_LINE_TITLE' | translate}}</ion-title>
    <ion-buttons slot="end">
      <!-- <ion-button color="primary" (click)="saveLMD()" class="help-button-style">
        <fa-icon class="icon-style-help" icon="list"></fa-icon>
      </ion-button> -->
      <ion-button color="primary" class="help-button-style">

        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"
          (click)="helpService.switchMode(); enableAllFields();"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="info-circle"
          (click)="helpService.switchMode(); disableAllFields();"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:15px 10px 0px 3px">
    <p style="margin-top: 8px; padding-left: 14px">{{'CROPPING_ASSET_LIST_LABEL' | translate}} <span
        style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></p>

    <div style="display: inline-flex; width: 100%;">
      <ion-item (click)="(helpService.helpMode || readOnly) ? test() : presentModal('ASSET')" no-lines text-wrap
        tappable style="width: 100% !important;" *ngIf="assetList && assetList.length >= 0"
        class="ion-item-generic-style" mode="ios">
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
          *ngIf="selectedAssetName == ''" class="drop-down-arrow  value-field">{{ 'Select Asset' | translate
            }}</div>
        <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
          *ngIf="selectedAssetName != ''" class="value-field">{{selectedAssetName}}</div>
        <ion-note item-right style="display:inline-flex">
          <p class="drop-down-arrow">
            <fa-icon class="icon-style" icon="sort-down"></fa-icon>
          </p>
        </ion-note>
      </ion-item>
    </div>

  </div>
  <div style="padding:15px 10px 0px 17px">
    <p style="margin-top: 8px">{{'Is this a Samson Certificate' | translate}}</p>

    <ion-radio-group [(ngModel)]="samsonCert">
      <ion-row class="ion-radio-row">
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio mode="md" item-left value="yes" labelPlacement="end" slot="start"
              [disabled]='(helpService.helpMode || readOnly || selectedAssetName == "" || selectedAssetName == undefined || selectedAssetName == null)'>
            </ion-radio>
            <ion-label>{{'Yes' | translate}}</ion-label>
          </ion-item>
        </ion-col>

        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio mode="md" item-left value="no" labelPlacement="end" slot="start"
              [disabled]='(helpService.helpMode || readOnly || selectedAssetName == "" || selectedAssetName == undefined || selectedAssetName == null)'>
            </ion-radio>
            <ion-label>{{'No' | translate}}</ion-label>
          </ion-item>
        </ion-col>
      </ion-row>
    </ion-radio-group>
  </div>
  <!-- & if samson certificate YES - START -->
  <div *ngIf="samsonCert == 'yes'">
    <div style="padding:15px 10px 0px 3px">
      <p style="margin-top: 8px;padding-left: 14px ;">{{'CROPPING_CERT_LIST_LABEL' | translate}}<span
          style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span></p>
      <div style="display: inline-flex; width: 100%;">
        <ion-item
          (click)="!(helpService.helpMode || readOnly) && selectedAssetName != '' ? presentModal('CERTIFICATE') : test()"
          no-lines text-wrap tappable style="width: 100% !important;" class="ion-item-generic-style" mode="ios">
          <div
            style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
            *ngIf="selectedCertNo == ''" class="drop-down-arrow  value-field">{{ 'Select Certificate No' | translate
              }}</div>
          <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
            *ngIf="selectedCertNo != ''" class="value-field">{{selectedCertNo}}</div>
          <ion-note item-right style="display:inline-flex">
            <p class="drop-down-arrow">
              <fa-icon class="icon-style" icon="sort-down"></fa-icon>
            </p>
          </ion-note>
        </ion-item>
      </div>
    </div>

    <div style="padding:15px 10px 0px 17px" *ngIf="selectedCertNo != undefined && selectedCertNo != ''">
      <label>{{'CERTIFICATE_DATE_LABEL' | translate}}</label>
      <ion-row>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_CERT_DATE'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput [matDatepicker]="picker" [max]="maxDate" disabled [(ngModel)]="certDate"
              placeholder="{{'CERT_DATE_PLACEHOLDER' | translate}}">
            <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </ion-col>
      </ion-row>
    </div>
    <div style="padding:15px 10px 0px 17px" *ngIf="selectedCertNo != undefined && selectedCertNo != ''">
      <label>{{'MANUFACTURER_LABEL' | translate}}</label>
      <ion-row>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_MANUFACTURER'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="manufacturer" maxlength="225"
              placeholder="{{'MANUFACTURER_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
    </div>
    <div style="padding:15px 10px 0px 17px" *ngIf="selectedCertNo != undefined && selectedCertNo != ''">
      <label>{{'PRODUCT_NAME_LABEL' | translate}}</label>
      <ion-row>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_NAME'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="productName" maxlength="225"
              placeholder="{{'PRODUCT_NAME_PLACEHOLDER' | translate}}" step="any">
          </mat-form-field>
        </ion-col>
      </ion-row>
    </div>
    <div style="padding:15px 10px 0px 17px" *ngIf="selectedCertNo != undefined && selectedCertNo != ''">
      <label>{{'PRODUCT_DESC_LABEL' | translate}}</label>
      <ion-row>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_DESC'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="productDesc" maxlength="225"
              placeholder="{{'PRODUCT_DESC_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
    </div>

    <!-- & APPLICATION SELECTION START -->
    <div style="padding:15px 10px 0px 17px" *ngIf="selectedCertNo != undefined && selectedCertNo != ''">
      <label>{{'INSPECTION_SETUP_APPLICATION_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_CONSTRUCTION'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="selectedApplicationType" maxlength="225"
              placeholder="{{'REQUEST_NEW_LINE_CONSTRUCTION_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <mat-form-field style="width: 100% !important" *ngIf="applicationTypeList">
            <mat-select
              placeholder="{{ 'INSPECTION_SETUP_APPLICATION_LABEL' | translate}}" labelPlacement="stacked" interface="popover"
              [(value)]="selectedApplicationType">
              <mat-option *ngFor="let option of applicationTypeList, let i = index" [value]="option">
                {{option}}
              </mat-option>
            </mat-select>
          </mat-form-field>
      </ion-row>
    </div>
    <!-- & APPLICATION SELECTION END -->
  </div>
  <!-- & if samson certificate YES - END -->

  <!-- & if samson certificate NO - START -->
  <div *ngIf="samsonCert == 'no'">

    <div style="padding:15px 10px 0px 17px">
      <label>{{'ALTERNATE_CERT_NUMBER_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_ALTERNATE_CERT'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="alternateCertNo" maxlength="225"
              placeholder="{{'ALTERNATE_CERT_NUMBER_PLACEHOLDER' | translate}}" step="any">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="alternateCertNoForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="alternateCertNo" maxlength="225"
                placeholder="{{'ALTERNATE_CERT_NUMBER_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('alternateCertNo')" [required] formControlName="alternateCertNoCtrl">
              <mat-error *ngIf="hasErrorStart('alternateCertNo')">{{alternateCertNoErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>
    <div style="padding:15px 10px 0px 17px">
      <label>{{'CERTIFICATE_DATE_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_CERT_DATE'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput [matDatepicker]="picker" [max]="maxDate" disabled [(ngModel)]="certDate"
              placeholder="{{'CERT_DATE_PLACEHOLDER' | translate}}">
            <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="certDateForm">
            <mat-form-field style="width:100%">
              <input matInput [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="certDate"
                placeholder="{{'CERT_DATE_PLACEHOLDER' | translate}}" step="any" (change)="onChangeDisable('certDate')"
                (dateInput)="addEvent('input', $event)" (dateChange)="addEvent('change', $event)"
                formControlName="certDateCtrl" (keydown)="keyPressed($event, 'eventDate', false)"
                (focus)="picker.open()" (click)="picker.open()" readonly>
              <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <!-- <mat-error *ngIf="hasErrorStart('certDate')">{{certDateErrorMessage}}</mat-error> -->
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>
    <!-- ~ MANUFACTURER START-->
    <div style="padding:15px 10px 0px 17px">
      <!-- & HELP MODE ENABLED -->
      <div *ngIf='helpService.helpMode'>
        <label>{{'MANUFACTURER_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> *</span></label>
        <div tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_MANUFACTURER'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
          <ion-select
          style="padding-left: 0px;" placeholder="{{ 'MANUFACTURER_SELECT_PLACEHOLDER' | translate}}"
          labelPlacement="stacked" interface="popover" [(ngModel)]="manufacturer" disabled="true">
            <ion-select-option *ngFor="let option of manufacturerList" value="{{option.ID}}">
              {{option.NAME}}</ion-select-option>
          </ion-select>
        </div>
      </div>
      <!-- & HELP MODE DISABLED -->
      <div *ngIf='!helpService.helpMode'>
        <label>{{'MANUFACTURER_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> *</span></label>
        <ion-select style="padding-left: 0px;" placeholder="{{ 'MANUFACTURER_SELECT_PLACEHOLDER' | translate}}"
        labelPlacement="stacked" interface="popover" [(ngModel)]="manufacturer">
          <ion-select-option *ngFor="let option of manufacturerList" value="{{option.ID}}">
            {{option.NAME}}</ion-select-option>
        </ion-select>
      </div>
    </div>
    <!-- ~ MANUFACTURER END-->
    
    <div style="padding:15px 10px 0px 17px">
      <label>{{'PRODUCT_NAME_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_NAME'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="productName" maxlength="225"
              placeholder="{{'PRODUCT_NAME_PLACEHOLDER' | translate}}" step="any">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="productNameForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="productName" maxlength="225"
                placeholder="{{'PRODUCT_NAME_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('productName')" [required] formControlName="productNameCtrl">
              <mat-error *ngIf="hasErrorStart('productName')">{{productNameErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>
    <div style="padding:15px 10px 0px 17px">
      <label>{{'PRODUCT_TYPE_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>

      <div tooltip="{{ 'HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_TYPE' | translate }}" positionV="bottom"
      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
      [hideOthers]=helpService.hideOthers *ngIf='helpService.helpMode'>
        <ion-select style="padding-left: 0px;" placeholder="{{'Select Product Type' | translate}}"
        labelPlacement="stacked" interface="popover" [(ngModel)]="productType" disabled="true">
          <ion-select-option *ngFor="let option of prodTypeList" value="{{option.PRODUCT_TYPE}}">
            {{option.PRODUCT_TYPE}}</ion-select-option>
        </ion-select>
      </div>

      <div *ngIf='!helpService.helpMode'>
        <ion-select style="padding-left: 0px;" placeholder="{{'Select Product Type' | translate}}"
        labelPlacement="stacked" interface="popover" [(ngModel)]="productType">
          <ion-select-option *ngFor="let option of prodTypeList" value="{{option.PRODUCT_TYPE}}">
            {{option.PRODUCT_TYPE}}</ion-select-option>
        </ion-select>
      </div>
    </div>
    <div style="padding:15px 10px 0px 17px">
      <label>{{'PRODUCT_DESC_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_DESC'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="productDesc" maxlength="225"
              placeholder="{{'PRODUCT_DESC_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="productDescForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="productDesc" maxlength="225"
                placeholder="{{'PRODUCT_DESC_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('productDesc')" [required] formControlName="productDescCtrl">
              <mat-error *ngIf="hasErrorStart('productDesc')">{{productDescErrorMessage}}</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>

    <div style="padding:15px 10px 0px 17px">
      <label>{{'REQUEST_NEW_LINE_SIZE_IN_INCH' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_SIZE_INCH'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="sizeInInch" maxlength="225"
              placeholder="{{'REQUEST_NEW_LINE_SIZE_IN_INCH_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="sizeInInchForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="sizeInInch" maxlength="225"
                placeholder="{{'REQUEST_NEW_LINE_SIZE_IN_INCH_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('sizeInInch')" formControlName="sizeInInchCtrl"
                (keydown)="keyPressed($event, tpf, false)" step="any" inputmode="decimal">
            <mat-error *ngIf="sizeInInchForm.get('sizeInInchCtrl').errors?.required">{{'REQUEST_NEW_LINE_SIZE_IN_INCH' | translate}} is mandatory</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>

    <div style="padding:15px 10px 0px 17px">
      <label>{{'REQUEST_NEW_LINE_SIZE_IN_MILLIMETER' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_SIZE_MM'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="sizeInMillimeter" maxlength="225"
              placeholder="{{'REQUEST_NEW_LINE_SIZE_IN_MILLIMETER_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="sizeInMillimeterForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="sizeInMillimeter" maxlength="225"
                placeholder="{{'REQUEST_NEW_LINE_SIZE_IN_MILLIMETER_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('sizeInMillimeter')" formControlName="sizeInMillimeterCtrl"
                (keydown)="keyPressed($event, tpf, false)" step="any" inputmode="decimal">
                <mat-error *ngIf="sizeInMillimeterForm.get('sizeInMillimeterCtrl').errors?.required">{{'REQUEST_NEW_LINE_SIZE_IN_MILLIMETER' | translate}} is mandatory</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>

    <div style="padding:15px 10px 0px 17px">
      <label>{{'REQUEST_NEW_LINE_LENGTH_IN_METER' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_LENGTH_M'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="lengthInMeter" maxlength="225"
              placeholder="{{'REQUEST_NEW_LINE_LENGTH_IN_METER_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="lengthInMeterForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="lengthInMeter" maxlength="225"
                placeholder="{{'REQUEST_NEW_LINE_LENGTH_IN_METER_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('lengthInMeter')" formControlName="lengthInMeterCtrl"
                (keydown)="keyPressed($event, tpf, false)" step="any" inputmode="decimal">
                <mat-error *ngIf="lengthInMeterForm.get('lengthInMeterCtrl').errors?.required">{{'REQUEST_NEW_LINE_LENGTH_IN_METER' | translate}} is mandatory</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>

    <div style="padding:15px 10px 0px 17px">
      <label>{{'REQUEST_NEW_LINE_STRENGTH' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_STRENGTH'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="number" [disabled]='true' [(ngModel)]="strength" maxlength="20" inputmode="decimal"
              placeholder="{{'REQUEST_NEW_LINE_STRENGTH_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="strengthForm">
            <mat-form-field style="width:100%">
              <input matInput type="number" [(ngModel)]="strength" maxlength="20"
                placeholder="{{'REQUEST_NEW_LINE_STRENGTH_PLACEHOLDER' | translate}}" step="any" inputmode="decimal"
                (change)="onChangeDisable('strength')" (keydown)="strengthChanged($event)" (focusout)="reset('strengthCtrl')" formControlName="strengthCtrl">
                <mat-error *ngIf="strengthForm.get('strengthCtrl').errors?.required">{{'REQUEST_NEW_LINE_STRENGTH' | translate}} is mandatory</mat-error>
                <mat-error *ngIf="strengthForm.get('strengthCtrl').errors?.maxlength">{{ "Character Limit Exceeded" | translate: { limit: '20'} }}</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>

    <div style="padding:15px 10px 0px 17px">
      <label>{{'REQUEST_NEW_LINE_LINEAR_DENSITY' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_LINEAR_DENSITY'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="number" [disabled]='true' [(ngModel)]="linearDensity" maxlength="20" inputmode="decimal"
              placeholder="{{'REQUEST_NEW_LINE_LINEAR_DENSITY_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="linearDensityForm">
            <mat-form-field style="width:100%">
              <input matInput type="number" [(ngModel)]="linearDensity" maxlength="20" inputmode="decimal"
                placeholder="{{'REQUEST_NEW_LINE_LINEAR_DENSITY_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('linearDensity')" (keydown)="linearDensityChanged($event)" (focusout)="reset('linearDensityCtrl')" formControlName="linearDensityCtrl">
                <mat-error *ngIf="linearDensityForm.get('linearDensityCtrl').errors?.required">{{'REQUEST_NEW_LINE_LINEAR_DENSITY' | translate}} is mandatory</mat-error>
                <mat-error *ngIf="linearDensityForm.get('linearDensityCtrl').errors?.maxlength">{{ "Character Limit Exceeded" | translate: { limit: '20'} }}</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>

    <div style="padding:15px 10px 0px 17px">
      <label>{{'REQUEST_NEW_LINE_CONSTRUCTION' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <!-- <div>

        </div> -->
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_CONSTRUCTION'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="construction" maxlength="225"
              placeholder="{{'REQUEST_NEW_LINE_CONSTRUCTION_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <!-- <div [formGroup]="constructionForm">
          
        </div> -->
        <mat-form-field style="width: 100% !important" *ngIf="constructionsList">
            <mat-select disableOptionCentering
              placeholder="{{'REQUEST_NEW_LINE_CONSTRUCTION_PLACEHOLDER' | translate}}" labelPlacement="stacked" interface="popover"
              [(value)]="construction" [formControl]="constructionCtrl">
              <mat-select-trigger
                *ngIf="construction != undefined && construction != ''">
                <div style="display: inline-flex;"><img style="width: 70px; height: 70px"
                    [src]='construction.IMAGE'><span style="height: 100%; margin: auto;">
                    {{construction.NAME}}</span></div>
              </mat-select-trigger>
              <mat-option *ngFor="let option of constructionsList, let i = index" [value]="option"
                style="padding: 10px 5px; height: 100px">
                <img width="70" height="70" [src]='option.IMAGE'>
                {{option.NAME}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="constructionCtrl.errors?.required">Construction in mandatory</mat-error>
          </mat-form-field>

    
      </ion-row>
    </div>

    <!-- & APPLICATION SELECTION - START -->
    <div style="padding:15px 10px 0px 17px">
      <label>{{'INSPECTION_SETUP_APPLICATION' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_INSPECTION_SETUP_APPLICATION_LABEL'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="selectedApplicationType" maxlength="225"
              placeholder="{{'INSPECTION_SETUP_APPLICATION_LABEL_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <mat-form-field style="width: 100% !important" *ngIf="applicationTypeList">
            <mat-select 
              placeholder="{{'INSPECTION_SETUP_APPLICATION_LABEL_PLACEHOLDER' | translate}}" labelPlacement="stacked" interface="popover"
              [(value)]="selectedApplicationType">
              <mat-option *ngFor="let option of applicationTypeList, let i = index" [value]="option">
                {{option}}
              </mat-option>
            </mat-select>
          </mat-form-field>
      </ion-row>
    </div>


    <!-- & APPLICATION SELCTION END -->

    <div style="padding:15px 10px 0px 17px">
      <label>{{'REQUEST_NEW_LINE_MATERIAL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
      <ion-row *ngIf='helpService.helpMode'>
        <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_MATERIAL'|translate}}" positionV="bottom"
          [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
          [hideOthers]=helpService.hideOthers>
          <mat-form-field style="width:100%">
            <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="material" maxlength="225"
              placeholder="{{'REQUEST_NEW_LINE_MATERIAL_PLACEHOLDER' | translate}}">
          </mat-form-field>
        </ion-col>
      </ion-row>
      <ion-row *ngIf='!helpService.helpMode'>
        <ion-col>
          <div [formGroup]="materialForm">
            <mat-form-field style="width:100%">
              <input matInput type="text" [(ngModel)]="material" maxlength="225"
                placeholder="{{'REQUEST_NEW_LINE_MATERIAL_PLACEHOLDER' | translate}}" step="any"
                (change)="onChangeDisable('material')" formControlName="materialCtrl">
                <mat-error *ngIf="materialForm.get('materialCtrl').errors?.required">{{'REQUEST_NEW_LINE_MATERIAL' | translate}} is required</mat-error>
            </mat-form-field>
          </div>
        </ion-col>&nbsp;
      </ion-row>
    </div>
  </div>
  <!-- & if samson certificate NO - END -->
  <div style="padding:15px 10px 0px 17px">
    <label>{{'EVENT_DATE_LABEL' | translate}}<span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-row *ngIf='helpService.helpMode'>
      <ion-col tooltip="{{'HELP_TEXT_REQUEST_NEW_LINE_EVENT_DATE'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <mat-form-field style="width:100%">
          <input matInput [matDatepicker]="picker" [max]="maxDate" disabled [(ngModel)]="eventDate"
            placeholder="{{'EVENT_DATE_PLACEHOLDER' | translate}}">
          <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </ion-col>
    </ion-row>
    <ion-row *ngIf='!helpService.helpMode'>
      <ion-col>
        <div [formGroup]="eventDateForm">
          <mat-form-field style="width:100%">
            <input matInput [matDatepicker]="picker" [max]="maxDate" [(ngModel)]="eventDate"
              placeholder="{{'EVENT_DATE_PLACEHOLDER' | translate}}" step="any" (change)="onChangeDisable('eventDate')"
              (dateInput)="addEvent('input', $event)" (dateChange)="addEvent('change', $event)"
              formControlName="eventDateCtrl" (keydown)="keyPressed($event, 'eventDate', false)" (focus)="picker.open()"
              (click)="picker.open()" readonly>
            <mat-datepicker-toggle matSuffix [for]="picker" style="font-size: x-large;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <!-- <mat-error *ngIf="hasErrorStart('eventDate')">{{eventDateErrorMessage}}</mat-error> -->
          </mat-form-field>
        </div>
      </ion-col>&nbsp;
    </ion-row>
  </div>


  <div style="padding:100px 10px 0px 17px">
  </div>
  <!-- Save Button -->
  <ion-fab *ngIf='!readOnly' vertical="bottom" horizontal="end" slot="fixed" [topOffset]=helpService.topOffset
    tooltip="{{'Tap to save Request new line'|translate}}" positionV="top" positionH="right"
    [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="helpService.helpMode ? test() : saveLMD()">
      <fa-icon class="icon-style-other" icon="save" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>


<!-- Footer -->
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); enableAllFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>
</div>