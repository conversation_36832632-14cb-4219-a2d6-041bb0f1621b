<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Line History</ion-title>
  </ion-toolbar>
  <ion-toolbar>
    <div>

      <div style="display: inline-flex; width: 100%;">

        <div style="display: inline-flex; width: 90%; padding-left: 10px;">

          <div [formGroup]="fromDateForm" style="width:50%; padding: 3px; padding-left: 4px;">
            <mat-form-field style="width:100%;">
              <input matInput [matDatepicker]="fromDatePicker" [(ngModel)]="eventStartDateModel" [max]="maxDate"
                formControlName="fromDateCtrl" [value]="eventStartDateModel" (change)="setChanged()"
                (dateChange)="setChanged()" placeholder="From" step="any" [required]
                (keydown)="keyPressed($event, 'installDate', false)" (click)="fromDatePicker.open()" readonly>
              <mat-datepicker-toggle matSuffix [for]="fromDatePicker" style="font-size: x-large;">
              </mat-datepicker-toggle>
              <mat-datepicker #fromDatePicker (selectedChanged)="setChanged()"></mat-datepicker>
            </mat-form-field>
          </div>

          <div [formGroup]="toDateForm" style="width:50%; padding: 3px; padding-left: 4px;">
            <mat-form-field style="width:100%;">
              <mat-label>To</mat-label>
              <input matInput [matDatepicker]="toDatePicker" [(ngModel)]="eventEndDateModel" [max]="maxDate"
                formControlName="toDateCtrl" [value]="eventEndDateModel" (change)="setChanged()"
                (dateChange)="setChanged()" placeholder="{{'EVENT_DATE_PLACEHOLDER' | translate}}" step="any" [required]
                (keydown)="keyPressed($event, 'installDate', false)" (click)="toDatePicker.open()" readonly>
              <mat-datepicker-toggle matSuffix [for]="toDatePicker" style="font-size: x-large;"></mat-datepicker-toggle>
              <mat-datepicker #toDatePicker (selectedChanged)="setChanged()"></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <button style="padding-bottom: 7px; background-color: #0057b3; margin-top: 20px; margin-right: 20px;" mat-mini-fab
          (click)="(eventEndDateModel === '' || eventStartDateModel === '' || selectedAssetName == '') ? presentToast() : onDateChange()"
          slot="end" class="icon" type="submit">
          <fa-icon class="icon-style" icon="search"></fa-icon>
        </button>
        <ion-label *ngIf="hasError" style="color: red; padding-left: 15px;">{{eventDateErrorMessage}}</ion-label>

      </div>





      <!-- <mat-form-field style="width:43%; padding: 3px; padding-left: 4px;" appearance="outline">
        <mat-label>From</mat-label>
        <input matInput [matDatepicker]="picker1" [(ngModel)]="eventStartDateModel" [max]="maxDate" disabled
          placeholder="Start Date" [required]>
        <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
        <mat-datepicker #picker1 disabled="false"></mat-datepicker>
      </mat-form-field>
      <mat-form-field style="width:43%; padding: 3px;" appearance="outline">
        <mat-label>To</mat-label>
        <input matInput [matDatepicker]="picker2" [(ngModel)]="eventEndDateModel" [max]="maxDate" disabled
          placeholder="End Date">
        <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
        <mat-datepicker #picker2 disabled="false"></mat-datepicker>
      </mat-form-field>
      <button style=" padding-bottom: 7px; background-color: #0057b3;" mat-mini-fab
        (click)="(eventEndDateModel === '' || eventStartDateModel === '' || selectedAssetName == '') ? presentToast() : onDateChange()"
        slot="end" class="icon" type="submit">
        <fa-icon class="icon-style" icon="search"></fa-icon>
      </button>
      <ion-label *ngIf="hasError" style="color: red; padding-left: 15px;">{{eventDateErrorMessage}}</ion-label> -->


      
    </div>
    <div>
      <p style="margin-top: 8px; padding-left: 14px">{{'CROPPING_ASSET_LIST_LABEL' | translate}}<span
          style="color:rgb(221, 82, 82);font-size:15px;"> *
        </span></p>

      <div style="display: inline-flex; width: 100%;">
        <ion-item (click)="(helpService.helpMode || readOnly) ? test() : presentModal('ASSET')" no-lines text-wrap
          tappable style="width: 100% !important;" *ngIf="assetList && assetList.length >= 0"
          class="ion-item-generic-style" mode="ios">
          <div
            style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
            *ngIf="selectedAssetName == ''" class="drop-down-arrow  value-field">{{ 'Select Asset' | translate
              }}</div>
          <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
            *ngIf="selectedAssetName != ''" class="value-field">{{selectedAssetName}}</div>
          <ion-note item-right style="display:inline-flex">
            <p class="drop-down-arrow">
              <fa-icon class="icon-style" icon="sort-down"></fa-icon>
            </p>
          </ion-note>
        </ion-item>
      </div>

    </div>
  </ion-toolbar>
</ion-header>


<ion-content>

  <ion-list lines="none">
    <ion-item-sliding *ngFor="let item of maintData; let i = index">
      <ion-item (click)="icon(i)" style="border-top:1px solid rgba(199, 199, 199, 0.753);">
        <ion-label style="margin-left: 10px;">
          <h2 style="color:rgb(37, 91, 145);">Type: {{item.LMD_TYPE}}</h2>
          <p *ngIf="(item.LMD_TYPE != 'RequestNewLine' && item.LMD_TYPE != 'EquipmentInsp' ) && item.LMD_DATA.certNo != ''" style="color:rgba(104, 102, 102, 0.753);">Certificate No: {{item.LMD_DATA.certNo}}</p>
          <p *ngIf="item.LMD_TYPE === 'RequestNewLine' && item.LMD_DATA.certNo != '' && item.LMD_DATA.certNo != undefined" style="color:rgba(104, 102, 102, 0.753);">Certificate No: {{item.LMD_DATA.certNo}}</p>
          <p *ngIf="item.LMD_TYPE === 'RequestNewLine' && item.LMD_DATA.alternateCertNo != '' && item.LMD_DATA.alternateCertNo != undefined" style="color:rgba(104, 102, 102, 0.753);">Certificate No: {{item.LMD_DATA.alternateCertNo}}</p>
          <p *ngIf="item.LMD_TYPE === 'EquipmentInsp' && item.LMD_DATA.selectedEquipmentName != ''" style="color:rgba(104, 102, 102, 0.753);">Equipment: {{item.LMD_DATA.selectedEquipmentName}}</p>
        </ion-label>
        <ion-note style="font-size: 16px;" slot="end">{{item.LMD_DATA.eventDate}}</ion-note>
      </ion-item>

      <ion-item *ngIf="checkSectionIsOpen(i)" lines="none"
        style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829);">
        <ion-label style="padding-left: 20px;">
          <ion-text color="primary" *ngIf="checkSectionIsOpen(i)">
            <div *ngIf="item.LMD_TYPE === 'Cropping'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf="item.LMD_DATA.lengthCropped">Length Cropped</p>
                <p *ngIf="item.LMD_DATA.endCropped">End being Repaired</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p *ngIf="item.LMD_DATA.assetName">{{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">{{item.LMD_DATA.totalWorkingHour}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p *ngIf="item.LMD_DATA.lengthCropped">{{item.LMD_DATA.lengthCropped}}</p>
                <p *ngIf="item.LMD_DATA.endCropped">{{item.LMD_DATA.endCropped}}</p>
                <p *ngIf="item.LMD_DATA.eventDate">{{item.LMD_DATA.eventDate}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'Repair'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf="item.LMD_DATA.endInUse">End being Repaired</p>
                <p *ngIf="item.LMD_DATA.distanceInEye">Distance From Eye</p>
                <p *ngIf="item.LMD_DATA.repairType">Repair Type</p>
                <p *ngIf="item.LMD_DATA.damageType">Reason for Repair</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p *ngIf="item.LMD_DATA.assetName">{{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">{{item.LMD_DATA.totalWorkingHour}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p *ngIf="item.LMD_DATA.endInUse">{{item.LMD_DATA.endInUse}}</p>
                <p *ngIf="item.LMD_DATA.distanceInEye">{{item.LMD_DATA.distanceInEye}}</p>
                <p *ngIf="item.LMD_DATA.repairType">{{item.LMD_DATA.repairType}}</p>
                <p *ngIf="item.LMD_DATA.damageType">{{item.LMD_DATA.damageType}}</p>
                <p *ngIf="item.LMD_DATA.eventDate">{{item.LMD_DATA.eventDate}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'EndForEnd'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p *ngIf="item.LMD_DATA.assetName">{{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">{{item.LMD_DATA.totalWorkingHour}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p *ngIf="item.LMD_DATA.eventDate">{{item.LMD_DATA.eventDate}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'InstallLine'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.selectedEquipmentName">Equipment</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p *ngIf="item.LMD_DATA.assetName">{{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.selectedEquipmentName">{{item.LMD_DATA.selectedEquipmentName}}</p>
                <p *ngIf="item.LMD_DATA.eventDate">{{item.LMD_DATA.eventDate}}</p>
              </div>
            </div>


            <div *ngIf="item.LMD_TYPE === 'Rotation'" style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                <p *ngIf="item.LMD_DATA.fromWinch">From Winch</p>
                <p *ngIf="item.LMD_DATA.toWinch">To Winch</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p *ngIf="item.LMD_DATA.assetName">{{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingHour">{{item.LMD_DATA.totalWorkingHour}}</p>
                <p *ngIf="item.LMD_DATA.totalWorkingOperation">{{item.LMD_DATA.totalWorkingOperation}}</p>
                <p *ngIf="item.LMD_DATA.fromWinch">{{item.LMD_DATA.fromWinch}}</p>
                <p *ngIf="item.LMD_DATA.toWinch">{{item.LMD_DATA.toWinch}}</p>
                <p *ngIf="item.LMD_DATA.eventDate">{{item.LMD_DATA.eventDate}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'EquipmentInsp'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName"> Asset</p>
                <p *ngIf="item.LMD_DATA.selectedEquipmentName">Equipment</p>
                <p *ngIf="item.LMD_DATA.ropeContact">Rope Contact</p>
                <p *ngIf="item.LMD_DATA.surfaceRating">Surface Rating</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p *ngIf="item.LMD_DATA.assetName"> {{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.selectedEquipmentName">{{item.LMD_DATA.selectedEquipmentName}}</p>
                <p *ngIf="item.LMD_DATA.ropeContact">{{item.LMD_DATA.ropeContact}}</p>
                <p *ngIf="item.LMD_DATA.surfaceRating">{{item.LMD_DATA.surfaceRating}}</p>
                <p *ngIf="item.LMD_DATA.eventDate">{{item.LMD_DATA.eventDate}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'RequestNewLine'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.alternateCertNo">Alternate Cert</p>
                <p *ngIf="item.LMD_DATA.manufacturer">Manufacturer</p>
                <p *ngIf="item.LMD_DATA.productName">Product Name</p>
                <p *ngIf="item.LMD_DATA.productDesc">product Desc</p>
                <p *ngIf="item.LMD_DATA.eventDate">Event Date</p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.workOrder">{{item.LMD_DATA.workOrder}}</p>
                <p *ngIf="item.LMD_DATA.assetName">{{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.alternateCertNo">{{item.LMD_DATA.alternateCertNo}}</p>
                <p *ngIf="item.LMD_DATA.manufacturer">{{item.LMD_DATA.manufacturer}}</p>
                <p *ngIf="item.LMD_DATA.productName">{{item.LMD_DATA.productName}}</p>
                <p *ngIf="item.LMD_DATA.productDesc">{{item.LMD_DATA.productDesc}}</p>
                <p *ngIf="item.LMD_DATA.eventDate"> {{item.LMD_DATA.eventDate}}</p>
              </div>
            </div>

            <div *ngIf="item.LMD_TYPE === 'AssetActivity'"
              style="display: inline-flex !important; width: 100% !important;">
              <div style="width:40% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                <p *ngIf="item.LMD_DATA.account">Account</p>
                <p *ngIf="item.LMD_DATA.portCountry">Port Country</p>
                <p *ngIf="item.LMD_DATA.portName">Port Name</p>
                <p *ngIf="item.LMD_DATA.allFast"><span *ngIf="isUtility == true">Start of Job</span><span *ngIf="isUtility == false">Port Entered</span></p>
                <p *ngIf="item.LMD_DATA.allLetGo"><span *ngIf="isUtility == true">End of Job</span><span *ngIf="isUtility == false">Port Exited</span></p>
              </div>
              <div style="width:60% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                <p *ngIf="item.LMD_DATA.assetName">{{item.LMD_DATA.assetName}}</p>
                <p *ngIf="item.LMD_DATA.account">{{item.LMD_DATA.account}}</p>
                <p *ngIf="item.LMD_DATA.portCountry">{{item.LMD_DATA.portCountry}}</p>
                <p *ngIf="item.LMD_DATA.portName">{{item.LMD_DATA.portName}}</p>
                <p *ngIf="item.LMD_DATA.allFast">{{item.LMD_DATA.allFast}}</p>
                <p *ngIf="item.LMD_DATA.allLetGo">{{item.LMD_DATA.allLetGo}}</p>
              </div>
            </div>

          </ion-text>
        </ion-label>
      </ion-item>
    </ion-item-sliding>
  </ion-list>
  <div *ngIf='maintData && maintData.length == 0'
    style="width: 100% !important; text-align: center !important; padding-top: 30px !important">
    {{'Please select a date range and asset to get line history'}}</div>


</ion-content>

<ion-fab *ngIf='maintData.length > 0' vertical="bottom" horizontal="end" slot="fixed" class="fabBtn">
  <!-- <ion-fab *ngIf='maintData.length > 0' vertical="bottom" horizontal="end" slot="fixed"> -->
  <ion-fab-button color="primary">
    <fa-icon class="icon-style" icon="chevron-circle-left" style="font-size: 25px;" class="icon-style"></fa-icon>
  </ion-fab-button>
  <ion-fab-list side="start">
    <button (click)="generateCSV('export')" mat-raised-button class="styleButton">
      <fa-icon class="icon-style" icon="download" style="font-size: 16px;" class="icon-style"></fa-icon>
      Export
    </button>
    <button *ngIf="device.platform != 'browser'" (click)="composeEmail()" mat-raised-button color="primary" class="styleButton">
      <fa-icon class="icon-style" icon="envelope" style="font-size: 16px;" class="icon-style"></fa-icon>
      Email
    </button>
  </ion-fab-list>
</ion-fab>

<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>