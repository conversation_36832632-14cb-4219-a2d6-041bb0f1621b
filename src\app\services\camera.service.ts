import { Injectable, NgZone} from '@angular/core';
import { Platform, ToastController, ActionSheetController, NavController, ModalController, AlertController } from '@ionic/angular';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Camera, CameraOptions } from '@awesome-cordova-plugins/camera/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { Router, NavigationExtras } from '@angular/router';
import { NgxImageCompressService } from 'ngx-image-compress';
import { VisionAIService } from './vision-ai.service';
import { AlertService } from './alert.service';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { LocationAccuracy } from '@awesome-cordova-plugins/location-accuracy/ngx';
// import { CameraPage } from '../camera/camera.page';
import { AppConstant } from '../../constants/appConstants';
import { PlatformService } from './platform.service';

import { SamsonCamera, CameraOptions as Camopts } from "@samson/camcrop";
import { UserGuidePromptComponent } from '../Insight-AI/user-guide-prompt/user-guide-prompt.component';
import { WebView } from "@awesome-cordova-plugins/ionic-webview/ngx";
import { TranslateService } from '@ngx-translate/core';
declare var imageEditor;
declare var windowsImageEditor: any;
declare var window: any;
@Injectable({
  providedIn: 'root'
})
export class CameraService {
  private platformId: string = this.platformService.getPlatformId();
  private win: any = window;
  textValue: string;
  annotationsForImage: string[];
  originalImgList: any = [];
  editedImg: any[] = [];
  editedExternalImg: any = [];
  baselineExternal: any = [];
  baselineExternalOriginal: any = [];
  baselineInternal: any = [];
  baselineInternalOriginal: any = [];
  imageAndAnnotationArray: any = [];
  imageAndAnnotation: any;
  imageAndAnnotationInternal: any;
  imageAndAnnotationExternal: any;
  locationLong: string = "";
  locationLat: string = "";
  baselineIntEdited: boolean = false;
  baselineExtEdited: boolean = false;
  measurementEdited: boolean = false;
  report: string;
  androidExtras: any;
  cameraMode: string;
  browserJson: any;
  browserJsonParms: any;
  comparatorImage: string = '';
  comparatorPage: any;
  imageData:any;

  constructor(private platform: Platform, 
    public platformService: PlatformService,
    public screenOrientation: ScreenOrientation, 
    private imageCompress: NgxImageCompressService, 
    public modalController: ModalController,
    public navCtrl: NavController, 
    public toastController: ToastController, 
    public router: Router, 
    private file: File, 
    private _DomSanitizationService: DomSanitizer, 
    private zone: NgZone, 
    public unviredCordovaSDK: UnviredCordovaSDK, 
    public actionSheetController: ActionSheetController, 
    private device: Device, 
    private camera: Camera,
    private visionAIService:VisionAIService,
    private alertService:AlertService,
    private geolocation: Geolocation,
    private domSanitizer: DomSanitizer,
    private locationAccuracy:LocationAccuracy,
    private webView: WebView,
    private alertController:AlertController,
    private translate:TranslateService) { }

  async takePicture(mode?: string, abrMode?: string, pageComponent?: any, maxRating?: string, currentRating?: any, productName?: any, isDemo?: boolean,openAsModal?:boolean) {
    currentRating = "" + currentRating;
    this.annotationsForImage = [];
    this.locationLong = "";
    this.locationLat = "";
    if (!isDemo) {
      isDemo = false;
    }
    this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
    if (this.platformId == 'electron') { 
      this.presentActionSheet().then((dismissed) => {
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.FILE_URI,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          this.camera.getPicture(options).then((imageData) => {
            console.log("IMAGEDATD ++++++++++" + imageData)
            windowsImageEditor.captureImage({ "imageUrl": imageData, "mode": { mode: abrMode }, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo }, (res: any) => {
              if (res.type === ResultType.success) {
                console.log('Success Result: ', res);
                let respData: any = res.data;
                let camOrgImgPath = (respData.Image === '' || respData.Image == null) ? '' : respData.Image;
                camOrgImgPath = (camOrgImgPath == '') ? camOrgImgPath : `ms-appdata:///local/${camOrgImgPath.substring(camOrgImgPath.lastIndexOf("\\") + 1)}`;
                let camEdtImgPath = (respData.EditedImage === '' || respData.EditedImage == null) ? '' : respData.EditedImage;
                camEdtImgPath = (camEdtImgPath == '') ? camEdtImgPath : `ms-appdata:///local/${camEdtImgPath.substring(camEdtImgPath.lastIndexOf("\\") + 1)}`;
                this.zone.run(() => {
                  this.editedImg = this.captureImageHandler(mode, pageComponent, respData.Rating, respData.Latitude, respData.Longitude, respData.Texts, camOrgImgPath, camEdtImgPath, respData.Report, "windows camera");
                  return this.editedImg;
                });
              } else if (res.type === ResultType.error) {
                console.log('Error Result: ', res);
                this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(res.message));
              }
            })
          }, (err) => {
            console.log(err);
            // this.imageUrl.next(null);
            this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(err.message))
          });
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            sourceType: this.camera.PictureSourceType.SAVEDPHOTOALBUM,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          console.log(this.file.dataDirectory);
          this.camera.getPicture(options).then(async (imageData) => {
            console.log("IMAGEDATD ++++++++++" + imageData)

            let realData = imageData;
            let blob = this.b64toBlob(realData.split(',')[1], 'image/jpeg');
            // var imgURL = URL.createObjectURL(blob);
            // this.imageData = this.domSanitizer.bypassSecurityTrustResourceUrl(imgURL);
            let orgFileName = 'org_img_' + new Date().getTime() + '.jpg';
            let edtFileName = 'edtd_img_' + new Date().getTime() + '.jpg';
            let galOrgImgPath = this.file.dataDirectory + orgFileName;
            let galEdtImgPath = this.file.dataDirectory + edtFileName;

            this.file.writeFile(this.file.dataDirectory, orgFileName, blob, { replace: true })
              .then(() => {
                this.file.writeFile(this.file.dataDirectory, edtFileName, blob, { replace: true })
                  .then(() => {
                    console.log('File stored at:', galOrgImgPath);
                    let orgPath = galOrgImgPath;
                    let edtPath = galEdtImgPath;

                    this.zone.run(() => {
                      this.editedImg = this.captureImageHandler(mode, pageComponent, 5, 0, 0, 'Sample Test', orgPath, edtPath, 'yes', "windows gallery");
                      return this.editedImg;
                    });
                  }).catch((err) => {
                    console.error("Error writing edited image file:", err);
                    this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(err.message));
                  });
              }).catch((err) => {
                console.error("Error writing original image file:", err);
                this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(err.message));
              });
           
            const satizedURL = this._DomSanitizationService.bypassSecurityTrustUrl(galOrgImgPath);
            const satizedURL2 = this._DomSanitizationService.bypassSecurityTrustUrl(galEdtImgPath); 
           
          }, (err) => {
            console.log(err);
            // Handle error
            this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(err.message))
          });
        } else {
          console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        this.zone.run(() => {
          return ""
        })
      })
    } else if (this.device.platform == 'browser') { //! -----------------------------------BROWSER-----------------------------------------------
      this.browserJsonParms = {
        "mode": mode, "abrMode": abrMode, "pageComponent": pageComponent, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo
      }
      this.presentActionSheet().then((dismissed) => {
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          const options: CameraOptions = {
            quality: 50,
            targetWidth: 640,
            targetHeight: 480,
            destinationType: this.camera.DestinationType.FILE_URI,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          } 
          this.camera.getPicture(options).then((imageData) => {
            imageData = "data:image/jpeg;base64," + imageData || imageData
            windowsImageEditor.captureImage({ "imageUrl": imageData, "mode": { mode: abrMode }, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo }, (res: any) => {
              if (res.Type === ResultType.success) {
                console.log('Success Result: ', res);
                this.zone.run(() => {
                  this.editedImg = this.captureImageHandlerBrowser(mode, pageComponent, res.Rating, res.Latitude, res.Longitude, res.Texts, res.Image, res.EditedImage, res.Report, "browser camera",);
                  return this.editedImg;
                });
              } else if (res.type === ResultType.error) {
                console.log('Error Result: ', res);
                this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(res.message));
              }
            })
          }, (err) => {
            console.log(err);
            // this.imageUrl.next(null);
            this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(err.message))
          });
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
            document.getElementById('myInput').click();
            this.browserJson = { "imageUrl": '', "mode": { mode: abrMode }, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo }
        } else {
          console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        this.zone.run(() => {
          return ""
        })
      })
    } else if (this.platform.is("android")) {
      // var extras : NavigationExtras = { state: {currentRating: currentRating}}
      this.androidExtras = {mode: mode, pageComponent: pageComponent, abrMode: abrMode, maxRating: maxRating, productName: productName, isDemo: isDemo, currentRating: currentRating}
      this.presentActionSheet().then(async (dismissed) => {
        console.log(dismissed);
        this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , JSON.stringify(dismissed));
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          this.cameraMode = dismissed.role;
          // this.unlockScreen();
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL, // base64 string
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE,
            targetWidth: 1280,
            targetHeight: 1280,
            correctOrientation: true,
            saveToPhotoAlbum: false,
            cameraDirection: this.camera.Direction.BACK
          };
      
          try {
            const imageData = await this.camera.getPicture(options);
            if (imageData) {
              // this.androidExtras = androidExtras; // set if needed for writePicture
              this.writePicture(imageData.split(',')[1]); // imageData is base64 string (no prefix)
            }
          } catch (error) {
            console.error("Camera error:", error);
          }
        

          // if(openAsModal) {
          //   extras['isDisplayedAsModal'] = true;
          //   const modal = await this.modalController.create({
          //     component: CameraPage,
          //     componentProps: extras,
          //     cssClass : 'customConfigurationCamera',
          //     backdropDismiss: false
          //   });
          //   await modal.present()
          //   modal.onDidDismiss().then(async (data) => {
          //   })
          // } else {
          //   this.router.navigate(['camera'], extras);
          // }
          
      } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
        this.cameraMode = dismissed.role;
        let onSuccess = async (imageURI) => {
          window.resolveLocalFileSystemURL(imageURI, (fileEntry) => {
            fileEntry.file(async (file) => {
              console.log("File size in bytes:", file.size);

              if (file.size / (1024 * 1024) > AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB) {
                await this.zone.run(async () => {
                  if (this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                  await this.alertService.showAlert("Warning!", AppConstant.MAX_ALLOWED_IMAGE_WARNING_MSG);
                });
              } else {
                console.log("File type:", file.type);
                console.log("Last modified:", file.lastModifiedDate);

                const newFileName = 'image-' + (new Date().getTime()).toString(16) + '.jpg';
                const newDir = this.file.externalRootDirectory + 'Download/Inspections/';

                const onError = (err: any) => {
                  console.error("Error name:", err.name);
                  console.error("Error code:", err.code);
                  console.error("Full error:", err);
                };

                const sourcePath = fileEntry.filesystem.root.nativeURL;
                const fileName = fileEntry.name;
                const targetPath = newDir;

                try {
                  await this.file.copyFile(sourcePath, fileName, targetPath, newFileName);
                  console.log('File copied successfully!');
                  await this.zone.run(async () => {
                    if (this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                    this.captureImageHandlerAndroid(this.androidExtras.currentRating,0, 0,'',"storage/emulated/0/Download/Inspections/" + newFileName,"storage/emulated/0/Download/Inspections/" + newFileName,'yes',"android Gallery");
                  });
                } catch (error) {
                  console.error('Failed to copy file:', error);
                }
              }
            }, (err) => {
              console.error("Error getting file info", err);
            });
          });
        };

        let onFail = async (message) => {
          console.error("Failed to get picture: " + message);
          await this.zone.run(async () => {
            if (this.alertService.isLoading) {
              await this.alertService.dismiss();
            }
            await this.alertService.showAlert("Warning!", message);
          });
        };

        const openGallery = async () => {
          await this.alertService.present();
          setTimeout(() => {
            (navigator as any).camera.getPicture(onSuccess, onFail, {
              destinationType: this.camera.DestinationType.FILE_URI,
              sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
              correctOrientation: true,
              quality: 100,
            });
          }, 100);
        };
        await openGallery();
      } else {
        this.cameraMode = ''
        console.log('**************  NO ACTION  ***************');
      }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        this.zone.run(() => {
          return ""
        })
      })
    } else {
      this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE","TAKE PICTURE METHOD CALLED - BEFORE CAPTURING IMAGE")
      imageEditor.captureImage({ "mode": abrMode, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo, maxImageSize: AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB, imageSizeWarningMsg:AppConstant.MAX_ALLOWED_IMAGE_WARNING_MSG }, (res: any) => {
        this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(res))
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        if (this.platform.is("android")) {
          res = JSON.parse(res);
        }
        this.zone.run(() => {
          this.editedImg = this.captureImageHandler(mode, pageComponent, res.rating, res.latitude, res.longitude, res.texts, res.image, res.editedImage, res.report, "iosc camera");
          return this.editedImg;
        });
      }, async err => {
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "ERROR CAPTURING IMAGE")
        console.log(err)
        if(err?.sizeError) {
          await this.alertService.showAlert("Warning!",err.message)
          console.log("ios picker error",JSON.stringify(err))
        } else {
          var tempToast = await this.toastController.create({
            message: "Capture Image Failed",
            duration: 2000,
            color: "dark",
            position: "middle"
          });
          tempToast.present();
        }
        this.zone.run(() => {
          return ""
        })
      })
      this.unviredCordovaSDK.logError("CAMERA SERVICE","TAKE PICTURE","TAKE PICTURE METHOD END")
    }
  }

  async getNativeURL(path:string) {
    let url =this._DomSanitizationService.bypassSecurityTrustUrl(path);
    return url;
  }

    async insightAiAlert() {
      var confAlert = await this.alertController.create({
        backdropDismiss: false,
        animated: true,
        mode: 'ios',
        header: "Warning!",
        keyboardClose: true,
        message: "Switching to Manual will lose the Insight AI data, Are you sure?",
        buttons: [
          {
            text: this.translate.instant('Cancel'),
            role: 'cancel',
            cssClass: 'secondary'
          }, {
            text: this.translate.instant('Ok'),
            role: 'continue'
          }
        ]
      });
      await confAlert.present();

      const { role }  = await confAlert.onDidDismiss();
    return role;
    }

  writePicture(imagen) {
    let UUID = 'image-' + (new Date().getTime()).toString(16);
    let realData = imagen;
    let blob = this.b64toBlob(realData, 'image/jpeg');
    console.log("WRITEPICTURE")
    console.log("*******" + this.file.externalRootDirectory + "*************")
    console.log("*******" + this.file.externalDataDirectory + "*************")
    console.log("*******" + this.file.externalRootDirectory + "*************")
    console.log("*******file:///storage/emulated/0/*************")
    this.file.checkDir(this.file.externalRootDirectory + "Download/", 'Inspections')
        .then(_ => {
          this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/" , UUID + '.jpg', blob).then(response => {
            this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , "After Image Picker");
            this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , "Write File Successful : Path"  + this.file.externalRootDirectory + "Download/Inspections/");
            this.captureImageHandlerAndroid(this.androidExtras.currentRating, 0, 0, '', "/storage/emulated/0/Download/Inspections/" + UUID + '.jpg', "/storage/emulated/0/Download/Inspections/" + UUID + '.jpg', 'yes', " android camera")
          }).catch(err => {
          })
        })
        .catch(err => {
          this.file.createDir(this.file.externalRootDirectory + "Download/", 'Inspections/', false).then(result => {
            this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/", UUID + '.jpg', blob).then(response => {
              this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , "After Image Picker");
              this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , "Write File Successful : Path"  + this.file.externalRootDirectory + "Download/Inspections/");
              this.captureImageHandlerAndroid(this.androidExtras.currentRating, 0, 0, '', "/storage/emulated/0/Download/Inspections/" + UUID + '.jpg', "/storage/emulated/0/Download/Inspections/" + UUID + '.jpg', 'yes', "android camera")
              }).catch(err => {
            })
          })
        });
  }

//   async getFileSize(fileUri) {
//     return new Promise(function(resolve, reject) {    
//         window.resolveLocalFileSystemURI(fileUri, function(fileEntry:any) {
//             fileEntry.file(function(fileObj) {
//                 resolve(fileObj.size);
//             },
//             function(err){
//                 reject(err);
//             });
//         }, 
//         function(err){
//             reject(err);
//         });
//     });
// }

  async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
  
      reader.onloadend = () => {
        const result = reader.result;
        if (typeof result === 'string') {
          const base64 = result.split(',')[1];
          resolve(base64);
        } else {
          reject(new Error('Failed to read blob as base64 string.'));
        }
      };
  
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }


  async getBase64SizeInMB(base64):Promise<number> {
    let padding = (base64.endsWith("==")) ? 2 : (base64.endsWith("=")) ? 1 : 0;
    let base64Size = (base64.length * 3) / 4 - padding;
    let sizeInMB = base64Size / (1024 * 1024); // Convert bytes to MB
    return sizeInMB;
  }

  b64toBlob(b64Data, contentType) {
    contentType = contentType || '';
    var sliceSize = 512;
    var byteCharacters = atob(b64Data);
    var byteArrays = [];

    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);

      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      var byteArray = new Uint8Array(byteNumbers);

      byteArrays.push(byteArray);
    }

    var blob = new Blob(byteArrays, {type: contentType});
    return blob;
  }

  normalizeURL(url: string) {
    if (url == null || url.length == 0) {
      return ""
    }
    // Normalized Url starts with "http://"
    if (url.startsWith("http://")) return url;
    var updatedUrl = url
    // For iOS, Extract the part after /Documents & construct the actual file URL.
    // in iOS, the sandbox folder location changes for every session.
    // Therefore we cannot us any absolute URL.
    if (this.platform.is('ios')) {
      let stringParts = updatedUrl.split('/Documents/')
      if (stringParts.length > 1) {
        let suffix = stringParts[1]
        updatedUrl = this.file.documentsDirectory + suffix
      }
    }

    var fixedURL = this.win.Ionic.WebView.convertFileSrc(updatedUrl)
    fixedURL = this._DomSanitizationService.bypassSecurityTrustUrl(fixedURL)
    return fixedURL
  }

  reset() {
    this.editedImg = []
    this.originalImgList = []
    this.annotationsForImage = [];
    this.baselineExternalOriginal = [];
    this.baselineInternalOriginal = []
    this.imageAndAnnotationArray = [];
    this.imageAndAnnotation = {};
    this.imageAndAnnotationInternal = {};
    this.imageAndAnnotationExternal = {};
    this.baselineExtEdited = false;
    this.baselineIntEdited = false;
    this.measurementEdited = false;
  }

  setData(path: any) {
    this.editedImg = path;
    // this.editedImg =[];
    // for( var i = 0 ; i < path.length; i++) {
    //   console.log("path image=>",path[i]);
    //   if(this.device.platform=='windows') {
    //     let fixedURL = this._DomSanitizationService.bypassSecurityTrustUrl(path[i].Image.changingThisBreaksApplicationSecurity);
    //     console.log("fixed URL 2=>",fixedURL);
    //     this.editedImg.push({"Image": fixedURL ,"mode": path[i].mode,"insightAIScore": path[i].insightAIScore})
    //   } else {
    //     this.editedImg.push({"Image": path[i].Image,"mode": path[i].mode,"insightAIScore": path[i].insightAIScore})
    //   }
    // }
  }

  internalPicture() {
    imageEditor.captureImage("", res => {
      this.zone.run(() => {
        // alert("IMAGE DATA: " + JSON.stringify(res))
        this.textValue = res.texts
        this.originalImgList = this.normalizeURL(res.image)
        this.baselineInternal.push(this.normalizeURL(res.editedImage))
        return this.editedImg;
      })
    }, err => {
      this.zone.run(() => {
        return ""
      })
    })

  }

  externalPicture() {
    imageEditor.captureImage("", res => {
      this.zone.run(() => {
        // alert("IMAGE DATA: " + JSON.stringify(res))
        this.textValue = res.texts
        this.originalImgList = this.normalizeURL(res.image)
        this.baselineExternal.push(this.normalizeURL(res.editedImage))
        return this.editedImg;
      })
    }, err => {
      this.zone.run(() => {
        return ""
      })
    })

  }

  async editImage(path, index, mode, baselineMode?: string, pageComponent?: any, maxRating?: string, currentRating?: any, productName?: any, isDemo?: boolean, isAndroidCapture?: boolean) {
    if (!isDemo) {
      isDemo = false;
    }
    if (this.platformId == 'electron') {
      let localPath = path.changingThisBreaksApplicationSecurity;
      let localStoragePath = `${localPath.replace(localPath.substring(localPath.lastIndexOf("/") + 1), '')}`;
      let localFileName = `${localPath.substring(localPath.lastIndexOf("/") + 1)}`;
      var response = await fetch(localPath);
      const initialPath = await response.blob()
      var filepath = URL.createObjectURL(initialPath)
      this.file.readAsDataURL(localStoragePath, localFileName)
        .then((base64ImgUrl) => {
          let imageData = base64ImgUrl;
          windowsImageEditor.editImage({ "imageUrl": filepath, "fileName": localFileName, "mode": mode, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo, "report": this.imageAndAnnotation.originalImgList[index].report }, (res: any) => {
            if (res.type === ResultType.success) {
              console.log('Success Result: ', res);
              let respData: any = res.data;

              let imgToEditPath = (respData.Image === '' || respData.Image == null) ? '' : respData.Image;
              imgToEditPath = (imgToEditPath == '') ? imgToEditPath : `ms-appdata:///local/${imgToEditPath.substring(imgToEditPath.lastIndexOf("\\") + 1)}`;
              let editedImgPath = (respData.EditedImage === '' || respData.EditedImage == null) ? '' : respData.EditedImage;
              editedImgPath = (editedImgPath == '') ? editedImgPath : `ms-appdata:///local/${editedImgPath.substring(editedImgPath.lastIndexOf("\\") + 1)}`;
              this.zone.run(() => {
                this.editImageHandler(index, baselineMode, pageComponent, respData.Rating, respData.Latitude, respData.Longitude, respData.Texts, imgToEditPath, editedImgPath, respData.Report);
              });
            } else if (res.type === ResultType.error) {
              console.log('Error Result: ', res);
              this.unviredCordovaSDK.logError("CAMERA SERVICE", "EDIT PICTURE", JSON.stringify(res.message));
            }
          }, async error => {
            this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "EDIT PICTURE", "LOG MEMORY USAGE")
            var tempToast = await this.toastController.create({
              message: "Edit Image Failed",
              duration: 2000,
              color: "dark",
              position: "middle"
            });
            tempToast.present();
            console.log("error editing image " + JSON.stringify(error))
          })
        }).catch((err) => {
          console.log(err);
          // Handle error
          this.unviredCordovaSDK.logError("CAMERA SERVICE", "EDIT PICTURE", JSON.stringify(err.message));
        })
    } else if (this.device.platform == 'browser') {
      var response = await fetch(path);
      const initialPath = await response.blob()
      var filepath = URL.createObjectURL(initialPath)
      this.browserJsonParms = {
        "path": filepath, "index": index, "mode": mode, "baselineMode": baselineMode, "pageComponent": pageComponent, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo
      }
      windowsImageEditor.editImage({ "imageUrl": path, "fileName": '', "mode": mode, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo, "report": this.imageAndAnnotation.originalImgList[index].report }, (res: any) => {
        if (res.Type === ResultType.success) {
          this.zone.run(() => {
            this.editImageHandlerBrowser(index, baselineMode, pageComponent, res.Rating, res.Latitude, res.Longitude, res.Texts, res.Image, res.EditedImage, res.Report);
          });
        }
      }, async error => {
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "EDIT PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Edit Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        console.log("error editing image " + JSON.stringify(error))
      });
      
    } else if(this.platform.is("android")) {
      var deleteOriginal = true;
      if(isAndroidCapture != undefined) {
        deleteOriginal = isAndroidCapture;
      }
      currentRating = "" + currentRating;
      console.log("+++++++++++++++" + path);
      var tempPath = path.changingThisBreaksApplicationSecurity;
      tempPath = tempPath.substr(tempPath.indexOf('_app_file_') + 10, tempPath.length - 1)
      tempPath = "file:///" + tempPath;
      var fileName = tempPath.substring(tempPath.lastIndexOf('/') + 1)
      console.log("+++++++++++++++" + tempPath);
      // this.file.checkFile(tempPath.substring(0, tempPath.lastIndexOf('/') + - 1), fileName).then(() => {
      //   this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "Filename sent : " + fileName + "exists")
      //     }).catch((err) => {
      //       this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "Filename sent : " + fileName + " not exists")
      //     });

      this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "Filename sent : " + fileName)
      this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "path sent : " + tempPath)
      this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
      imageEditor.editImage({ "imageName": fileName, "mode": mode, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo, report: this.imageAndAnnotation.originalImgList[index].report, 'deleteOriginal': deleteOriginal  }, res => {
        console.log(JSON.stringify(res))
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "after editing")
        this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(res))
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        if (this.platform.is("android")) {
          res = JSON.parse(res);
        }
        this.zone.run(() => {
          this.editImageHandler(index, baselineMode, pageComponent, res.rating, res.latitude, res.longitude, res.texts, res.image, res.editedImage, res.report);
        })
      }, async error => {
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        if(isAndroidCapture == true) {
          if(index > -1){
            if (this.editedImg[index].Image != undefined) {
              this.zone.run(() => {
                this.editedImg.splice(index, 1);
              })
            }
            if (this.imageAndAnnotationArray[index] != undefined) {
              this.zone.run(() => {
                this.imageAndAnnotationArray.splice(index, 1);
              })
            }
          }
        }
        console.log("error editing image " + JSON.stringify(error))
      })
    } else {
      currentRating = "" + currentRating;
      console.log("+++++++++++++++" + path);
      var tempPath = path.changingThisBreaksApplicationSecurity;
      tempPath = tempPath.substr(tempPath.indexOf('_app_file_') + 10, tempPath.length - 1)
      tempPath = "file:///" + tempPath;
      var fileName = tempPath.substring(tempPath.lastIndexOf('/') + 1)
      console.log("+++++++++++++++" + tempPath);
      this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
      imageEditor.editImage({ "imageName": fileName, "mode": mode, "maxRating": maxRating, "currentRating": currentRating, "productName": productName, "isDemo": isDemo, report: this.imageAndAnnotation.originalImgList[index].report }, res => {
        console.log(JSON.stringify(res))
        this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(res))
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        if (this.platform.is("android")) {
          res = JSON.parse(res);
        }
        this.zone.run(() => {
          this.editImageHandler(index, baselineMode, pageComponent, res.rating, res.latitude, res.longitude, res.texts, res.image, res.editedImage, res.report);        
        })
      }, async error => {
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        console.log("error editing image " + JSON.stringify(error))
      })
    }
  }

  async setImages(pathArray: any, originalImages: any, mode?: string) {
    this.editedImg = []
    this.baselineInternal = [];
    this.baselineExternal = [];
    this.baselineExternalOriginal = [];
    this.baselineInternalOriginal = [];
    this.imageAndAnnotationArray = [];
    this.imageAndAnnotation = {};
    this.imageAndAnnotationInternal = {};
    this.imageAndAnnotationExternal = {};
    // Set image path (original and edited)
    if (this.platformId == 'electron') {
      if (pathArray != undefined) {
        for (var i = 0; i < pathArray.length; i++) {
          console.log("Path: ",pathArray[i]);
          let imageData : any = '';
          if (pathArray[i]?.Image?.changingThisBreaksApplicationSecurity) {
            imageData = pathArray[i].Image.changingThisBreaksApplicationSecurity;
          } else if (pathArray[i]?.image?.image) {
            imageData = pathArray[i].image.image;
          }
          console.log("Image Data: ",imageData);
          pathArray[i] = this.normalizeURL(imageData)
        }
      }
      if (originalImages != undefined && JSON.stringify(originalImages) != '{}') {
        if (originalImages.originalImgList.length > 0) {
          for (var ind = 0; ind < originalImages.originalImgList.length; ind++) {
            console.log(originalImages.originalImgList[ind].originalImgList.changingThisBreaksApplicationSecurity)
            var oimageData = originalImages.originalImgList[ind].originalImgList.changingThisBreaksApplicationSecurity
            originalImages.originalImgList[ind].originalImgList = this.normalizeURL(oimageData)
          }
        }
      }
    } else if (this.device.platform == 'browser') {
    }else {
      var result = await this.unviredCordovaSDK.getAttachmentFolderPath()
      console.log(JSON.stringify(result))
      if (result.type == ResultType.success) {
        if (pathArray != undefined) {
          for (var i = 0; i < pathArray.length; i++) {
            if(pathArray[i]?.image?.mode=="InsightAI") {

            } else {
              console.log(pathArray[i].Image.changingThisBreaksApplicationSecurity)
              var imageData = pathArray[i].Image.changingThisBreaksApplicationSecurity
              imageData = imageData.substr(imageData.indexOf('_app_file_') + 10, imageData.length - 1)
              var fileName = imageData.substring(imageData.lastIndexOf('/') + 1)
              imageData = "file://" + result.data.substr(0, result.data.lastIndexOf('Documents')) + "/Documents/" + fileName;
              if (this.platform.is("android")) {
                // imageData = "file://" + result.data.substr(0,result.data.lastIndexOf('ACCOUNT')) + fileName ;
                imageData = "file://" + "/storage/emulated/0/Download/Inspections/" + fileName
              }
              console.log("------------" + imageData);
              pathArray[i].Image = this.normalizeURL(imageData)
            }
          }
        }
        if (originalImages != undefined && JSON.stringify(originalImages) != '{}') {
          if (originalImages.originalImgList.length > 0) {
            for (var ind = 0; ind < originalImages.originalImgList.length; ind++) {
              if (originalImages.originalImgList[ind].originalImgList?.mode == "InsightAI") {

              } else {
                console.log(originalImages.originalImgList[ind].originalImgList.changingThisBreaksApplicationSecurity)
                var oimageData = originalImages.originalImgList[ind].originalImgList.changingThisBreaksApplicationSecurity
                oimageData = oimageData.substr(oimageData.indexOf('_app_file_') + 10, oimageData.length - 1)
                var ofileName = oimageData.substring(oimageData.lastIndexOf('/') + 1)
                oimageData = "file://" + result.data.substr(0, result.data.lastIndexOf('Documents')) + "/Documents/" + ofileName;
                if (this.platform.is("android")) {
                  // imageData = "file://" + result.data.substr(0,result.data.lastIndexOf('ACCOUNT')) + fileName ;
                  oimageData = "file://" + "/storage/emulated/0/Download/Inspections/" + ofileName
                }
                console.log("------------" + oimageData);
                originalImages.originalImgList[ind].originalImgList = this.normalizeURL(oimageData)
              }

            }
          }
        }
      }
    }
    // Check opration mode
    if (mode === "baseline-ext") {
      this.baselineExternal = pathArray
      if (originalImages != undefined && JSON.stringify(originalImages) != '{}') {
        this.baselineExternalOriginal = originalImages.originalImgList
        this.imageAndAnnotationExternal = originalImages;
      } else {
        this.imageAndAnnotationExternal = {};
      }
    } else if (mode === "baseline-int") {
      this.baselineInternal = pathArray
      if (originalImages != undefined && JSON.stringify(originalImages) != '{}') {
        this.baselineInternalOriginal = originalImages.originalImgList
        this.imageAndAnnotationInternal = originalImages;
      } else {
        this.imageAndAnnotationInternal = {};
      }
    } else {
      // this.editedImg =[];
      for( var i = 0 ; i < pathArray.length; i++) {
        this.editedImg.push({"Image": pathArray[i].Image,"mode": pathArray[i].mode})
      }
      if (originalImages != undefined && JSON.stringify(originalImages) != '{}') {
        this.imageAndAnnotationArray = originalImages.originalImgList
        this.imageAndAnnotation = originalImages;
      } else {
        this.imageAndAnnotation = {};
      }
    }
  }

  async presentActionSheet() {
    this.unviredCordovaSDK.logError("CAMERA SERVICE","PRESENT ACTION SHEET","CAMERA_SERVICE - PRESENTACTIONSHEET METHOD START")
    const actionSheet = await this.actionSheetController.create({
      cssClass: 'my-custom-class',
      buttons: [{
        text: 'Camera',
        icon: 'camera',
        handler: () => {
          actionSheet.dismiss(1, 'camera');
        }
      }, {
        text: 'Gallery',
        icon: 'image',
        handler: () => {
          actionSheet.dismiss(2, 'gallery');
        }
      }]
    });
    await actionSheet.present().then(()=>{
      this.unviredCordovaSDK.logError("CAMERA SERVICE","ACTION SHEET PRESENT","CAMERA_SERVICE -PRESENT SUCCESSFUL");
    }).catch(err=>{
      this.unviredCordovaSDK.logError("CAMERA SERVICE","ACTION SHEET PRESENT","CAMERA_SERVICE -PRESENT FAILURE : "+err);
    });

    let dataOnDismissed = await actionSheet.onDidDismiss();
    this.unviredCordovaSDK.logError("CAMERA SERVICE","PRESENT ACTION SHEET","CAMERA_SERVICE - PRESENTACTIONSHEET METHOD END");
    return dataOnDismissed;
  }

  updatePromptGuideStatus(screenType: string, status: boolean) {
    this.unviredCordovaSDK.dbUpdate("APP_SETTINGS_HEADER", { VALUE: JSON.stringify(status) }, "KEY_FLD='" + screenType + "'").then((result) => {
      if (result.type == ResultType.success) {
        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "updatePromptGuideStatus", "Successfully updated prompt status for " + screenType);
      } else {
        this.unviredCordovaSDK.logError("CAMERA SERVICE", "updatePromptGuideStatus", "Error updating prompt status for " + screenType + ": " + result.message);
      }
    }).catch((error) => {
      this.unviredCordovaSDK.logError("CAMERA SERVICE", "updatePromptGuideStatus", "Exception while updating prompt status for " + screenType + ": " + error);
    });
  }

  async takePictureVisionAI(cert?:any, screenMode?: string,abrasionType?: Camopts["inspectionType"]) {
    return new Promise(async (resolve,reject)=>{
      await this.presentActionSheet().then(async (dismissed) => {
        let isFirstTimeGuidePromptOpen:boolean = true;
        let guidePrompt: string = "";
        let role: string = "";
        if(abrasionType=="External") {
          guidePrompt = "InsightExternalFirst"
        } else if(abrasionType=="Internal") {
          guidePrompt = "InsightInternalFirst"
        }

        this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "takePictureVisionAI", "CAMERA_SERVICE - SUCCESSFULLY PRESENTED ACTIONSHEET")
        if (dismissed.data == 1 && dismissed.role == "camera") {
          // this.visionAIService.imageSource = "camera";
          let result = await this.unviredCordovaSDK.dbSelect("APP_SETTINGS_HEADER", `KEY_FLD='${guidePrompt}'`);
          if (result.type == ResultType.success) {
            if (result.data.length > 0) {
              isFirstTimeGuidePromptOpen = JSON.parse(result.data[0].VALUE);
            } else {
              isFirstTimeGuidePromptOpen = true;
            }

            if (isFirstTimeGuidePromptOpen) {
              const guidePrompt = await this.modalController.create({
                component: UserGuidePromptComponent,
                cssClass: 'insightAIGuidePrompt',
                componentProps: {
                  role: dismissed.role,
                  abrasionType: abrasionType
                }
              })
              guidePrompt.present();
              let promptDismissed = await guidePrompt.onDidDismiss()
              if(promptDismissed.data && promptDismissed.data.role=='cancelled') return;
            }
          } else {
            this.unviredCordovaSDK.logError("INSIGHT AI HOME", "Error fetching APP_SETTINGS_HEADER:", result.message);
          }

          this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "takePictureVisionAI", "CAMERA_SERVICE - CAMERA OPTION SELECTED FROM ACTION SHEET")
          const options: Camopts = {
            preview: true,
            location: false,
            croppingEnabled: false,
            flashEnabled: true,
            autoFlash: false,
            mode: "InsightAI",
            cropOnPreview: true,
            inspectionType: abrasionType,
            minCamRectHeight: 256,
            minCamRectWidth: 256,
            cropperOptions: {
              mode: "InsightAI",
              inspectionType: abrasionType,
              minCropBoxHeight: 256,
              minCropBoxWidth: 256,
            }
          };
          const camera = new SamsonCamera();
          camera
            .openCamera(options)
            .then(result => {
              if (result) {
              // this.visionAIService.imageData = result.image;
              resolve(result);
          }
            })
            .catch(err => {
              // Handle user cancel or error
              console.log(err);
              if (this.alertService.isLoading) {
                this.alertService.dismiss();
              }
              reject(err);
            });

        } else if (dismissed.data == 2 && dismissed.role == "gallery") {
          // this.visionAIService.imageSource = "gallery";
          this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "takePictureVisionAI", "CAMERA_SERVICE - GALLERY OPTION SELECTED FROM ACTION SHEET");
          await this.alertService.present();
          if(this.device.platform=='browser') {
          var fileInput = document.createElement("input");
          fileInput.setAttribute("type", "file");
          fileInput.setAttribute("id", "myFileInput");
          fileInput.setAttribute("name", "myFileInputName");

          fileInput.style.display = "none";

          fileInput.oncancel = async ()=>{
            if(this.alertService.isLoading) {
              await this.alertService.dismiss();
            }
          }

          fileInput.onchange = (event: any) => {
            var selectedFiles = event.target.files;
            const inputElement = event.target as HTMLInputElement;
            const file = inputElement.files[0];
            const reader = new FileReader();
            reader.onload = async () => {
              this.imageData = <string>reader.result;
              const sizeInMB = await this.getBase64SizeInMB(this.imageData);
              if(sizeInMB>AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB) {
                if(this.alertService.isLoading) {
                  await this.alertService.dismiss();
                }
                await this.alertService.showAlert("Warning!",AppConstant.MAX_ALLOWED_IMAGE_WARNING_MSG);
                return; // Exit early to prevent further processing
              } else {
                // For browser, we need to create a blob URL from the base64 data
                const base64Data = this.imageData.split(',')[1];
                const blob = this.b64toBlob(base64Data, 'image/jpeg');
                const imageUrl = URL.createObjectURL(blob);
                
                const options: Camopts = {
                  image: imageUrl,
                  preview: true,
                  flashEnabled: true,
                  autoFlash: false,
                  location: false,
                  mode: "InsightAI",
                  inspectionType: abrasionType,
                  croppingEnabled: false,
                  cropOnPreview: true,
                  cropperOptions: {
                    mode: "InsightAI",
                    inspectionType: abrasionType,
                    minCropBoxHeight: 256,
                    minCropBoxWidth: 256,
                  }
                };
                const camera = new SamsonCamera();
                camera
                  .openCamera(options)
                  .then(async result => {
                    if (this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                    console.log("Success:", result);
                    // Clean up the blob URL
                    URL.revokeObjectURL(imageUrl);
                    resolve(result);
                  })
                  .catch(err => {
                    console.error(err);
                    // Clean up the blob URL on error
                    URL.revokeObjectURL(imageUrl);
                  });
                if(this.alertService.isLoading) {
                  await this.alertService.dismiss();
                }
              }
            };

            reader.onerror = async (error)=>{
              if(this.alertService.isLoading) {
                await this.alertService.dismiss();
              }
              this.alertService.showAlert("Error!","error reading file")
            }
            reader.readAsDataURL(file);
          };
          fileInput.click();
          } else if(this.platformId == 'electron') { //! windows
            var fileInput = document.createElement("input");
            fileInput.setAttribute("type", "file");
            fileInput.setAttribute("accept", "image/*");
            fileInput.style.display = "none";

            fileInput.oncancel = async ()=>{
              if(this.alertService.isLoading) {
                await this.alertService.dismiss();
              }
            }

            fileInput.onchange = (event: any) => {
              const inputElement = event.target as HTMLInputElement;
              const file = inputElement.files[0];
              if (!file) return;
              
              const reader = new FileReader();
              reader.onload = async () => {
                this.imageData = <string>reader.result;
                const sizeInMB = await this.getBase64SizeInMB(this.imageData);
                if(sizeInMB>AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB) {
                  if(this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                  await this.alertService.showAlert("Warning!",AppConstant.MAX_ALLOWED_IMAGE_WARNING_MSG);
                  return;
                } else {
                  const base64Data = this.imageData.split(',')[1];
                  const blob = this.b64toBlob(base64Data, 'image/jpeg');
                  const imageUrl = URL.createObjectURL(blob);
                  
                  const options: Camopts = {
                    image: imageUrl,
                    preview: true,
                    flashEnabled: true,
                    autoFlash: false,
                    location: false,
                    mode: "InsightAI",
                    inspectionType: abrasionType,
                    croppingEnabled: false,
                    cropOnPreview: true,
                    cropperOptions: {
                      mode: "InsightAI",
                      inspectionType: abrasionType,
                      minCropBoxHeight: 256,
                      minCropBoxWidth: 256,
                    }
                  };
                  const camera = new SamsonCamera();
                  camera
                    .openCamera(options)
                    .then(async result => {
                      if (this.alertService.isLoading) {
                        await this.alertService.dismiss();
                      }
                      console.log("Success:", result);
                      URL.revokeObjectURL(imageUrl);
                      resolve(result);
                    })
                    .catch(err => {
                      console.error(err);
                      URL.revokeObjectURL(imageUrl);
                    });
                  if(this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                }
              };

              reader.onerror = async (error)=>{
                if(this.alertService.isLoading) {
                  await this.alertService.dismiss();
                }
                this.alertService.showAlert("Error!","error reading file")
              }
              reader.readAsDataURL(file);
            };
            fileInput.click();
          } else if(this.platform.is('android')) {
            const options: CameraOptions = {
              quality: 100,
              destinationType: this.camera.DestinationType.FILE_URI,
              sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
              encodingType: this.camera.EncodingType.JPEG,
              correctOrientation: true,
              mediaType: this.camera.MediaType.PICTURE
            };
            
            this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "takePictureVisionAI", "CAMERA_SERVICE - ACCESSING IMAGE FROM GALLERY START");
            await this.camera.getPicture(options).then(async (imageData) => {
                const cleanPath = imageData.split('?')[0];
                (window as any).resolveLocalFileSystemURL(cleanPath, (fileEntry: any) => {
                  fileEntry.file(async (file: any) => {
                    if (file.size / (1024 * 1024) > AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB) {
                      await this.zone.run(async () => {
                        if (this.alertService.isLoading) {
                          await this.alertService.dismiss();
                        }
                        await this.alertService.showAlert("Warning!", AppConstant.MAX_ALLOWED_IMAGE_WARNING_MSG);
                      });
                      return; // Exit early to prevent further processing
                    } else {
                    // Use file path instead of converting to base64
                    const safePath = this.webView.convertFileSrc(cleanPath);
                    if(this.alertService.isLoading) {
                      await this.alertService.dismiss()
                    }
                    this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "takePictureVisionAI", "CAMERA_SERVICE - SUCCESSFULLY LOADED IMAGE SELECTED FROM GALLERY");
                    const options: Camopts = {
                      image: safePath,
                      preview: true,
                      flashEnabled: true,
                      autoFlash: false,
                      location: false,
                      mode: "InsightAI",
                      inspectionType: abrasionType,
                      croppingEnabled: false,
                      cropOnPreview: true,
                      cropperOptions: {
                        mode: "InsightAI",
                        minCropBoxHeight: 256,
                        minCropBoxWidth: 256,
                        inspectionType: abrasionType
                      }
                    };
                    const camera = new SamsonCamera();
                    camera
                      .openCamera(options)
                      .then(async result => {
                        console.log("result", result);
                        // console.log("Captured image:", result.image);
                        console.log("Latitude:", result.latitude, "Longitude:", result.longitude);
                        if (this.alertService.isLoading) {
                          await this.alertService.dismiss();
                        }
                        console.log("Success:", result);
                        this.visionAIService.imageData = result.image;
                        resolve(result);
                      })
                      .catch(err => {
                        console.error(err);
                      });
                    }
                  }, async (fileError: any) => {
                    console.error("Error getting file object:", fileError);
                    if(this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                  });
                }, async (resolveError: any) => {
                  console.error("Error resolving file URI:", resolveError);
                  if(this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                });
              }).catch(async err=>{
                this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "takePictureVisionAI", "CAMERA_SERVICE - ERROR LOADING IMAGE SELECTED FROM GALLERY");
                if(this.alertService.isLoading) {
                  await this.alertService.dismiss();
                  this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "takePictureVisionAI", "CAMERA_SERVICE - DISMISSED LOADER");
                }
                console.log(err);
              });
          } else if (this.device.platform == 'iOS') {
            let options: CameraOptions = {
              quality: 100,
              correctOrientation: true,
              destinationType: this.camera.DestinationType.FILE_URI,
              sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
              mediaType: this.camera.MediaType.PICTURE,
              encodingType: this.camera.EncodingType.JPEG
            };
 
            this.camera.getPicture(options).then(async (imageData) => {
              let imagePath = this.webView.convertFileSrc(imageData);
              const opts: Camopts = {
                image: imagePath,
                preview: true,
                flashEnabled: true,
                autoFlash: false,
                location: false,
                mode: "InsightAI",
                inspectionType: abrasionType,
                croppingEnabled: false,
                cropOnPreview: true,
                cropperOptions: {
                  mode: "InsightAI",
                  inspectionType: abrasionType,
                  minCropBoxHeight: 256,
                  minCropBoxWidth: 256,
                }
              };
              const camera = new SamsonCamera();
              camera
                .openCamera(opts)
                .then(async result => {
                  if (this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                  console.log("Success:", result);
                  // this.visionAIService.imageData = result.image;
                  resolve(result);
 
                })
                .catch(async err => {
                  console.error(err);
                  if (this.alertService.isLoading) {
                    await this.alertService.dismiss();
                  }
                  await this.alertService.showAlert("Error", "Failed to capture image: " + err.message);
                });
            }).catch(async (err) => {
              console.error("Error selecting image from gallery:", err);
              if (this.alertService.isLoading) {
                await this.alertService.dismiss();
              }
            });
          }
        }
      });
    })
  }

  // async writeFileToStorage(imageData:string,component?:string,insightAIScore?:any,fileName?:string) {
  //   if(this.device.platform!='browser') {
  //     imageData = imageData.split(',')[1];
  //     let UUID = "image-" + new Date().getTime().toString(16);
  //     let blob = this.b64toBlob(imageData , "image/jpeg");
  //     await this.writePictureToStorage(blob,fileName,component,"insightAI",insightAIScore,"","")
  //   } else {
  //     await this.writePictureToStorage(imageData,fileName,component,"insightAI",insightAIScore,"","")
  //   }
  // }

  // async writeRoutineFileToStorage(imageSrc:any,fileName:any) {
  //   this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","WRITEROUTINEFILETOSTORAGE METHOD START");
  //   return new Promise(async (resolve,reject)=>{
  //     let imageData = imageSrc.split(',')[1];
  //     let blob = this.b64toBlob(imageData , "image/jpeg");
  //     try {
  //       if(this.device.platform=='Android') {
  //         this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","CHECKING INSPECTIONS FOLDER EXISTS OR NOT");
  //         await this.file.checkDir(this.file.externalRootDirectory + "Download/", 'Inspections').then(async _ => {
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","INSPECTIONS FOLDER EXISTS, CONTINUE WRITING ROUTINE INSIGHT AI IMAGE TO STORAGE");
  //             await this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/" , fileName, blob,{ replace:true }).then(response => {
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","SUCCESSFULLY WROTE ROUTINE INSIGHT AI IMAGE TO FILE STORAGE");
  //               // this.LMDAttatchments.push(this.normalizeURL(this.file.externalRootDirectory+fileName));
  //               let path = this.normalizeURL(this.file.externalRootDirectory+"Download/Inspections/"+fileName);
  //               resolve(path);
  //             }).catch(err => {
  //               reject(err);
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","ERROR WRITING ROUTINE INSIGHT AI IMAGE TO DEVICE FILE STORAGE");
  //             })
  //           }).catch(async err => {
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","INSPECTIONS FOLDER DOESN'T EXISTS, CREATING INSPECTIONS FOLDER");
  //             await this.file.createDir(this.file.externalRootDirectory + "Download/", 'Inspections/', false).then(async result => {
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","SUCCESSFULLY CREATED INSPECTIONS FOLDER, CONTINUE WRITING IMAGE TO STORAGE");
  //               await this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/", fileName, blob,{ replace:true }).then(response => {
  //                 this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","SUCCESSFULLY WROTE ROUTINE INSIGHT AI IMAGE TO FILE STORAGE");
  //                 this.LMDAttatchments.push(this.normalizeURL(this.file.externalRootDirectory+fileName));
  //                 let path = this.normalizeURL(this.file.externalRootDirectory+"Download/Inspections/"+fileName);
  //                 resolve(path);
  //               }).catch(err => {
  //                 this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","ERROR WRITING ROUTINE INSIGHT AI IMAGE TO DEVICE FILE STORAGE");
  //                 reject(err);
  //               })
  //             }).catch(error=>{
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","ERROR CREATING INSPECTIONS FOLDER");
  //             })
  //           });
  //       } else if(this.device.platform=='iOS') {
  //           await this.file.writeFile(this.file.documentsDirectory,fileName,blob,{ replace:true }).then(res=>{
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","SUCCESSFULLY WROTE IMAGE TO FILE STORAGE");
  //               this.LMDAttatchments.push(this.normalizeURL(this.file.documentsDirectory+fileName))
  //               let path = this.normalizeURL(this.file.documentsDirectory+fileName)
  //               resolve(path);
  //           }).catch(err=>{
  //             console.log(err);
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","ERROR WRITING ROUTINE INSIGHT AI IMAGE TO DEVICE FILE STORAGE");
  //           });
  //       } else if(this.device.platform=='windows') {
  //         await this.file.writeFile(this.file.dataDirectory, fileName, blob, { replace: true }).then(response => {
  //           this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","SUCCESSFULLY WROTE IMAGE TO FILE STORAGE");
  //             this.LMDAttatchments.push(this.normalizeURL(this.file.dataDirectory+fileName))
  //             let path = this.normalizeURL(this.file.dataDirectory+fileName);
  //             resolve(path);
  //         }).catch(err => {
  //           this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","ERROR WRITING ROUTINE INSIGHT AI IMAGE TO DEVICE FILE STORAGE");
  //           reject(err);
  //         })
  //       } else {
  //           this.LMDAttatchments.push(imageData)
  //           resolve(imageSrc);
  //       }
  //     } catch (error) {
  //       if(this.alertService.isLoading) {
  //         await this.alertService.dismiss();
  //       }
  //       this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","WRITEROUTINEFILETOSTORAGE","WRITEROUTINEFILETOSTORAGE CATCH BLOCK : "+error);
  //       reject(error);
  //     }
  //   })
  // }

  // async writePictureToStorage(blob?:any,fileName?,component?:string, imageMode?:any,insightAIScore?:any,latitude?:string,longitude?:string) {
  //   try {
  //     if(this.device.platform=='Android') {
  //       this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","SAVING IMAGE TO DEVICE FILE STORAGE START");
  //       this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","CHECKING INSPECTIONS FOLDER EXISTS OR NOT");
  //       await this.file.checkDir(this.file.externalRootDirectory + "Download/", 'Inspections').then(async _ => {
  //         this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","INSPECTIONS FOLDER EXISTS, CONTINUE WRITING IMAGE TO STORAGE");
  //           await this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/" , fileName, blob,{ replace:true }).then(response => {
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","SUCCESSFULLY WROTE IMAGE TO FILE STORAGE");
  //             if(component!=undefined && component=='ROUTINE_INSPECTION') {
  //               this.LMDAttatchments.push(this.normalizeURL(this.file.externalRootDirectory+fileName));
  //               let path = this.normalizeURL(this.file.externalRootDirectory+fileName);
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","RETURNING STORAGE FILE PATH");
  //               return path;
  //             } else {
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","RETURNING STORAGE FILE PATH");
  //               this.editedImg.push({"Image":this.normalizeURL(this.file.externalRootDirectory+"Download/Inspections/"+fileName),"mode": imageMode,"insightAIScore":insightAIScore});
  //               this.imageAndAnnotationArray.push({ originalImgList: this.normalizeURL(this.file.externalRootDirectory+"Download/Inspections/"+fileName), annotationsForImage: '', latitude: latitude, longitude: longitude, report: true })
  //               this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
  //             }
  //           }).catch(async err=> {
  //             console.log("error writing file to storage");
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","ERROR WRITING IMAGE TO DEVICE FILE STORAGE : "+err);
  //           })
  //       }).catch(async(err)=> {
  //         this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","INSPECTIONS FOLDER DOESN'T EXISTS, CREATING INSPECTIONS FOLDER : "+err);
  //           await this.file.createDir(this.file.externalRootDirectory + "Download/", 'Inspections/', false).then(async result => {
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","SUCCESSFULLY CREATED INSPECTIONS FOLDER, CONTINUE WRITING IMAGE TO STORAGE");
  //             await this.file.writeFile(this.file.externalRootDirectory + "Download/Inspections/", fileName, blob,{ replace:true }).then(response => {
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","SUCCESSFULLY WROTE IMAGE TO FILE STORAGE");
  //               if(component!=undefined && component=='ROUTINE_INSPECTIONS') {
  //                 this.LMDAttatchments.push(this.normalizeURL(this.file.externalRootDirectory+fileName));
  //                 let path = this.normalizeURL(this.file.externalRootDirectory+fileName);
  //                 return path;
  //               } else {
  //                 this.editedImg.push({"Image":this.normalizeURL(this.file.externalRootDirectory+"Download/Inspections/"+fileName),"mode": imageMode,"insightAIScore":insightAIScore});
  //                 this.imageAndAnnotationArray.push({ originalImgList: this.normalizeURL(this.file.externalRootDirectory+"Download/Inspections/"+fileName), annotationsForImage: '', latitude: latitude, longitude: longitude, report: true })
  //                 this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
  //               }
  //             }).catch(err=> {
  //               console.log("error writing file to storage");
  //               this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","ERROR WRITING IMAGE TO DEVICE FILE STORAGE : "+err);
  //             })
  //           }).catch(err=>{
  //             this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","ERROR CREATING INSPECTIONS FOLDER : "+err);
  //           })
  //         });
  //     } else if(this.device.platform=='iOS') {
  //         await this.file.writeFile(this.file.documentsDirectory,fileName,blob,{ replace:true }).then(res=>{
  //           this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","SUCCESSFULLY WROTE IMAGE TO FILE STORAGE");
  //           if(component!=undefined && component=='ROUTINE_INSPECTIONS') {
  //             this.LMDAttatchments.push(this.normalizeURL(this.file.documentsDirectory+fileName))
  //           } else {
  //             this.editedImg.push({"Image":this.normalizeURL(this.file.documentsDirectory+fileName),"mode": imageMode});
  //             this.imageAndAnnotationArray.push({ originalImgList: this.normalizeURL(this.file.documentsDirectory+fileName), annotationsForImage: '', latitude: 0, longitude: 0, report: true })
  //             this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
  //           }
  //         }).catch(async err=>{
  //           if(this.alertService.isLoading) {
  //             await this.alertService.dismiss();
  //           }
  //           this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","ERROR WRITING IMAGE TO DEVICE FILE STORAGE");
  //         });
  //     } else if(this.device.platform=='windows') {
  //       await this.file.writeFile(this.file.dataDirectory, fileName, blob, { replace: true }).then(response => {
  //         this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","SUCCESSFULLY WROTE IMAGE TO FILE STORAGE");
  //         if(component!=undefined && component=='ROUTINE_INSPECTIONS') {
  //           this.LMDAttatchments.push(this.normalizeURL(this.file.dataDirectory+fileName));
  //         } else {
  //           this.editedImg.push({"Image":this.normalizeURL(this.file.dataDirectory+fileName),"mode": imageMode});
  //           this.imageAndAnnotationArray.push({ originalImgList: this.normalizeURL(this.file.dataDirectory+fileName), annotationsForImage: '', latitude: 0, longitude: 0, report: true })
  //           this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
  //         }
  //       }).catch(async err => {
  //         if(this.alertService.isLoading) {
  //           await this.alertService.dismiss();
  //         }
  //         this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","ERROR WRITING IMAGE TO DEVICE FILE STORAGE");
  //       })
  //     } else {
  //       if(component!=undefined && component=='ROUTINE_INSPECTIONS') {
  //         this.LMDAttatchments.push(blob)
  //       } else {
  //         this.editedImg.push({"Image":blob,"mode": imageMode});
  //         this.imageAndAnnotationArray.push({ originalImgList:blob, annotationsForImage: '', latitude: 0, longitude: 0, report: true })
  //         this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
  //       }
  //     }
  //   } catch (error) {
  //     if(this.alertService.isLoading) {
  //       await this.alertService.dismiss();
  //     }
  //     this.unviredCordovaSDK.logInfo("CAMERA_SERVICE","writeFileToStorage","WRITEPICTURETOSTORAGE CATCH BLOCK : "+ error);
  //   }
  // }

  captureImageHandler(opMode?: string, pageComp?: any, rating?: any, latitude?: any, longitude?: any, annotatedText?: any, orgnlImg?: any, edImg?: any, reported?: any, imageMode?: any) {
    let editedImage: any = this.editedImg;
    this.textValue = annotatedText
    if (opMode === "baseline-ext") {
      if (pageComp) {
        pageComp.setExternalRange(rating);
      }
      this.baselineExtEdited = true;
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported;
      this.originalImgList.push(this.normalizeURL(orgnlImg))
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.baselineExternal.push(this.normalizeURL(edImg))
      this.baselineExternalOriginal.push({ originalImgList: this.normalizeURL(orgnlImg), annotationsForImage: this.annotationsForImage, latitude: this.locationLat, longitude: this.locationLong, report: this.report })
      this.imageAndAnnotationExternal = { originalImgList: this.baselineExternalOriginal }
    } else if (opMode === "baseline-int") {
      if (pageComp) {
        pageComp.setInternalRange(rating);
      }
      this.baselineIntEdited = true;
      this.originalImgList.push(this.normalizeURL(orgnlImg))
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported;
      this.baselineInternal.push(this.normalizeURL(edImg))
      this.baselineInternalOriginal.push({ originalImgList: this.normalizeURL(orgnlImg), annotationsForImage: this.annotationsForImage, latitude: this.locationLat, longitude: this.locationLong, report: this.report })
      this.imageAndAnnotationInternal = { originalImgList: this.baselineInternalOriginal }
    } else {
      this.measurementEdited = true;
      if (editedImage.length == 1 && editedImage[0].Image == './assets/img/samson2.png') {
        editedImage = [];
      }
      if (this.originalImgList.length == 1 && this.originalImgList[0] == './assets/img/samson2.png') {
        this.originalImgList = [];
      }
      console.log("---------------" + edImg)
      this.originalImgList.push(this.normalizeURL(orgnlImg))
      let safeURL: SafeUrl= this.normalizeURL(edImg)
      editedImage.push({"Image":safeURL,"mode":imageMode})
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported;
      console.log(editedImage[editedImage.length - 1])
      this.imageAndAnnotationArray.push({ originalImgList: this.normalizeURL(orgnlImg), annotationsForImage: this.annotationsForImage, latitude: this.locationLat, longitude: this.locationLong, report: this.report })
      this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
      // this.imageAndAnnotation = { originalImgList: this.originalImgList, annotationsForImage: this.annotationsForImage, latitude : this.locationLat, longitude: this.locationLong}
      if (pageComp) {
        if (rating !== '') {
          pageComp.setRating(Number(rating));
        } else {

        }
      }
    }
    return editedImage;
  }

  async editImageHandler(ind?: any, opMode?: string, pageComp?: any, rating?: any, latitude?: any, longitude?: any, annotatedText?: any, imgToEdit?: any, editedImg?: any, reported?: any) {
    this.measurementEdited = true;
    if (opMode === "baseline-ext") {
      if (pageComp) {
        pageComp.setExternalRange(rating);
      }
      this.baselineExtEdited = true;
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = this.report;
      this.originalImgList[ind] = this.normalizeURL(imgToEdit)
      this.baselineExternal[ind] = this.normalizeURL(editedImg);
      this.editedImg[ind].Image = this.normalizeURL(editedImg)
      var previousAnnot = "";
      if (this.baselineExternalOriginal.length > 0) {
        if (this.baselineExternalOriginal[ind] != undefined) {
          if (this.baselineExternalOriginal[ind].annotationsForImage != undefined) {
            previousAnnot = this.baselineExternalOriginal[ind].annotationsForImage
          }
        }
      }
      this.baselineExternalOriginal[ind] = { originalImgList: (imgToEdit != undefined && imgToEdit != "") ? this.normalizeURL(imgToEdit) : this.baselineExternalOriginal[ind].originalImgList, annotationsForImage: previousAnnot + ", " + this.annotationsForImage, latitude: this.locationLat, longitude: this.locationLong, report: this.report }
      this.imageAndAnnotationExternal = { originalImgList: this.baselineExternalOriginal }
    } else if (opMode === "baseline-int") {
      if (pageComp) {
        pageComp.setInternalRange(rating);
      }
      this.baselineIntEdited = true;
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported;
      this.originalImgList[ind] = this.normalizeURL(imgToEdit)
      this.baselineInternal[ind] = this.normalizeURL(editedImg)
      this.editedImg[ind].Image = this.normalizeURL(editedImg)
      var previousAnnot = "";
      if (this.baselineInternalOriginal.length > 0) {
        if (this.baselineInternalOriginal[ind] != undefined) {
          if (this.baselineInternalOriginal[ind].annotationsForImage != undefined) {
            previousAnnot = this.baselineInternalOriginal[ind].annotationsForImage
          }
        }
      }
      this.baselineInternalOriginal[ind] = { originalImgList: (imgToEdit != undefined && imgToEdit != "") ? this.normalizeURL(imgToEdit) : this.baselineInternalOriginal[ind].originalImgList, annotationsForImage: previousAnnot + ", " + this.annotationsForImage, latitude: this.locationLat, longitude: this.locationLong, report: this.report }
      this.imageAndAnnotationInternal = { originalImgList: this.baselineInternalOriginal }
    } else {
      if (pageComp) {
        if (rating !== '') {
          pageComp.setRating(Number(rating));
        } else {

        }
      }
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported
      this.originalImgList[ind] = this.normalizeURL(imgToEdit)
      this.editedImg[ind].Image = this.normalizeURL(editedImg)
      var previousAnnot = "";
      if (this.imageAndAnnotationArray.length > 0) {
        if (this.imageAndAnnotationArray[ind] != undefined) {
          if (this.imageAndAnnotationArray[ind].annotationsForImage != undefined && this.imageAndAnnotationArray[ind].annotationsForImage != '') {
            previousAnnot = this.imageAndAnnotationArray[ind].annotationsForImage
          }
        }
      }
      var tempAnnotation = ''
      if(previousAnnot == '') {
        tempAnnotation = tempAnnotation + this.annotationsForImage
      } else {
        tempAnnotation = previousAnnot + ", " + this.annotationsForImage
      }
      
      this.imageAndAnnotationArray[ind] = { originalImgList: (imgToEdit != undefined && imgToEdit != "") ? this.normalizeURL(imgToEdit) : this.imageAndAnnotationArray[ind].originalImgList, annotationsForImage: tempAnnotation, latitude: this.locationLat, longitude: this.locationLong, report: this.report }
      // let tempUserSetting = await this.unviredCordovaSDK.userSettings();
      // let inspHeader = await this.utilService.getSelectedInspectionHeader();
      // if(inspHeader?.INSPECTION_STATUS==AppConstant.REOPENED) {
      //   if(tempUserSetting.data.USER_ID.toUpperCase().indexOf('CUSTOMER') < 0 ) {
      //     this.editedImg[ind]['existingEdited'] = true;
      //     this.imageAndAnnotationArray[ind]['existingEdited'] = true;
      //   }
      // }
      this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
      
    }
  }

  captureImageHandlerAndroid(rating?: any, latitude?: any, longitude?: any, annotatedText?: any, orgnlImg?: any, edImg?: any, reported?: any, imagemode?: any, isDisplayedAsModal?: boolean) {
    let editedImage: any = this.editedImg;
    this.textValue = annotatedText
      this.measurementEdited = true;
      if (editedImage.length == 1 && editedImage[0].Image == './assets/img/samson2.png') {
        editedImage = [];
      }
      if (this.originalImgList.length == 1 && this.originalImgList[0] == './assets/img/samson2.png') {
        this.originalImgList = [];
      }
      this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , "Before Edit Image");
      this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , "Write File Successful : Path"  + "EditImage Path---------------" + edImg);
      console.log("EditImage Path---------------" + edImg)
      this.originalImgList.push(this.normalizeURL(orgnlImg))
      editedImage.push({"Image":this.normalizeURL(edImg),"mode": imagemode})
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported;
      console.log(editedImage[editedImage.length - 1])
      this.imageAndAnnotationArray.push({ originalImgList: this.normalizeURL(orgnlImg), annotationsForImage: this.annotationsForImage, latitude: this.locationLat, longitude: this.locationLong, report: this.report })
      this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
      this.unviredCordovaSDK.logInfo("Camera Service", "takePicture" , "this.editImage(this.editedImg[this.editedImg.length - 1], this.editedImg.length - 1, this.androidExtras.abrMode, this.androidExtras.opMode, this.androidExtras.pageComponent, this.androidExtras.maxRating, rating, this.androidExtras.productName, this.androidExtras.isDemo, false)    ------ "  + JSON.stringify(this.editedImg[this.editedImg.length - 1]) + ',' + (this.editedImg.length - 1) + ',' + this.androidExtras.abrMode + ',' + this.androidExtras.opMode + ',' + this.androidExtras.pageComponent + ',' + this.androidExtras.maxRating + ',' + rating + ',' + this.androidExtras.productName + ',' + this.androidExtras.isDemo + ',' + false );
      // this.editImage(this.editedImg[this.editedImg.length - 1], this.editedImg.length - 1, this.androidExtras.abrMode, this.androidExtras.opMode, this.androidExtras.pageComponent, this.androidExtras.maxRating, rating, this.androidExtras.productName, this.androidExtras.isDemo, true)
      if(this.cameraMode != 'gallery') {
        this.editImage(this.editedImg[this.editedImg.length - 1].Image, this.editedImg.length - 1, this.androidExtras.abrMode, this.androidExtras.opMode, this.androidExtras.pageComponent, this.androidExtras.maxRating, rating, this.androidExtras.productName, this.androidExtras.isDemo, false)
        if(isDisplayedAsModal!=undefined && !isDisplayedAsModal) {
          this.navCtrl.pop();
        }
      } else if(this.cameraMode == 'gallery') {
        this.editImage(this.editedImg[this.editedImg.length - 1].Image, this.editedImg.length - 1, this.androidExtras.abrMode, this.androidExtras.opMode, this.androidExtras.pageComponent, this.androidExtras.maxRating, rating, this.androidExtras.productName, this.androidExtras.isDemo, false)
      }
  }

  async onFileSelected(event) {
    // console.log(event);
    var filepath = URL.createObjectURL(event.target.files[0])
    var mimeType = event.target.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      var message = "Only images are supported.";
      return;
    }

    // Size check before loading
    var sizeInMB = event.target.files[0].size / (1024 * 1024);
    if (sizeInMB > AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB) {
      if(this.alertService.isLoading) {
        await this.alertService.dismiss();
      }
      await this.alertService.showAlert("Warning!", AppConstant.MAX_ALLOWED_IMAGE_WARNING_MSG);
      return;
    }

    var reader = new FileReader();
    var imagePath = event.target.files;
    reader.readAsDataURL(event.target.files[0]);
    reader.onload = (_event) => {
      let imgURL = reader.result;
      // let size = await this.getBase64SizeInMB(imgURL.split[','][1]);
      // console.log(imgURL)
      this.browserJson.imageUrl = filepath
      windowsImageEditor.captureImage(this.browserJson, (res: any) => {
        if (res.Type === ResultType.success) {
          console.log('Success Result: ', res);
          this.zone.run(() => { 
            this.editedImg = this.captureImageHandlerBrowser(this.browserJsonParms.mode, this.browserJsonParms.pageComponent, res.Rating, res.Latitude, res.Longitude, res.Texts, res.Image, res.EditedImage, res.Report, "browser gallery");
            return this.editedImg;
          });
        } else if (res.type === ResultType.error) {
          console.log('Error Result: ', res);
          this.unviredCordovaSDK.logError("CAMERA SERVICE", "TAKE PICTURE", JSON.stringify(res.message));
        }
      })
    }
  } 

  captureImageHandlerBrowser(opMode?: string, pageComp?: any, rating?: any, latitude?: any, longitude?: any, annotatedText?: any, orgnlImg?: any, edImg?: any, reported?: any, imageMode?: any) {
    let editedImage: any = this.editedImg;
    this.textValue = annotatedText
    var orientation = -1;    
    this.measurementEdited = true;
      if (editedImage.length == 1 && editedImage[0].Image == './assets/img/samson2.png') {
        editedImage = [];
      }
      if (this.originalImgList.length == 1 && this.originalImgList[0].Image == './assets/img/samson2.png') {
        this.originalImgList = [];
      }
      console.log("---------------" + edImg)
      
      
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported;
      console.log(editedImage[editedImage.length - 1])
      this.imageAndAnnotationArray.push({ originalImgList: orgnlImg, annotationsForImage: this.annotationsForImage, latitude: this.locationLat, longitude: this.locationLong, report: this.report })
      this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
      // this.imageAndAnnotation = { originalImgList: this.originalImgList, annotationsForImage: this.annotationsForImage, latitude : this.locationLat, longitude: this.locationLong}
      if (pageComp) {
        if (rating !== '') {
          pageComp.setRating(Number(rating));
        } else {

        }
      }

      this.imageCompress.compressFile(orgnlImg, orientation, 50, 50).then(
        result => {
          orgnlImg = result;
          this.sizeOFCompressedImage = this.imageCompress.byteCount(orgnlImg) / (1024 * 1024)
          console.warn('Size in bytes after compression:', this.sizeOFCompressedImage);
          this.originalImgList.push(orgnlImg)
        }, error => {
          this.originalImgList.push(orgnlImg)
        });
  
        this.imageCompress.compressFile(edImg, orientation, 50, 50).then(
          result => {
            edImg = result;
            editedImage.push({"Image":edImg,"mode":imageMode})
            this.sizeOFCompressedImage = this.imageCompress.byteCount(edImg) / (1024 * 1024)
            console.warn('Size in bytes after compression:', this.sizeOFCompressedImage);
          }, error => {
            editedImage.push({"Image":edImg,"mode":imageMode})
          });
    return editedImage;
  }

  editImageHandlerBrowser(ind?: any, opMode?: string, pageComp?: any, rating?: any, latitude?: any, longitude?: any, annotatedText?: any, imgToEdit?: any, editedImg?: any, reported?: any) {
    this.measurementEdited = true;
      if (pageComp) {
        if (rating !== '') {
          pageComp.setRating(Number(rating));
        } else {

        }
      }
      this.annotationsForImage = annotatedText;
      if (this.annotationsForImage == undefined) {
        this.annotationsForImage = [""]
      }
      this.locationLat = latitude;
      this.locationLong = longitude;
      this.report = reported
      this.originalImgList[ind] = imgToEdit
      this.editedImg[ind].Image = editedImg
      var previousAnnot = "";
      if (this.imageAndAnnotationArray.length > 0) {
        if (this.imageAndAnnotationArray[ind] != undefined) {
          if (this.imageAndAnnotationArray[ind].annotationsForImage != undefined && this.imageAndAnnotationArray[ind].annotationsForImage != '') {
            previousAnnot = this.imageAndAnnotationArray[ind].annotationsForImage
          }
        }
      }
      var tempAnnotation = ''
      if(previousAnnot == '') {
        tempAnnotation = tempAnnotation + this.annotationsForImage
      } else {
        tempAnnotation = previousAnnot + ", " + this.annotationsForImage
      }
      this.imageAndAnnotationArray[ind] = { originalImgList: (imgToEdit != undefined && imgToEdit != "") ? imgToEdit : this.imageAndAnnotationArray[ind].originalImgList, annotationsForImage: tempAnnotation, latitude: this.locationLat, longitude: this.locationLong, report: this.report }
      this.imageAndAnnotation = { originalImgList: this.imageAndAnnotationArray }
    }





  localUrl: any;
  localCompressedURl: any;
  sizeOfOriginalImage: number;
  sizeOFCompressedImage: number;
  fileSelected(event: any) {
    var fileName: any;
    this.file = event.target.files[0];
    fileName = this.file['name'];
    if (event.target.files && event.target.files[0]) {
      var reader = new FileReader();
      reader.onload = (event: any) => {
        this.localUrl = event.target.result;
        this.compressFile(this.localUrl, fileName)
      }
      reader.readAsDataURL(event.target.files[0]);
    }
  }
  imgResultBeforeCompress: string;
  imgResultAfterCompress: string;
  compressFile(image, fileName) {
    var orientation = -1;
    this.sizeOfOriginalImage = this.imageCompress.byteCount(image) / (1024 * 1024);
    console.warn('Size in bytes is now:', this.sizeOfOriginalImage);
    this.imageCompress.compressFile(image, orientation, 50, 50).then(
      result => {
        this.imgResultAfterCompress = result;
        this.localCompressedURl = result;
        this.sizeOFCompressedImage = this.imageCompress.byteCount(result) / (1024 * 1024)
        console.warn('Size in bytes after compression:', this.sizeOFCompressedImage);
        // create file from byte
        const imageName = fileName;
        // call method that creates a blob from dataUri
        const imageBlob = this.dataURItoBlob(this.imgResultAfterCompress.split(',')[1]);
      });
  }
  dataURItoBlob(dataURI) {
    const byteString = window.atob(dataURI);
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const int8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteString.length; i++) {
      int8Array[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([int8Array], { type: 'image/jpeg' });
    return blob;
  }

  unlockScreen(){
    // allow user rotate
    this.screenOrientation.unlock();
  }

  setPortrait(){
    // set to portrait
    this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.PORTRAIT);
  }

  setComparator(imageData) {
    this.comparatorImage = imageData;
  }

  getComparator() {
    return this.comparatorImage;
  }

  setComparatorPage(page) {
    this.comparatorPage = page;
  }

  getComparatorPage() {
    return this.comparatorPage
  }

  async deleteAttachments(file:any) {
    if(this.device.platform=='Android') {
        let path = "file://"+file.substring(file.indexOf('_app_file_') + 11, file.lastIndexOf('/'))
        let fileName = file.substring(file.lastIndexOf('/') + 1)
        let res=await this.file.removeFile(path,fileName);
        console.log(res);
    }
  }

  async takePictureComparator() {
    
    var that = this;
    if (this.platformId == 'electron') {
     return await this.presentActionSheet().then((dismissed) => {
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          return this.camera.getPicture(options).then((imageData) => {
            // let realData = imageData;
            // let blob = this.b64toBlob(realData.split(',')[1], 'image/jpeg');
            // var imgURL = URL.createObjectURL(blob);
            // this.imageData = this._DomSanitizationService.bypassSecurityTrustResourceUrl(imgURL);
            return imageData;
          }, (err) => {
            console.log(err);
          });
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            sourceType: this.camera.PictureSourceType.SAVEDPHOTOALBUM,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          return this.camera.getPicture(options).then((imageData) => {
            // console.log("IMAGEDATD ++++++++++" + imageData)
            // let realData = imageData;
            // let blob = this.b64toBlob(realData.split(',')[1], 'image/jpeg');
            // var imgURL = URL.createObjectURL(blob);
            // this.imageData = this._DomSanitizationService.bypassSecurityTrustResourceUrl(imgURL);
            return imageData;
          }, (err) => {
            console.log(err);
          });
        } else {
          console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
      })
    } else if (this.device.platform == 'browser') {
      return await this.presentActionSheet().then((dismissed) => {
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          const options: CameraOptions = {
            quality: 50,
            targetWidth: 640,
            targetHeight: 480,
            destinationType: this.camera.DestinationType.FILE_URI,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          return this.camera.getPicture(options).then((imageData) => {
            // imageData = "data:image/jpeg;base64," + imageData
            // this.imageData = imageData;
            return imageData;
          }, (err) => {
            console.log(err);
          });
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          document.getElementById('myInput').click();
        } else {
          console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
      })
    } else if (this.platform.is("android")) {
      return await this.presentActionSheetComparator().then(async(dismissed) => {
        console.log(dismissed);
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          const options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE,
            sourceType: this.camera.PictureSourceType.CAMERA,
            allowEdit: false,
            correctOrientation: true
          };
    
          try {
            const imageData = await this.camera.getPicture(options);
            console.log("IMAGEDATD ++++++++++" + imageData);
            return imageData;
            // const realData = imageData;
            // const blob = this.b64toBlob(realData.split(',')[1], 'image/jpeg');
            // const imgURL = URL.createObjectURL(blob);
            // this.imageData = this._DomSanitizationService.bypassSecurityTrustResourceUrl(imgURL);
            // return this.imageData;
            // Optionally call a function to save or process this blob
            // this.processCapturedImage(blob, 'image.jpeg');
    
          } catch (err) {
            console.error('Camera error: ', err);
            const tempToast = await this.toastController.create({
              message: "Capture Image Failed",
              duration: 2000,
              color: "dark",
              position: "middle"
            });
            // tempToast.present();
            await tempToast.present();
            return null;
          }
          // this.cameraService.setComparatorPage(that);
          // this.unlockScreen();
          // var extras : NavigationExtras = { state: {currentPage: this.component}}
          // this.router.navigate(['camera-comparator']);
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          let cameraOptions: CameraOptions = {
            quality: 100,
            destinationType: this.camera.DestinationType.DATA_URL,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE,
            sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
          }

          return await this.camera.getPicture(cameraOptions).then((imageData: string) => {
          // let realData = imageData;
          // let blob = this.b64toBlob(realData.split(',')[1], 'image/jpeg');
          // var imgURL = URL.createObjectURL(blob);
          // this.imageData = this._DomSanitizationService.bypassSecurityTrustResourceUrl(imgURL);
          return imageData;
          });
          // this.imagePicker.getPictures(options).then((results) => {
          //   for (var i = 0; i < results.length; i++) {
          //       console.log('Image URI: ' + results[i]);
          //   }
          //   this.imageData = imageData;

          // }, (err) => {
          //   console.log(err)
          // });
        } else {
          // this.cameraMode = ''
          // console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        // this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        // this.zone.run(() => {
        //   return ""
        // })
      })
    } else {
      return await this.presentActionSheetComparator().then((dismissed) => {
        console.log(dismissed);
        if (dismissed.data == 1 && dismissed.role == 'camera') {
          let options: CameraOptions = {
            quality: 50,
            destinationType: this.camera.DestinationType.DATA_URL,
            sourceType: this.camera.PictureSourceType.CAMERA,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE
          }
          return this.camera.getPicture(options).then((imageData: string) => {
            // let realData = imageData;
            // let blob = this.b64toBlob(realData.split(',')[1], 'image/jpeg');
            // var imgURL = URL.createObjectURL(blob);
            // this.imageData = this._DomSanitizationService.bypassSecurityTrustResourceUrl(imgURL);
            return this.imageData;
          });
          //   this.cameraMode = dismissed.role
          //   this.unlockScreen();
          // this.router.navigate(['camera-comparator']);
        } else if (dismissed.data == 2 && dismissed.role == 'gallery') {
          let cameraOptions: CameraOptions = {
            quality: 100,
            destinationType: this.camera.DestinationType.DATA_URL,
            encodingType: this.camera.EncodingType.JPEG,
            mediaType: this.camera.MediaType.PICTURE,
            sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,

          }

          return this.camera.getPicture(cameraOptions).then((imageData: string) => {
            // let realData = imageData;
            // let blob = this.b64toBlob(realData.split(',')[1], 'image/jpeg');
            // var imgURL = URL.createObjectURL(blob);
            // this.imageData = this._DomSanitizationService.bypassSecurityTrustResourceUrl(imgURL);
            return this.imageData;
          });
          // this.imagePicker.getPictures(options).then((results) => {
          //   for (var i = 0; i < results.length; i++) {
          //       console.log('Image URI: ' + results[i]);
          //   }
          //   this.imageData = imageData;

          // }, (err) => {
          //   console.log(err)
          // });
        } else {
          // this.cameraMode = ''
          // console.log('**************  NO ACTION  ***************');
        }
      }).catch(async (err) => {
        console.log('**************  ERROR BLOCK  ***************');
        // this.unviredCordovaSDK.logInfo("CAMERA SERVICE", "TAKE PICTURE", "LOG MEMORY USAGE")
        var tempToast = await this.toastController.create({
          message: "Capture Image Failed",
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
        console.log("Image data:", this.imageData);
        return this.imageData;
        // this.zone.run(() => {
        //   return ""
        // })
      })

    }
  }
  async presentActionSheetComparator() {
    const actionSheet = await this.actionSheetController.create({
      cssClass: 'my-custom-class',
      buttons: [{
        text: 'Camera',
        icon: 'camera',
        handler: () => {
          actionSheet.dismiss(1, 'camera');
        }
      }, {
        text: 'Gallery',
        icon: 'image',
        handler: () => {
          actionSheet.dismiss(2, 'gallery');
        }
      }]
    });
    await actionSheet.present();

    let dataOnDismissed = await actionSheet.onDidDismiss();
    return dataOnDismissed;
  }
}