import { Component, OnInit } from '@angular/core';
import { MenuController } from '@ionic/angular';
import { Router } from '@angular/router';
import { HelpService } from 'src/app/services/help.service';
import { TranslateService } from '@ngx-translate/core';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-lmd-configuration-type',
  templateUrl: './lmd-configuration-type.page.html',
  styleUrls: ['./lmd-configuration-type.page.scss'],
})
export class LmdConfigurationTypePage implements OnInit {
  constructor(public menu: MenuController,
    public helpService: HelpService,
    public translate: TranslateService,
    public dataService: DataService,
    public utilityService: UtilserviceService,
    public router: Router) { }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  gotoHome() {
    this.utilityService.menuAlert('home', 'home')
  }
  goToLineTracker() {
    this.utilityService.menuAlert('lineTracker', 'line-tracker-home')
  }

  gotoInspections() {
    this.utilityService.menuAlert('inspections', 'inspection-home')
  }

  gotoResources() {
    this.utilityService.menuAlert('resources', 'resource')
  }

  gotoContact() {
    this.utilityService.menuAlert('contact', 'contact')
  }

  navigateToNewConfigurationsPage() {
    this.router.navigate(['configuration-winches']);
  }

  navigateToPreviousConfigurationsPage() {
    this.router.navigate(['configuration-port-details']);
  }

}
