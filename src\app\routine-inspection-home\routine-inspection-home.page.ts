import { Component, OnInit } from '@angular/core';
import { UtilserviceService } from '../services/utilservice.service';
import { MenuController } from '@ionic/angular';
import { HelpService } from '../services/help.service';
import { Router } from '@angular/router';
import { DataService } from '../services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip, faMagnifyingGlass, faTrash, faCircleChevronLeft } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from '../services/platform.service';
import { async } from 'rxjs';
@Component({
  selector: 'app-routine-inspection-home',
  templateUrl: './routine-inspection-home.page.html',
  styleUrls: ['./routine-inspection-home.page.scss'],
})
export class RoutineInspectionHomePage implements OnInit {

  mode = true;
  inspectionCount: any = 0;
  platformId: string = this.platformService.getPlatformId();

  constructor(private route: Router, public device: Device, private service: UtilserviceService, public menu: MenuController, public helpService: HelpService,
    public dataService: DataService,public faIconLibrary : FaIconLibrary, public platformService: PlatformService) {

      this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip, faMagnifyingGlass, faTrash, faCircleChevronLeft)
    
    this.getCountInspections()
    this.dataService.getApplicationSetting()
    this.dataService.setRoutineInspectionSelectedUom();
  }

  async getCountInspections() {
    var res = await this.dataService.getCountRoutineInspections();
    if (res.type == ResultType.success) {
      if (res.data.length > 0) {
        this.inspectionCount = res.data[0].COUNT;
      }
    }
  }
  ngOnInit() {
  }

  newRoutineInspection() {
    this.route.navigate(['routine-inspection-certificates']);
    //  this.service.setInspState('new');
    //  this.route.navigate(['inspection']);
  }
  
  screenMode() {
    this.mode = !this.mode;
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  //Exit help mode
  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  async ionViewWillEnter() {
    await this.getCountInspections()
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  openInprogressIlst() {
    this.route.navigate(['routine-inspection-in-progress']);
  }

  openCompletedList() {
    this.route.navigate(['routine-inspection-completed']);
  }
}

