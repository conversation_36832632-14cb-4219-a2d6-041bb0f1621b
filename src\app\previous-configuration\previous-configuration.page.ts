import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { AlertController, Platform, MenuController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AppConstant } from 'src/constants/appConstants';
import { CONFIGURATION } from 'src/models/CONFIGURATION';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faInfoCircle, faBolt, faR, faGear, faSyncAlt, faSpinner, faTrashAlt, faEnvelope, faCloudUploadAlt, faBell, faCloudDownloadAlt, faFileLines, faCircleInfo } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-previous-configuration',
  templateUrl: './previous-configuration.page.html',
  styleUrls: ['./previous-configuration.page.scss'],
})
export class PreviousConfigurationPage implements OnInit {

  inspectionHeader: any;
  configurationList: any;
  tempArray: any[] = [];
  tempList: any[] = [];
  selectedUnit: any;
  //   = [{
  //     configName: 'Grommet',
  //     CreatedDate: '2020/04/02'
  //   },
  //   {
  //     configName: 'Strop',
  //     CreatedDate: '2020/04/02'
  //   },
  // ]

  constructor(
    public alertService: AlertService,
    public unviredSDK: UnviredCordovaSDK,
    public helpService: HelpService,
    public translate: TranslateService,
    public alertController: AlertController,
    public dataService: DataService,
    public router: Router,
    public platform: Platform,
    public device: Device,
    public menu: MenuController,
    public utilityService: UtilserviceService,
    private faIconLibrary: FaIconLibrary
  ) {
    this.faIconLibrary.addIcons(faInfoCircle, faBolt,faR, faGear,faSyncAlt, faSpinner, faTrashAlt, faEnvelope, faCloudUploadAlt, faBell, faCloudDownloadAlt, faFileLines, faCircleInfo);
    
    this.inspectionHeader = this.utilityService.getSelectedInspectionHeader();
    this.getPreviousConfigurations();
    this.dataService.measurementChanged = false;
    this.selectedUnit = this.dataService.selectedUom;
  }

  ngOnInit() {
  }

  async getPreviousConfigurations() {
    var temp = await this.alertService.present()
    var tempInsp = await this.unviredSDK.dbSelect('INSPECTION_HEADER', `INSPECTION_ID like '${this.inspectionHeader.INSPECTION_ID}'`)
    if (tempInsp.type == ResultType.success) {
      if (tempInsp.data.length > 0) {
        this.inspectionHeader = tempInsp.data[0];
      }
    }
    var query = ''
    if (this.platform.is("android") || this.device.platform == "browser") {
      query = `Select INSPECTION_HEADER.INSPECTION_ID, INSPECTION_HEADER.CONFIG_REFERENCE, INSPECTION_HEADER.LENGTH_UOM, CREATED_DATE, CONFIGURATION.CONFIG_NAME from INSPECTION_HEADER , CONFIGURATION  where INSPECTION_HEADER.INSPECTION_ID = CONFIGURATION.INSPECTION_ID AND INSPECTION_HEADER.CERTIFICATE_NUM like '${this.inspectionHeader.CERTIFICATE_NUM}' AND (( INSPECTION_HEADER.CONFIG_STATUS != 'Select Configuration'  AND INSPECTION_HEADER.CONFIG_STATUS != 'Add Configuration Measurements' ) OR ( INSPECTION_HEADER.CONFIG_STATUS isnull))  GROUP BY INSPECTION_HEADER.INSPECTION_ID`
    } else {
      query = `Select INSPECTION_HEADER.INSPECTION_ID, INSPECTION_HEADER.CONFIG_REFERENCE, INSPECTION_HEADER.LENGTH_UOM, INSPECTION_HEADER.CERTIFICATE_NUM, CREATED_DATE, CONFIGURATION.CONFIG_NAME from INSPECTION_HEADER , CONFIGURATION  where INSPECTION_HEADER.INSPECTION_ID = CONFIGURATION.INSPECTION_ID AND INSPECTION_HEADER.CERTIFICATE_NUM like '${this.inspectionHeader.CERTIFICATE_NUM}' AND ( INSPECTION_HEADER.CONFIG_STATUS not like '${AppConstant.ADD_MEASUREMENTS}' AND INSPECTION_HEADER.CONFIG_STATUS not like '${AppConstant.SELECT_CONFIGURATION}' ) GROUP BY INSPECTION_HEADER.INSPECTION_ID`
    }
    var previousconfig = await this.unviredSDK.dbExecuteStatement(query)
    this.alertService.dismiss();
    if (previousconfig.type == ResultType.success) {
      if (previousconfig.data.length > 0) {
        this.configurationList = previousconfig.data;
        for(var i = 0; i < this.configurationList.length; i++) {
          this.configurationList[i].CREATED_DATE = this.configurationList[i].CREATED_DATE.slice(0, 10).replace(/-/g, '/');
        }
      } else {
        this.configurationList = [];
      }
    } else {
      this.configurationList = [];
    }

  }

  openMenu() {
    this.menu.toggle('menu');
  }

  async confirmCopy(index) {
    console.log(this.configurationList[index])
    if (!this.helpService.helpMode) {
      var confAlert = await this.alertController.create({
        backdropDismiss: false,
        animated: true,
        mode: 'ios',
        keyboardClose: true,
        message: this.translate.instant("Photos will not be transferred, please review and associate new photos with each measurement."),
        buttons: [
          {
            text: this.translate.instant('Cancel'),
            role: 'cancel',
            cssClass: 'secondary',
            handler: () => {

            }
          }, {
            text: this.translate.instant('Continue'),
            handler: () => {
              this.insertInspectionToDb(index)
            }
          }
        ]
      });
      await confAlert.present();
    }
  }

  async insertInspectionToDb(index) {
    var temp = await this.alertService.present();
    this.inspectionHeader.CONFIG_STATUS = "" + AppConstant.ADD_MEASUREMENTS
    if (this.dataService.measurementChanged == true) {
      this.inspectionHeader.CONFIG_REFERENCE = UtilserviceService.guid();
    } else {
      this.inspectionHeader.CONFIG_REFERENCE = this.configurationList[index].CONFIG_REFERENCE
    }
    this.inspectionHeader.PRODUCT_CONFIG = this.configurationList[index].CONFIG_NAME
    // this.inspectionHeader.ORIGINAL_CONFIG = this.configurationList[index].CONFIG_NAME

    var result = await this.unviredSDK.dbUpdate("INSPECTION_HEADER", this.inspectionHeader, "INSPECTION_ID like '" + this.inspectionHeader.INSPECTION_ID + "'")
    if (result.type == ResultType.success) {
      var config = await this.unviredSDK.dbSelect("CONFIGURATION", `INSPECTION_ID LIKE '${this.configurationList[index].INSPECTION_ID}'`)
      if (config.type == ResultType.success) {
        if (config.data.length > 0) {
          for (var x = 0; x < config.data.length; x++) {
            var tempData = JSON.parse(config.data[x].DATA)
            tempData.externalImage = [];
            tempData.originalImages = {};
            var measurementItem = new CONFIGURATION();

            measurementItem.INSPECTION_ID = this.inspectionHeader.INSPECTION_ID;
            measurementItem.CONFIG_ID = UtilserviceService.guid(),
              tempData.id = measurementItem.CONFIG_ID
            measurementItem.CONFIG_TYPE_ID = config.data[x].CONFIG_TYPE_ID;
            measurementItem.FID = this.inspectionHeader.LID
            measurementItem.DATA = JSON.stringify(tempData);
            measurementItem.CONFIG_NAME = this.configurationList[index].CONFIG_NAME

            // selectedHeader = await this.createAttachmentItem(measurementJson, selectedHeader.data[0].LID)     
            var selectedHeader = await this.unviredSDK.dbInsertOrUpdate(AppConstant.TABLE_CONFIGURATION_ITEM, measurementItem, false)
            if (selectedHeader.type == ResultType.success) {

              this.utilityService.setSelectedInspectionHeader(this.inspectionHeader)
              this.dataService.setConfigLocationOptions(this.inspectionHeader);
              setTimeout(() => {
                this.router.navigate(['inspection-configuration']);
                this.alertService.dismiss();
              }, 2000);
            } else {
              this.unviredSDK.logError("new-configuration-list", "setConfigAndNavigate", "Error while fetching data " + JSON.stringify(selectedHeader))
              this.alertService.dismiss();
            }
          }
        } else {
          this.router.navigate(['inspection-configuration']);
          this.alertService.dismiss();
        }
      } else {
        this.unviredSDK.logError("new-configuration-list", "setConfigAndNavigate", "Error while fetching data " + JSON.stringify(config))
        this.alertService.dismiss();
      }
      // this.router.navigate(['inspection-configuration']);
    } else {
      this.unviredSDK.logError("new-configuration-list", "setConfigAndNavigate", "Error while fetching data " + JSON.stringify(result))
      this.alertService.dismiss();
    }

  }

  async expandedItem(index, item) {
    var tempIndex = index.toString();
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
      this.tempList.splice(ind, 1);
    } else {
      var result = await this.dataService.selectAllConfigMeasurements(item)
      if (result.type == ResultType.success) {
        var resultData = result.data;
        var tempResult = [];
        for (var i = 0; i < resultData.length; i++) {
          let baseLineObject = {}
          baseLineObject["DATA"] = JSON.parse(resultData[i].DATA)
          if (baseLineObject["DATA"].otherData) {
            baseLineObject["ending"] = baseLineObject["DATA"].otherData.ending
          }
          else {
            baseLineObject["ending"] = '-'
          }
          tempResult.push(baseLineObject)
        }
        var temp = {
          tempIndex: tempResult
        }

        this.tempList.push(tempResult)
      } else {
        this.tempList.push([])
      }
      this.tempArray.push(index);
    }
  }

  getArray(index) {
    var temp = this.tempArray.indexOf(index, 0).toString();
    return this.tempList[this.tempArray.indexOf(index, 0)]
  }

  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

}
