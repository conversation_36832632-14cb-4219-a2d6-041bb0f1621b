import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, NavController } from '@ionic/angular';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { AppConstant } from 'src/constants/appConstants';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faHome, faT, faTh } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from 'src/app/services/platform.service';

@Component({
  selector: 'app-guest-inspection-home',
  templateUrl: './guest-inspection-home.page.html',
  styleUrls: ['./guest-inspection-home.page.scss'],
})
export class GuestInspectionHomePage implements OnInit {

  platformId: string = this.platformService.getPlatformId();
  mode = true;
  inspectionCount: any = 0;

  constructor(private route: Router, public device: Device, private service: UtilserviceService, public menu: MenuController,
    public platformService: PlatformService,
    public helpService: HelpService,
    public dataService: DataService,
    public unviredCordovaSDK: UnviredCordovaSDK,
    private navCtrl: NavController,
    public faIconLibrary: FaIconLibrary) {
      this.getCountInspections()
      this.faIconLibrary.addIcons(faBars,faHome,faTh,faEnvelope)
  }

  async getCountInspections() {
    var res = await this.getCountInspectionsDB();
    if (res.type == ResultType.success) {
      var tempList = [];
      if (res.data.length > 0) {
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].INSPECTION_STATUS == AppConstant.IN_PROGRESS) {
            tempList.push(res.data[i])
          }
        }
        this.inspectionCount = tempList.length;
      }
    }
  }

  async getCountInspectionsDB() {
    return await this.unviredCordovaSDK.dbExecuteStatement("SELECT * from INSPECTION_HEADER where (INSPECTION_STATUS like '" + AppConstant.IN_PROGRESS + "' OR INSPECTION_STATUS like '" + AppConstant.READY + "') AND IS_DELETED not like 'X'  OR IS_DELETED is null")
  }



  ngOnInit() {
    
  }

  newInspection() {
    this.route.navigate(['guest-new-inspection']);
    //  this.service.setInspState('new');
    //  this.route.navigate(['inspection']);
  }

  openInsp() {
    this.service.setInspState('open');
    this.route.navigate(['guest-inspection']);
  }

  historyInsp() {
    this.service.setInspState('history');
    this.route.navigate(['guest-inspection']);
  }
  screenMode() {
    this.mode = !this.mode;
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  gotoHome() {
    this.navCtrl.navigateRoot('/guest-home');
  }

  gotoInspections() {
    this.navCtrl.navigateRoot('/guest-inspection-home');
  }

  gotoResources() {
    this.navCtrl.navigateRoot('/guest-resource');
  }

  gotoContact() {
    this.navCtrl.navigateRoot('/guest-contact');
  }

  //Exit help mode
  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  async ionViewWillEnter() {
    await this.getCountInspections()
  }
}
