import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { BaselinePageRoutingModule } from './baseline-routing.module';

import { BaselinePage } from './baseline.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TooltipsModule } from 'ionic4-tooltips';
import { GuestFooterComponent } from '../guest/guest-footer/guest-footer.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    FontAwesomeModule,
    BaselinePageRoutingModule,
    TooltipsModule,
    GuestFooterComponent
  ],
  declarations: [BaselinePage]
})
export class BaselinePageModule {}
