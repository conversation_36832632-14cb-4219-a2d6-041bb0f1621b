import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, Platform, NavController } from '@ionic/angular';
import { AppConstant } from 'src/constants/appConstants';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faListCheck, faGrip, faEnvelope, faCircleInfo } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-configuration-type',
  templateUrl: './configuration-type.page.html',
  styleUrls: ['./configuration-type.page.scss'],
})
export class ConfigurationTypePage implements OnInit {

  slideOpts = {
    initialSlide: 0,
    allowTouchMove: false
  };

  inspectionHeader: any;
  disablePreviousConfig: boolean;

  constructor(public helpService: HelpService,
    public menu: MenuController,
    public unviredSDK: UnviredCordovaSDK,
    public utilityService: UtilserviceService,
    public router: Router,
    public dataService: DataService,
    public platform: Platform,
    public navController: NavController,
    public device: Device,
    private faIconLibrary: FaIconLibrary,
    public alertService: AlertService) {
    this.faIconLibrary.addIcons(faBars, faListCheck, faGrip, faEnvelope, faCircleInfo)
    this.inspectionHeader = this.utilityService.getSelectedInspectionHeader();
    this.getPreviousConfigurations();
  }

  ngOnInit() {
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  navigateToNewConfigurationsPage() {
    this.router.navigate(['new-configuration-list']);
  }

  navigateToPreviousConfigurationsPage() {
    this.router.navigate(['previous-configuration']);
  }

  backButtonClick() {
    if (this.utilityService.getFromfromInspection()) {
      this.navController.navigateBack('/inspection')
    } else {
      this.navController.navigateBack('/inspection-home')
    }
  }

  async getPreviousConfigurations() {
    var temp = await this.alertService.present()
    var query = ''
    if (this.platform.is("android") || this.device.platform == 'browser') {
      query = `Select INSPECTION_HEADER.INSPECTION_ID, INSPECTION_HEADER.CONFIG_REFERENCE, INSPECTION_HEADER.LENGTH_UOM, INSPECTION_HEADER.CREATED_DATE, CONFIGURATION.CONFIG_NAME from INSPECTION_HEADER , CONFIGURATION  where INSPECTION_HEADER.INSPECTION_ID = CONFIGURATION.INSPECTION_ID AND INSPECTION_HEADER.CERTIFICATE_NUM like '${this.inspectionHeader.CERTIFICATE_NUM}' AND (( INSPECTION_HEADER.CONFIG_STATUS != 'Select Configuration'  AND INSPECTION_HEADER.CONFIG_STATUS != 'Add Configuration Measurements' ) OR ( INSPECTION_HEADER.CONFIG_STATUS isnull))  GROUP BY INSPECTION_HEADER.INSPECTION_ID`
    } else {
      query = `Select INSPECTION_HEADER.INSPECTION_ID, INSPECTION_HEADER.CONFIG_REFERENCE, INSPECTION_HEADER.LENGTH_UOM, INSPECTION_HEADER.CERTIFICATE_NUM, INSPECTION_HEADER.CREATED_DATE, CONFIGURATION.CONFIG_NAME from INSPECTION_HEADER , CONFIGURATION  where INSPECTION_HEADER.INSPECTION_ID = CONFIGURATION.INSPECTION_ID AND INSPECTION_HEADER.CERTIFICATE_NUM like '${this.inspectionHeader.CERTIFICATE_NUM}' AND ( INSPECTION_HEADER.CONFIG_STATUS not like '${AppConstant.ADD_MEASUREMENTS}' AND INSPECTION_HEADER.CONFIG_STATUS not like '${AppConstant.SELECT_CONFIGURATION}' ) GROUP BY INSPECTION_HEADER.INSPECTION_ID`
    }
    var previousconfig = await this.unviredSDK.dbExecuteStatement(query)
    this.alertService.dismiss();
    if (previousconfig.type == ResultType.success) {
      if (previousconfig.data.length > 0) {
        this.disablePreviousConfig = false;
      } else {
        this.disablePreviousConfig = true;
      }
    } else {
      this.disablePreviousConfig = true;
    }

  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

}