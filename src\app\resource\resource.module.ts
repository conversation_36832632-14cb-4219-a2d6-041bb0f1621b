import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ResourcePageRoutingModule } from './resource-routing.module';
import { ResourcePage } from './resource.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FooterComponent } from '../components/footer/footer.component';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ResourcePageRoutingModule,
    TranslateModule,
    FontAwesomeModule,
    FooterComponent
  ],
  declarations: [ResourcePage]
})
export class ResourcePageModule {}
