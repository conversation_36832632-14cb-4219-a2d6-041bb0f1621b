import { Injectable } from '@angular/core';
import { FileOpener } from '@awesome-cordova-plugins/file-opener/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { PlatformService } from './platform.service';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { platformId } from 'cordova';

declare var window: any;

@Injectable({
  providedIn: 'root'
})
export class FileOpenerService {

  private mimeTypes = {
    pdf: 'application/pdf',
    csv: 'text/csv',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    doc: 'application/msword',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    xls: 'application/vnd.ms-excel',
    txt: 'text/plain',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png'
  };

  constructor(
    private fileOpener: FileOpener,
    private file: File,
    private device: Device,
    private platformService: PlatformService,
    private unviredSdk: UnviredCordovaSDK
  ) { }

  async openFile(fileName: string, folder: string = 'pdf'): Promise<void> {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const mimeType = this.mimeTypes[extension] || 'application/octet-stream';
    const platformId = this.platformService.getPlatformId();
    
    if (this.device.platform === "Android") {
      await this.openFileAndroid(fileName, folder, mimeType);
    } else if (platformId === "electron") {
      await this.openFileElectron(fileName, folder);
    } else {
      await this.openFileOthers(fileName, mimeType);
    }
  }

  private async openFileAndroid(fileName: string, folder: string, mimeType: string): Promise<void> {
    const paths = [
      this.file.dataDirectory + `/${folder}/` + fileName,
      this.file.dataDirectory + '/' + fileName,
      this.file.externalRootDirectory + '/Download/Inspections/' + fileName,
    ];
    
    const attachmentPath = await this.getAttachmentPath();
    if (attachmentPath) {
      paths.unshift(attachmentPath + '/' + fileName);
    }
    
    for (const path of paths) {
      try {
        await this.fileOpener.open(path, mimeType);
        return;
      } catch (error) {
        continue;
      }
    }
  }

  private async openFileElectron(fileName: string, folder: string): Promise<void> {
    const paths = [
      this.file.dataDirectory + `${folder}/` + fileName,
      this.file.dataDirectory + fileName
    ];
    
    const attachmentPath = await this.getAttachmentPath();
    if (attachmentPath) {
      paths.unshift(attachmentPath + '/' + fileName);
    }
    
    for (const path of paths) {
      const normalizedPath = path.replace(/\\/g, '/').replace(/\/{2,}/g, '/');
      const finalPath = normalizedPath.replace(/\//g, '\\');
      try {
        await this.checkFileExists(path);
        if (window?.electronAPI?.openExternal) {
          window.electronAPI.openExternal(finalPath);
          return;
        }
      } catch (error) {
        continue;
      }
    }
  }

  private async openFileOthers(fileName: string, mimeType: string): Promise<void> {
    const attachmentPath = await this.getAttachmentPath();
    
    // Try attachment folder first
    if (attachmentPath) {
      try {
        const attachmentFilePath = "file://" + attachmentPath;
        const result = await this.file.copyFile(attachmentFilePath, fileName, this.file.dataDirectory, fileName);
        await this.fileOpener.open(result.nativeURL, mimeType);
        return;
      } catch (error) {
        // Continue to try other paths
      }
    }
    
    // Try assets folder
    const filePath = this.file.applicationDirectory + 'www/assets';
    try {
      const result = await this.file.copyFile(filePath, fileName, this.file.dataDirectory, fileName);
      await this.fileOpener.open(result.nativeURL, mimeType);
    } catch (error: any) {
      if (error.code === 12) {
        await this.fileOpener.open(this.file.dataDirectory + '/' + fileName, mimeType);
      }
      console.log(JSON.stringify(error));
    }
  }

  private async getAttachmentPath(): Promise<string | null> {
    try {
      const result = await this.unviredSdk.getAttachmentFolderPath();
      return (result && typeof result === 'string' && result.length > 0) ? result : null;
    } catch (error) {
      return null;
    }
  }

  private checkFileExists(path: string): Promise<void> {
    return new Promise((resolve, reject) => {
      window.resolveLocalFileSystemURL(path, resolve, reject);
    });
  }

  async openFileGuest(fileName: string): Promise<void> {
    const platformId = this.platformService.getPlatformId();
    const sourcePaths = [
      this.file.applicationDirectory + 'www/assets',
      this.file.applicationDirectory + 'app.asar/assets'
    ];
    
    for (const sourcePath of sourcePaths) {
      try {
        const result = await this.file.copyFile(sourcePath, fileName, this.file.dataDirectory, fileName);
        if (platformId === "electron") {
          window.electronAPI.openExternal(result.nativeURL);
        } else {
          await this.fileOpener.open(result.nativeURL, 'application/pdf');
        }
        return;
      } catch (error: any) {
        if (error.code === 12) {
          const filePath = this.file.dataDirectory + fileName;
          if (platformId === "electron") {
            window.electronAPI.openExternal(filePath);
          } else {
            await this.fileOpener.open(filePath, 'application/pdf');
          }
          return;
        }
        continue;
      }
    }
  }
}