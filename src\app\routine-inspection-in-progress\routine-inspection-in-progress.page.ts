import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType, RequestType, NotificationListenerType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, Platform, AlertController, ToastController, PopoverController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AppConstant } from 'src/constants/appConstants';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { LmdService } from '../services/lmd.service';
import { PresentToastService } from '../services/present-toast.service';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip, faMagnifyingGlass, faTrash, faCircleChevronLeft, faCircle, faUpload } from '@fortawesome/free-solid-svg-icons';
import { faCircle as farCircle } from '@fortawesome/free-regular-svg-icons';
@Component({
  selector: 'app-routine-inspection-in-progress',
  templateUrl: './routine-inspection-in-progress.page.html',
  styleUrls: ['./routine-inspection-in-progress.page.scss'],
})
export class RoutineInspectionInProgressPage implements OnInit {

  searchbar = false;
  selectedLmds = [];
  lmdsToDelete = [];
  searchIcon = true;
  title = true;
  inProgressData = [];
  initialData = [];
  view;
  completedArray = [];
  tempArray = [];
  dataBackupCompleted: any;
  dataBackupInProgress: any;
  syncButtonClicked: boolean = false;
  submissionStarted: boolean = false;
  constructor(private translate: TranslateService,
    public router: Router,
    public menu: MenuController,
    public dataService: DataService,
    public alertService: AlertService,
    public platform: Platform,
    public utilityService: UtilserviceService,
    public alertController: AlertController,
    public helpService: HelpService,
    public toastController: ToastController,
    public lmdService: LmdService,
    public device: Device,
    private popoverController: PopoverController,
    private toastService: PresentToastService,
    private unviredCordovaSDK: UnviredCordovaSDK,public faIconLibrary : FaIconLibrary) {

      this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip, faMagnifyingGlass, faTrash, faCircleChevronLeft, faCircle, faUpload, farCircle)
    }

  ngOnInit() {
    this.getInitialData();
  }

  async getInfoMsg(event: any, object: any) {
    var that = this
    if (object.SYNC_STATUS == '3') {
      event.preventDefault();
      event.stopPropagation();
      var result = await this.unviredCordovaSDK.getInfoMessages(AppConstant.TABLE_LMD_HEADER, object.LID)
      if (result.type == ResultType.success) {
        let title = "Info Message";

        if (this.platform.is("android")) {
          if (result.message != "" && result.message != "") {
            that.showAlert(title, result.message);
          } else {
            if (result.data.length > 0) {
              var message = "";
              for (var i = 0; i < result.data.length; i++) {
                message = message + result.data[i].MESSAGE + "<br>"
              }
              that.showAlert(title, message);
            } else {
              that.showAlert(title, "Error while submitting inspection");
            }
          }
        } else if (this.platform.is("ios")) {
          var message = "";
          for (var i = 0; i < result.data.length; i++) {
            message = message + result.data[i].MESSAGE + "<br>"
          }
          that.showAlert(title, message);
        } else {
          if (result.message !== null && result.message !== '') {
            var message = "";
            message = `${message}${result.message}`;
            that.showAlert(title, message.trim());
          } else {
            let resultData = result.data;
            if (resultData !== null && resultData.length > 0) {
              let messageArray: string[] = resultData.map((data: any) => {
                if (data.MESSAGE && data.MESSAGE.trim().length !== 0) {
                  return data.MESSAGE;
                }
              });
              if (messageArray.length > 0) {
                let messageToDisplay: string = messageArray.join(' ').trim();
                that.showAlert(title, messageToDisplay);
              }
            }
          }
        }
      }
      else {
        that.showAlert("Error", JSON.stringify(result));
      }

    } else if (object.SYNC_STATUS == '2') {
      that.showAlert("Info Message", "Message submitted waiting for response")
    } else if (object.SYNC_STATUS == '1') {
      that.showAlert("Info Message", "Message queued for submission")
    } else if (object.SYNC_STATUS == '0') {
      if (object.INSPECTION_STATUS == AppConstant.READY) {
        that.showAlert("Info Message", "Inspection ready for submission")
      }
    }
  }


  toggleSearch() {
    this.searchbar = !this.searchbar;
    this.searchIcon = !this.searchIcon;
    this.title = !this.title;
  }
  cancelHistoricalItem(ev) {
    const val = ev.target.value;
    if (val === '') {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
    } else {
      this.searchbar = !this.searchbar;
      this.searchIcon = !this.searchIcon;
      this.title = !this.title;
      this.initializeItems();
    }
  }
  searchHistoricalItem(ev) {
    // this.getInitialData();
    const val = ev.target.value;
    if (val && val.trim() !== '') {
      this.inProgressData = this.initialData.filter((item) => {
        return (
          (item.LID && item.LID.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LMD_TYPE && item.LMD_TYPE.toString().toLowerCase().indexOf(val.toLowerCase()) > -1) ||
          (item.LMD_DATA.certNo && item.LMD_DATA.certNo.toString().toLowerCase().indexOf(val.toLowerCase()) > -1)
        );
      });
    }
    else {
      this.getInitialData();
    }
  }

  onChecked(event: any) {
    const index = this.selectedLmds.indexOf(event);
    if (index > -1) {
      console.log(index);
      this.selectedLmds.splice(index, 1);
    } else {
      this.selectedLmds.push(event);
    }
    console.log(this.selectedLmds);
  }

  initializeItems() {
    this.inProgressData = this.initialData;
  }

  icon(index) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }
  async presentPopover(type: string) {
    // const popover = await this.popoverController.create({
    //   component: LineTrackerPopoverPage,
    //   componentProps: { page: this, modal: type, filterData: this.initialData },
    //   event,
    //   showBackdrop: true,
    //   animated: true
    // });
    // await popover.present();
    // popover.onDidDismiss().then((data) => {
    //   const filterValue = data.data;
    //   this.view = data.data;
    //   this.applyFilters(this.view);
    //   console.log('filter value' + filterValue);
    // });
  }

  applyFilters(filterValue) {
    if (filterValue === 'showAll') {
      this.initializeItems();
    } else {
      this.inProgressData = this.initialData.filter((item) => {
        return (
          (item.LID && item.LID.toString().toLowerCase().indexOf(filterValue.toLowerCase()) > -1) ||
          (item.LMD_DATA.certNo && item.LMD_DATA.certNo.toString().toLowerCase().indexOf(filterValue.toLowerCase()) > -1) ||
          (item.LMD_DATA.totalWorkingHour && item.LMD_DATA.totalWorkingHour.toString().indexOf(filterValue.toLowerCase()) > -1)
        );
      });
    }
  }
  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }
  async getInitialData() {
    if (this.submissionStarted == false) {
      const result = await this.unviredCordovaSDK.dbSelect('LMD_HEADER', "(LMD_STATUS = 'InProgress' OR LMD_STATUS = 'In Progress') AND LMD_TYPE == 'RoutineInspection'");
      if (result.type === ResultType.success) {
        this.initialData = result.data;
        console.log('db data' + JSON.stringify(this.initialData));
        this.inProgressData = [];
        const resultData = result.data;

        // tslint:disable-next-line:prefer-for-of
        for (let i = 0; i < resultData.length; i++) {
          const value = result.data[i];
          const dataToFormat = value.LMD_DATA;
          const formatedLmdData = dataToFormat.replace(/\"/g, '"');
          const newFormatedData = JSON.parse(formatedLmdData);
          value.LMD_DATA = newFormatedData;
          this.inProgressData.unshift(value);
        }
      } else {
        this.unviredCordovaSDK.logError("in-progress-lmd", "getInitialData", "Error While Fetching data " + JSON.stringify(result))
        this.showAlert("Error", "Error While Fetching data " + result.error)
      }
    }
  }
  async deleteLmd() {
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < this.selectedLmds.length; i++) {
      const lmdToDelete = this.selectedLmds[i];
      const formatLmd = '\'' + lmdToDelete + '\'';
      this.lmdsToDelete.push(formatLmd);
    }
    const deleteAlert = await this.alertController.create({
      header: 'Are you sure?',
      message: 'You will loose the selected LMDs permanently.',
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (val) => {
            // this.getInitialData();
            // this.lmdsToDelete = [];
            // this.selectedLmds = [];
            // this.toastService.presentToast('Deletion cancelled');
          }
        }, {
          text: 'Yes',
          handler: async () => {
            for (var ind = 0; ind < this.selectedLmds.length; ind++) {
              await this.unviredCordovaSDK.deleteOutBoxEntry(this.selectedLmds[ind]);
            }
            const result = await this.unviredCordovaSDK.dbDelete('LMD_HEADER', `LID IN (${this.lmdsToDelete})`);
            if (result.type === ResultType.success) {
              const aaresult = await this.unviredCordovaSDK.dbDelete('ASSET_ACTIVITY_HEADER', `LID IN (${this.lmdsToDelete})`);
              if (aaresult.type === ResultType.success) {
                this.getInitialData();
                this.toastService.presentToast('Selected LMDs are deleted');
                this.lmdsToDelete = [];
                this.selectedLmds = [];
              } else {
                this.getInitialData();
                this.toastService.presentToast('Selected LMDs are deleted');
                this.lmdsToDelete = [];
                this.selectedLmds = [];
              }
            } else {
              this.getInitialData();
              this.lmdsToDelete = [];
              this.selectedLmds = [];
            }
          }
        }
      ]
    });
    await deleteAlert.present();
  }



  async submitLmd() {
    const submitAlert = await this.alertController.create({
      header: 'Are you sure?',
      message: 'You want to submit the selected LMDs.',
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            // this.getInitialData();
            // this.lmdsToDelete = [];
            // this.selectedLmds = [];
            // this.toastService.presentToast('Submission cancelled');
          }
        }, {
          text: 'Yes',
          handler: async () => {
            // tslint:disable-next-line:prefer-for-of
            this.alertService.present();
            this.submissionStarted = true;
            for (let i = 0; i < this.selectedLmds.length; i++) {
              const currentlmd = this.selectedLmds[i];
              const lmdHeader = await this.getLmdValuesById(currentlmd);
              console.log('lid' + lmdHeader[0].LID);
              let inputObject = { LMD_HEADER: lmdHeader[0] };
              console.log('input obj' + JSON.stringify(inputObject));
              if (!this.syncButtonClicked) {
                console.log('inside async background calls');
                this.syncButtonClicked = true;
                console.log('other lmd submitting');
                inputObject.LMD_HEADER.LMD_DATA = JSON.stringify(inputObject.LMD_HEADER.LMD_DATA);
                // tslint:disable-next-line: max-line-length
                const result = await this.unviredCordovaSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, "LMD", lmdHeader[0].LID, false);
                console.log(JSON.stringify(result))
                this.syncButtonClicked = false;
                if (i == this.selectedLmds.length - 1) {
                  this.submissionStarted = false;
                }
              }
            }
            if (this.submissionStarted == false) {
              setTimeout(() => {
                this.dataService.refreshData();
                this.unviredCordovaSDK.getMessages();
                this.getInitialData();
                this.presentToast();
                this.lmdsToDelete = [];
                this.selectedLmds = [];
                this.alertService.dismiss();
              }, 2000);
            }
          }
        }
      ]
    });
    await submitAlert.present();
  }

  async presentToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("Submitted LMDs"),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  getLmdValuesById(lmdId) {
    const lmdData = this.initialData.filter((item) => {
      return (
        (item.LID && item.LID.toString().toLowerCase().indexOf(lmdId.toLowerCase()) > -1) ||
        (item.LMD_DATA.cert && item.LMD_DATA.cert.toString().toLowerCase().indexOf(lmdId.toLowerCase()) > -1) ||
        (item.LMD_DATA.totalWorkingHour && item.LMD_DATA.totalWorkingHour.toString().indexOf(lmdId.toLowerCase()) > -1)
      );
    });
    return lmdData;
  }

  async ionViewWillEnter() {
    setTimeout(() => {      
      this.unviredCordovaSDK.getMessages();
    }, 5000);
    this.unviredCordovaSDK.registerNotifListener().subscribe((result) => {
      // this.alertService.dismiss();
      this.unviredCordovaSDK.logInfo('HOME', 'registerNotifListener', JSON.stringify(result));
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          this.syncButtonClicked = false;
          this.getInitialData();
          break;
        case NotificationListenerType.dataChanged:
          this.syncButtonClicked = false;
          this.getInitialData();
          break;
        case NotificationListenerType.dataSend:
          // alert('data send');
          this.getInitialData();
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.appReset:
          this.syncButtonClicked = false;
          this.getInitialData();
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.getInitialData();
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.getInitialData();
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          this.getInitialData();
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.syncButtonClicked = false;
          this.getInitialData();
          break;
        case NotificationListenerType.infoMessage:
          this.syncButtonClicked = false;
          this.handleInfoMessage(result);
          this.getInitialData();
          break;
        case NotificationListenerType.serverError:
          this.syncButtonClicked = false;
          this.getInitialData();
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.getInitialData();
          this.syncButtonClicked = false;
          break;
        default:
      }
    });
    this.getInitialData()
  }

  async uploadItem(item) {
    // var tempItem = JSON.parse(JSON.stringify(item))
    // tempItem.LMD_DATA = JSON.stringify(tempItem.LMD_DATA)
    // console.log("tempItem",tempItem)
    var temp = await this.unviredCordovaSDK.dbSelect("LMD_HEADER",`LMD_ID = '${item.LMD_ID}'`);
    if(temp.type==ResultType.success) {
      if(temp.data.length>0) {
        let lmdObj= temp.data[0];
        lmdObj.OBJECT_STATUS = 1;
        lmdObj.SYNC_STATUS = 1;
        let result =await this.unviredCordovaSDK.dbUpdate("LMD_HEADER", lmdObj, `LMD_ID='${lmdObj.LMD_ID}'`);
        if(result.type==ResultType.success) {
          var temp = await this.dataService.getSelectedLMDHeaderFromDb(lmdObj.LMD_ID)
          if(temp.type == ResultType.success) {
            if(temp.data.length > 0) {
              var res = await this.submitLMD(temp.data[0])
              if(res.type == ResultType.error) {
                this.showAlert("Error", result.message);
                this.syncButtonClicked = false;
              } else {
                this.alertService.presentEditedToast()
                this.syncButtonClicked = false;
              }
              this.alertService.dismiss()
              this.dataService.refreshData();
            }
          } else {
            console.log("error:",temp.error)
            this.unviredCordovaSDK.logError("ROUTINE_INSPECTION_IN_PROGRESS", "uploadItem", "Error reading LMD_HEADER after updating OBJECT_STATUS and SYNC_STATUS: " + temp.error)
          }
        } else {
          this.unviredCordovaSDK.logError("ROUTINE_INSPECTION_IN_PROGRESS", "uploadItem", "Error: " + temp.error)
          this.alertService.showAlert("", this.translate.instant("Failed to Update DB, Please contact Samson"));
        }
      } else {
      this.unviredCordovaSDK.logError("ROUTINE_INSPECTION_IN_PROGRESS", "uploadItem", "NO rows found in LMD_HEADER for LMD_ID: " + item.LMD_ID)
      }
    } else {
      this.unviredCordovaSDK.logError("ROUTINE_INSPECTION_IN_PROGRESS", "uploadItem", "Error: " + temp.error)
      this.alertService.showAlert("", this.translate.instant("Failed to read LMD from DB, Please contact Samson"));
    }
  }

  async submitLMD(item) {
    await this.alertService.present();
    let inputObject = {
      "LMD_HEADER": item
    }
    console.log("LID" + item.LID)
    let sendLmdToServer: any = {};
    let categoryLmd: any = {};
    if (this.device.platform == "browser") {
      this.unviredCordovaSDK.dbSaveWebData();
      categoryLmd['LMD_HEADER'] = item;
      sendLmdToServer['LMD'] = [categoryLmd];
      return this.unviredCordovaSDK.syncForeground(RequestType.RQST, "", sendLmdToServer, AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, true)
    } else {
      return this.unviredCordovaSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, "LMD", item.LID, false)
    }
  }

  navigateToDetailsPage(item) {
    this.lmdService.setIsFromInProgress(true)
    this.lmdService.setIsFromCompleted(false);
    this.utilityService.setLMDEditMode(true);
    if (item.SYNC_STATUS == 0 || item.SYNC_STATUS == 3) {
      this.lmdService.setReadOnlyMode(false)
    } else {
      this.lmdService.setReadOnlyMode(true)
    }
    this.lmdService.setSelectedAsset(item)
    this.lmdService.setSelectedAssetActivity(item)
    this.lmdService.setSelectedActivityFromList(false)
    this.utilityService.setSelectedLMD(item);
    switch (item.LMD_NAME) {
      case 'Cropping':
        this.router.navigate(['cropping-lmd']);
        break;
      case 'Repair':
        this.router.navigate(['repair-lmd']);
        break;
      case 'EndForEnd':
        this.router.navigate(['end-for-end-lmd']);
        break;
      case 'EquipmentInsp':
        this.router.navigate(['equipment-insp-lmd']);
        break;
      case 'Rotation':
        this.router.navigate(['rotation-lmd']);
        break;
      case 'InstallLine':
        this.router.navigate(['install-line-lmd']);
        break;
      case 'RequestNewLine':
        this.router.navigate(['request-new-line-lmd']);
        break;
      case 'AssetActivity':
        if (item.LMD_DATA.isGeneral == true) {
          this.router.navigate(['general-line-usage-details']);
        } else {
          this.router.navigate(['asset-activity-details']);
        }
        break;
    }

  }

  public handleInfoMessage(result) {

    this.unviredCordovaSDK.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });

    this.showAlert(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }

  async showAlert(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
    alert.onDidDismiss().then(async () => {
      // await this.alertService.present() 
      setTimeout(() => {
        this.unviredCordovaSDK.getMessages();
        // this.alertService.dismiss()
      }, 1000);

    });
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  gotoInspections() {
    this.dataService.navigateToInspection()
  }

  gotoResources() {
    this.dataService.navigateToResources()
  }

  gotoContact() {
    this.dataService.navigateToContact()
  }

  async inProgressItemTrash(index, item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Lmd'),
      message: '<strong>' + this.translate.instant('You want to delete this Inspection') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Ok'),
          handler: async () => {
            this.icon(index);
            var temp = this.selectedLmds.indexOf(item.LID);
            if (temp > -1) {
              this.selectedLmds.splice(temp, 1);
            }
            var result = await this.markAsDeleted(item);
            this.inProgressData.splice(index, 1);
            this.getInitialData();
          }
        }
      ]
    });
    await alert.present();
  }

  async markAsDeleted(item) {
    var whereClause = "LMD_ID like '" + item.LMD_ID + "'"
    // await this.unviredCordovaSDK.deleteOutBoxEntry(item.LID)
    return await this.unviredCordovaSDK.dbDelete("LMD_HEADER", whereClause)
  }

  doRefresh(event) {
    this.unviredCordovaSDK.getMessages();
    setTimeout(() => {
      event.target.complete();
    }, 3000);
  }

}
