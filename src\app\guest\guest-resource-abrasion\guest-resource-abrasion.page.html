<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;"></ion-back-button>
    </ion-buttons>
    <ion-title>Abrasion</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:10px">
    <h1 style='color: #0057b3'>Understanding Abrasion</h1>
    <p>There are two types of abrasion: internal abrasion caused by the relative movement of internal and external
      yarns, and external abrasion caused by contact with external surfaces. An unprotected rope moving over a rough
      surface, such as a poorly maintained chock can be subjected to both. Upon inspection, it’s easy to see that the
      external strands are abraded by a rough surface: often, fibers can be left behind on the surface that caused the
      abrasion, and the surface of the rope readily shows abraded yarns.
      <figure style="float:left; display: table; margin: 15px !important ">
        <img src="./assets/img/img1.png">
        <figcaption style="color:#0057b3; display: table-caption; caption-side: bottom;">Compare your surface yarns with
          internal yarns</figcaption>
      </figure>
    </p>
    <p>
      <figure style="float:left; display: table; margin: 15px !important ">
        <img src="./assets/img/img2.png">
        <figcaption style="color:#0057b3; display: table-caption; caption-side: bottom;">Inspect for internal abrasion
        </figcaption>
      </figure>The same rough surfaces can also cause internal abrasion due to the movement of the internal strands
      relative to each other. When the rope’s surface strands pass over rough surfaces, they are slowed relative to the
      strands next to them, causing friction. Heat is created from friction – and heat is among the biggest enemies of
      synthetic ropes.
    </p><br>
    <p style="font-weight:bolder !important">This information is based on testing performed by Samson and is provided as
      a guideline. If you are unsure of the condition of your rope, please contact your Samson representative</p>
  </div>
</ion-content>
<div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-guest-footer (Menu)="openMenu()" (Home)="gotoHome()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-guest-footer>