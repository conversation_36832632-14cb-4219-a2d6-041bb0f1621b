import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip } from '@fortawesome/free-solid-svg-icons';


@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  standalone: true,
  imports: [IonicModule, FontAwesomeModule]
})
export class FooterComponent  implements OnInit {

  constructor(
    private faIconLibrary: FaIconLibrary,
  ) {
    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip) 
  }

  // @Output() footerAction=new EventEmitter<string>();

  ngOnInit() {}

  @Output() Menu = new EventEmitter<void>();
  @Output() LineTracker = new EventEmitter<void>();
  @Output() Inspections = new EventEmitter<void>();
  @Output() Resources = new EventEmitter<void>();
  @Output() Contact = new EventEmitter<void>();
}
