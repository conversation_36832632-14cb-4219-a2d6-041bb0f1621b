
<ion-header>
  <ion-toolbar>
    <ion-title>{{'Observations' | translate}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
      <div (click)="backButtonClick()"
        style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;background-color: transparent;z-index: 1000;">
      </div>
    </ion-buttons>

    <ion-buttons slot="end">
      <ion-button (click)="editInspection()" color="light" fill="clear"
        *ngIf="inspectionHeader.INSPECTION_STATUS==statusReopened && !dissableAdd">
        <ion-icon name="create" color="primary"></ion-icon>
      </ion-button>
      <ion-button color="primary" *ngIf='!dissableAdd' (click)="helpService.helpMode ? test() : presentAlertConfirm()">
        <span style="text-transform: none !important; font-size: medium !important">{{'Complete' | translate}}</span>
      </ion-button>
      <ion-button color="primary" *ngIf='dissableAdd && this.dataService.showEmailFlag == true' (click)="helpService.helpMode ? test() : presentEmailAlertConfirm('email')">
        <span style="text-transform: none !important; font-size: medium !important">{{'Email' | translate}}</span>
      </ion-button>
      <!-- <ion-button color="primary" *ngIf='dissableAdd && this.dataService.showEmailFlag == true'
        (click)="helpService.helpMode ? test() : presentEmailAlertConfirm('email')">
        <span style="text-transform: none !important; font-size: medium !important">{{'Email' | translate}}</span>
      </ion-button>
      <ion-button color="primary" class="utilityButtons" *ngIf='(!dissableAdd) && isUtility'
        (click)="helpService.helpMode ? test() : presentAlertLmd()">
        <span style="text-transform: none !important; font-size: medium !important">{{'Repair' | translate}}</span>
      </ion-button>
      <ion-button color="primary" class="utilityButtons" *ngIf='!dissableAdd'
        (click)="helpService.helpMode ? test() : presentAlertConfirm()">
        <span style="text-transform: none !important; font-size: medium !important">{{'Complete' | translate}}</span>
      </ion-button>
      <ion-button color="primary" class="nonUtilityButtons" *ngIf='(!dissableAdd) && (!isUtility)'
        (click)="helpService.helpMode ? test() : presentAlertConfirm()">
        <span style="text-transform: none !important; font-size: medium !important">{{'Complete' | translate}}</span>
      </ion-button>
      <ion-button matTooltip="Add Repair" class="utilityButtonsDropDown" *ngIf='(!dissableAdd) && isUtility'
        (click)="helpService.helpMode ? test() : presentAlertLmd()"
        style="background-color: var(--ion-color-primary);border-radius: 5px;">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"
          style="background-color: var(--ion-color-primary); width: 19px; height: 19px;">
          <image href="./assets/img/Constructions/other_WHite.png" x="30" y="30" height="452" width="452" />
        </svg>
      </ion-button>
      <ion-button color="primary" class="utilityButtonsDropDown" *ngIf='(!dissableAdd) && isUtility'
        (click)="helpService.helpMode ? test() : presentAlertConfirm()" matTooltip="Complete Inspection"
        style="background-color: var(--ion-color-primary);border-radius: 5px;">
        <fa-icon icon="check" style="font-size:18px;color: var(--ion-color-primary-contrast)"></fa-icon>
      </ion-button> -->
    </ion-buttons>
    
    <!-- <ion-buttons color="primary" slot="end" (click)="helpService.switchMode()" class="help-button-style">
      <span *ngIf='helpService.helpMode'
        style="padding-right: 5px !important; font-size: medium !important">{{'Exit Help' | translate}}</span>
        <fa-icon class="icon-style-help"  *ngIf='helpService.helpMode' icon="times"></fa-icon>
      <fa-icon class="icon-style-help"  *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
    </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content style="--padding-bottom:40px !important">

  <div style="padding:10px 10px 0px 10px" *ngIf='!dissableAdd'>
    <div class="card" style="background: bisque; display: block;" *ngIf="showPurchaseOption == true">
      <div style="display: block;">
        <div style="display: inline-block; padding: 10px 10px 0px 10px;">
          <ion-checkbox slot="end" (ionChange)="resetPermission($event);" style="display: inline-block;" (click)="stopEvent()" [checked]="checkIfSelected()"></ion-checkbox>
        </div>
        <p style="display: inline-block;margin: 8px 10px 0px 0px;vertical-align: top;"> Request For Enhanced Support</p>
        <div style="float:right; padding-top: 2px"  (click)="showAdvancedAssistenceInfoAlert()">
          <fa-icon style="padding-right: 23px; margin-top: 10px; font-size: 21px;" *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
        </div>
      </div>
      <!-- <p style="display: inline-block;"> View your inspection report in the Completed section of the app. For Enhanced Review from Samson, email the report to yourself and click the link at the bottom of the report.</p> -->
      <!-- <div style="padding-top:5px;text-align: center;" *ngIf='dissableAdd && this.dataService.showEmailFlag == true'>
        <ion-button color="mediumBlack" (click)="showModal()">Details</ion-button>
      </div> -->
    </div>

    <!-- <div class="card" style="background: bisque; display: block; margin-top: 5px; padding: 0px 5px 0px 10px;" *ngIf="showPurchaseOption == true && isChecked == true">
      <p >Thank you for requesting an Enhanced Review of your inspection findings from Samson’s technical staff. This review will include feedback from Samson’s technical staff to help you determine cause and remedy to the damage captured in the inspection. You will now be taken to Samson’s payment gateway to transact this Enhanced Review request. Once the transaction is complete, Samson will respond within 2 business days with the results of our technical analysis and to deliver your report.</p>
 
      <p>If you are an Icaria Partner member and have received this message, please contact your Samson representative as you require no payment for this service.</p>
    </div> -->
  </div>
  <div style="padding:10px 10px 0px 10px">
    <div class="card" style="padding:10px 10px 0px 10px;">
      <p *ngIf="productName != ''" style="font-size:16px"> {{'Product Name' | translate}} : <span
          style="font-size:15px;color:rgb(133, 128, 128)">{{productName}}</span></p>
      <p style="font-size:16px"> {{' Cert Number' | translate}}&nbsp; &nbsp;: <span
          style="font-size:14px;color:rgb(133, 127, 127)">{{certNumber}}</span></p>
      <p style="font-size:16px"> {{' Starting End' | translate}}&nbsp; &nbsp;: <span
          style="font-size:14px;color:rgb(133, 127, 127)">{{startPoint}}</span></p>
      <p style="font-size:16px"> {{' Configuration' | translate}}&nbsp; &nbsp;: <span
          style="font-size:14px;color:rgb(133, 127, 127)">{{productConfig}}</span>
        <ion-button size="small" fill="solid" color="primary" *ngIf="isLoggedInUserEmployee && dataService.getConfigurationOption() == 'yes'" style="margin:0 0 9px 7px;height:25px;" (click)="editConfiguration()" title="Edit Configuration">
          <!-- <ion-icon slot="icon-only" name="pencil-outline" color="light" *ngIf="!dissableAdd"></ion-icon> -->
          <fa-icon icon="pencil" style="color:#ffffff;" *ngIf="!dissableAdd"></fa-icon>
          <fa-icon icon="eye" style="color:#ffffff;" *ngIf="dissableAdd"></fa-icon>
        </ion-button>
      </p>
    </div>
  </div>
  <!-- <ion-grid style="padding-top:10px;padding-left:15px" *ngIf="helpService.helpMode">
    <ion-row>
      <ion-col tooltip="{{'Tap to see current external'|translate}}" positionV="bottom" [event]="helpService.tooltipEvent"
      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
          <span style="font-size:16px">{{'Current External' | translate}}</span>&nbsp;
          <div style="float:right;padding-left:7px;">
          <ion-button size="small" mode="md" (click)="helpService.helpMode ? test() : currentExternal()">{{this.external}}
          </ion-button>
          </div>
          </ion-col>
      </ion-row>
      <ion-row>
      <ion-col tooltip="{{'Tap to see current internal'|translate}}" positionV="bottom" [event]="helpService.tooltipEvent"
      [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        
            <span style="font-size:16px">{{'Current Internal' | translate}}</span>&nbsp;
          <div style="float:right;padding-left:7px;">
            <ion-button size="small" mode="md" (click)="helpService.helpMode ? test() : currentInternal()">{{this.internal}}
            </ion-button>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid> -->

  <!-- <ion-grid style="padding-top:10px;padding-left:15px" *ngIf="!helpService.helpMode">
    <ion-row>
      <ion-col>
          <span style="font-size:16px">{{'Current External' | translate}}</span>&nbsp;
          <div style="float:right;padding-left:7px;">
          <ion-button size="small" mode="md" (click)="helpService.helpMode ? test() : currentExternal()">{{this.external}}
          </ion-button>
          </div>
          </ion-col>
      </ion-row>
      <ion-row>
      <ion-col>
        
            <span style="font-size:16px">{{'Current Internal' | translate}}</span>&nbsp;
          <div style="float:right;padding-left:7px;">
            <ion-button size="small" mode="md" (click)="helpService.helpMode ? test() : currentInternal()">{{this.internal}}
            </ion-button>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid> -->
  <p style="font-size:16px;padding-top:10px;padding-left:17px" *ngIf="!isLoggedInUserEmployee">{{'Recorded Observations' | translate}} :</p>
  <div *ngIf="emptyListInprogress && !isLoggedInUserEmployee" style="padding:10px 15px 0px 15px">
    <div class="alert alert-info" role="alert" style="text-align:center">
      {{'Hey, No observations recorded yet.Tap on the + button to add one now.' | translate}}
    </div>
  </div>

  <div *ngIf="emptyListCompleted && !isLoggedInUserEmployee" style="padding:10px 15px 0px 15px">
    <div class="alert alert-info" role="alert" style="text-align:center">
      {{'Hey, No observations recorded.' | translate}}
    </div>
  </div>
  <ion-list lines="none" *ngIf="!isLoggedInUserEmployee">
    <div *ngFor="let item of baselineArray ; let i = index">
      <ion-item-sliding
        [hidden]="(item.DATA.start !== null && item.DATA.end === null && item.DATA.type === 'External') || (item.DATA.start !== null && item.DATA.end === null && item.DATA.type === 'Internal')">
        <ion-item *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal') && item.DATA.start !== null">
          <ion-label style="font-size:16px;" *ngIf="(item.DATA.type == 'Twist')" class="label-display">
            <span>{{item.DATA.start}} - {{item.ending}} <span>{{selectedUnit}}</span> {{item.DATA.type}} <span
                *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal')"> Abrasion</span> </span>
                <span class="created_date_text" *ngIf="(item.DATA.EventDate != '' && item.DATA.EventDate != undefined && item.DATA.EventDate != null)">Reported: {{item.DATA.EventDate}} </span>
          </ion-label>
          <ion-label style="font-size:16px;" *ngIf="!(item.DATA.type == 'Twist')" class="label-display">
            <span
              *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal') && item.DATA.start !== null">{{item.DATA.start}}
              - {{item.DATA.end}} {{selectedUnit}} {{item.DATA.type}} <span
                *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal')"> Abrasion</span></span>
            <span *ngIf="item.DATA.type !== 'External' && item.DATA.type !== 'Internal'">{{item.DATA.start}} -
              {{item.ending}} {{selectedUnit}} {{item.DATA.type}} <span
                *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal')"> Abrasion</span></span>
                <span class="created_date_text" *ngIf="(item.DATA.EventDate != '' && item.DATA.EventDate != undefined && item.DATA.EventDate != null)">Reported: {{item.DATA.EventDate}} </span>
          </ion-label>
          <ion-button
            *ngIf="((item.DATA.type == 'External' || item.DATA.type == 'Internal') && item.DATA.start !== null)  || (item.DATA.type !== 'External' && item.DATA.type !== 'Internal')"
            (click)="review(item.DATA)" fill="outline" style="text-transform: none;" color="darkMedium" slot="end"
            mode="md" [disabled]='helpService.helpMode'>
            &nbsp; {{'Review' | translate}} &nbsp;
          </ion-button>
        </ion-item>
        <ion-item *ngIf="item.DATA.type !== 'External' && item.DATA.type !== 'Internal'">
          <ion-label style="font-size:16px;" *ngIf="(item.DATA.type == 'Twist')" class="label-display">
            <span>{{item.DATA.start}} - {{item.ending}} <span>{{selectedUnit}}</span> {{item.DATA.type}} <span
                *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal')"> Abrasion</span></span>
                <span class="created_date_text" *ngIf="(item.DATA.EventDate != '' && item.DATA.EventDate != undefined && item.DATA.EventDate != null)">Reported: {{item.DATA.EventDate}} </span>
          </ion-label>
          <ion-label style="font-size:16px;" *ngIf="!(item.DATA.type == 'Twist')" class="label-display">
            <span
              *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal') && item.DATA.start !== null">{{item.DATA.start}}
              - {{item.DATA.end}} {{selectedUnit}} {{item.DATA.type}} <span
                *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal')"> Abrasion</span> <span
                *ngIf="(item.DATA.type == 'Cuts')"> Damage</span></span>
            <span *ngIf="item.DATA.type !== 'External' && item.DATA.type !== 'Internal'">{{item.DATA.start}} -
              {{item.ending}} {{selectedUnit}} <span *ngIf="(item.DATA.type == 'Parting')">Line </span> <span
                *ngIf="(item.DATA.type != 'Kinking')">{{item.DATA.type}}</span>
              <span *ngIf="(item.DATA.type == 'External' || item.DATA.type == 'Internal')"> Abrasion</span>
              <span *ngIf="(item.DATA.type == 'Cuts')"> Damage</span>
              <span *ngIf="(item.DATA.type == 'Pulled')"> Yarns/Strands</span>
              <span *ngIf="(item.DATA.type == 'Kinking')"> Kinked Yarns</span>
            </span>
            <span class="created_date_text" *ngIf="(item.DATA.EventDate != '' && item.DATA.EventDate != undefined && item.DATA.EventDate != null)">Reported: {{item.DATA.EventDate}} </span>
          </ion-label>
          <ion-button
            *ngIf="((item.DATA.type == 'External' || item.DATA.type == 'Internal') && item.DATA.start !== null)  || (item.DATA.type !== 'External' && item.DATA.type !== 'Internal')"
            (click)="review(item.DATA)" fill="outline" style="text-transform: none;" color="darkMedium" slot="end"
            mode="md" [disabled]='helpService.helpMode'>
            &nbsp; {{'Review' | translate}} &nbsp;
          </ion-button>
        </ion-item>
        <ion-item-options side="end" *ngIf='!dissableAdd'>
          <ion-item-option color="light" (click)="resetConfirm(item)">
            <ion-icon slot="icon-only" mode="md" color="danger" name="trash" style="font-size:22px;"></ion-icon>
          </ion-item-option>
        </ion-item-options>
      </ion-item-sliding>
    </div>
  </ion-list>

  <!-- & observations grid section for employee login - START -->
  <div *ngIf="isLoggedInUserEmployee" style="box-shadow: #00000029 0px 1px 4px;color:black;border: 1px solid #b8b8bd;margin:8px 10px 0 10px;border-radius: 10px;">
    <div class="flex-container">
      <div class="flex-item flex-grow">
        <h5>Sample Observations :</h5>
      </div>
      <div class="flex-item">
        <ion-button fill="clear" color="primary" (click)="toggleSample()">
          <ion-icon slot="icon-only" slot="end" [name]="checkSampleSectionIsOpen() ? 'chevron-up-outline' :'chevron-down-outline'"
          style="color:rgb(41, 40, 40)"></ion-icon>
        </ion-button>
      </div>
    </div>

    <!--! obsservatonis table for employee login in desktop & talet mode - START  -->
    <div class="desktopConfig" *ngIf="isLoggedInUserEmployee">
      <ion-card *ngIf="isSampleGridOpen">
        <div class="table_component" role="region" tabindex="0">
          <table class="configTable">
            <thead>
              <tr>
                <th>Sample</th>
                <th>Type</th>
                <th>Abrasion rating or cut strand formula dependent on choice type</th>
                <th>Start</th>
                <th>End</th>
                <th>Notes</th>
                <th>Photo</th>
                <th>Options</th>
              </tr>
            </thead>
            <tbody *ngIf="labEmployeeObservationList.length!=0">
              <tr *ngFor="let observation of labEmployeeObservationList;let i=index">
                <td>{{ i + 1 }}</td>
                <td>{{ observation.type }}</td>
                <td>
                  <div>
                    {{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.other : '' }}
                  </div>
                </td>
                <td>
                  <div>
                    {{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.start : '' }}
                  </div>
                </td> <!-- * show start value if observation type contains DATA-->
                <td>
                  <div>
                    {{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.otherData.ending : '' }}
                  </div>
                </td>
                <td>{{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.otherData.observationNotes: '' }}</td>
                <td *ngIf="observation.DATA!=undefined && observation.DATA!='' && observation.DATA.externalImage != '';else noImages">
                  <div style="display: flex;flex-direction: row;justify-content: flex-start;flex-wrap: wrap;border:none;">
                    <ion-thumbnail *ngFor="let photo of observation.photos" class="configPhoto" style="margin: 0px 3px !important;">
                      <img [src]="photo" />
                    </ion-thumbnail>
                  </div>
                </td>
                <ng-template #noImages>
                    <td></td>
                </ng-template>
                <td>
                  <ion-button color="primary" size="small" fill="solid" class="configBtn optionsBtn" (click)="Edit(observation.typeValue,i,'edit')">
                    <fa-icon icon="pencil" style="color:#ffffff;" *ngIf="!dissableAdd" title="Edit {{observation.type}}"></fa-icon>
                    <fa-icon icon="eye" style="color:white;" *ngIf="dissableAdd"  title="Preview {{observation.type}}"></fa-icon>
                  </ion-button>

                  <ion-button color="primary" size="small" fill="solid" class="configBtn optionsBtn" (click)="presentPopover(observation.typeValue,i,'add',$event)" title="Add observation" *ngIf="!dissableAdd">
                    <fa-icon icon="plus" style="color:#ffffff;"></fa-icon>
                  </ion-button>
                  <ion-button *ngIf="!dissableAdd" size="small" fill="clear" color="danger" class="configBtn optionsBtn" title="Delete {{ observation.type }}" (click)="resetConfirm(observation)">
                    <fa-icon icon="trash"></fa-icon>
                  </ion-button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <p *ngIf="labEmployeeObservationList.length==0" style="color:red;text-align:center; margin-top: 50px;">No Observations available</p>
            <ion-button *ngIf="labEmployeeObservationList.length==0 && !dissableAdd" color="primary" fill="solid" style="position:absolute;bottom: 0;right:0;" (click)="presentPopover('',0,'add',$event)">
              <fa-icon icon="plus"></fa-icon>
            </ion-button>
      </ion-card>
    </div>
    <!--! obsservatonis table for employee login in desktop & tablet mode - END  -->

    <!--! observations grid for employee login in mobile screen mode - START -->
    <div class="modileConfig" *ngIf="isLoggedInUserEmployee">
      <div *ngIf="isSampleGridOpen">
        <p *ngIf="labEmployeeObservationList.length==0" style="color:red;text-align:center; margin-top: 14px;">No Observations available</p>
        <div style="display: flex;flex-direction: row;justify-content: end;">
          <ion-button *ngIf="labEmployeeObservationList.length==0 && !dissableAdd" color="primary" fill="solid"  (click)="presentPopover('',0,'add',$event)">
            <fa-icon icon="plus"></fa-icon>
          </ion-button>
        </div>
        
        <ion-card *ngFor="let observation of labEmployeeObservationList;let i=index" >
          <ion-card-header style="padding:5px 0 0 10px">
            <ion-row>
              <ion-col size="8">
                <ion-card-title>{{ observation.type }}</ion-card-title>
              </ion-col>
              <ion-col size="4" style="text-align:end">
                <ion-button size="small" color="primary" fill="solid" (click)="Edit(observation.typeValue,i, 'edit')" style="width:40px">
                  <fa-icon icon="pencil" style="color:#ffffff;" *ngIf="!dissableAdd"></fa-icon>
                  <fa-icon icon="eye" style="color:white;" *ngIf="dissableAdd"></fa-icon>
                </ion-button>
                <ion-button color="primary" size="small" fill="solid" class="configBtn optionsBtn" (click)="presentPopover(observation.typeValue,i,'add',$event)" *ngIf="!dissableAdd">
                  <fa-icon icon="plus" style="color:#ffffff;"></fa-icon>
                </ion-button>
                <ion-button size="small" *ngIf="!dissableAdd" color="danger" fill="clear" title="reset Configuration Eye A" (click)="resetConfirm(observation)" style="width:40px">
                  <fa-icon icon="trash"></fa-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </ion-card-header>
        
          <ion-card-content>
            <!-- * using div start-->
            <div style="display: flex;width: 100%;"><strong style="width: 40%;">Sample </strong><span style="width: 60%;">: {{ i + 1 }} </span> </div>
            <div style="display: flex;width: 100%;"><strong style="width: 40%;">Type </strong><span style="width: 60%;">: {{ observation.type }} </span></div>
            <div style="display: flex;width: 100%;"> <strong style="width: 40%;">Abrasion rating or cut strand formula dependent on choice type </strong> <span style="width: 60%;">: {{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.other  : '' }} </span></div>
            <div>
              <div  style="display: flex;width: 100%;">
                <strong style="width: 40%;">Start </strong><span style="width: 60%;">: {{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.start : '' }}</span>
              </div>
            </div>
            <div>
              <div  style="display: flex;width: 100%;">
                <strong style="width: 40%;">End </strong><span style="width: 60%;">: {{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.otherData.ending : '' }}</span>
              </div>
            </div>
            <div  style="display: flex;width: 100%;"><strong style="width: 40%;">Notes </strong> <span style="width: 60%;">: {{ observation.DATA!=undefined && observation.DATA!='' ? observation.DATA.otherData.observationNotes: '' }}</span> </div>
            <div style="width: 100%;"><strong>Photo </strong>
              <div *ngIf="observation.DATA!=undefined && observation.DATA!='' && observation.DATA.externalImage != '';else noImages" style="display: flex;flex-direction: row;flex-wrap: wrap;">
                <ion-thumbnail *ngFor="let photo of observation.photos" class="configPhoto" style="margin: 0px 3px !important;">
                  <img [src]="photo"/>
                </ion-thumbnail>
              </div>
              <ng-template #noImages>
                  <ion-label>
                    
                  </ion-label>
              </ng-template>
            </div>
            <!-- * using div end -->
            
          </ion-card-content>
        </ion-card>
      </div>
    </div>
      <!--! observations grid for employee login in mobile screen mode - END -->
  </div>
  <!-- & observations grid section for employee login - END -->
  
  <!--^ specimens grid section for employee login - START  -->
  <div *ngIf="isLoggedInUserEmployee" style="box-shadow: #00000029 0px 1px 4px;color:black;border: 1px solid #b8b8bd;margin:8px 10px 0 10px;border-radius: 10px;">
    <!--! specimens table for employee login in desktop & talet mode - START  -->
    <div class="desktopConfig">
      <div class="flex-container">
        <div class="flex-item flex-grow">
          <h5>Specimen Observations :</h5>
        </div>
        <div class="flex-item">
          <ion-button fill="clear" color="primary" (click)="toggleSpeciman()">
            <ion-icon slot="icon-only" slot="end" [name]="checkSpecimanSectionIsOpen() ? 'chevron-up-outline' :'chevron-down-outline'"
            style="color:rgb(41, 40, 40)"></ion-icon>
          </ion-button>
        </div>
      </div>
      <ion-card *ngIf="isSpecimanGridOpen" style="border-radius:10px">
            <table class="specimenTable">
              <thead>
                <tr>
                  <th>Specimen number</th>
                  <th>Start - Feet/Meter</th>
                  <th>End - Feet/Meter</th>
                  <th>Break Location</th>
                  <th>Break Location on Specimen feet/meter</th>
                  <th>Options</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let specimen of labEmployeeSpecimenslist;let j=index">
                  <td>{{ specimen.specimenNum }}</td>
                  <td>{{ specimen.start }}</td>
                  <td>{{ specimen.end }}</td>
                  <td>{{ specimen.breakLocation }}</td>
                  <td>{{ specimen.breakLocOnSpecimen }}</td>
                  <td>
                    <ion-button color="primary" size="small" fill="solid" class="configBtn optionsBtn" *ngIf="(!dissableAdd && inspectionHeader.INSPECTION_STATUS!=statusReopened) || (inspectionHeader.INSPECTION_STATUS==statusReopened && specimen.OBJECT_STATUS!=0)" (click)="addSpecimen(true,j)" title="edit {{specimen.specimenNum}}">
                      <fa-icon icon="pencil" title="Edit" style="color:#ffffff;" ></fa-icon>
                    </ion-button>
                    <ion-button color="primary" size="small" fill="solid" class="configBtn optionsBtn" *ngIf="dissableAdd || (inspectionHeader.INSPECTION_STATUS==statusReopened && !dissableAdd && specimen.OBJECT_STATUS==0)" title="Preview" (click)="addSpecimen(true,j)" title="Preview {{specimen.specimenNum}}">
                      <fa-icon icon="eye"  style="color:#ffffff;"></fa-icon>
                    </ion-button>
                    <ion-button *ngIf="!dissableAdd" size="small" fill="clear" color="danger" class="configBtn optionsBtn" title="Delete" (click)="deleteSpecimen(specimen.specimenNum,j)">
                      <fa-icon icon="trash" style="font-size: 20px;"></fa-icon>
                    </ion-button>
                  </td>
                </tr>
              </tbody>
            </table>
            <p *ngIf="labEmployeeSpecimenslist.length==0" style="color:red;text-align:center;">No Specimen available</p>
            <ion-button color="primary" fill="solid" style="position:absolute;bottom: 0;right:0;" (click)="addSpecimen()" *ngIf="!dissableAdd" title="Add Specimen">
              <fa-icon icon="plus" style="color:#ffffff"></fa-icon>
            </ion-button>
      </ion-card>

    </div>
    <!--! specimens table for employee login in desktop & talet mode - END -->

    <!--! specimens grid for employee login in mobile screen mode - START -->
    <div class="modileConfig">
      <div class="flex-container">
        <div class="flex-item flex-grow">
          <h5>Specimen Observations :</h5>
        </div>
        <div class="flex-item">
          <ion-button fill="clear" color="primary" (click)="toggleSpeciman()">
            <ion-icon slot="icon-only" slot="end" [name]="checkSpecimanSectionIsOpen() ? 'chevron-up-outline' :'chevron-down-outline'"
            style="color:rgb(41, 40, 40)"></ion-icon>
          </ion-button>
        </div>
      </div>
        <div *ngIf="isSpecimanGridOpen">
          <ion-card *ngFor="let specimen of labEmployeeSpecimenslist;let j=index">
            <ion-card-content>
              <div style="display:flex;flex-direction: row;width:100%;align-items: end;">
                <div style="width:70%"><strong>Specimen number :</strong> {{ specimen.specimenNum }} </div>
                <div style="width:30%;text-align:end;">
                  <ion-button size="small" color="primary" fill="solid" *ngIf="(!dissableAdd && inspectionHeader.INSPECTION_STATUS!=statusReopened) || (inspectionHeader.INSPECTION_STATUS==statusReopened && specimen.OBJECT_STATUS!=0)" (click)="addSpecimen(true,j)" style="width:40px">
                    <fa-icon icon="pencil" title="Edit" style="color:#ffffff;" ></fa-icon>
                  </ion-button>

                  <ion-button size="small" color="primary" fill="solid" *ngIf="dissableAdd || (inspectionHeader.INSPECTION_STATUS==statusReopened && !dissableAdd && specimen.OBJECT_STATUS==0)" (click)="addSpecimen(true,j)" style="width:40px">
                    <fa-icon icon="eye" style="color:#ffffff;"></fa-icon>
                  </ion-button>

                  <ion-button size="small" color="danger" fill="clear" *ngIf="!dissableAdd" (click)="deleteSpecimen(specimen.specimenNum,j)" style="width:40px">
                    <fa-icon icon="trash"></fa-icon>
                  </ion-button>
                </div>
              </div>
              
              <div><strong>Start - Feet/Meter :</strong> {{ specimen.start }} </div>
              <div><strong>End - Feet/Meter :</strong> {{ specimen.end }} </div>
              <div><strong>Break Location :</strong> {{ specimen.breakLocation }} </div>
              <div><strong>Break Location on Specimen feet/meter :</strong> {{ specimen.breakLocOnSpecimen }} </div>
            </ion-card-content>
          </ion-card>
          <p *ngIf="labEmployeeSpecimenslist.length==0" style="color: red;margin-top: 14px;text-align: center;">No specimens available</p>
          <div style="display: flex;flex-direction: row;justify-content: end;">
            <ion-button color="primary" fill="solid" (click)="addSpecimen()" *ngIf="!dissableAdd">
              <fa-icon icon="plus" style="color:#ffffff"></fa-icon>
            </ion-button>
          </div>
        </div>
    </div>
    <!--! specimens grid for employee login in mobile screen mode - END -->
  </div>
  <!--^ specimens grid section for employee login - END  -->

</ion-content>

<div *ngIf='!dissableAdd && !isLoggedInUserEmployee' class="customHelpBtn">
  <!-- <ion-fab style="position: absolute; bottom: 70px;" vertical="bottom" horizontal="end" slot="fixed" tooltip="Choose Observations" desktopEvent="none" arrow="true" poistionH="left" positionV="top"
  mobileEvent="press" duration="2000"  >
    <ion-fab-button color="primary" (mouseleave)="tooltip.hide()" (click)="helpService.helpMode ? test() : newInspection();"  style="font-size:26px!important"  matTooltip="Choose Observations" matTooltipPosition="above" #tooltip="matTooltip" matTooltipClass="observation-tooltip">
      <fa-icon class="icon-style-other" icon="plus" 
      ></fa-icon>
    </ion-fab-button>
  </ion-fab> -->
  <ion-fab style="position: absolute; bottom: 70px;" vertical="bottom" horizontal="end" slot="fixed" tooltip="Choose Observations" desktopEvent="none" arrow="true" poistionH="left" positionV="top"
  mobileEvent="press" duration="2000"  >
  <ion-fab-button color="primary" (mouseleave)="tooltip.hide()" (click)="helpService.helpMode ? test() : newInspection();"  style="font-size:26px!important"  matTooltip="Choose Observations" matTooltipPosition="above" #tooltip="matTooltip" matTooltipClass="observation-tooltip">
    <fa-icon class="icon-style-other" icon="plus" ></fa-icon>
  </ion-fab-button>
  </ion-fab>
</div>

<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer *ngIf="!footerClose" (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
  (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>