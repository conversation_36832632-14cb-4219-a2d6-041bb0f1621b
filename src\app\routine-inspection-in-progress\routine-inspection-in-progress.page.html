<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="primary">
      <ion-button color="primary" slot="end" class="icon" (click)="toggleSearch()" *ngIf="searchIcon">
        <fa-icon class="icon-style" icon="magnifying-glass"></fa-icon>
      </ion-button>
      <!-- <ion-button color="primary" slot="end" class="icon" (click)="presentPopover('filter')">
        <fa-icon class="icon-style" icon="filter"></fa-icon>
      </ion-button> -->
      <!-- <ion-button color="primary" slot="end"  class="icon" (click)="presentPopover('dotIcon')">
        <fa-icon class="icon-style"  icon="ellipsis-v"></fa-icon>
      </ion-button>  -->
    </ion-buttons>
    <ion-title>In Progress</ion-title>
  </ion-toolbar>

  <ion-toolbar *ngIf="searchbar" style="padding-bottom:5px;">
    <ion-searchbar class="searchBar" showCancelButton debounce="500" (ionCancel)="cancelHistoricalItem($event)" animated
      (ionInput)="searchHistoricalItem($event)">
    </ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list lines="none">
    <ion-item-sliding *ngFor="let item of inProgressData; let i = index">
      <ion-item style="border-top:1px solid rgba(199, 199, 199, 0.753);">
        <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start" icon="circle"
          *ngIf="item.SYNC_STATUS == '3'" class="syncError"
          (click)="getInfoMsg($event, item)" tappable>
        </fa-icon>
        <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start" icon="circle"
          *ngIf="item.SYNC_STATUS == '2'" class="syncProgress"
          (click)="getInfoMsg($event, item)" tappable>
        </fa-icon>
        <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start"
          [icon]="['far', 'circle']" *ngIf="(item.SYNC_STATUS == '1')"
          class="syncReady" (click)="getInfoMsg($event, item)"
          tappable>
        </fa-icon>
        <fa-icon style="margin-left: 0px !important; margin-right: 15px !important" slot="start"
          [icon]="['far', 'circle']" *ngIf="(item.SYNC_STATUS == '0')"
          class="syncNotReady" (click)="getInfoMsg($event, item)"
          tappable>
        </fa-icon>
        <ion-item>
          <!-- <ion-checkbox color="primary" (ionChange)="onChecked(item.LID)" [value]="item.LID" [disabled]="(item.SYNC_STATUS == 1 || item.SYNC_STATUS == 2)"></ion-checkbox> -->
        </ion-item>
        <ion-label style="margin-left: 10px;">
          <h2
            *ngIf="(item.LMD_DATA.CERT_NAME != undefined && item.LMD_DATA.CERT_NAME != '') && item.LMD_TYPE != 'AssetActivity'"
            style="color:rgb(37, 91, 145);">{{'Cert' }} : {{item.LMD_DATA.CERT_NAME}}</h2>
          <h2
            *ngIf="(item.LMD_DATA.CERT_NAME == undefined || item.LMD_DATA.CERT_NAME == '') && item.LMD_TYPE != 'AssetActivity'"
            style="color:rgb(37, 91, 145);">{{'Asset' }} : {{item.LMD_DATA.ASSET_NAME}}</h2>
          
          <p style="color:rgba(104, 102, 102, 0.753);">Type: {{item.LMD_NAME}}</p>
        </ion-label>
        <ion-buttons (click)="icon(i)" tappable>
          <ion-button style="padding-bottom: 8px;">
            <ion-icon slot="icon-only" slot="end" [name]="checkSectionIsOpen(i) ? 'chevron-up-outline' :'chevron-down-outline'"
              style="color:rgb(41, 40, 40)"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-item>

      <ion-item *ngIf="checkSectionIsOpen(i)" lines="none"
        style="margin-top:0 !important;border-bottom:1px rgba(172, 172, 172, 0.829);">
        <ion-label style="padding-left: 20px;">
          <ion-text color="primary" *ngIf="checkSectionIsOpen(i)">
            <div>
              <div *ngIf="item.LMD_TYPE === 'RoutineInspection'" style="display: flex !important; width: 100% !important;">
                <div style="width:50% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                  <!-- <p *ngIf="item.LMD_DATA.workOrder">Workorder</p>
                  <p *ngIf="item.LMD_DATA.assetName">Asset</p>
                  <p *ngIf="item.LMD_DATA.totalWorkingHour">Working Hours</p>
                  <p *ngIf="item.LMD_DATA.totalWorkingOperation">Working Operation</p>
                  <p *ngIf="item.LMD_DATA.lengthCropped">Length Cropped</p>
                  <p *ngIf="item.LMD_DATA.endCropped">End being Repaired</p>
                  <p *ngIf="item.LMD_DATA.eventDate">Event Date</p> -->
                  <p *ngIf="item.LMD_DATA.ASSET_NAME">Asset Name</p>
                  <p *ngIf="item.LMD_DATA.INSPECTION_DATE && item.LMD_DATA.INSPECTION_DATE != 'Invalid date'">Inspection Date</p>
                  <p *ngIf="item.LMD_DATA.CONSTRUCTION_TYPE">Construction</p>
                  <p *ngIf="item.LMD_DATA.APPLICATION">Application</p>
                  <p *ngIf="item.LMD_DATA.DIAM">Diameter</p>
                  <p *ngIf="item.LMD_DATA.PRODUCT">Product</p>
                  <p *ngIf="item.LMD_DATA.WINCH_NAME">Winch</p>
                  <p *ngIf="item.LMD_DATA.END_IN_USE">End in Use</p>
                  <p *ngIf="item.LMD_DATA.ADDITIONAL_NUMBER_OF_JACKETS_REPAIR">Jacket Repairs</p>
                  <p *ngIf="item.LMD_DATA.ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES">Jacket Ruptures</p>
                  <p *ngIf="item.LMD_DATA.EXTRA_PASS_FAIL">Pass/Fail</p>
                  <p *ngIf="item.LMD_DATA.EXTRA_TOTAL_OF_OPERATION">Total Operation</p>
                  <p *ngIf="item.LMD_DATA.EXTRA_TOTAL_WORKING_HOURS">Working Hours</p>
                 
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_SMALLEST_VISIBLE_DIAM">Smallestr Diam(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_LARGEST_VISIBLE_DIAM">Largest Diam(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_CUT_STRAND_COUNT_COVER">Cut Strand Count(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_SMALLEST_VISIBLE_DIAM">Smallestr Diam(Z2)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_LARGEST_VISIBLE_DIAM">Largest Diam(Z2)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_CUT_STRAND_COUNT_COVER">Cut Strand Count(Z2)</p>
  
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION">Wire Breaks in Termination(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_WAVINESS_GAP_MEASUREMENT">Waviness Gap Measurement(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_MAX_WIRE_BREAKES">Max Wire Breaks(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_WAVINESS_GAP_MEASUREMENT">Waviness Gap Measurement(Z2)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION">Wire Breaks in Termination(Z2)</p>
  
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_VALLEY_WIRE_BREAKS_IN_6_DIAM">Wire Breaks in 6 Diam</p>
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_WIRE_BREAKS_IN_30_DIAM">Wire Breaks in 30 Diam</p>
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_EXTERNAL_COROSION">External Corosion</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_EXTERNAL_ABRASION">External Abrasion(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_INTERNAL_ABRASION">Internal Abrasion(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_CUT_YARN_COUNT">Cut Yarn Count(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_LENGTH_OF_GLAZING">Length of Glazing(Z1)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_EXTERNAL_ABRASION">External Abrasion(Z2)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_INTERNAL_ABRASION">Internal Abrasion(Z2)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_CUT_YARN_COUNT">Cut Yarn Count(Z2)</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_LENGTH_OF_GLAZING">Length of Glazing(Z2)</p>
                  <p *ngIf="item.LMD_DATA.TWIST_PER_METER">wist Per Meter</p>
                  <p *ngIf="item.LMD_DATA.CHAFE_GEAR_HOLE_COUNT">Chafe Gear Hole Count</p>
                  <p *ngIf="item.LMD_DATA.TAIL_BEARING_POINT_CONNECTION">Tail Bearing Point Connection</p>
                  <p *ngIf="item.LMD_DATA.TAIL_FULLY_CUT_STRANDS">Tail Fully Cut Strand</p>
                </div>
                <div style="width:50% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                  <p *ngIf="item.LMD_DATA.ASSET_NAME">{{item.LMD_DATA.ASSET_NAME}}</p>
                  <p *ngIf="item.LMD_DATA.INSPECTION_DATE && item.LMD_DATA.INSPECTION_DATE != 'Invalid date'"> {{item.LMD_DATA.INSPECTION_DATE}}</p>
                  <p *ngIf="item.LMD_DATA.CONSTRUCTION_TYPE">{{item.LMD_DATA.CONSTRUCTION_TYPE}}</p>
                  <p *ngIf="item.LMD_DATA.APPLICATION">{{item.LMD_DATA.APPLICATION}}</p>
                  <p *ngIf="item.LMD_DATA.DIAM">{{item.LMD_DATA.DIAM}}</p>
                  <p *ngIf="item.LMD_DATA.PRODUCT">{{item.LMD_DATA.PRODUCT}}</p>
                  <p *ngIf="item.LMD_DATA.WINCH_NAME">{{item.LMD_DATA.WINCH_NAME}}</p>
                  <p *ngIf="item.LMD_DATA.END_IN_USE">{{item.LMD_DATA.END_IN_USE}}</p>
                  <p *ngIf="item.LMD_DATA.ADDITIONAL_NUMBER_OF_JACKETS_REPAIR">{{item.LMD_DATA.ADDITIONAL_NUMBER_OF_JACKETS_REPAIR}}</p>
                  <p *ngIf="item.LMD_DATA.ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES">{{item.LMD_DATA.ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES}}</p>
                  <p *ngIf="item.LMD_DATA.EXTRA_PASS_FAIL">{{item.LMD_DATA.EXTRA_PASS_FAIL}}</p>
                  <p *ngIf="item.LMD_DATA.EXTRA_TOTAL_OF_OPERATION">{{item.LMD_DATA.EXTRA_TOTAL_OF_OPERATION}}</p>
                  <p *ngIf="item.LMD_DATA.EXTRA_TOTAL_WORKING_HOURS">{{item.LMD_DATA.EXTRA_TOTAL_WORKING_HOURS}}</p>
                 
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_SMALLEST_VISIBLE_DIAM">{{item.LMD_DATA.ZONE_ONE_SMALLEST_VISIBLE_DIAM}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_LARGEST_VISIBLE_DIAM">{{item.LMD_DATA.ZONE_ONE_LARGEST_VISIBLE_DIAM}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_CUT_STRAND_COUNT_COVER">{{item.LMD_DATA.ZONE_ONE_CUT_STRAND_COUNT_COVER}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_SMALLEST_VISIBLE_DIAM">{{item.LMD_DATA.ZONE_TWO_SMALLEST_VISIBLE_DIAM}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_LARGEST_VISIBLE_DIAM">{{item.LMD_DATA.ZONE_TWO_LARGEST_VISIBLE_DIAM}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_CUT_STRAND_COUNT_COVER">{{item.LMD_DATA.ZONE_TWO_CUT_STRAND_COUNT_COVER}}</p>
  
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION">{{item.LMD_DATA.ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_WAVINESS_GAP_MEASUREMENT">{{item.LMD_DATA.ZONE_ONE_WAVINESS_GAP_MEASUREMENT}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_MAX_WIRE_BREAKES">{{item.LMD_DATA.ZONE_ONE_MAX_WIRE_BREAKES}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_WAVINESS_GAP_MEASUREMENT">{{item.LMD_DATA.ZONE_TWO_WAVINESS_GAP_MEASUREMENT}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION">{{item.LMD_DATA.ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION}}</p>
                  
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_VALLEY_WIRE_BREAKS_IN_6_DIAM">{{item.LMD_DATA.ANOMOLIES_VALLEY_WIRE_BREAKS_IN_6_DIAM}}</p>
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_WIRE_BREAKS_IN_30_DIAM">{{item.LMD_DATA.ANOMOLIES_WIRE_BREAKS_IN_30_DIAM}}</p>
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_EXTERNAL_COROSION">{{item.LMD_DATA.ANOMOLIES_EXTERNAL_COROSION}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_EXTERNAL_ABRASION">{{item.LMD_DATA.ZONE_ONE_EXTERNAL_ABRASION}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_INTERNAL_ABRASION">{{item.LMD_DATA.ZONE_ONE_INTERNAL_ABRASION}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_CUT_YARN_COUNT">{{item.LMD_DATA.ZONE_ONE_CUT_YARN_COUNT}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_ONE_LENGTH_OF_GLAZING">{{item.LMD_DATA.ZONE_ONE_LENGTH_OF_GLAZING}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_EXTERNAL_ABRASION">{{item.LMD_DATA.ZONE_TWO_EXTERNAL_ABRASION}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_INTERNAL_ABRASION">{{item.LMD_DATA.ZONE_TWO_INTERNAL_ABRASION}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_CUT_YARN_COUNT">{{item.LMD_DATA.ZONE_TWO_CUT_YARN_COUNT}}</p>
                  <p *ngIf="item.LMD_DATA.ZONE_TWO_LENGTH_OF_GLAZING">{{item.LMD_DATA.ZONE_TWO_LENGTH_OF_GLAZING}}</p>
                  <p *ngIf="item.LMD_DATA.TWIST_PER_METER">{{item.LMD_DATA.TWIST_PER_METER}}</p>
                  <p *ngIf="item.LMD_DATA.CHAFE_GEAR_HOLE_COUNT">{{item.LMD_DATA.CHAFE_GEAR_HOLE_COUNT}}</p>
                  <p *ngIf="item.LMD_DATA.TAIL_BEARING_POINT_CONNECTION">{{item.LMD_DATA.TAIL_BEARING_POINT_CONNECTION}}</p>
                  <p *ngIf="item.LMD_DATA.TAIL_FULLY_CUT_STRANDS">{{item.LMD_DATA.TAIL_FULLY_CUT_STRANDS}}</p>
                </div>
              </div>
              <div *ngIf="item.LMD_TYPE === 'RoutineInspection'" style="display: flex !important; width: 100% !important;">
                <div style="width:50% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_SELECTED.length > 0">Anomalies</p>
                </div>
                <div style="width:50% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                  <p *ngIf="item.LMD_DATA.ANOMOLIES_SELECTED.length > 0"><span style="overflow: visible; white-space: break-spaces;">
                    <span *ngFor="let anomalyData of item.LMD_DATA.ANOMOLIES_SELECTED; let x = index">{{anomalyData.ANOMOLY_NAME}}<span *ngIf="x<(item.LMD_DATA.ANOMOLIES_SELECTED.length -1)">,&nbsp;</span> </span>
                  </span></p>
                </div>
              </div>
              <div *ngIf="item.LMD_TYPE === 'RoutineInspection'" style="display: flex !important; width: 100% !important;">
                <div style="width:50% !important; white-space: nowrap; overflow:hidden; text-overflow: ellipsis;">
                  <p *ngIf="item.LMD_DATA.NOTES">Notes</p>
                </div>
                <div style="width:50% !important; white-space: normal; overflow:visible; text-overflow: ellipsis;">
                  <p *ngIf="item.LMD_DATA.NOTES">{{item.LMD_DATA.NOTES}}</p>
                </div>
              </div>
            </div>

          </ion-text>
          <ion-buttons style="float:right;padding-top:10px;">
            <ion-button style="padding-bottom: 8px;" *ngIf="(item.SYNC_STATUS == '0')">
              <fa-icon class="icon-style" slot="end" icon="trash" style="color:rgba(224, 77, 77, 0.979)" class="fa-lg"
                (click)="inProgressItemTrash(i,item)"></fa-icon>
            </ion-button>&nbsp;
            <ion-button style="padding-bottom: 8px;" *ngIf="item.SYNC_STATUS == 0 || item.SYNC_STATUS == 3" (click)="uploadItem(item)">
              <fa-icon class="icon-style" slot="end" icon="upload" class="fa-upload" >
              </fa-icon>
            </ion-button>
          </ion-buttons>
        </ion-label>
      </ion-item>
    </ion-item-sliding>
  </ion-list>
  <div *ngIf='inProgressData && inProgressData.length == 0'
    style="width: 100% !important; text-align: center !important; padding-top: 30px !important">
    {{'No in progress items found.'}}</div>
  <!-- <ion-fab *ngIf='selectedLmds.length > 0' vertical="bottom" horizontal="end" slot="fixed" >
    <ion-fab-button color="primary">
      <fa-icon class="icon-style" icon="chevron-circle-left" style="font-size: 25px;" class="icon-style"></fa-icon>
    </ion-fab-button>
    <ion-fab-list side="start">
      <ion-fab-button color="primary" (click)="submitLmd()" style="margin: 0px 15px 0px 15px">
        <fa-icon class="icon-style" icon="upload" style="font-size: 16px;" class="icon-style"></fa-icon>
      </ion-fab-button>
      <ion-fab-button color="primary" (click)="deleteLmd()" style="margin: 0px 15px 0px 15px">
        <fa-icon class="icon-style" icon="trash" style="font-size: 16px;" class="icon-style"></fa-icon>
      </ion-fab-button>
    </ion-fab-list>
  </ion-fab> -->
</ion-content>
<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<app-footer (Menu)="openMenu()" (LineTracker)="goToLineTracker()" (Inspections)="gotoInspections()"
    (Resources)="gotoResources()" (Contact)="gotoContact()"></app-footer>